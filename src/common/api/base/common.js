import { post } from 'COMMON/utils/requestUtils';

// 上传加密 bos
export const updateToBos = (params, options) => {
    return post('/core/bos/create', params, options);
};

// 查询bos文件临时token
export const getBosToken = (params) => {
    return post('/core/bos/queryTempToken', params);
};

// 查询业务模块的Token
export const getBusinessModuleToken = (params) => {
    return post('/core/business/module/token', params);
};

// 查询代码库模块名称
export const getIcodeList = (params) => {
    return post('/core/common/icode/list', params);
};

// 查询用户名模块名称
export const getUserList = (params) => {
    return post('/core/user/apigo/list', params);
};

// 查询itp用例组
export const getItpCaseGroupList = (params) => {
    return post('/core/common/itp/node/groupnode', params);
};

// 查询业务线关联的质量数字化关联业务
export const getDigitRelation = (params) => {
    return post('/core/quality/digitization/relation/list', params);
};

// 新建业务线关联的质量数字化关联业务
export const updateDigitRelation = (params) => {
    return post('/core/quality/digitization/relation/create', params);
};

// 查询质量数字化二级业务
export const queryDigitRelation = (params) => {
    return post('/core/quality/digitization/relation/name', params);
};
