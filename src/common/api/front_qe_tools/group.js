import {post} from 'COMMON/utils/requestUtils';

// 用例组相关

// 查询用例组
export const getGroupList = (params) => {
    return post('/core/group/list', params);
};

// 创建用例组
export const createGroup = (params) => {
    return post('/core/group/create', params);
};

// 更新用例组
export const updateGroup = (params) => {
    return post('/core/group/update', params);
};

// 删除用例组
export const deleteGroup = (params) => {
    return post('/core/group/delete', params);
};

// 归档用例组
export const archiveGroup = (params) => {
    return post('/core/group/archive', params);
};

// 取消归档用例组
export const unarchiveGroup = (params) => {
    return post('/core/group/unarchive', params);
};