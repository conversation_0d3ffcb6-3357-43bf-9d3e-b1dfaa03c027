import { post } from 'COMMON/utils/requestUtils';

// UI智能执行任务创建
export const createUIGenerateTask = (params) => {
    return post('/core/ui/generate/plan/create', params);
};
// 取消执行任务
export const cancelUIGenerateTask = (params) => {
    return post('/core/ui/generate/plan/cancel', params);
};
// 查询用例的智能执行任务
export const getUIGenerateTaskDetail = (params) => {
    return post('/core/ui/generate/plan/detail', params);
};
// 查询用例的生成详情
export const getUIGenerateCaseDetail = (params) => {
    return post('/core/ui/generate/case/detail', params);
};

// 获取智能执行配置
export const getUIGenerateTaskConfig = (params) => {
    return post('/core/ui/generate/plan/config', params);
};
