import { post } from 'COMMON/utils/requestUtils';

// 查询计划列表
export const getPlanList = (params) => {
    return post('/core/plan/list', params);
};

// 查询计划详情
export const getPlanDetail = (params) => {
    return post('/core/regression/plan/detail', params);
};

// 创建计划
export const createPlan = (params) => {
    return post('/core/plan/create', params);
};

// 删除计划
export const deletePlan = (params) => {
    return post('/core/regression/plan/delete', params);
};

// 修改计划
export const updatePlan = (params) => {
    return post('/core/regression/plan/update', params);
};

// 删除任务
export const deleteTask = (params) => {
    return post('/core/regression/round/taskDelete', params);
};

// 查询轮次
export const getRoundList = (params) => {
    return post('/core/regression/round/list', params);
};

// 查询轮次详情
export const getRoundDetail = (params) => {
    return post('/core/regression/round/detail', params);
};

// 查询轮次用例树
export const getRoundTree = (params) => {
    return post('/core/regression/round/case/tree', params);
};

// 查询单个轮次用例自动化执行信息
export const getRoundCloudList = (params) => {
    return post('/core/regression/round/cloud/list', params);
};

// 查询单个用例自动化执行信息
export const getCaseCloudList = (params) => {
    return post('/core/regression/round/log/list', params);
};

// 更新当前轮次用例
export const updateRoundCase = (params) => {
    return post('/core/regression/round/case/update', params);
};

// 开启新轮次
export const startNextRound = (params) => {
    return post('/core/regression/round/new', params);
};

// 结束轮次
export const finishCurSign = (params) => {
    return post('/core/regression/round/end', params);
};

// 签章重置
export const resetSignInfo = (params) => {
    return post('/core/regression/round/signReset', params);
};

// 测试计划签章
export const createPlanSignInfo = (params) => {
    return post('/core/regression/round/sign', params);
};

// 测试计划签章更新 （仅更新最新一次签章内容）
export const updatePlanSignInfo = (params) => {
    return post('/core/regression/round/sign/update', params);
};

// 单用例任务详细信息
export const getSingleCaseDetail = (params) => {
    return post('/core/regression/round/nodeDetail', params);
};

// 单用例任务重试
// export const retrySingleCase = (params) => {
//     return post('/core/regression/automan/caseRetry', params);
// };

// 自动化任务创建
export const createCloudTask = (params) => {
    return post('/core/regression/automan/create', params);
};

// 自动化任务取消
export const cancelCloudTask = (params) => {
    return post('/core/regression/automan/cancel', params);
};

// 自动化任务详情
export const getCloudTaskDetail = (params) => {
    return post('/core/regression/automan/config', params);
};

// 自动化任务状态
export const getCloudTaskStatus = (params) => {
    return post('/core/regression/round/automanDetail', params);
};

// 新建计划模板
export const createPlanTemplate = (params) => {
    return post('/core/plan/template/create', params);
};
// 查询模板列表
export const getPlanTemplateList = (params) => {
    return post('/core/plan/template/list', params);
};
// 查询模板详情
export const getPlanTemplateDetail = (params) => {
    return post('/core/plan/template/detail', params);
};
// 编辑计划模板
export const updatePlanTemplate = (params) => {
    return post('/core/plan/template/update', params);
};
// 模板创建测试计划
export const createPlanByTemplate = (params) => {
    return post('/core/plan/template/task/create', params);
};
// 删除模板
export const deletePlanTemplate = (params) => {
    return post('/core/plan/template/delete', params);
};
// 逻辑设备池查询
export const getDevicePoolList = (params) => {
    return post('/core/device/pool/list', params);
};
// 巡检任务创建
export const createCheckTask = (params) => {
    return post('/core/daily/task/create', params);
};
// 巡检任务更新
export const updateCheckTask = (params) => {
    return post('/core/daily/task/update', params);
};
// 查询度量数据
export const getMetricList = (params) => {
    return post('/core/plan/round/metric/list', params);
};

// 服务端模版计划创建
export const createServerPlanTemplate = (params) => {
    return post('/core/plan/server/template/create', params);
};
// 更新模板
export const updateServerPlanTemplate = (params) => {
    return post('/core/plan/server/template/update', params);
};
// 删除模板
export const deleteServerPlanTemplate = (params) => {
    return post('/core/plan/server/template/delete', params);
};
// 查询模板列表
export const getServerPlanTemplateList = (params) => {
    return post('/core/plan/server/template/list', params);
};
// 模板计划概览
export const getServerPlanTemplateDetail = (params) => {
    return post('/core/plan/server/template/detail', params);
};
// 模板计划执行
export const executeServerPlanTemplate = (params) => {
    return post('/core/plan/server/template/task/new', params);
};
// 重新执行任务
export const reExecuteServerPlanTemplate = (params) => {
    return post('/core/plan/server/template/task/rerun', params);
};
// 终止执行
export const endServerPlanTemplate = (params) => {
    return post('/core/plan/server/template/task/cancel', params);
};
// 报告摘要
export const getServerPlanTemplateTaskReport = (params) => {
    return post('/core/plan/server/template/history/abstract', params);
};
