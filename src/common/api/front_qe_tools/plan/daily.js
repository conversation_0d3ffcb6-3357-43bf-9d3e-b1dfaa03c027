import { post, get } from 'COMMON/utils/requestUtils';
// 巡检任务创建
export const createDailyTask = (params) => {
    return post('/core/daily/plan/create', params);
};
// 巡检任务更新
export const updateDailyTask = (params) => {
    return post('/core/daily/plan/update', params);
};
// 查询计划列表
export const queryDailyTaskList = (params) => {
    return post('/core/daily/plan/list', params);
};
// 查询计划详细信息
export const queryDailyTaskDetail = (params) => {
    return post('/core/daily/plan/detail', params);
};
// 计划删除
export const deleteDailyPlan = (params) => {
    return post('/core/daily/plan/delete', params);
};
// 计划启动
export const startDailyPlan = (params) => {
    return post('/core/daily/plan/start', params);
};
// 计划停止
export const pauseDailyPlan = (params) => {
    return post('/core/daily/plan/end', params);
};
// 查询轮次列表
export const queryDailyRoundList = (params) => {
    return post('/core/daily/round/list', params);
};
// 查询自动化执行列表
export const queryCloudDailyRoundList = (params) => {
    return post('/core/daily/round/cloud/list', params);
};
// 根据自动化计划Id查询任务列表
export const queryCloudDailyTaskList = (params) => {
    return post('/core/regression/automan/nodeDetail/list', params);
};
