import {post} from 'COMMON/utils/requestUtils';

// 查询签章所处阶段
export const getSignStage = (params) => {
    return post('/core/sign/stage/status/query', params);
};

// 更新签章阶段状态
export const changeSignStage = (params) => {
    return post('/core/sign/stage/update', params);
};

// 查询签章历史轮次记录
export const getSignHistory = (params) => {
    return post('/core/sign/rounds/query', params);
};

// 重置签章信息
export const resetSignInfo = (params) => {
    return post('/core/sign/reset', params);
};

// 开启下一轮签章
export const startNextRound = (params) => {
    return post('/core/sign/stage/round/new', params);
};

// 完成测试
export const finishCurSign = (params) => {
    return post('/core/sign/stage/finish', params);
};