import { post, get } from 'COMMON/utils/requestUtils';

// 环境配置
// 查询环境配置列表
export const getEnvList = (params) => {
    return post('/core/module/env/list', params);
};

// 查询环境配置详情
export const getEnvDetail = (params) => {
    return post('/core/module/env/detail', params);
};

// 创建环境配置
export const createEnv = (params) => {
    return post('/core/module/env/create', params);
};

// 更新环境配置
export const updateEnv = (params) => {
    return post('/core/module/env/update', params);
};

// 删除环境
export const deleteEnv = (params) => {
    return post('/core/module/env/delete', params);
};

// 测试片段
// 查询测试片段列表
export const getSnippetList = (params) => {
    return post('/core/module/snippet/list', params);
};

// 创建测试片段
export const createSnippet = (params) => {
    return post('/core/module/snippet/create', params);
};

// 更新测试片段
export const updateSnippet = (params) => {
    return post('/core/module/snippet/update', params);
};

// 删除测试片段
export const deleteSnippet = (params) => {
    return post('/core/module/snippet/delete', params);
};

// scheme片段
// 查询scheme片段列表
export const getSchemeList = (params) => {
    return post('/core/module/scheme/list', params);
};

// 创建scheme片段
export const createScheme = (params) => {
    return post('/core/module/scheme/create', params);
};

// 更新scheme片段
export const updateScheme = (params) => {
    return post('/core/module/scheme/update', params);
};

// 删除scheme片段
export const deleteScheme = (params) => {
    return post('/core/module/scheme/delete', params);
};

// app
// 查询app列表
export const getAppList = (params) => {
    return post('/core/module/app/list', params);
};

// 创建app
export const createApp = (params) => {
    return post('/core/module/app/create', params);
};

// 更新app
export const updateApp = (params) => {
    return post('/core/module/app/update', params);
};

// 删除app
export const deleteApp = (params) => {
    return post('/core/module/app/delete', params);
};

// server
// 查询server列表
export const getServerList = (params) => {
    return post('/core/module/server/list', params);
};

// 创建server
export const createServer = (params) => {
    return post('/core/module/server/create', params);
};

// 更新server
export const updateServer = (params) => {
    return post('/core/module/server/update', params);
};

// 删除server
export const deleteServer = (params) => {
    return post('/core/module/server/delete', params);
};

// 全局变量
// 查询全局变量列表
export const getParamList = (params) => {
    return post('/core/module/params/list', params);
};

// 创建全局变量
export const createParams = (params) => {
    return post('/core/module/params/create', params);
};

// 更新全局变量
export const updateParams = (params) => {
    return post('/core/module/params/update', params);
};

// 删除全局变量
export const deleteParams = (params) => {
    return post('/core/module/params/delete', params);
};
// 查询个人配置内容
export const getUserConfig = (params) => {
    return post('/core/module/env/user/detail', params);
};

// 更新个人配置内容
export const updateUserConfig = (params) => {
    return post('/core/module/env/user/update', params);
};

// 获取数据库列表
export async function getDBList(params) {
    return post('/core/module/db/list', params);
}

// 创建数据库配置
export async function createDB(params) {
    return post('/core/module/db/create', params);
}

// 更新数据库配置
export async function updateDB(params) {
    return post('/core/module/db/update', params);
}

// 删除数据库配置
export async function deleteDB(params) {
    return post('/core/module/db/delete', params);
}

// 创建Redis数据库配置
export async function createRedis(params) {
    return post('/core/module/redis/create', params);
}

// 更新Redis数据库配置
export async function updateRedis(params) {
    return post('/core/module/redis/update', params);
}

// 删除Redis数据库配置
export async function deleteRedis(params) {
    return post('/core/module/redis/delete', params);
}

// 获取Redis数据库配置
export async function getRedisList(params) {
    return post('/core/module/redis/list', params);
}

// 测试数据库连接
export const testDBConnection = (params) => {
    return post('/core/module/env/connect', params);
};