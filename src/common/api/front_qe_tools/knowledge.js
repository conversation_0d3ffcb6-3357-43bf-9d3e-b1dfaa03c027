import { post } from 'COMMON/utils/requestUtils';

// 查询知识目录树
export function getTreeNodeKnowledge(params) {
    return post('/core/tree/node/knowledge/list', params);
}
// 查询节点知识信息
export function getCaseNodeKnowledge(params) {
    return post('/core/case/node/knowledge/query', params);
}
// 编辑节点知识信息
export function updateCaseNodeKnowledge(params) {
    return post('/core/case/node/knowledge/update', params);
}