import {post} from 'COMMON/utils/requestUtils';
// 目录树相关

// 查询目录树
export const getTreeNodeList = (params) => {
    return post('/core/tree/node/query', params);
};

// treeNodeId查询目录树
export const getTreeNodeListByTreeNode = (params) => {
    return post('/core/case/tree/queryByTreeNode', params);
};

// 创建目录树
export const createTreeNode = (params) => {
    return post('/core/tree/node/create', params);
};

// 更新目录节点
export const updateTreeNode = (params) => {
    return post('/core/tree/node/update', params);
};

// 查询用例扩展信息
export const getCaseExtraInfo = (params) => {
    return post('/core/case/extra', params);
};

// 查询用例映射关系（1.0 / 2.0 映射关系）
export const getTransformCaseInfo = (params) => {
    return post('/core/transform/case', params);
};

// 删除目录节点
export const deleteTreeNode = (params) => {
    return post('/core/tree/node/delete', params);
};

// 移动目录节点
export const dropTreeNode = (params) => {
    return post('/core/tree/node/migrate', params);
};

// 复制目录节点
export const copyTreeNode = (params) => {
    return post('/core/tree/node/duplicate', params);
};
