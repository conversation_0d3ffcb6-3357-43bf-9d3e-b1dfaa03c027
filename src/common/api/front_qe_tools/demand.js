import { post } from 'COMMON/utils/requestUtils';

// 查询需求文档树
export const getTreeNodeDocList = (params) => {
    return post('/core/doc/tree/query', params);
};

// 查询icafe卡片的层级关系
export const getIcafeRelation = (params) => {
    return post('/core/icafe/tiers', params);
};

// 查询icafe列表的详细信息（包括知识库链接）
export const getIcafeRelationDetail = (params) => {
    return post('/core/icafe/detail', params);
};

// 创建需求文档节点
export const createTreeNodeDoc = (params) => {
    return post('/core/doc/tree/create', params);
};

// 删除需求文档节点
export const deleteTreeNodeDoc = (params) => {
    return post('/core/doc/node/delete', params);
};

// 节点编辑
export const updateTreeNodeDoc = (params, options) => {
    return post('/core/doc/node/update', params, options);
};

// 节点内容编辑
export const updateTreeNodeDocContent = (params, options) => {
    return post('/core/doc/node/content/update', params, options);
};

// 查询生成任务的详情
export const getGenerateTaskDetail = (params) => {
    return post('/core/generate/task/detail', params);
};

// 批量查询生成任务
export const getGenerateTaskList = (params) => {
    return post('/core/generate/task/list', params);
};

// 创建生成任务
export const createGenerateTask = (params) => {
    return post('/core/generate/task/create', params);
};

// 采纳生成节点
export const adoptGenerateNode = (params) => {
    return post('/core/doc/node/adoptionAsync', params);
};
// 批量查询执行中的接口生成任务
export const getInterfaceTaskList = (params) => {
    return post('/core/generate/interface/list', params);
};
// 查询接口生成任务详情
export const getInterfaceTaskDetail = (params) => {
    return post('/core/generate/interface/detail', params);
};
