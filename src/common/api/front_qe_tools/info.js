import {post} from 'COMMON/utils/requestUtils';

// 编辑用例的成员配置
export function updateMemberInfo(params) {
    return post('/core/case/member/update', params);
}
// 查询用例数据
export function getCaseMetric(params) {
    return post('/core/case/metric/info', params);
}
// 查询签章的度量数据
export function getSignMetric(params) {
    return post('/core/sign/metric/info', params);
}
// 查询目录节点配置信息
export const getTreeNodeConfig = (params) => {
    return post('/core/tree/node/config/query', params);
};
// 编辑目录节点配置信息
export const editTreeNodeConfig = (params) => {
    return post('/core/tree/node/update', params);
};