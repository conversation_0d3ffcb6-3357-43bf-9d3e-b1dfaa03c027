import {post} from 'COMMON/utils/requestUtils';

// 查询用例录制记录
export const queryRecordList = (params) => {
    return post('/core/recommend/record/list', params);
};

// 恢复录制版本数据
export const recoveryRecord = (params) => {
    return post('/core/recommend/record/recover', params);
};

// 新建录制
export const createRecord = (params) => {
    return post('/core/recommend/record/create', params);
};

// 完成录制
export const finishRecord = (params) => {
    return post('/core/recommend/record/finish', params);
};

// 同步录制结果到测试用例
export const refreshCase = (params) => {
    return post('/core/recommend/record/refreshCase', params);
}

// 查询录制记录详情
export const queryRecordDetail = (params) => {
    return post('/core/recommend/record/detail', params);
};

// 查询未结束的录制任务
export const getRecordTaskList = (params) => {
    return post('/core/recommend/record/tree/list', params);
};
