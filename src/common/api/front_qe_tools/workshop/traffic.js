import { post, get } from 'COMMON/utils/requestUtils';

const BASE_URL = '/itpCase';
// const BASE_URL = '';

// 新增流量任务接口
export const createTrafficTask = (params) => {
    return post('/core/flow/generate/task/create', params);
};

// 获取某个流量配置任务的详情
export const getTrafficTaskDetail = (params) => {
    return post('/core/flow/generate/task/detail', params);
};

// 删除某个流量配置任务
export const deleteTrafficTask = (params) => {
    return post('/core/flow/generate/task/delete', params);
};

// 修改流量任务
export const updateTrafficTask = (params) => {
    return post('/core/flow/generate/task/update', params);
};

// 流量任务配置列表分页查询
export const getTrafficTaskList = (params) => {
    return post('/core/flow/generate/task/page/list', params);
};

// 流量任务执行记录分页查询
export const getTrafficTaskRecordList = (params) => {
    return post('/core/flow/generate/task/execute/list', params);
};

// 查询某次执行的执行概览信息
export const getTrafficTaskRecordOverview = (params) => {
    return post('/core/flow/generate/task/execute/data', params);
};

// 查询某次执行的生成用例信息（分页查询接口） 不需要了
// export const getTrafficTaskRecordCaseList = (params) => {
//     return post('', params);
// };
// 查询用户XSTP产品线
export const getUserXSTPProductLine = (params) => {
    return post('/core/flow/generate/xstp/product/list', params);
};
// 查询用户XSTP模块信息
export const getUserXSTPModules = (params) => {
    return post('/core/flow/generate/xstp/module/list', params);
};
// 查询用户XSTP规则信息
export const getUserXSTPRules = (params) => {
    return post('/core/flow/generate/xstp/rule/list', params);
};
// /xstp/uploadTraffic/check
// 流量校验
export const checkTrafficFile = (params) => {
    return post('/core/flow/generate/xstp/upload/check', params);
};
// traffic/ebpf/currentApp
// 流量录制—查询当前应用信息
export const getEbpfCurrentApp = (params) => {
    return post('/core/flow/generate/ebpf/current/app/list', params);
};
// 查询 eBPF 当前应用的上游应用信息
export const getEbpfUpstreamApp = (params) => {
    return post('/core/flow/generate/ebpf/upstream/app/list', params);
};
// 查询EBPF产品线
export const getEbpfProducts = (params) => {
    return post('/core/flow/generate/ebpf/product/list', params);
};
// 触发生成任务
export const runTrafficTask = (params) => {
    return post('/core/flow/generate/task/run', params);
};

// 查询任务详情
export const getTrafficTaskDetailById = (params) => {
    return post('/core/flow/generate/task/detail', params);
};