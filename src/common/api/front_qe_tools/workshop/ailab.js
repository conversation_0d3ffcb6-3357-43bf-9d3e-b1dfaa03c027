import {post, get} from 'COMMON/utils/requestUtils';

// prompt 类型获取
export const getPromptType = (params) => {
    return post('/lazyailab/prompt/queryType', params);
};

// prompt list检索
export const getPromptList = (params) => {
    return post('/lazyailab/prompt/queryList', params);
};

// 查看prompt概览信息详情
export const getPromptDetail = (params) => {
    return post('/lazyailab/prompt/queryDetail', params);
};

// 查询历史发布prompt列表
export const getPrompVersiontList = (params) => {
    return post('/lazyailab/prompt/queryReleaseList', params);
};

// 删除已发布prompt版本
export const deletePromptVersion = (params) => {
    return post('/lazyailab/prompt/deleteRelease', params);
};

// 查询已发布的历史版本详情
export const getPromptVersionDetail = (params) => {
    return post('/lazyailab/prompt/queryReleaseDetail', params);
};

// prompt模板列表查询
export const getQueryTemplateList = (params) => {
    return post('/lazyailab/prompt/queryTemplateList', params);
};

// 新增prompt
export const addPrompt = (params) => {
    return post('/lazyailab/prompt/add', params);
};

// 删除prompt
export const deletePrompt = (params) => {
    return post('/lazyailab/prompt/delete', params);
};

// prompt重命名
export const promptRename = (params) => {
    return post('/lazyailab/prompt/rename', params);
};

// 更新prompt
export const updatePrompt = (params) => {
    return post('/lazyailab/prompt/update', params);
};

// 更新requirement 富文本
export const updateRequirement = (params) => {
    return post('/lazyailab/prompt/updateRequirement', params);
};

// 运行
export const promptRunCustom = (params) => {
    return post('/lazyailab/prompt/debugAsync', params);
};

export const pollingPromptRunCustomResult = (params) => {
    return post('/lazyailab/prompt/asyncQueryResult', params);
};

export const pollingPromptRunPresetResult = (params) => {
    return post('/lazyailab/prompt/asyncPresetQueryResult', params);
};

export const pollingPromptqueryModelList = (params) => {
    return post('/lazyailab/prompt/queryModelList', params);
};

export const pollingPromptqueryRelease = (params) => {
    return post('/lazyailab/prompt/release', params);
};

/**
 * 系统预设运行
 * @param {*} params
 * @returns
 */
export const promptRunPresetAsync = (params) => {
    return post('/lazyailab/prompt/presetDebugAsync', params);
};

export const runGeneratePresetKnowledge = (params) => {
    return post('/lazyailab/prompt/generatePresetKnowledge', params);
};
// 废弃
// export const asyncPresetQueryKnowledge = (params) => {
//     return post('/lazyailab/prompt/asyncPresetQueryKnowledge', params);
// };
// 查询最新taskId
export const getQueryLastTaskId = (params) => {
    return post('/lazyailab/prompt/queryLastTaskId', params);
};

export const getKnowledgeManagerQueryTypeList = (params) => {
    return get('/knowledgeManager/knowledge/queryTypeList', params);
};

export const promptPresetWithRetrievalDebugAsync = (params) => {
    // /lazyailab/prompt/presetWithRetrievalDebugAsync
    return post('/lazyailab/prompt/presetWithRetrievalDebugAsync', params);
};
