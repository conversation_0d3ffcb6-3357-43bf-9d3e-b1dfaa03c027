import {post, get} from 'COMMON/utils/requestUtils';

/**
 * 业务知识库
 */

// 新增知识
export const addKnowledge = (params) => {
    return post('/knowledgeManager/knowledge/add', params);
};

// 分页查询知识列表
export const getKnowledgeList = (params) => {
    return post('/knowledgeManager/knowledge/pageQuery', params);
};

// 知识-标注
export const updateAnnotateKnowledge = (params) => {
    return post('/knowledgeManager/knowledge/annotateKnowledge', params);
};