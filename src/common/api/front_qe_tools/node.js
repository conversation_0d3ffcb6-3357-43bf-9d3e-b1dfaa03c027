import { post } from 'COMMON/utils/requestUtils';
import { withTreeMapUpdate } from 'COMMON/utils/treeUtils';



// 查询节点对应信息
export const getCaseNodeInfo = (params) => post('/core/case/node/extra', params);

// 编辑节点
export const changeNode = withTreeMapUpdate((params) => {
    return post('/core/case/node/update', params).then((result) => {
        return result;
    });
}, 'update');

// 删除节点
export const changeNodeDel = withTreeMapUpdate((params) => {
    return post('/core/case/node/updateDel', params).then((result) => {
        // 触发 treeMap 更新事件
        return result;
    });
}, 'delete');

// 编辑节点 （不改变更新时间）
export const changeNodeWithoutTime = withTreeMapUpdate((params) => {
    return post('/core/case/node/updateWithoutTime', params).then((result) => {
        return result;
    });
}, 'update');

export const migrateNode = withTreeMapUpdate((params) => {
    return post('/core/case/node/migrate', params).then((result) => {
        // 触发 treeMap 更新事件
        return result;
    });
}, 'move');

export const addNode = withTreeMapUpdate((params) => {
    return post('/core/case/node/create', params).then((result) => {
        return result;
    });
}, 'add');

export const batchAddNode = withTreeMapUpdate((params) => {
    return post('/core/case/node/batch/create', params).then((result) => {
        return result;
    });
}, 'add');

export const copyNode = withTreeMapUpdate((params) => {
    return post('/core/case/node/duplicate', params);
}, 'copy');

// 查询脑图
export const getCaseTree = (params) => post('/core/case/tree/query', params);

// 下载脑图
export const downloadCaseTree = (params) => {
    return post('/core/case/download', params);
};
// 查询用例树diff信息
export const getCaseDiff = (params) => {
    return post('/core/v2/case/tree/diff', params);
};
