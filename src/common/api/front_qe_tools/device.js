import {post} from 'COMMON/utils/requestUtils';

// 获取设备列表
export function getDeviceList(params) {
    return post('/core/device/device/query', params);
}

// 筛选业务线设备列表中的设备
export function getDeviceBrandList(params) {
    return post('/core/device/model/list', params);
}

// 设备型号查询
export function getDeviceModelByNickModel(params) {
    return post('/core/device/model/query', params);
}

// 录入设备
export function createDevice(params) {
    return post('/core/device/device/register', params);
}

// 设备删除
export function deleteDevice(params) {
    return post('/core/device/device/delete', params);
}

// 设备运维 1-申请运维 2-开始运维 3-结束运维
export function operateDevice(params) {
    return post('/core/device/device/operate', params);
};

// 设备代理启用/关闭
export function updateDeviceProxy(params) {
    return post('/core/device/proxy/updateEnable', params);
}

// 设备状态信息更新
export function updateDevice(params) {
    return post('/core/device/device/update', params);
}

// 设备迁移 设备池
export function setPoolTransfer(params) {
    return post('/core/device/pool/transfer', params);
}
// 设备迁移 机柜
export function setCabinetTransfer(params) {
    return post('/core/device/cabinet/transfer', params);
}

// 查询业务线设备池
export function getDevicePoolList(params) {
    return post('/core/device/pool/list', params);
}

// 查询设备池中所包含设备信息
export function getPoolDeviceList(params) {
    return post('/core/device/pool/detail', params);
}


// 创建设备池
export function createPool(params) {
    return post('/core/device/pool/create', params);
}

// 编辑设备名称
export function updateDevicePool(params) {
    return post('/core/device/pool/edit', params);
}

// 删除设备分池
export function deleteDevicePool(params) {
    return post('/core/device/pool/delete', params);
}

// 共享设备池
export function sharePool(params) {
    return post('/core/device/pool/share', params);
}

// 移动设备分池的设备
export function movePoolDeviceToPool(params) {
    return post('/core/device/pool/deviceBind', params);
}


// 查询业务线机柜名称
export function getCabinetList(params) {
    return post('/core/device/cabinet/list', params);
}

// 查询机柜中所包含设备信息
export function getCabinetDeviceList(params) {
    return post('/core/device/cabinet/detail', params);
}

// 移动实体管理设备
export function moveDeviceToCabinet(params) {
    return post('/core/device/cabinet/deviceBind', params);
}

// 创建机柜
export function createCabinet(params) {
    return post('/core/device/cabinet/create', params);
}

// 更新机柜信息
export function updateCabinet(params) {
    return post('/core/device/cabinet/update', params);
}

// 删除机柜
export function deleteCabinet(params) {
    return post('/core/device/cabinet/delete', params);
}

// 设备彻底删除
export function completelyDelete(params) {
    return post('/core/device/device/delete', params);
}

// 获取设备缓存信息
export function getDeviceModel(params) {
    return post('/core/device/cabinet/return', params);
}

// 外借设备
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/LWyELLqriZ/0k6OScQv5OTKAH
export function deviceBorrow(params) {
    return post('/core/device/borrow/borrowDevice', params);
}

// 归还设备
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/LWyELLqriZ/PVsAC-g2CN3aGp
export function deviceReturn(params) {
    return post('/core/device/borrow/returnDevice', params);
}

// 设备位置查询
export function getDeviceLocationList(params) {
    return post('/core/device/location/list', params);
}

// 设备移位
export function setLocationMove(params) {
    return post('/core/device/location/list', params);
}