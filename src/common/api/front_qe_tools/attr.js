import {post} from 'COMMON/utils/requestUtils';

// 查询节点描述
export const getNodeDesc = (params) => {
    return post('/core/case/node/desc/query', params);
};

// 编辑节点描述
export const changeNodeDesc = (params) => {
    return post('/core/case/node/desc/update', params);
};

// 提交签章记录
export const createSignInfo = (params) => {
    return post('/core/sign/node/create', params);
};

// 更新签章记录 （仅更新最新一次签章内容）
export const updateSignInfo = (params) => {
    return post('/core/sign/node/update', params);
};