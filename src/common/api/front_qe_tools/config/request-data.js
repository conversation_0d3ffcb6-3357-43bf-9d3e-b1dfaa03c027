import { post } from 'COMMON/utils/requestUtils';

// 请求数据
// 查询请求数据列表
export const getRequestDataList = (params) => {
    return post('/core/proxy/digital/tree', params);
};

// 查询请求数据详情
export const getRequestDataDetail = (params) => {
    return post('/core/proxy/digital/node/detail', params);
};

// 移动节点
export const migrateRequestData = (params) => {
    return post('/core/proxy/digital/node/migrate', params);
};

// 创建请求数据
export const createRequestData = (params) => {
    return post('/core/proxy/digital/node/create', params);
};

// 更新请求数据
export const updateRequestData = (params) => {
    return post('/core/proxy/digital/node/update', params);
};

// 复制请求数据
export const copyRequestData = (params) => {
    return post('/core/proxy/digital/node/duplicate', params);
};

// 删除请求数据
export const deleteRequestData = (params) => {
    return post('/core/proxy/digital/node/del', params);
};

// 查询数据节点关联的用例
export const getRequestDataRelationList = (params) => {
    return post('/core/proxy/digital/node/relation/list', params);
};

// mock步骤引用数据节点
export const updateRequestDataRelation = (params) => {
    return post('/core/proxy/digital/node/step/relation', params);
};
