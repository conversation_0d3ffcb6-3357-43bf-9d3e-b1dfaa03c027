import { get, post } from 'COMMON/utils/requestUtils';

// 添加MCP服务器
export const createMcpServer = (params) => {
    return post('/rag/api/mcpserver/add', params);
};

// 删除MCP服务器
export const deleteMcpServer = (params) => {
    return post('/rag/api/mcpserver/delete', params);
};

// 更新MCP服务器
export const updateMcpServer = (params) => {
    return post('/rag/api/mcpserver/update', params, { method: 'put' });
};

// 分页查询MCP服务器
export const getMcpServer = (params) => {
    return post('/rag/api/mcpserver/paging', params);
};

// 获取指定分组的MCP服务器列表
export const getMcpServerListByGroup = (groupId) => {
    return get(`/rag/api/mcpserver/get/list/in/group?groupId=${groupId}`);
};
