import { get, post } from 'COMMON/utils/requestUtils';

// 旧的不分页查询
export const getAgent = (data) => {
    return post('/rag/api/agent/query', data);
};

// 新的分页查询
export const getAgentPaging = (data) => {
    return post('/rag/api/agent/query/paging', data);
};

// 创建智能体
export const saveAgent = (data) => {
    return post('/rag/api/agent/add', data);
};

// 根据ID获取智能体详情
export const getAgentById = (id) => {
    return get(`/rag/api/agent/detail?id=${id}`);
};

// 更新智能体
export const updateAgent = (data) => {
    return post('/rag/api/agent/update', data, { method: 'put' });
};
