import { post } from 'COMMON/utils/requestUtils';

// RAG文档检索接口
export const searchDocuments = (params) => {
    return post('/rag/api/search', params);
};

// RAG生成回答接口
export const generateResponse = (params) => {
    return post('/rag/api/generate', params);
};

// RAG文档索引接口
export const indexDocument = (params) => {
    return post('/rag/api/index', params);
};

// RAG混合搜索接口
export const hybridSearch = (params) => {
    return post('/rag/api/hybrid-search', params);
};

// RAG问题改写接口
export const rewriteQuestion = (params) => {
    return post('/rag/api/rewrite-question', params);
};

// RAG生成摘要接口
export const generateSummary = (params) => {
    return post('/rag/api/generate-summary', params);
};