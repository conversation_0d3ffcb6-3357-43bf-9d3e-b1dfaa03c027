import { post } from 'COMMON/utils/requestUtils';

// 文档向量搜索接口
export const searchDocuments = (params) => {
    return post('/rag/api/search', params);
};

// 混合搜索接口（向量搜索 + 关键词搜索）
export const hybridSearch = (params) => {
    return post('/rag/api/hybrid-search', params);
};

// 关键词搜索接口
export const keywordSearch = (params) => {
    return post('/rag/api/keyword-search', params);
};

// 相似文档推荐接口
export const getSimilarDocuments = (params) => {
    return post('/rag/api/similar-documents', params);
};