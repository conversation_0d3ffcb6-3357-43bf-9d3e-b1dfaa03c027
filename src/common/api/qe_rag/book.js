import { get, post } from 'COMMON/utils/requestUtils';

// 根据ID获取知识库详情
export const getBook = (id) => {
    return get(`/rag/api/knowledge/base/detail?id=${id}`);
};

// 获取知识库列表（分页）
export const getBooks = (params) => {
    return post('/rag/api/knowledge/base/paging', params);
};

// 删除知识库
export const remove = (params) => {
    return post('/rag/api/knowledge/base/delete', params);
};

// 编辑知识库
export const edit = (params) => {
    return post('/rag/api/knowledge/base/update', params, { method: 'put' });
};

// 创建知识库
export const create = (params) => {
    return post('/rag/api/knowledge/base/add', params);
};

// 根据工作组ID获取知识库
export const getKDB = (groupId) => {
    return get(`/rag/api/knowledge/base/get/in/group?groupId=${groupId ?? 0}`);
};
