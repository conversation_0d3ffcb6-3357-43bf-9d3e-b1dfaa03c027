import { get, post } from 'COMMON/utils/requestUtils';

// 获取用户所在的组信息
export const getGroup = (params = {}) => {
    return post('/rag/api/workgroup/join/query', params);
};

// 获取所有群组信息
export const getGroups = (params = {}) => {
    return post('/rag/api/workgroup/all/query', params);
};

// 编辑工作组信息
export const editGroup = (params) => {
    return post('/rag/api/workgroup/update', params);
};

// 编辑工作组的用户信息
export const editGroupUser = (params) => {
    return post('/rag/api/workgroup/update', params);
};

// 创建工作组
export const createGroup = (params) => {
    return post('/rag/api/workgroup/add', params);
};

// 提交申请加入工作组
export const submitApplication = (params) => {
    return post('/rag/api/workgroup/apply/join', params);
};

// 查询工作组列表
export const checkList = (params) => {
    return get('/rag/api/audit/message/query', params);
};

// 审批工作组-同意
export const applyYes = (params) => {
    return post('/rag/api/workgroup/apply/approve', params);
};

// 审批工作组-拒绝
export const applyNo = (params) => {
    return post('/rag/api/workgroup/apply/reject', params);
};

// 获取未读消息数量
export const getUnreadMessages = (params) => {
    return get('/rag/api/audit/message/num/query', params);
};

// 获取业务单元列表
export const getBuList = (params) => {
    return get('/rag/api/bu/query', params);
};

// 获取用户已加入的工作组列表
export const getJoinedList = (params) => {
    return post('/rag/api/workgroup/query/joined/list', params);
};

// 获取所有工作组列表
export const getAllGroupsList = (params) => {
    return post('/rag/api/workgroup/query/all/list', params);
};