import {useEffect, forwardRef, useState, useCallback, useImperativeHandle} from 'react';
import {useLocation} from 'umi';
import {Select, message, Avatar} from 'antd';
import {CaretDownOutlined, UserOutlined} from '@ant-design/icons';
import {post} from 'COMMON/utils/requestUtils';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import styles from './MemberSelect.module.less';

const MemberSelect = (props) => {
    const [messageApi, contextHolder] = message.useMessage();
    const {isSetLocalStorage, variant, mode, username} = props;
    const [data, setData] = useState([]);
    const setLocalStorage = () => {
        try {
            // 本地存储
            const optionsMap = JSON.parse(localStorage.getItem(USER_PICKER_OPTIONS) || '{}');
            optionsMap[id] = data;
            localStorage.setItem(USER_PICKER_OPTIONS, JSON.stringify(optionsMap));
        } catch (error) { }
    };
    const fetchSuggestions = async (value, callback) => {
        post('/core/user/apigo/list', {queryName: value, searchName: username})
            .then((data) => {
                callback(data?.userInfoList);
                isSetLocalStorage && setLocalStorage();
            });
    };
    const handleSearch = (newValue) => {
        if (newValue) {
            fetchSuggestions(newValue, setData);
        } else {
            setData([]);
        }
    };
    return (
        <div className={styles.memberSelect}>
            {contextHolder}
            <Select
                mode={mode ?? 'multiple'}
                variant={variant || 'borderless'}
                onSearch={(e) => {
                    handleSearch(e);
                }}
                popupMatchSelectWidth={false}
                suffixIcon={<CaretDownOutlined style={{pointerEvents: 'none'}} />}
                className={styles.select}
                options={data.map((d) => ({
                    value: d.username,
                    label: d.username,
                    avatar: d.avatar,
                    name: d.name,
                    department: d.department,
                    imageUrl: d.imageUrl,
                }))}
                optionRender={(option) => (
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            width: '100%',
                            overflow: 'scroll'
                        }}
                    >
                        <Avatar
                            icon={<UserOutlined />}
                            src={option?.data?.imageUrl}
                            style={{marginRight: 8}}
                        />
                        <div>
                            <div>
                                {option?.data?.name
                                    ? `${option?.data?.name} (${option?.data?.label})`
                                    : option?.data?.label}
                            </div>
                            <div style={{fontSize: 12, color: '#888'}}>
                                {option?.data?.department || '暂无部门信息'}
                            </div>
                        </div>
                    </div>
                )}
                allowClear
                showSearch
                filterOption={false}
                notFoundContent={null}
                // placeholder={placeholder}
                {...props}
            />
        </div>
    );
};

export default connectModel([baseModel], (state) => ({
    username: state.common.base.username,
    spaceList: state.common.base.spaceList,
    currentSpace: state.common.base.currentSpace,
    filterOsType: state.common.base.filterOsType
}))(MemberSelect);
