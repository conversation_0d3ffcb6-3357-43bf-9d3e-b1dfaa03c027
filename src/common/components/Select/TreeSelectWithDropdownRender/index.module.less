.title {
    position: relative;
    padding: 5px;

    .info {
        width: calc(100% - 50px);
    }

    .text {
        font-weight: bold;
    }

    .opera {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);

        .operaIcon {
            margin-left: 8px;
            font-size: 12px;
            color: #777;
        }

        .operaIcon:hover {
            color: var(--primary-color);
        }
    }


}

.label {
    .title {
        width: 230px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .editIcon {
        display: none;
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 14px;
        color: #777;

        &:hover {
            color: var(--primary-color);
        }
    }

    &:hover {
        .editIcon {
            display: inline-block;
        }
    }

}