import {TreeSelect, Tooltip, Flex} from 'antd';
import {
    ContainerOutlined, EditOutlined, EyeOutlined,
    PlusOutlined, SearchOutlined, SettingOutlined
} from '@ant-design/icons';
import {useEffect, useState} from 'react';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import Search from 'COMMON/components/Search';
import styles from './index.module.less';

function TreeSelectWithDropdownRender(props) {
    const {text, treeData, mode, value, labelInValue = false,
        addText, archiveText, searchText, settingText,
        addChange, addPopverShow, disabled,
        editChange, settingChange, archiveChange,
        showSearchIcon = true, editType = 'normal',
        onChange, ...restProps
    } = props;
    const [searchTreeData, setSearchTreeData] = useState([]);
    const [showSearch, setShowSearch] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    const [open, setOpen] = useState(false);

    const getSearchDataList = (data, searchData = []) => {
        data.forEach(item => {
            if (item.nodeName?.includes(searchValue)) {
                searchData.push(item);
            } else if (item.children && Array.isArray(item.children) && item.children.length > 0) {
                getSearchDataList(item.children, searchData);
            }
        });
        return searchData;
    };

    useEffect(() => {
        if (searchValue === '') {
            setSearchTreeData(treeData);
            return;
        }
        let data = getSearchDataList(treeData);
        setSearchTreeData(data);
    }, [treeData, searchValue]);

    return (
        <TreeSelect
            {...restProps}
            disabled={disabled}
            mode={mode}
            open={open}
            value={value}
            treeDefaultExpandAll
            treeData={searchTreeData}
            onDropdownVisibleChange={(value) => setOpen(value)}
            labelInValue={labelInValue}
            onChange={(value) => {
                onChange && onChange(value);
                if (!mode) {
                    setOpen(false);
                }
            }}
            dropdownRender={(menu) => (
                <>
                    <div className={styles.title}>
                        <div className={styles.info}>
                            {!showSearch ?
                                <span className={styles.text}>{text}</span> :
                                <Search
                                    value={searchValue}
                                    onChange={(data) => setSearchValue(data)}
                                />
                            }
                        </div>
                        {
                            -1 === ['readonly', 'debug', 'execute'].indexOf(editType) &&
                            <div className={styles.opera}>
                                {showSearchIcon &&
                                    <Tooltip title={searchText ?? '搜索'}>
                                        <SearchOutlined
                                            className={styles.operaIcon}
                                            onClick={() => {
                                                if (showSearch) {
                                                    setSearchValue('');
                                                }
                                                setShowSearch(!showSearch);
                                            }}
                                            onSearch={(value) => setSearchValue(value)}
                                        />
                                    </Tooltip>}
                                {
                                    archiveChange && <Tooltip title={archiveText ?? '查看已归档用例组'}>
                                        <ContainerOutlined
                                            className={styles.operaIcon}
                                            onClick={() => {
                                                archiveChange();
                                                setOpen(false);
                                            }}
                                        />
                                    </Tooltip>
                                }
                                {addPopverShow}
                                {
                                    addChange && <Tooltip title={addText ?? '新增'}>
                                        <PlusOutlined
                                            className={styles.operaIcon}
                                            onClick={() => {
                                                addChange();
                                                setOpen(false);
                                            }}
                                        />
                                    </Tooltip>
                                }
                                {
                                    settingChange && <Tooltip title={settingText ?? '配置'}>
                                        <SettingOutlined
                                            className={styles.operaIcon}
                                            onClick={() => {
                                                settingChange();
                                                setOpen(false);
                                            }}
                                        />
                                    </Tooltip>
                                }
                            </div>
                        }
                    </div>
                    {menu}
                </>
            )
            }
            treeTitleRender={(option) => {
                return (
                    <div className={styles.label}>
                        <div className={styles.title}>
                            <Tooltip title={option.label} placement='left'>
                                {option.label}
                            </Tooltip>
                        </div>
                        {
                            editChange &&
                            <Tooltip title='查询'>
                                <EyeOutlined
                                    className={styles.editIcon}
                                    onClick={() => editChange(option)}
                                />
                            </Tooltip>
                        }
                    </div>
                );
            }}
        />
    );
}

export default connectModel([baseModel], state => ({
    spaceList: state.common.base.spaceList,
    currentSpace: state.common.base.currentSpace,
    filterOsType: state.common.base.filterOsType,
}))(TreeSelectWithDropdownRender);