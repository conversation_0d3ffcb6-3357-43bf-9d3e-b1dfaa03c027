import classnames from 'classnames';
import { Select, message } from 'antd';
import { useLocation } from 'umi';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import styles from './OsTypeSelect.module.less';
import { CaretDownOutlined } from '@ant-design/icons';

function OsTypeSelect(props) {
    const { osType, setOsType, filterOsType = [], onChange, className } = props;
    const location = useLocation();
    const [messageApi, contextHolder] = message.useMessage();
    const osTypeOptions = [
        {
            label: 'Android',
            value: 1
        },
        {
            label: 'iOS',
            value: 2
        },
        {
            label: 'Web',
            value: 3
        }
    ];
    const handleChangeOsType = async (value) => {
        setOsType(value);
        onChange && onChange(value);
        localStorage.setItem('regressionCase_osType', value);
    };
    return (
        <>
            {contextHolder}
            <Select
                popupMatchSelectWidth={false}
                suffixIcon={
                    filterOsType.length === 1 ? (
                        false
                    ) : (
                        <CaretDownOutlined style={{ pointerEvents: 'none' }} />
                    )
                }
                className={classnames(styles.select, className)}
                bordered={false}
                size="small"
                placeholder="请选择系统"
                value={+osType}
                onChange={onChange ?? handleChangeOsType}
                options={
                    location.pathname.includes('integration')
                        ? osTypeOptions.filter((item) => filterOsType.includes(item.value))
                        : osTypeOptions
                }
                placement="bottomRight"
            />
        </>
    );
}

export default connectModel([baseModel], (state) => ({
    spaceList: state.common.base.spaceList,
    currentSpace: state.common.base.currentSpace,
    filterOsType: state.common.base.filterOsType
}))(OsTypeSelect);
