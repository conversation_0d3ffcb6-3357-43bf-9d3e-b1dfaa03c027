.title {
    position: relative;
    padding: 5px;

    .info {
        width: calc(100% - 50px);
    }

    .text {
        font-weight: bold;
    }

    .opera {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);

        .operaIcon {
            margin-left: 8px;
            font-size: 12px;
            color: #777;
        }

        .operaIcon:hover {
            color: var(--primary-color);
        }
    }
}

.editIcon {
    font-size: 12px;
    color: #777;
}

.editIcon:hover {
    color: var(--primary-color);
}
.dropdownContainer {
    display: flex;
    flex-direction: column;
    height: 350px;
}

.fixedHeader {
    position: sticky;
    top: 0;
    z-index: 1;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    background: white;
    
    .info {
        flex: 1;
        
        .text {
            font-weight: bold;
        }
    }
    
    .opera {
        display: flex;
        gap: 8px;
        
        .operaIcon {
            font-size: 12px;
            color: #777;
            cursor: pointer;
            
            &:hover {
                color: var(--primary-color);
            }
        }
    }
}

.splitContent {
    display: flex;
    flex: 1;
    overflow: hidden;
}
.categoryPanel {
    width: 140px;
    overflow-y: auto;
    border-right: 1px solid #f0f0f0;
    
    .categoryItem {
        padding: 8px 12px;
        cursor: pointer;
        
        &:hover {
            background-color: #f5f5f5;
        }
        
        &.active {
            background-color: #e6f7ff;
            font-weight: bold;
        }
    }
}

.menuPanel {
    flex: 1;
    overflow-y: auto;
}