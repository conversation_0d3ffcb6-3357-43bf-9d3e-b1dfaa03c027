import { Select, Tooltip, Flex } from 'antd';
import {
    ContainerOutlined,
    EditOutlined,
    PlusOutlined,
    SearchOutlined,
    SettingOutlined
} from '@ant-design/icons';
import { useState } from 'react';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import Search from 'COMMON/components/Search';
import styles from './SelectWithDropdownRender.module.less';

function SelectWithDropdownRender(props) {
    const {
        text,
        options,
        mode,
        value,
        labelInValue = false,
        placeholder,
        addText,
        archiveText,
        searchText,
        settingText,
        addChange,
        addPopverShow,
        disabled,
        editChange,
        settingChange,
        archiveChange,
        showSearchIcon = true,
        editType = 'normal',
        onChange,
        showSplitView,
        categories,
        selectedCategory,
        onCategorySelect,
        ...restProps
    } = props;
    const [showSearch, setShowSearch] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    const [open, setOpen] = useState(false);

    const header = (
        <div className={styles.fixedHeader}>
            <div className={styles.info}>
                {!showSearch ? (
                    <span className={styles.text}>{text}</span>
                ) : (
                    <Search value={searchValue} onChange={(data) => setSearchValue(data)} />
                )}
            </div>
            {-1 === ['readonly', 'debug', 'execute'].indexOf(editType) && (
                <div className={styles.opera}>
                    {addChange && (
                        <Tooltip title={addText ?? '新增'}>
                            <PlusOutlined
                                className={styles.operaIcon}
                                onClick={() => {
                                    addChange();
                                    setOpen(false);
                                }}
                            />
                        </Tooltip>
                    )}
                    {settingChange && (
                        <Tooltip title={settingText ?? '配置'}>
                            <SettingOutlined
                                className={styles.operaIcon}
                                onClick={() => {
                                    settingChange();
                                    setOpen(false);
                                }}
                            />
                        </Tooltip>
                    )}
                    {showSearchIcon && (
                        <Tooltip title={searchText ?? '搜索'}>
                            <SearchOutlined
                                className={styles.operaIcon}
                                onClick={() => {
                                    if (showSearch) {
                                        setSearchValue('');
                                    }
                                    setShowSearch(!showSearch);
                                }}
                            />
                        </Tooltip>
                    )}
                </div>
            )}
        </div>
    );

    return (
        <Select
            {...restProps}
            placeholder={placeholder}
            disabled={disabled}
            mode={mode}
            open={open}
            value={value}
            options={options?.filter((item) => item.label.includes(searchValue))}
            onDropdownVisibleChange={(value) => setOpen(value)}
            labelInValue={labelInValue}
            onChange={(data) => {
                onChange && onChange(data);
                if (!mode) {
                    setOpen(false);
                }
            }}
            dropdownRender={(menu) => {
                return (
                    <div className={styles.dropdownContainer}>
                        {header}
                        {showSplitView ? (
                            <div className={styles.splitContent}>
                                <div className={styles.categoryPanel}>
                                    {categories?.map((category) => (
                                        <div
                                            key={category}
                                            className={`${styles.categoryItem} ${
                                                selectedCategory === category ||
                                                (selectedCategory === null &&
                                                    category === categories[0])
                                                    ? styles.active
                                                    : ''
                                            }`}
                                            onClick={() => onCategorySelect?.(category)}
                                        >
                                            {category}
                                        </div>
                                    ))}
                                </div>
                                <div className={styles.menuPanel}>{menu}</div>
                            </div>
                        ) : (
                            menu
                        )}
                    </div>
                );
            }}
            optionRender={(option) => {
                return (
                    <Flex justify="space-between">
                        <Tooltip
                            title={option.data.label}
                            placement="topLeft"
                            overlayStyle={{
                                maxWidth: 400,
                                wordBreak: 'break-word',
                                whiteSpace: 'pre-wrap'
                            }}
                            mouseEnterDelay={0.2}
                        >
                            <span className={styles.optionText}>
                                {option.data.label.length > 20
                                    ? `${option.data.label.substring(0, 20)}...`
                                    : option.data.label}
                            </span>
                        </Tooltip>
                        {-1 === ['readonly', 'debug', 'execute'].indexOf(editType) &&
                            editChange &&
                            value === option.data.value && (
                                <Tooltip title="修改">
                                    <EditOutlined
                                        className={styles.editIcon}
                                        onClick={() => editChange(option.data.value)}
                                    />
                                </Tooltip>
                            )}
                    </Flex>
                );
            }}
        />
    );
}

export default connectModel([baseModel], (state) => ({
    spaceList: state.common.base.spaceList,
    currentSpace: state.common.base.currentSpace,
    filterOsType: state.common.base.filterOsType
}))(SelectWithDropdownRender);
