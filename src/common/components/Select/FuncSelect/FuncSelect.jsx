import {Select} from 'antd';
import {CaretDownOutlined} from '@ant-design/icons';
import styles from './FuncSelect.module.less';

function FuncSelect(props) {
    const {value, options, onChange, headerStyle} = props;
    return (
        <div className={styles.headerTitle} style={headerStyle}>
            <Select
                variant='borderless'
                tabIndex={-1}
                value={value}
                popupMatchSelectWidth={false}
                suffixIcon={<CaretDownOutlined style={{pointerEvents: 'none'}} />}
                options={options}
                onChange={(value) => {
                    onChange && onChange(value);
                }}
            />
        </div>
    );
}

export default FuncSelect;