import { useState, useMemo } from 'react';
import SelectWithDropdownRender from 'COMMON/components/Select/SelectWithDropdownRender';
import { isEmpty } from 'lodash';

function SelectWithCategory(props) {
    const {
        dataSource = [],
        categoryKey = 'tabName',
        labelKey = 'label',
        valueKey = 'value',
        defaultCategory = '未分类',
        ...restProps
    } = props;

    // 获取所有分类
    const categories = useMemo(() => {
        return [...new Set(dataSource.map((item) => item[categoryKey] || defaultCategory))];
    }, [dataSource, categoryKey, defaultCategory]);

    // 根据选中分类过滤选项
    const options = useMemo(() => {
        return dataSource
            .filter(
                (item) =>
                    isEmpty(restProps.selectedCategory) ||
                    (item[categoryKey] || defaultCategory) === restProps.selectedCategory
            )
            .map((item) => ({
                ...item,
                label: item[labelKey],
                value: item[valueKey],
                tabName: item[categoryKey] || defaultCategory
            }));
    }, [dataSource, restProps.selectedCategory, categoryKey, labelKey, valueKey, defaultCategory]);

    return (
        <SelectWithDropdownRender
            {...restProps}
            options={options}
            categories={categories}
            showSplitView
            text={restProps.selectedCategory || restProps.text || '请选择分类'}
        />
    );
}

export default SelectWithCategory;
