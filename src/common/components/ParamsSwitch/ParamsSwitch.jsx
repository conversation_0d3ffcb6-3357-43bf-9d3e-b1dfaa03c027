import {Switch} from 'antd';
import classnames from 'classnames';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import styles from './ParamsSwitch.module.less';

function ParamsSwitch(props) {
    const {showParams, setShowParams, className} = props;
    return (
        <div className={classnames(styles.stepListSwitch, className)}>
            <span>
                展示参数
            </span>
            <Switch
                size="small"
                checked={showParams}
                onChange={setShowParams}
                tabIndex={-1}
            />
        </div>
    );
}

export default connectModel([baseModel], state => ({
    showParams: state.common.base.showParams,
}))(ParamsSwitch);