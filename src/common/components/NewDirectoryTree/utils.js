import { message } from 'antd';
import { isEmpty } from 'lodash';
import EventBus from 'COMMON/utils/eventBus';
import { updateTreeNode } from 'COMMON/api/front_qe_tools/tree';
import { deepcopy } from 'COMMON/components/TreeComponents/Step/utils';

// 树拖拽移动
export async function onDrop(info, treeData, setLoading) {
    try {
        // node         代表当前被drop 的对象
        // dragNode     代表当前需要drop 的对象
        // dropPosition 代表drop后的节点位置；不准确
        // dropToGap    代表移动到非最顶级组第一个位置
        const dropKey = info.node.key;
        const dragKey = info.dragNode.key;
        const dropPos = info.node.pos.split('-');
        const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);
        if (info.node.type !== 1 && dropPosition !== 1) {
            message.error('用例仅能移动到分组中');
            return;
        }
        // 0 表示拖拽到元素上
        // 1 表示拖拽到元素下面   那么放置元素的时候  应该放到这个位置+1的位置上
        // -1 表示拖拽到元素上面   那么放置元素的时候 直接放到这个位置即可
        const loop = (data, key, callback) => {
            for (let i = 0; i < data.length; i++) {
                if (data[i].key === key) {
                    return callback(data[i], i, data);
                }
                if (data[i].children) {
                    loop(data[i].children, key, callback);
                }
            }
        };
        // 深拷贝树
        const delTreeKeys = (data) => {
            for (let i = 0; i < data.length; i++) {
                delete data[i].icon;
                delete data[i].title;
                if (data[i].children) {
                    delTreeKeys(data[i].children);
                }
            }
            return data;
        };
        const data = deepcopy(delTreeKeys(treeData));
        let curNodeId = info.dragNode.nodeId;
        let dropNodeId = null;
        let sibNodeId = -1;
        // 为判断元素位置假设删除该元素
        loop(data, dragKey, (item, index, arr) => {
            // 查找到后删除此元素
            arr.splice(index, 1);
        });
        if (!info.dropToGap) {
            // 拖拽到内容上
            loop(data, dropKey, (item) => {
                // 父节点id
                dropNodeId = item.nodeId;
            });
            // 拖拽到元素之下的缝隙中，元素有子节点，并且当前元素子节点展开。此时元素放到了子节点的第一位
        } else if (
            // 平级移动、交叉组移动、移动到其他组(非最顶级)非第一个位置
            (info.node.children || []).length > 0 && // 有child
            info.node.expanded && // node展开
            dropPosition === 1 // 位置元素下面
        ) {
            // 寻找父节点id
            loop(data, dropKey, (item) => {
                dropNodeId = item.nodeId;
            });
            // 位置元素上面
        } else {
            loop(data, dropKey, (_item, index, arr) => {
                sibNodeId = _item.nodeId;
                dropNodeId = _item.parentId;
            });
        }
        if (null !== dropNodeId) {
            let body = {
                treeNodeId: curNodeId,
                parentId: dropNodeId
            };
            if (sibNodeId !== -1) {
                body.preSibId = sibNodeId;
            }
            setLoading(true);
            updateTreeNode(body)
                .then(() => {
                    EventBus.emit('refreshTreeNodeList');
                    setLoading(false);
                })
                .catch((err) => {
                    setLoading(false);
                    console.log(err?.message ?? err);
                });
        }
    } catch (err) {}
}
