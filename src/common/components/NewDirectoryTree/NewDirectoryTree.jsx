import {useMemo, useState, useCallback, useEffect} from 'react';
import {
    Tree
} from 'antd';
import {
    CaretDownFilled,
    CaretRightFilled
} from '@ant-design/icons';
import {getQueryParams} from 'COMMON/utils/utils';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import NodeIcon from 'COMMON/components/NewAddDropdown/components/NodeIcon';
import Loading from 'COMMON/components/common/Loading';
import {onDrop} from './utils';
import {isEmpty} from 'lodash';
import CaseTitle from './components/CaseTitle';
import styles from './NewDirectoryTree.module.less';

const {DirectoryTree} = Tree;

function NewDirectoryTree(props) {
    const {
        showAddDropdownItems, // 新增菜单展示项
        type,
        directoryList,
        searchValue,
        getSelectKey,
        handleClickDirectoryTreeNode,
        getOperaItems,
        handleChangeCaseName,
        handleOprator,
        expandedKeys,
        setExpandedKeys,
        expandedParent, // 选择操作函数
        setExpandedParent,
        checkabled = false, // 是否可选择
        checkedKeys, // 选择元素
        onChecked, // 选择元素函数
        disabledData, // 不可选择元素
        currentModule
    } = props;
    const query = getQueryParams();
    const [loading, setLoading] = useState(false);

    const selectKey = useMemo(() => {
        return getSelectKey();
    }, [query]);

    useEffect(() => {
        if (!selectKey) {
            return;
        }
        setExpandedKeys([selectKey]);
        setExpandedParent(true);
    }, []);

    useEffect(() => {
        if (searchValue !== '') {
            return;
        }
        setExpandedKeys([selectKey]);
        setExpandedParent(true);
    }, [searchValue]);

    const onExpand = (expandedKeys) => {
        setExpandedKeys(expandedKeys);
        setExpandedParent(false);
    };

    const getDirectotyTree = useCallback((data) => {
        if (!data) {
            return;
        }
        return data.map(item => {
            return {
                ...item,
                title: item.nodeName,
                type: item.nodeType,
                disabled: disabledData?.nodeType.includes(item.nodeType),
                key: 'node_' + item.nodeId,
                switcherIcon: ({expanded}) => {
                    if (!expanded & item.nodeType === 1) {
                        return <CaretRightFilled className={styles.arrowIcon} />;
                    }
                    if (expanded & item.nodeType === 1) {
                        return <CaretDownFilled className={styles.arrowIcon} />;
                    }
                    return <NodeIcon type={item.nodeType} className={styles.nodeIcon} />;

                },
                children: getDirectotyTree(item.children)
            };
        });
    }, [directoryList]);

    // 获取目录树
    const directoryTree = useMemo(() => {
        return getDirectotyTree(directoryList);
    }, [directoryList]);

    if (loading) {
        return <Loading />;
    }

    return (
        <DirectoryTree
            icon={false}
            treeData={directoryTree}
            draggable={{icon: false, node: true}}
            onExpand={onExpand}
            checkable={checkabled}
            checkedKeys={checkedKeys}
            selectedKeys={[selectKey]}
            autoExpandParent={expandedParent}
            onCheck={onChecked}
            onDrop={(data) => onDrop(data, directoryTree, setLoading)}
            onSelect={(value, e) => {
                const {selectedNodes} = e;
                handleClickDirectoryTreeNode(value, selectedNodes[0], selectKey);
            }}
            height={window.innerHeight - 100}
            titleRender={(node) => {
                return (
                    <CaseTitle
                        node={node}
                        type={type}
                        showAddDropdownItems={showAddDropdownItems}
                        searchValue={searchValue}
                        selectKey={selectKey}
                        items={getOperaItems(node)}
                        handleChangeCaseName={handleChangeCaseName}
                        handleOprator={handleOprator}
                        showKnowledgeType={!isEmpty(node?.knowledgeType)}
                    />
                );
            }}
            expandedKeys={[...expandedKeys]}
        />
    );
}


export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentGroup: state.common.case.currentGroup,
}))(NewDirectoryTree);
