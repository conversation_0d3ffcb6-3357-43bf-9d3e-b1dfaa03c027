import {Popconfirm} from 'antd';

function DeleteCase({node, deleteCase}) {
    return (
        <Popconfirm
            placement="right"
            title="确定要删除此数据吗？"
            okText="确定"
            cancelText="取消"
            destroyTooltipOnHide
            onConfirm={() => {
                deleteCase(node);
            }}
        >
            <a>
                <span style={{color: 'red'}}>删除</span>
            </a>
        </Popconfirm>
    );
}

export default DeleteCase;
