@import "RESOURCES/css/common.less";

.treenode {
    display: flex;
    align-items: center;
    padding: 0px;
    height: 20px;
    width: 100%;
    margin: 5px;
    overflow: hidden;
    cursor: pointer;

    &:hover {
        .oprator {
            display: block;

            a {
                margin-right: 5px;
            }

            .activedAddIcon {
                color: var(--primary-color);
            }
        }

        .activedOprator {
            display: block;
            flex-shrink: 0;

            a {
                margin-right: 5px;
                color: #fff !important;
            }

            .activedAddIcon {
                color: #fff !important;
            }
        }
    }
}

.highlightTitle {
    background-color: yellow;
    color: #000;
    padding: 0 2px;
    border-radius: 2px;
}

.showTreenode {
    display: none;
}

.oprator {
    display: none;
    flex-shrink: 0;

    a {
        margin-right: 5px;
    }
}

.activedOprator {
    display: block;
    flex-shrink: 0;

    a {
        margin-right: 5px;
        color: #fff;
    }
}

.activedAddIcon {
    color: #fff;
}

.treeNodeTitle {
    width: 100px;
    padding: 0px;
    height: 20px;
    line-height: 20px;
    user-select: none;
    font-size: 12px;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.checkedTreeNode {
    background: var(--base-selected-background-color);
}

.checkedNodeIcon {
    font-size: 12px;
    color: #fff !important;
}

.btnPos {
    float: right;
}

.arrowIcon {
    font-size: 10px;
}