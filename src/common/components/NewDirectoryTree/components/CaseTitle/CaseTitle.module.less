@import "RESOURCES/css/common.less";

.treenode {
    display: flex;
    align-items: center;
    padding: 0px;
    height: 22px;
    width: 100%;
    margin-top: 5px;
    overflow: hidden;
    cursor: pointer;

    :global(.ant-tag) {
        margin-right: 5px;
        line-height: 16px;
    }

    &:hover {
        .oprator {
            display: block;

            .activedAddIcon {
                color: var(--icon-color);
            }
        }

        .activedOprator {
            display: block;
            flex-shrink: 0;

            a,
            .activedAddIcon {
                color: #fff !important;
            }
        }
    }
}

.activedTreeNode {
    color: #fff;

    &:hover {
        .treeNodeTitle {
            color: #fff;
        }
    }
}

.highlightTitle {
    background-color: yellow;
    color: #000;
    padding: 0 2px;
    border-radius: 2px;
}

.showTreenode {
    display: none;
}

.oprator {
    display: none;
    flex-shrink: 0;
    margin-right: 5px;
}

.activedOprator {
    display: block;
    flex-shrink: 0;

    a {
        color: #fff;
    }
}

.activedAddIcon {
    color: #fff;
    font-size: 12px;
    /* 根据需要调整图标大小 */
}

.nodeSearch {
    background-color: #ffff00;
    color: #000;
    border-radius: 5px;
    padding: 2px;
}

.treeNodeTitle {
    width: 100px;
    padding: 0px;
    height: 20px;
    line-height: 20px;
    user-select: none;
    font-size: 12px;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.checkedTreeNode {
    background: var(--base-selected-background-color);
}

.checkedNodeIcon {
    font-size: 12px;
    color: #fff !important;
}

.btnPos {
    float: right;
}

.arrowIcon {
    font-size: 10px;
}

.iconWrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.iconWrapper:hover {
    background-color: rgba(0, 0, 0, 0.1);
    /* 你可以根据需要调整颜色 */
}