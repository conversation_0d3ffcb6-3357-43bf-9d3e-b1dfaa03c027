import { useState, useEffect, useRef } from 'react';
import { Dropdown, Input, Popover, Tooltip, Form, Tag } from 'antd';
import classnames from 'classnames';
import { EllipsisOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import AddDropdown from 'COMMON/components/NewAddDropdown/AddDropdown';
import styles from './CaseTitle.module.less';

function CaseTitle(props) {
    const {
        type,
        showAddDropdownItems = ['1', '2'],
        node,
        selectKey,
        searchValue,
        items,
        handleChangeCaseName,
        handleOprator,
        showKnowledgeType = false
    } = props;
    const [openEditInput, setOpenEditInput] = useState(false);
    const [editCaseId, setEditCaseId] = useState(null);
    const [editCaseName, setEditCaseName] = useState('');
    const inputRef = useRef(null);

    useEffect(() => {
        setOpenEditInput(editCaseId !== null);
    }, [editCaseId]);

    useEffect(() => {
        if (inputRef?.current) {
            if (openEditInput) {
                inputRef?.current?.focus();
            } else {
                inputRef?.current?.blur();
            }
        }
    }, [openEditInput]);

    const RenderTitle = (node) => {
        const strTitle = node.nodeName;
        const index = strTitle.indexOf(searchValue);
        const beforeStr = strTitle.substring(0, index);
        const afterStr = strTitle.slice(index + searchValue.length);
        const title =
            '' !== searchValue && index > -1 ? (
                <span>
                    {beforeStr}
                    <span className={styles.nodeSearch}>{searchValue}</span>
                    {afterStr}
                </span>
            ) : (
                <span>{strTitle}</span>
            );
        return title;
    };

    return (
        <div
            className={classnames(styles.treenode, styles.caseItem, {
                [styles.activedTreeNode]: selectKey === node.key
            })}
            style={{ position: 'relative' }}
        >
            <Tooltip title={node.title}>
                <div className={styles.treeNodeTitle}>
                    <Popover
                        trigger="click"
                        open={openEditInput}
                        onOpenChange={() => {
                            setEditCaseId(null);
                        }}
                        placement="right"
                        content={
                            <Input
                                ref={inputRef}
                                defaultValue={node?.nodeName}
                                onBlur={(e) => {
                                    handleChangeCaseName(node, e.target.value);
                                    setEditCaseId(null);
                                }}
                                onPressEnter={(e) => {
                                    e.currentTarget.blur();
                                }}
                            />
                        }
                    >
                        <span>{RenderTitle(node)}</span>
                    </Popover>
                </div>
            </Tooltip>
            {/* 根据值渲染是自动化知识还是场景知识 */}
            {showKnowledgeType && (
                <Tag color={node.knowledgeType?.[0] === 1 ? 'blue' : 'cyan'}>
                    {node.knowledgeType?.[0] === 1 ? '自动化知识' : '场景知识'}
                </Tag>
            )}
            <div
                className={classnames(styles.oprator, {
                    [styles.activedOprator]: selectKey === node.key
                })}
            >
                {node.type === 1 && (
                    <a onClick={(e) => e.stopPropagation()}>
                        <AddDropdown
                            type={type}
                            className={styles.activedAddIcon}
                            parentId={node.nodeId}
                            showItems={showAddDropdownItems}
                        />
                    </a>
                )}
                <a onClick={(e) => e.stopPropagation()}>
                    <Dropdown
                        menu={{
                            items: items?.filter((item) => item.disabled !== true),
                            onClick: (e) => {
                                e.domEvent.stopPropagation();
                                if (node?.nodeId) {
                                    handleOprator &&
                                        handleOprator(e, node, setEditCaseId, setEditCaseName);
                                }
                            }
                        }}
                        trigger={['click']}
                    >
                        <div className={styles.iconWrapper}>
                            <EllipsisOutlined className={styles.activedAddIcon} />
                        </div>
                    </Dropdown>
                </a>
            </div>
        </div>
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace
}))(CaseTitle);
