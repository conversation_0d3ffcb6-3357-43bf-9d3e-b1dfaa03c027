import {Empty} from 'antd';
import classnames from 'classnames';
import noContent from 'RESOURCES/svg/noContent.svg';
import styles from './NoContent.module.less';

export default function NoContent({text = '暂无数据', className, children}) {
    return (
        <Empty
            className={classnames(styles.defaultNoContent, className)}
            image={noContent}
            imageStyle={{height: 60}}
            description={<span className={styles.description}>{text}</span>}
        >
            {children}
        </Empty>
    );
}
