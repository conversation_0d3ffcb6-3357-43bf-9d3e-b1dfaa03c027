.relationItem {
    padding: 8px 0;
    cursor: pointer;
    overflow: hidden;

    &:hover {
        padding-bottom: 7px;
        border-bottom: 1px solid var(--primary-color);
    }

    .relationItemIcon {
        margin-right: 5px;
        font-size: 14px;
        color: #777;
    }

    .relationItemInfoLeft,
    .relationItemInfoRight {
        height: 30px;
        line-height: 30px;
    }

    .relationItemInfoLeft {
        float: left;
        width: 30px;
    }

    .relationItemInfoRight {
        float: left;
        width: calc(100% - 30px);

        .relationCaseName {
            float: left;
            width: calc(100% - 130px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-weight: bold;
        }

        .relationCaseTag {
            margin-left: 10px;
        }
    }

    .relationToCase {
        display: none;
        font-size: 12px;
    }

    &:hover {
        .relationToCase {
            display: inline-block;
        }
    }
}