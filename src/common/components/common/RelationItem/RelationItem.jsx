import { BookOutlined, ApiOutlined } from '@ant-design/icons';
import { useNavigate } from 'umi';
import { Tooltip, Tag } from 'antd';
import { stringifyUrl } from 'query-string';
import styles from './RelationItem.module.less';

const RelationItem = ({ item, currentSpace, getCaseNodeInfo, hideModal, messageApi }) => {
    const navigate = useNavigate();

    const handleClick = () => {
        const { caseNodeId, stepId } = item;
        getCaseNodeInfo({ caseNodeId })
            .then((res) => {
                const { treeNodeId, groupId } = res;
                navigate(
                    stringifyUrl({
                        url: '/case/edit',
                        query: {
                            moduleId: currentSpace?.id,
                            caseNodeId,
                            treeNodeId,
                            groupId,
                            view: 200,
                            os: item?.osType
                        }
                    })
                );
                hideModal();
            })
            .catch((err) => {
                messageApi.error(err?.message ?? err);
            });
    };

    return (
        <div className={styles.relationItem}>
            <div className={styles.relationItemInfoLeft}>
                <BookOutlined className={styles.relationItemIcon} />
            </div>
            <div className={styles.relationItemInfoRight}>
                <div className={styles.relationCaseName}>
                    <Tooltip title={item.caseNodeName} placement="left">
                        {item.caseNodeName}
                    </Tooltip>
                </div>
                <span className={styles.relationCaseTag}>
                    <Tag
                        color={item?.osType === 2 ? 'green' : 'blue'}
                        style={{ width: 60, textAlign: 'center' }}
                    >
                        {item?.osType === 2 ? 'iOS' : 'Android'}
                    </Tag>
                    <a className={styles.relationToCase} onClick={handleClick}>
                        <ApiOutlined /> 跳转
                    </a>
                </span>
            </div>
        </div>
    );
};

export default RelationItem;
