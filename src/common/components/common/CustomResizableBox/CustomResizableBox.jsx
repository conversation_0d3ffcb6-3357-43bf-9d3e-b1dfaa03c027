import { useState, useEffect } from 'react';
import classnames from 'classnames';
import { ResizableBox } from 'react-resizable';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import styles from './CustomResizableBox.module.less';

const CustomResizableBox = (props) => {
    const {
        asider = true,
        setAsider,
        children,
        showResizableBoxIcon = true,
        border = true
    } = props;

    const [resizeSize, setResizeSize] = useState({ width: 260 });

    const handleChangeExpand = () => {
        if (resizeSize.width < 3) {
            setAsider?.(true);
            setResizeSize({ width: 260 });
        } else {
            setAsider?.(false);
            setResizeSize({ width: 0 });
        }
    };

    useEffect(() => {
        setResizeSize({ width: asider ? 300 : 0 });
    }, [asider]);

    const renderResizableBoxHandle = () => {
        const handleIconStyle = resizeSize.width < 3 ? { left: '120%' } : {};
        return (
            <div className={styles.resizableBoxHandle}>
                {showResizableBoxIcon && (
                    <div
                        className={styles.resizableBoxHandleIcon}
                        onClick={handleChangeExpand}
                        style={handleIconStyle}
                    >
                        {resizeSize.width < 3 ? <RightOutlined /> : <LeftOutlined />}
                    </div>
                )}
            </div>
        );
    };
    return (
        <ResizableBox
            width={resizeSize.width}
            axis="x"
            handle={renderResizableBoxHandle()}
            className={classnames(styles.resizableBox, { [styles.resizableBoxWithBorder]: border })}
            minConstraints={[1, -Infinity]}
            maxConstraints={[1000, Infinity]}
        >
            {children}
        </ResizableBox>
    );
};

export default CustomResizableBox;
