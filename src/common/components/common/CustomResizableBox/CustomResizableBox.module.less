.resizableBox {
    background: var(--background-color);
    height: 100%;
    position: relative;
    flex-shrink: 0;
}

.resizableBoxWithBorder {
    border-right: 1px solid var(--border-color);
}

.resizableBoxHandle {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: transparent;
    cursor: col-resize;
    z-index: 999;
}

.resizableBoxHandleIcon {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;

    position: absolute;
    top: 3%;
    width: 26px;
    height: 26px;
    line-height: 26px;

    opacity: 1;
    transition: opacity .2s ease 0s;

    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, .06);
    cursor: pointer;
    font-size: 14px;
    color: var(--color2);
    z-index: 1;
}

.resizableBoxHandleIcon:hover {
    background-color: var(--base-hover-background-color);
}

.outlinedIcon {
    font-size: 12px;
}