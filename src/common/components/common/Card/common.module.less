// cardLayout
.cardLayout {
    padding: 30px 40px;
    height: calc(100vh - 40px);
    overflow: scroll;
}

// card title
.cardTitle {
    padding-left: 8px;
    margin: 20px 0 10px 2px;
    font-size: 14px;
    font-weight: bold;
    color: var(--primary-color);
    border-left: 5px solid var(--primary-color);
    /* 新增样式 原因：解决表单里面 cardTitle更大 样式不一致问题*/
    height: 20px;
    line-height: 20px;
    /* 将line-height设置为高度 */
    display: flex;
    align-items: center;
}

.tips {
    margin-left: 5px;
    cursor: pointer;
}

// card header
.CardHeader {
    position: relative;
    font-size: 20px;
    font-weight: bold;
}

// card sub header
.CardSubHeader {
    position: relative;
    font-size: 16px;
    font-weight: bold;
    color: var(--color2);
    margin-top: 10px;
}

.opera {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);

    :global {

        .ant-btn,
        .custom-default-btn,
        .custom-dark-btn {
            width: 110px;
            margin-left: 10px;
            border-radius: 8px !important;
            overflow: hidden;
        }
    }
}