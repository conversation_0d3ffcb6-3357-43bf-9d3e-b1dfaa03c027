import {InfoCircleOutlined} from '@ant-design/icons';
import {Tooltip} from 'antd';
import styles from './common.module.less';

function CardTitle({text, tips, children, style}) {
    return (
        <div className={styles.cardTitle} style={style}>
            {text}
            {tips && (
                <Tooltip title={tips} placement="right">
                    <InfoCircleOutlined className={styles.tips} />
                </Tooltip>
            )}
            {children}
        </div>
    );
}

export default CardTitle;
