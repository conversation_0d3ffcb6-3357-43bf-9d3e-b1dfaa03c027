import { useState } from 'react';
import { Select, message, Avatar } from 'antd';
import { CaretDownOutlined, UserOutlined } from '@ant-design/icons';
import { getUserList } from 'COMMON/api/base/common';
import styles from './UserSearch.module.less';

function UserSearch(props) {
    const { onChange, username } = props;
    const [data, setData] = useState([]);
    const [searchValue, setSearchValue] = useState('');
    const USER_PICKER_OPTIONS = 'user_picker_options_map';

    const setLocalStorage = () => {
        try {
            // 本地存储
            const optionsMap = JSON.parse(localStorage.getItem(USER_PICKER_OPTIONS) || '{}');
            optionsMap[id] = data;
            localStorage.setItem(USER_PICKER_OPTIONS, JSON.stringify(optionsMap));
        } catch (error) {}
    };

    const fetchSuggestions = async (value, callback) => {
        getUserList({ queryName: value, searchName: username })
            .then((data) => {
                callback(data?.userInfoList);
                setLocalStorage();
            })
            .catch((error) => {
                console.error('请求错误', error);
            });
    };

    const handleSearchName = (newValue) => {
        if (newValue) {
            setSearchValue(newValue);
            fetchSuggestions(newValue, setData);
        } else {
            setSearchValue('');
            setData([]);
        }
    };
    return (
        <Select
            {...props}
            showSearch
            placeholder="请输入人员邮箱或邮件组"
            onSearch={handleSearchName}
            popupMatchSelectWidth={false}
            onChange={onChange}
            options={[
                ...(searchValue !== '' ? [] : EXTRA_USER),
                ...(data || localOptions || []).map((d) => ({
                    value: d.username,
                    label: d.username,
                    avatar: d.avatar,
                    name: d.name,
                    department: d.department,
                    imageUrl: d.imageUrl
                }))
            ]}
            optionRender={(option) => (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                        overflow: 'scroll'
                    }}
                >
                    <Avatar
                        icon={<UserOutlined />}
                        src={option?.data?.imageUrl}
                        style={{ marginRight: 8 }}
                    />
                    <div>
                        <div>
                            {option?.data?.name
                                ? `${option?.data?.name} (${option?.data?.label})`
                                : option?.data?.label}
                        </div>
                        <div style={{ fontSize: 12, color: '#888' }}>
                            {option?.data?.department || '暂无部门信息'}
                        </div>
                    </div>
                </div>
            )}
        />
    );
}

export default UserSearch;
