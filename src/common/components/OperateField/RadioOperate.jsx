import React, { useState, useEffect } from 'react';
import { Radio } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';

function RadioOperate(props) {
    const { id, onBlur } = props;
    const [value, setValue] = useState();
    const onChange = ({ target: { value } }) => {
        setValue(value);
        props.onChange(value);
    };
    return (
        <Radio.Group
            defaultValue={props.defaultValue}
            options={props.options}
            onBlur={onBlur}
            onChange={onChange}
            value={value}
        />
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace
}))(RadioOperate);
