import { Checkbox } from 'antd';

// const plainOptions = ['Apple', 'Pear', 'Orange'];
const CheckboxOperate = (props) => {
    const onChange = (checkedValues) => {
        // console.log('checked = ', checkedValues);
        props.onChange(checkedValues);
    };
    const { defaultValue, options, onBlur } = props;
    return (
        <Checkbox.Group
            options={options}
            defaultValue={defaultValue}
            onChange={onChange}
            onBlur={onBlur}
        />
    );
};
export default CheckboxOperate;
