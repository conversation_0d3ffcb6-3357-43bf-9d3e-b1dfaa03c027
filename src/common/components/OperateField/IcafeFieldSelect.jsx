import { Select, Input, Button } from 'antd';
import { useState, useEffect } from 'react';

const IcafeFieldSelect = (props) => {
    const { options, value, defaultValue, onChange, bordered, onBlur, editType, ...otherProps } =
        props;
    const [currentValue, setCurrentValue] = useState(defaultValue || value);

    useEffect(() => {
        setCurrentValue(value || defaultValue);
    }, [value, defaultValue]);

    const handleChange = (e) => {
        const inputValue = e.target.value;
        if (!inputValue) {
            setCurrentValue(null);
            onChange && onChange(null);
            return;
        }
        const val = `{{icafe.${inputValue}}}`;
        setCurrentValue(val);
        onChange && onChange(val);
    };

    const handleBlur = (e) => {
        const inputValue = e.target.value;
        const val = `{{icafe.${inputValue}}}`;
        if (inputValue) {
            setCurrentValue(val);
            onBlur && onBlur(val);
        } else {
            setCurrentValue(null);
            onBlur && onBlur(null);
        }
    };

    return (
        <div
            style={{
                display: 'flex',
                alignItems: 'center'
            }}
        >
            <Input
                addonBefore="icafe."
                style={{
                    width: '240px'
                }}
                placeholder="请填写 icafe 字段中文名"
                bordered={bordered}
                onChange={handleChange}
                value={(typeof currentValue === 'string' ? currentValue : '').replace(
                    /^{{icafe\.(.*?)}}$/,
                    '$1'
                )}
                onBlur={handleBlur}
                {...otherProps}
            />
        </div>
    );
};

export default IcafeFieldSelect;
