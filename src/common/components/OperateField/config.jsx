import {
    Select,
    InputNumber,
    // DatePicker,
    Radio,
    Checkbox,
    Input,
    Cascader,
    Tooltip
} from 'antd';
import dayjs from 'dayjs';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import {
    CalendarOutlined,
    DownCircleOutlined,
    FontColorsOutlined,
    CodeOutlined,
    UserOutlined
} from '@ant-design/icons';
import DatePickerOperate from './DatePickerOperate';
import CheckboxOperate from './CheckboxOperate';
import MultipleSelectOperate from './MultipleSelectOperate';
import NameSearchSelectOperate from './NameSearchSelectOperate';
import IcodeSearchSelectOperate from './IcodeSearchSelectOperate';
import RadioOperate from './RadioOperate';
import Editor from './Editor/Editor';
dayjs.extend(weekday);
dayjs.extend(localeData);


const {TextArea} = Input;

export const INPUT_OPERATE = [
    'number_field',
    'default_field',
    'text_area_field',
    'comment_field',
    'url_field',
    'custom_label'
];

export const SELECT_OPERATE = [
    'user_picker',
    'user_picker_single',
    'select_list',
    'project_management',
    'select_list_multiple'
];

export const CASCADER_OPERATE = ['tree_field', 'tag_tree', 'plan_box'];

const QAMATE_FIELD_OPTIONS = [
    {label: '{{osType}}', key: '1', desc: '端类型'},
    {label: '{{brainMapTitle}}', key: '2', desc: '脑图标题'},
    {label: '{{nodeName}}', key: '3', desc: '节点名'},
    {label: '{{rootName}}', key: '7', desc: '根节点名'},
    {label: '{{nodeNameSplitByLineBreak}}', key: '4', desc: '全部节点名换行分割'},
    {label: '{{nodeNameSplitByComma}}', key: '5', desc: '全部节点名逗号分割'},
    {label: '{{nodeNameSplitByArrow}}', key: '6', desc: '全部节点名箭头分割'}
];

/**
 *  component 组件
 *  formatValue 处理 defaultValue、value
 *  formatResult 处理onChange的值
 *  formatOption 处理formatOption
 */
export const OperateMap = {
    //  多选用户/邮件组
    // 'user_picker': {
    //     component: MultipleSelectOperate,
    //     defaultProp: {
    //         // 可以添加一些组件配置，默认可以不添加
    //         multiple: true // 比如都是使用Select组件，这个是多选
    //     },
    //     formatOption(data) {
    //         // 处理数据
    //         return data;
    //     }
    // },
    // 单选用户
    user_picker_single: {
        icon: <DownCircleOutlined />,
        placeholder: '请选择',
        component: NameSearchSelectOperate,
        defaultProp: {
            allowClear: true,
            showSearch: true,
            filterOption: (input, option) => (option?.label ?? '').includes(input),
            filterSort: (optionA, optionB) =>
                (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
        },
        formatOption(data) {
            // 处理数据
            return data;
        }
    },
    // 数字
    number_field: {
        icon: <FontColorsOutlined />,
        placeholder: '请输入',
        component: InputNumber
    },
    text_area_field: {
        icon: <FontColorsOutlined />,
        placeholder: '请输入',
        component: TextArea,
        defaultProp: {
            rows: 4
        },
        formatResult(data) {
            //  单选框中的 target.value 才是具体的值
            return data.target.value;
        }
    },
    // 日期
    date_time: {
        icon: <CalendarOutlined />,
        placeholder: '请选择',
        component: DatePickerOperate,
        formatValue(value) {
            return value && value !== '' ? dayjs(value) : undefined;
        },
        formatResult(data) {
            // console.log(data);
            return data;
        }
    },
    // 日期和时间
    datetime_field: {
        icon: <CalendarOutlined />,
        placeholder: '请选择',
        component: DatePickerOperate,
        defaultProp: {
            showTime: true
        },
        formatValue(value) {
            return value && value !== '' ? dayjs(value) : undefined;
        },
        formatResult(data) {
            return data;
        }
    },
    // 单选下拉列表
    select_list: {
        icon: <DownCircleOutlined />,
        placeholder: '请选择',
        component: Select,
        defaultProp: {
            style: {
                minWidth: '200px'
            },
            allowClear: true,
            showSearch: true,
            filterOption: (input, option) => (option?.label ?? '').includes(input),
            filterSort: (optionA, optionB) =>
                (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
        },
        formatOption(data) {
            // 处理数据
            // 比如 服务端给的是 [{value:'xxx'}]
            // ==> Select组件需要的是： [{label:'xxx', value:'xxxx'}]
            // 通过转化成 formatOption [{label:'xxx', value:'xxxx'}]
            return data.map((item) => {
                return {
                    label: item,
                    value: item
                };
            });
        },
        formatResult(data) {
            return data;
        }
    },
    // 所属项目
    project_management: {
        icon: <DownCircleOutlined />,
        placeholder: '请选择',
        component: Select,
        defaultProp: {
            showSearch: true,
            allowClear: true,
            multiple: false, // 比如都是使用Select组件，这个是单选
            filterOption: (input, option) => (option?.label ?? '').includes(input),
            filterSort: (optionA, optionB) =>
                (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
        },
        formatOption(data) {
            // 处理数据
            return data.map((item) => {
                return {
                    label: item,
                    value: item
                };
            });
        },
        formatResult(data) {
            return data;
        }
    },
    // 多选下拉列表
    select_list_multiple: {
        icon: <DownCircleOutlined />,
        placeholder: '请选择',
        component: MultipleSelectOperate,
        defaultProp: {
            // 可以添加一些组件配置，默认可以不添加
            multiple: true, // 比如都是使用Select组件，这个是多选
            style: {
                minWidth: '200px'
            }
        },
        formatOption(data) {
            // 处理数据
            return data.map((item) => {
                return {
                    label: item,
                    value: item
                };
            });
        },
        formatResult(data) {
            // 如果是空数据则传空数组
            if (!data || (Array.isArray(data) && data.length === 0)) {
                return [];
            }
            return data;
        }
    },
    // 单选框
    radio_field: {
        icon: <DownCircleOutlined />,
        placeholder: '请选择',
        component: RadioOperate
        // formatOption(data) {
        //     console.log(data,"data")
        //     return data.map((item) => ({
        //         label: item,
        //         value: item
        //     }));
        // },
        // formatResult(data) {
        //     // console.log(data)
        //     //  单选框中的 target.value 才是具体的值
        //     return data.target.value;
        // }
    },
    // 复选框
    check_box_field: {
        icon: <DownCircleOutlined />,
        placeholder: '请选择',
        component: CheckboxOperate,
        changeEvent: '',
        defaultProp: {
            // 可以添加一些组件配置，默认可以不添加
            multiple: true // 比如都是使用Select组件，这个是多选
        },
        formatOption(data) {
            // 处理数据
            return data.map((item) => ({
                label: item,
                value: item
            }));
        },
        formatResult(data) {
            if (!data || (Array.isArray(data) && data.length === 0)) {
                return null;
            }
            return data;
        }
    },
    // 树
    tree_field: {
        icon: <DownCircleOutlined />,
        placeholder: '请选择',
        defaultProp: {
            expandTrigger: 'hover',
            fieldNames: {
                label: 'value',
                value: 'value',
                children: 'children'
            },
            changeOnSelect: true, //  配置则表示可以只选择父节点
            showSearch: {
                filter: (inputValue, path) =>
                    path.some(
                        (option) =>
                            option &&
                            option.value &&
                            option.value.toLowerCase().indexOf(inputValue.toLowerCase()) > -1
                    )
            }
        },
        component: Cascader, // 他不需需要添加props，在PlanSelect组件中都已经写了
        formatResult(data) {
            return data.join('/'); // 将结果使用 '/' 使用 / 拼接
        }
    },
    // tag树
    tag_tree: {
        icon: <DownCircleOutlined />,
        placeholder: '请选择',
        defaultProp: {
            fieldNames: {
                label: 'value',
                value: 'value',
                children: 'children'
            },
            expandTrigger: 'hover',
            changeOnSelect: true, //  配置则表示可以只选择父节点
            showSearch: {
                filter: (inputValue, path) =>
                    path.some(
                        (option) =>
                            option &&
                            option.value &&
                            option.value.toLowerCase().indexOf(inputValue.toLowerCase()) > -1
                    )
            }
        },
        component: Cascader,
        formatResult(data) {
            return data.join('/'); // 将结果使用 '/' 使用 / 拼接
        }
    },
    plan_box: {
        icon: <DownCircleOutlined />,
        placeholder: '请选择',
        component: Cascader,
        defaultProp: {
            expandTrigger: 'hover',
            fieldNames: {
                label: 'value',
                value: 'value',
                children: 'children'
            },
            changeOnSelect: true, //  配置则表示可以只选择父节点
            showSearch: {
                filter: (inputValue, path) =>
                    path.some(
                        (option) =>
                            option &&
                            option.value &&
                            option.value.toLowerCase().indexOf(inputValue.toLowerCase()) > -1
                    )
            }
        },
        formatValue(data) {
            // 级联选择器需要的是一个数组
            // 如果给的是数组则不需要转化
            return data; // 即可
        },
        formatOption(data) {
            return data;
        },
        formatResult(data) {
            return data?.join('/'); // 将结果使用 '/' 使用 / 拼接
        }
    },
    // 输入框
    comment_field: {
        icon: <FontColorsOutlined />,
        placeholder: '请输入',
        component: TextArea,
        formatResult(data) {
            //  单选框中的 target.value 才是具体的值
            return data.target.value;
        }
    },
    default_field: {
        icon: <FontColorsOutlined />,
        placeholder: '请输入',
        component: Input,
        formatResult(data) {
            //  单选框中的 target.value 才是具体的值
            return data.target.value;
        }
    },
    // select
    user_picker: {
        icon: <UserOutlined />,
        placeholder: '请选择',
        component: NameSearchSelectOperate,
        defaultProp: {
            // 可以添加一些组件配置，默认可以不添加
            mode: 'multiple', // 比如都是使用Select组件，这个是多选
            showArrow: true,
            showSearch: true,
            allowClear: true,
            filterOption: false,
            allowClear: true,
            style: {
                minWidth: '200px'
            }
        },
        formatValue(data) {
            // console.log('formatValue', data);
            if (data === null) {
                return [];
            }
            return data;
        },
        formatOption(data) {
            // 处理数据
            return data.map((item) => ({
                label: item,
                value: item
            }));
        },
        formatResult(data) {
            // console.log('formatResult', data);
            if (!data || (Array.isArray(data) && data.length === 0)) {
                return null;
            }
            return data;
        }
    },
    // select
    external_datasource: {
        icon: <CodeOutlined />,
        placeholder: '请选择',
        component: IcodeSearchSelectOperate,
        defaultProp: {
            // 可以添加一些组件配置，默认可以不添加
            showArrow: true,
            showSearch: true,
            allowClear: true,
            filterOption: false,
            allowClear: true,
            style: {
                minWidth: '200px'
            }
        },
        formatValue(data) {
            if (data === null) {
                return [];
            }
            return data;
        },
        formatOption(data) {
            // 处理数据
            return data.map((item) => ({
                label: item,
                value: item
            }));
        },
        formatResult(data) {
            if (!data || (Array.isArray(data) && data.length === 0)) {
                return null;
            }
            return data;
        }
    },
    qamate_detail: {
        icon: <FontColorsOutlined />,
        placeholder: '请输入',
        component: Editor,
        formatOption(data = []) {
            // 处理数据 这个组件接受[{label: '', key: ''}] 类型的数据，
            // 对应服务端的数据， 可能需要调整取值逻辑
            return QAMATE_FIELD_OPTIONS;
        },
        formatResult(data = []) {
            //  单选框中的 target.value 才是具体的值
            return data;
        }
    },
    qamate_title: {
        icon: <FontColorsOutlined />,
        placeholder: '请输入',
        component: Editor,
        formatOption(data = []) {
            // 处理数据 这个组件接受[{label: '', key: ''}] 类型的数据，
            // 对应服务端的数据， 可能需要调整取值逻辑
            return QAMATE_FIELD_OPTIONS;
        },
        formatResult(data = []) {
            //  单选框中的 target.value 才是具体的值
            return data;
        }
    }
};
