import React, { useState, useEffect } from 'react';
import { Select } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import { getIcodeList } from 'COMMON/api/base/common';

const ICODE_PICKER_OPTIONS = 'external_datasource';

function IcodeSearchSelectOperate(props) {
    const { id, onBlur, defaultValue } = props;
    const [localOptions, setLocalOptions] = useState([]);
    const [data, setData] = useState([]);
    const setLocalStorage = () => {
        try {
            // 本地存储
            const optionsMap = JSON.parse(localStorage.getItem(ICODE_PICKER_OPTIONS) || '{}');
            optionsMap[id] = data;
            localStorage.setItem(ICODE_PICKER_OPTIONS, JSON.stringify(optionsMap));
        } catch (error) {}
    };
    const fetchSuggestions = async (value, callback) => {
        getIcodeList({ icodeName: value })
            .then((data) => {
                callback(data?.icodeNameList);
                setLocalStorage();
            })
            .catch((error) => {
                console.error('请求错误', error);
            });
    };

    useEffect(() => {
        try {
            const optionsMap = JSON.parse(localStorage.getItem(USER_PICKER_OPTIONS));
            if (Array.isArray(optionsMap[id])) {
                setLocalOptions(optionsMap[id]);
                setData(optionsMap[id]);
            }
        } catch (error) {}
    }, []);
    const handleSearch = (newValue) => {
        if (newValue) {
            fetchSuggestions(newValue, setData);
        } else {
            setData([]);
        }
    };
    const handleChange = (newValue) => {
        props.onChange(newValue);
    };
    return (
        <Select
            defaultValue={Array.isArray(props.value) ? defaultValue : []}
            {...props}
            onSearch={handleSearch}
            onChange={handleChange}
            onBlur={onBlur}
            options={(data || localOptions || []).map((d) => ({
                value: d,
                label: d
            }))}
        />
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace
}))(IcodeSearchSelectOperate);
