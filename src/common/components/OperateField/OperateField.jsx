import {Input} from 'antd';
import {OperateMap} from './config.jsx';

const OperateField = (props) => {
    // otherProps 父组件的props，透传到 RenderComponent组件中 使用解构语法排除 fieldType 等
    const {fieldType, options, onChange, defaultValue, value, ...otherProps} = props;
    
    // components: 取对应组件;  defaultProp: 取配置props; formatOption 获取处理数据函数
    const {
        component: RenderComponent = Input,
        defaultProp = {},
        formatOption,
        formatValue,
        formatResult
    } = OperateMap[fieldType] || OperateMap?.default_field;
    let formatOptions = options;
    
    // 如果存在处理数据方法，则处理数据之后在传递给组件
    if (formatOption && typeof formatOption === 'function') {
        // 兼容服务端返回的null
        formatOptions = formatOption(options || []);
    }
    
    // 处理初始值
    let formatDefaultValue = defaultValue;
    let formatData = value;
    if (formatValue && typeof formatValue === 'function') {
        formatDefaultValue = formatValue(defaultValue);
        formatData = formatValue(value);
    }
    // 监听子组件的onChange事件
    const onComponentChange = (value) => {
        // 格式化返回值数据
        if (formatResult && typeof formatResult === 'function') {
            // 通过config 配置的 formatResult 方法来处理 数据格式，可能会有一些拼接或者日期的格式化操作
            onChange && onChange(formatResult(value));
            return;
        }
        //   如果没有配置 formatResult 方法， 则直接不处理数据，直接返回onChange默认的结果
        onChange && onChange(value);
    };

    // 合并props， 外部组件的props可以覆盖默认配置的props
    const mergeProps = {...defaultProp, ...otherProps};
    return (
        <RenderComponent
            value={formatData}
            options={formatOptions}
            onChange={onComponentChange}
            defaultValue={formatDefaultValue}
            {...mergeProps}
        />
    );
};
export default OperateField;
