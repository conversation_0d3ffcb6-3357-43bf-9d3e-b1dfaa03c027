import React, { useState, useEffect } from 'react';
import { Select, message, Avatar } from 'antd';
import { CaretDownOutlined, UserOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import { getUserList } from 'COMMON/api/base/common';

const EXTRA_USER = [
    { value: '{{currentUser}}', label: '当前用户 {{currentUser}}' },
    { value: '{{pm}}', label: '当前脑图PM {{pm}}' },
    { value: '{{rd}}', label: '当前脑图RD {{rd}}' },
    { value: '{{qa}}', label: '当前脑图QA {{qa}}' },
    { value: '{{ue}}', label: '当前脑图UE {{ue}}' },
    { value: '{{other}}', label: '当前脑图其他人员 {{other}}' }
];

function NameSearchSelectOperate(props) {
    const { id, onBlur, editType, username, defaultValue } = props;
    const [data, setData] = useState([]);
    const [searchValue, setSearchValue] = useState('');

    const fetchSuggestions = async (value, callback) => {
        getUserList({ queryName: value, searchName: username })
            .then((data) => {
                callback(data?.userInfoList);
            })
            .catch((error) => {
                console.error('请求错误', error);
            });
    };

    const handleSearch = (newValue) => {
        if (newValue) {
            setSearchValue(newValue);
            fetchSuggestions(newValue, setData);
        } else {
            setSearchValue('');
            fetchSuggestions('', setData);
        }
    };
    const handleChange = (newValue) => {
        props.onChange(newValue);
    };

    return (
        <Select
            defaultValue={Array.isArray(props.value) ? defaultValue : []}
            {...props}
            popupMatchSelectWidth={false}
            onSearch={handleSearch}
            onChange={handleChange}
            onBlur={onBlur}
            options={[
                ...(editType === 'bugCreate' || searchValue !== '' ? [] : EXTRA_USER),
                ...data.map((d) => ({
                    value: d.username,
                    label: d.username,
                    avatar: d.avatar,
                    name: d.name,
                    department: d.department,
                    imageUrl: d.imageUrl
                }))
            ]}
            optionRender={(option) => (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                        overflow: 'scroll'
                    }}
                >
                    <Avatar
                        icon={<UserOutlined />}
                        src={option?.data?.imageUrl}
                        style={{ marginRight: 8 }}
                    />
                    <div>
                        <div>
                            {option?.data?.name
                                ? `${option?.data?.name} (${option?.data?.label})`
                                : option?.data?.label}
                        </div>
                        {option?.data?.department && (
                            <div style={{ fontSize: 12, color: '#888' }}>
                                {option?.data?.department || '暂无部门信息'}
                            </div>
                        )}
                    </div>
                </div>
            )}
        />
    );
}

export default connectModel([baseModel], (state) => ({
    username: state.common.base.username,
    currentSpace: state.common.base.currentSpace
}))(NameSearchSelectOperate);
