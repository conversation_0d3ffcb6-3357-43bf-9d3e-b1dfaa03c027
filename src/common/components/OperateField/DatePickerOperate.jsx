import { DatePicker } from 'antd';
// import moment from 'moment';

/**
 * 时间选择器, 将onOk 与onChange 统一处理成 onChange 事件
 * @param {*} props
 * @returns
 */
const DatePickerOperate = (props) => {
    const {
        defaultValue: defaultPickerValue,
        onChange: innerOnChange,
        onBlur,
        ...otherProps
    } = props;
    const onChange = (value, dateString) => {
        // console.log('onChange', value.valueOf()); // 时间戳
        innerOnChange(dateString);
        if (!props.showTime) {
            onBlur && onBlur(dateString);
        }
    };
    const onOk = (value) => {
        // console.log('ok', value.valueOf()); // 时间戳
        innerOnChange(value.format('YYYY-MM-DD HH:mm:ss'));
        onBlur && onBlur(value.format('YYYY-MM-DD HH:mm:ss'));
    };
    //
    return (
        <DatePicker
            defaultValue={props.defaultValue}
            onChange={onChange}
            onOk={onOk}
            showTime={props.showTime || false}
            {...otherProps}
            variant="borderless"
        />
    );
};
export default DatePickerOperate;
