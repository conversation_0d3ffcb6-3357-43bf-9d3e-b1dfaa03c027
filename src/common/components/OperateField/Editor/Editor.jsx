import {Input, Dropdown} from 'antd';
import {useRef, useState, useEffect} from 'react';
import '@baidu/morpho/dist/index.css';
import {
    <PERSON><PERSON><PERSON>,
    Editor,
    createEditor,
    Toolbar,
} from '@baidu/morpho';
import {fileImageUpload} from 'COMMON/utils/utils';

/**
 *
 * @param {*} param
 * @param {} inputProps 输入框的props
 * @returns
 */
const TextAreaEditor = (props) => {
    const [open, setOpen] = useState(false);
    const [result, setResult] = useState('');
    const inputRef = useRef(null);
    const [beforeText, setBeforeText] = useState('');
    const [afterText, setAfterText] = useState('');
    const {TextArea} = Input;

    useEffect(() => {
        setResult(props.defaultValue);
    }, [props.defaultValue]);

    const onInputChange = (e) => {
        const input = e.target;
        const value = input.value;
        setResult(value);
        // 将文本分成两段，一个是光标之前文本，一段是光标之后 获取光标位置之前的文本
        const beforeText = value.substring(0, input.selectionStart);
        // 获取光标位置之后的文本
        const afterText = value.substring(input.selectionEnd);
        setAfterText(afterText);
        setBeforeText(beforeText);
        // 判断前一段是不是{{ 结尾
        if (beforeText.endsWith('{{')) {
            setOpen(true);
            // 获取光标位置之前的文本
            const beforeValue = value.substring(0, input.selectionStart - 2);
            // 重新处理之前的文本
            setBeforeText(beforeValue);
        } else {
            setOpen(false);
        }
        props.onChange(value);
    };

    const handleMenuClick = (item) => {
        const replaceText = props.options.find((v) => v.key === item.key);
        let result = beforeText + replaceText.label + afterText;
        setResult(result);
        setOpen(false);
        inputRef.current.focus();
    };

    const onBlur = (e) => {
        if (open) {
            return;
        }
        setOpen(false);
        setTimeout(() => {
            props.onBlur && props.onBlur(result);
        }, 100);
    };
    useEffect(() => {
        props.onChange(result);
    }, [result]);

    return (
        <div>
            <Dropdown
                menu={{
                    items: props.options.map((item) => {
                        return {
                            key: item.key,
                            label: (
                                <div>
                                    {item.label}{' '}
                                    <span style={{color: '#ccc', paddingLeft: 5}}>
                                        // {item.desc}
                                    </span>
                                </div>
                            )
                        };
                    }),
                    onClick: handleMenuClick
                }}
                open={open}
            >
                <TextArea
                    {...props}
                    ref={inputRef}
                    value={result}
                    onChange={onInputChange}
                    autoSize={{minRows: 3, maxRows: 5}}
                    onBlur={onBlur}
                />
            </Dropdown>
        </div>
    );
};
const RichTextEditor = (props) => {
    const editor = createEditor({
        image: {
            upload: fileImageUpload,
            base64: false
        },
        embed: false
    });
    const [inputValue, setInputValue] = useState(editor.htmlSerializer.deserialize('<div/>'));
    useEffect(() => {
        setInputValue(editor.htmlSerializer.deserialize(`<div>${props.defaultValue}</div>`));
    }, [props.defaultValue]);

    useEffect(() => {
        props.onChange(inputValue);
    }, [inputValue]);
    return (
        <div>
            <Morpho editor={editor} style={{width: 200}}>
                <Toolbar />
                <Editor
                    style={{height: 300, width: 200, maxWidth: '100%', overflow: 'scroll'}}
                    readOnly
                    value={inputValue}
                    onBlur={(e) => {
                        typeof props.onBlur === 'function' && props.onBlur(e);
                    }}
                    onChange={setInputValue}
                />
            </Morpho>
        </div>
    );
};
export default function (props) {
    const {type, ...otherProps} = props;
    if (type === 'rich-text') {
        return <RichTextEditor {...otherProps} />;
    }
    return <TextAreaEditor {...otherProps} />;
}
