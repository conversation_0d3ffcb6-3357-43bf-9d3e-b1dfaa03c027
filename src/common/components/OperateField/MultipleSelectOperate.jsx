import {Select} from 'antd';
const options = [];
for (let i = 10; i < 36; i++) {
    options.push({
        label: i.toString(36) + i,
        value: i.toString(36) + i
    });
}

const MultipleSelectOperate = (props) => {
    const handleChange = (value) => {
        console.log(`selected ${value}`);
        props.onChange(value);
    };
    const {defaultValue, options, onBlur} = props;
    return (
        <Select
            showSearch
            {...props}
            mode="multiple"
            allowClear
            style={{
                width: '100%'
            }}
            placeholder="请选择"
            defaultValue={defaultValue === null ? [] : defaultValue}
            onChange={handleChange}
            options={options}
            onBlur={onBlur}
            filterOption={(input, option) => (option?.label ?? '').includes(input)}
            filterSort={(optionA, optionB) =>
            (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
            }
        />
    );
};
export default MultipleSelectOperate;
