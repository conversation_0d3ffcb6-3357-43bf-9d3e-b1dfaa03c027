import {Divider} from 'antd';
import {
    PushpinOutlined,
} from '@ant-design/icons';
import styles from './ParamsSmallTitle.module.less';

function ParamsSmallTitle({text}) {
    return (
        <div className={styles.settingTitle}>
            <Divider orientation="left" orientationMargin="0">
                <span className={styles.settingTitleText}>
                {/* <PushpinOutlined />&nbsp; */}
                    {text}
                </span>
            </Divider>
        </div>

    );
}
export default ParamsSmallTitle;