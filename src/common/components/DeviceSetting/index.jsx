import {useState, useMemo, useEffect} from 'react';
import {isEmpty} from 'lodash';
import {Select, Tooltip, Tag} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';

function DeviceSetting(props) {
    const {
        className,
        onChangeDevice,
        deviceList,
        curDevice,
        curOsType,
        osTypeList = [1, 2],
        disabled = false,
        placeholder = '无设备连接'
    } = props;
    const [deviceOpen, setDeviceOpen] = useState(false);
    const [filterDeviceList, setFilterDeviceList] = useState([]);

    useEffect(() => {
        let _filterDeviceList = [];
        if (osTypeList.includes(1)) {
            _filterDeviceList = [..._filterDeviceList, ...deviceList.android];
        }
        if (osTypeList.includes(2)) {
            _filterDeviceList = [..._filterDeviceList, ...deviceList.iOS];
        }
        // deviceList 前置未注册设备
        const noRegisterDeviceList = _filterDeviceList.filter(item => item.statusStage.register.status === 3);
        const registerDeviceList = _filterDeviceList.filter(item => item.statusStage.register.status !== 3);
        const newDeviceList = [...noRegisterDeviceList, ...registerDeviceList];
        setFilterDeviceList(newDeviceList);

        // 过滤之后重置当前设备，设置当前设备为第一项：
        if (!isEmpty(newDeviceList) && newDeviceList[0].statusStage.register.status === 3) {
            onChangeDevice(newDeviceList[0].deviceId);
        } else {
            // 设置为空
            onChangeDevice(null);
        }
    }, [curOsType, curDevice, deviceList]);

    // 选项样式设置
    const options = filterDeviceList.map((device) => {
        const {deviceStatusName, deviceStatusColor} = getInfo(
            device.status,
            device.statusStage.register.status
        );
        return {
            value: device.deviceId,
            label: (
                <>
                    <Tag color={deviceStatusColor}>
                        {deviceStatusName}
                    </Tag>
                    <Tooltip
                        placement="right"
                        title={
                            '4' === device.status
                                ? `${device.statusMsg} - ${device.deviceId}`
                                : `${device.deviceId}`
                        }
                    >
                        {'android' === device?.deviceType
                            ? `${device.brand} ${device.marketName
                            } - ${device.deviceId.substring(0, 8)}`
                            : `${device.marketName} - ${device.deviceId.substring(0, 8)}`}
                    </Tooltip>
                </>
            )
        };
    });

    return (
        <div className={className}>
            <Select
                popupMatchSelectWidth={false}
                variant='borderless'
                open={deviceOpen}
                onDropdownVisibleChange={(visible) => setDeviceOpen(visible)}
                size="small"
                style={{
                    width: '100%'
                }}
                value={curDevice?.deviceId}
                placeholder={placeholder ? placeholder : '无设备连接'}
                onChange={onChangeDevice}
            >
                {options.map((option) => (
                    <Select.Option
                        key={option.value}
                        value={option.value}
                        disabled={
                            disabled && deviceList[2 === curOsType ? 'iOS' : 'android'].some(
                                (device) => device.deviceId === option.value && device.statusStage.register.status !== 3
                            )
                        }
                    >
                        {option.label}
                    </Select.Option>
                ))}
            </Select>
        </div>
    );
}

function getInfo(deviceStatus, unregistered) {
    let deviceStatusName = '';
    let deviceStatusColor = '';
    if (unregistered === 3) {
        deviceStatus = 7;
    }
    switch (deviceStatus) {
        case 0:
            deviceStatusName = '已连接';
            deviceStatusColor = '#1677ff';
            break;
        case 1:
            deviceStatusName = '初始化';
            deviceStatusColor = '#1677ff';
            break;
        case 2:
            deviceStatusName = '空闲';
            deviceStatusColor = '#73d13d';
            break;
        case 3:
            deviceStatusName = '繁忙';
            deviceStatusColor = '#faad14';
            break;
        case 4:
            deviceStatusName = '异常';
            deviceStatusColor = '#ff4d4f';
            break;
        case 5:
            deviceStatusName = '离线';
            deviceStatusColor = '#bfbfbf';
            break;
        case 6:
            deviceStatusName = '离线中';
            deviceStatusColor = '#bfbfbf';
            break;
        case 7:
            deviceStatusName = '未注册';
            deviceStatusColor = '#bfbfbf';
            break;
        default:
            break;
    }
    return {deviceStatusName, deviceStatusColor};
}

export default connectModel([baseModel, commonModel], (state) => ({
    deviceList: state.common.base.deviceList,
    currentModule: state.common.base.currentModule,
}))(DeviceSetting);
