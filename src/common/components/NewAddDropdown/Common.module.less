// .addIcon {
//     margin-left: 5px;
//     font-size: 14px;
//     color: var(--text-color);
// }

// 新增
.addCaseText {
  font-size: 12px;
}

.pageContent {
  background-color: var(--background-color);
  height: 100%;
  padding: 30px;
  // margin: 10px;
  // border-top-left-radius: 10px;
  // border-top-right-radius: 10px;
}

.formContent {
  max-width: 800px;
}

.iconWrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.iconWrapper:hover {
  background-color: rgba(0, 0, 0, 0.1);
  /* 你可以根据需要调整颜色 */
}

.addIcon {
  color: var(--icon-color);
  font-size: 12px;
  /* 根据需要调整图标大小 */
}

.templateOption {
  width: 100%;

  .showIcon {
    display: none;
    color: #777;
    display: none;
  }
}

.templateOption:hover {
  .showIcon {
    display: block;
  }
}

.moreWrap {
  position: absolute;
  right: 0px;
  bottom: -25px;
  font-size: 12px;
  text-decoration: underline;

  .moreBtn {
    float: right;
    margin: 2px 0 0 10px;
    text-decoration: underline;
  }

  :global {
    .ant-checkbox-wrapper {
      font-size: 12px !important;
    }

    .ant-checkbox+span {
      padding: 2px 0 0 2px;
    }
  }
}

.link {
  padding: 2px 4px;
  background-color: #e3eafa;
  color: #2d6dfa;
  border-radius: 3px;
  font-size: 12px;
  margin-right: 5px;
}

.icafeOption {
  overflow: hidden;
  white-space: nowrap;
}