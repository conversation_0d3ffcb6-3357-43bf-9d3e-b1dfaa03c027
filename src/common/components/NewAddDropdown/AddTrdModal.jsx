import { forwardRef, useState, useRef, useCallback, useImperativeHandle } from 'react';
import { Modal, Form, Input, Button, Space, message } from 'antd';
import { useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import { getQueryParams } from 'COMMON/utils/utils';
import trdModel from 'COMMON/models/rd_effect_tools/trdModel';
import { createMrd } from 'COMMON/api/rd_effect_tools/trd';
import DemandBind from 'FEATURES/components/demand/DemandBind';
import styles from './Common.module.less';

function AddTrdModal(props, ref) {
    const { currentSpace, setShowModal, curTreeNodeId } = props;
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const [addForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    const demandBindRef = useRef();
    const { icafeLink } = getQueryParams();

    const showModal = useCallback(() => {
        setShowModal(true);
        setOpen(true);
    }, []);

    const hideModal = useCallback(() => {
        addForm.resetFields();
        setShowModal(false);
        setOpen(false);
    }, []);

    const onReset = () => {
        addForm.resetFields();
        hideModal();
    };

    const onClick = () => {
        addForm
            ?.validateFields()
            .then(async (values) => {
                let body = {
                    treeNodeId: curTreeNodeId
                };
                setLoading(true);
                // 需求绑定所需表单值
                const demondFormValues = await demandBindRef?.current?.handleSubmitByTRD();
                body.docNodeTreeList = demondFormValues?.docNodeTreeList;
                createMrd(body)
                    .then((res) => {
                        messageApi.success('创建成功');
                        EventBus.emit('refreshMrdList');
                        navigate(
                            stringifyUrl({
                                url: '/trd/detail',
                                query: {
                                    docNodeId: res.docNodeIdList?.[0],
                                    moduleId: currentSpace?.id,
                                    stage: 'trd'
                                }
                            })
                        );
                        hideModal();
                        setLoading(false);
                    })
                    .catch((err) => {
                        setLoading(false);
                    });
            })
            .catch((err) => {
                console.log(err?.message ?? err);
                setLoading(false);
                messageApi.warning('请填写完整表单');
            });
    };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    return (
        <>
            {contextHolder}
            <Modal
                title="新增需求"
                autoComplete="off"
                open={open}
                destroyOnClose
                transitionName=""
                onCancel={hideModal}
                footer={
                    <div>
                        <Space>
                            <Button onClick={onReset}>取消</Button>
                            <Button type="primary" loading={loading} onClick={onClick}>
                                确定
                            </Button>
                        </Space>
                    </div>
                }
                mask="true"
                maskClosable="false"
                width={window.innerWidth * 0.4}
            >
                <br />
                <Form form={addForm} colon={false} preserve={false} requiredMark={false}>
                    <DemandBind
                        ref={demandBindRef}
                        icafeId={icafeLink}
                        labelCol={{ span: 3 }}
                        showLevel={false}
                        showTitle={false}
                        showGenerateTaskKey={false}
                        disabledItems={{ docForm: true, nodeLink: false }}
                    />
                </Form>
            </Modal>
        </>
    );
}

export default connectModel([baseModel, trdModel], (state) => ({
    showModal: state.common.base.showModal,
    currentSpace: state.common.base.currentSpace,
    curTreeNodeId: state.common.trd.curTreeNodeId
}))(forwardRef(AddTrdModal));
