import { Dropdown } from 'antd';
import { useEffect, useRef } from 'react';
import classnames from 'classnames';
import { PlusOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import { useTabType } from 'HOOKS/useTabType';
import AddFileModal from './AddFileModal';
import AddCaseModal from './AddCaseModal';
import AddIntelligentCaseModal from './AddIntelligentCaseModal';
import AddTrdModal from './AddTrdModal';
import NodeIcon from './components/NodeIcon';
import styles from './Common.module.less';

function AddDropdown(props) {
    const {
        type,
        showTrdModal = false,
        className,
        parentId,
        preSibId,
        showItems = ['1', '2'] // 默认展示12, 1、文件夹 2、用例 3、智能测试 4、TRD
    } = props;
    const tabType = useTabType();
    const addFileModalRef = useRef();
    const addCaseModalRef = useRef();
    const addIntelligentCaseModalRef = useRef();
    const addTrdModalRef = useRef();

    useEffect(() => {
        if (showTrdModal) {
            addTrdModalRef.current?.show();
        }
    }, [showTrdModal]);

    const handleAdd = (e) => {
        switch (e.key) {
            case '1':
                addFileModalRef.current?.show();
                break;
            case '2':
                addCaseModalRef.current?.show();
                break;
            case '3':
                addIntelligentCaseModalRef.current?.show();
                break;
            case '4':
                addTrdModalRef.current?.show();
                break;
            default:
                break;
        }
    };

    const allItems = [
        {
            label: (
                <>
                    <NodeIcon type={4} />
                    <span className={styles.addCaseText}>新增需求</span>
                </>
            ),
            key: '4'
        },
        {
            label: (
                <>
                    <NodeIcon type={3} />
                    <span className={styles.addCaseText}>新建测试</span>
                </>
            ),
            key: '3'
        },
        {
            label: (
                <>
                    <NodeIcon type={2} />
                    <span className={styles.addCaseText}>新建用例</span>
                </>
            ),
            key: '2'
        },
        {
            label: (
                <>
                    <NodeIcon type={1} />
                    <span className={styles.addCaseText}>新建目录</span>
                </>
            ),
            key: '1'
        }
    ];

    // 根据showItems过滤要展示的选项
    const items = allItems.filter((item) => showItems.includes(item.key));

    return (
        <>
            {tabType === 'rd_effect_tools' ? (
                <div
                    className={classnames(styles.iconWrapper, className)}
                    onClick={() => handleAdd({ key: '4' })}
                >
                    <PlusOutlined className={classnames(styles.addIcon, className)} />
                </div>
            ) : (
                <Dropdown
                    menu={{
                        items,
                        onClick: handleAdd
                    }}
                    trigger={['click']}
                >
                    <div className={classnames(styles.iconWrapper, className)}>
                        <PlusOutlined className={classnames(styles.addIcon, className)} />
                    </div>
                </Dropdown>
            )}
            {showItems.includes('4') && (
                <AddTrdModal
                    type={type}
                    ref={addTrdModalRef}
                    parentId={parentId}
                    preSibId={preSibId}
                />
            )}
            {showItems.includes('3') && (
                <AddIntelligentCaseModal
                    type={type}
                    ref={addIntelligentCaseModalRef}
                    parentId={parentId}
                    preSibId={preSibId}
                />
            )}
            {showItems.includes('2') && (
                <AddCaseModal
                    type={type}
                    ref={addCaseModalRef}
                    parentId={parentId}
                    preSibId={preSibId}
                />
            )}
            {showItems.includes('1') && (
                <AddFileModal
                    type={type}
                    ref={addFileModalRef}
                    parentId={parentId}
                    preSibId={preSibId}
                />
            )}
        </>
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace
}))(AddDropdown);
