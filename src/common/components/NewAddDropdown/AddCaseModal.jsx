import { forwardRef, useState, useRef, useCallback, useImperativeHandle } from 'react';
import {
    Modal,
    Select,
    Form,
    Input,
    Button,
    Space,
    Radio,
    Checkbox,
    message,
    Divider,
    Tooltip
} from 'antd';
import { useNavigate } from 'umi';
import { isEmpty } from 'lodash';
import { EyeOutlined } from '@ant-design/icons';
import { stringifyUrl } from 'query-string';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { createTreeNode } from 'COMMON/api/front_qe_tools/tree';
import { getSignTypeInitialValueWith3 } from 'COMMON/utils/utils';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import styles from './Common.module.less';

function AddCaseModal(props, ref) {
    const {
        currentSpace,
        caseTemplateList,
        getCaseTemplateList,
        parentId,
        preSibId,
        currentModule,
        setShowModal,
        currentGroup,
        creationConfig
    } = props;
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const [addCaseForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    const settingModalRef = useRef();

    const showModal = useCallback(() => {
        getCaseTemplateList({ moduleId: currentSpace?.id });
        setShowModal(true);
        setOpen(true);
    }, []);

    const hideModal = useCallback(() => {
        addCaseForm.resetFields();
        setShowModal(false);
        setOpen(false);
    }, []);

    const onReset = () => {
        addCaseForm.resetFields();
        hideModal();
    };

    const onClick = () => {
        addCaseForm
            ?.validateFields()
            .then(async (values) => {
                let ids = caseTemplateList.map((item) => item.id);
                for (let element of values?.templateIdList) {
                    if (!ids.includes(element)) {
                        messageApi.warning('存在用例模板已被删除，请重新选择');
                        return;
                    }
                }
                let body = {
                    groupId: currentGroup?.groupId,
                    nodeName: values.name,
                    nodeType: 2
                };
                if (parentId) {
                    body.parentId = parentId;
                }
                if (preSibId) {
                    body.preSibId = preSibId;
                }
                if (values.osType === '7') {
                    body.osTypeList = values.osTypes;
                } else {
                    body.osType = +values.osType;
                }
                if (+values.osType === 3 || +values.osType === 7) {
                    body.signType = +values.signType;
                } else {
                    body.signType = 0;
                }
                if (!isEmpty(values.templateIdList)) {
                    body.templateIdList = values.templateIdList;
                }
                body.signStage = values.signStage;
                setLoading(true);
                createTreeNode(body)
                    .then((res) => {
                        messageApi.success('创建成功');
                        EventBus.emit('refreshTreeNodeList');
                        navigate(
                            stringifyUrl({
                                url: '/' + currentModule + '/edit',
                                query: {
                                    treeNodeId: res.nodeId,
                                    groupId: currentGroup?.groupId,
                                    moduleId: currentSpace?.id
                                }
                            })
                        );
                        hideModal();
                        setLoading(false);
                    })
                    .catch((err) => {
                        setLoading(false);
                    });
            })
            .catch((err) => {
                console.log(err?.message ?? err);
                setLoading(false);
                messageApi.warning('请填写完整表单');
            });
    };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    return (
        <>
            {contextHolder}
            <Modal
                title="创建用例"
                autoComplete="off"
                open={open}
                destroyOnClose
                transitionName=""
                onCancel={hideModal}
                footer={
                    <div>
                        <Space>
                            <Button onClick={onReset}>取消</Button>
                            <Button type="primary" loading={loading} onClick={onClick}>
                                确定
                            </Button>
                        </Space>
                    </div>
                }
                mask="true"
                maskClosable="false"
                width={window.innerWidth * 0.6}
            >
                <br />
                <Form form={addCaseForm} colon={false} preserve={false} requiredMark={false}>
                    <Form.Item
                        label="用例名称"
                        name="name"
                        rules={[
                            {
                                required: true,
                                message: '请输入用例名称'
                            }
                        ]}
                    >
                        <Input placeholder="请输入用例名称" autocomplete="off" />
                    </Form.Item>
                    <Form.Item
                        name="osType"
                        label="端类型"
                        rules={[
                            {
                                required: true,
                                message: '请选择端类型'
                            }
                        ]}
                        initialValue={(creationConfig?.caseConfig?.osType || '3').toString()}
                    >
                        <Radio.Group>
                            <Radio value="1">Android 端</Radio>
                            <Radio value="2">iOS 端</Radio>
                            <Radio value="3">Android 与 iOS 双端</Radio>
                            <Radio value="4">服务端</Radio>
                            <Radio value="5">Web 端</Radio>
                            <Radio value="6">HarmonyOS 端</Radio>
                            <Radio value="7">自定义</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) =>
                            prevValues.osType !== currentValues.osType
                        }
                    >
                        {({ getFieldValue }) => {
                            return getFieldValue('osType') === '7' ? (
                                <Form.Item
                                    name="osTypes"
                                    label="自定义端类型"
                                    rules={[
                                        {
                                            validator: (_, value) =>
                                                value && value.length >= 2
                                                    ? Promise.resolve()
                                                    : Promise.reject(
                                                          new Error('请至少选择两个端类型')
                                                      )
                                        }
                                    ]}
                                >
                                    <Checkbox.Group>
                                        <Checkbox value={1}>Android 端</Checkbox>
                                        <Checkbox value={2}>iOS 端</Checkbox>
                                        <Checkbox value={4}>服务端</Checkbox>
                                        <Checkbox value={5}>Web 端</Checkbox>
                                        <Checkbox value={6}>HarmonyOS 端</Checkbox>
                                    </Checkbox.Group>
                                </Form.Item>
                            ) : null;
                        }}
                    </Form.Item>
                    <Form.Item
                        label="用例模版"
                        name="templateIdList"
                        initialValue={creationConfig?.caseConfig?.caseTemplateList ?? []}
                    >
                        <Select
                            mode="multiple"
                            placeholder="可添加用例模版"
                            filterOption={(input, option) =>
                                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                            }
                            options={caseTemplateList.map((item) => ({
                                label: item.templateName,
                                value: item.id
                            }))}
                            optionRender={(option) => (
                                <Space className={styles.templateOption}>
                                    <span>{option.data.label}</span>
                                    <Tooltip title="点击查看详情">
                                        <EyeOutlined
                                            className={styles.showIcon}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                settingModalRef?.current?.show({
                                                    key: 'mind-template',
                                                    templateId: option.data.value
                                                });
                                            }}
                                        />
                                    </Tooltip>
                                </Space>
                            )}
                        />
                    </Form.Item>
                    <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) =>
                            prevValues.osType !== currentValues.osType
                        }
                    >
                        {({ getFieldValue }) => {
                            return +getFieldValue('osType') === 3 ||
                                +getFieldValue('osType') === 7 ? (
                                <Form.Item
                                    name="signType"
                                    label="是否共用签章"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请设置是否共用签章'
                                        }
                                    ]}
                                    initialValue={getSignTypeInitialValueWith3(creationConfig)}
                                >
                                    <Radio.Group>
                                        <Radio value={0}>不共用</Radio>
                                        <Radio value={1}>共用</Radio>
                                    </Radio.Group>
                                </Form.Item>
                            ) : null;
                        }}
                    </Form.Item>
                    <Form.Item
                        name="signStage"
                        label="签章阶段"
                        rules={[
                            {
                                required: true,
                                message: '请选择签章阶段'
                            }
                        ]}
                        initialValue={creationConfig?.signConfig?.signStageList || [1]}
                    >
                        <Checkbox.Group>
                            <Checkbox value={0}>准入</Checkbox>
                            <Checkbox value={1}>准出</Checkbox>
                        </Checkbox.Group>
                    </Form.Item>
                </Form>
            </Modal>
            <SettingModal ref={settingModalRef} />
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    showModal: state.common.base.showModal,
    currentModule: state.common.base.currentModule,
    currentSpace: state.common.base.currentSpace,
    creationConfig: state.common.base.creationConfig,
    currentGroup: state.common.case.currentGroup,
    caseTemplateList: state.common.case.caseTemplateList
}))(forwardRef(AddCaseModal));
