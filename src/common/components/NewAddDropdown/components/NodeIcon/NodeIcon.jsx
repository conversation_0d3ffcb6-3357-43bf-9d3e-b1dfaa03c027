import classnames from 'classnames';
import {
    FolderOutlined,
    PartitionOutlined,
    FileOutlined
} from '@ant-design/icons';
import styles from './NodeIcon.module.less';

function NodeIcon({type, className}) {
    return (
        <>
            {type === 1 && <FolderOutlined
                className={classnames(styles.caseIcon, className)}
            />}
            {type === 2 && <PartitionOutlined
                className={classnames(styles.caseIcon, className)}
            />}
            {type === 3 && <FileOutlined
                className={classnames(styles.caseIcon, className)}
            />}
        </>
    );
}

export default NodeIcon;
