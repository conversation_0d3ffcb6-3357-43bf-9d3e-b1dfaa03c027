import { forwardRef, useState, useEffect, useCallback, useImperativeHandle } from 'react';
import { Modal, Form, Input, Button, Space, message } from 'antd';
import { useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { createTreeNode } from 'COMMON/api/front_qe_tools/tree';
import styles from './Common.module.less';

function AddFileModal(props, ref) {
    const { currentSpace, currentGroup, currentModule, setShowModal, parentId, preSibId } = props;
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();

    const showModal = useCallback(() => {
        setShowModal(true);
        setOpen(true);
    }, []);

    const hideModal = useCallback(() => {
        form.resetFields();
        setShowModal(false);
        setOpen(false);
    }, []);

    const onReset = () => {
        form.resetFields();
        hideModal();
    };

    const onClick = () => {
        form?.validateFields().then(async (values) => {
            let body = {
                groupId: currentGroup?.groupId,
                nodeName: values.name,
                nodeType: 1
            };
            if (parentId) {
                body.parentId = parentId;
            }
            if (preSibId) {
                body.preSibId = preSibId;
            }
            createTreeNode(body).then((res) => {
                messageApi.success('创建成功');
                let curUrl = '/' + currentModule + '/index';
                EventBus.emit('refreshTreeNodeList', currentGroup?.groupId);
                navigate(
                    stringifyUrl({
                        url: curUrl,
                        query: {
                            treeNodeId: res.nodeId,
                            groupId: currentGroup?.groupId,
                            moduleId: currentSpace?.id
                        }
                    })
                );
                hideModal();
            });
        });
    };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    return (
        <>
            {contextHolder}
            <Modal
                title="创建目录"
                open={open}
                autoComplete="off"
                transitionName=""
                destroyOnClose
                onCancel={hideModal}
                footer={
                    <div>
                        <Space>
                            <Button onClick={onReset}>取消</Button>
                            <Button type="primary" onClick={onClick}>
                                确定
                            </Button>
                        </Space>
                    </div>
                }
                mask="true"
                maskClosable="false"
            >
                <br />
                <Form form={form} colon={false} requiredMark={false}>
                    <Form.Item
                        label="名称"
                        name="name"
                        rules={[
                            {
                                required: true,
                                message: '请输入目录名称'
                            },
                            {
                                max: 120,
                                message: '目录名称不能超过120个字符'
                            }
                        ]}
                    >
                        <Input placeholder="请输入目录名称" onPressEnter={onClick} />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    showModal: state.common.base.showModal,
    currentModule: state.common.base.currentModule,
    currentSpace: state.common.base.currentSpace,
    currentGroup: state.common.case.currentGroup
}))(forwardRef(AddFileModal));
