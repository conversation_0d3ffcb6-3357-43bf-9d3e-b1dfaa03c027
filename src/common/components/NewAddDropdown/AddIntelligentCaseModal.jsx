import { forwardRef, useState, useRef, useCallback, useImperativeHandle } from 'react';
import { Modal, Form, Input, Button, Space, Radio, message } from 'antd';
import { useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import EventBus from 'COMMON/utils/eventBus';
// redux
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import demandModel from 'COMMON/models/demandModel';
// api
import { createTreeNode } from 'COMMON/api/front_qe_tools/tree';
import { createTreeNodeDoc } from 'COMMON/api/front_qe_tools/demand';
// components
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import DemandBind from 'FEATURES/components/demand/DemandBind';
import styles from './Common.module.less';

function AddIntelligentCaseModal(props, ref) {
    const {
        currentSpace,
        getCaseTemplateList,
        parentId,
        preSibId,
        currentModule,
        setShowModal,
        currentGroup,
        creationConfig
    } = props;
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const [addCaseForm] = Form.useForm();
    const [demandForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    const settingModalRef = useRef();
    const demandBindRef = useRef();

    const showModal = useCallback(() => {
        getCaseTemplateList({ moduleId: currentSpace?.id });
        setShowModal(true);
        setOpen(true);
    }, []);

    const hideModal = useCallback(() => {
        addCaseForm.resetFields();
        demandForm.resetFields();
        setShowModal(false);
        setOpen(false);
    }, []);

    const onReset = () => {
        addCaseForm.resetFields();
        demandForm.resetFields();
        hideModal();
    };

    const onClick = async () => {
        setLoading(true);
        try {
            // 验证用例创建表单
            const caseValues = await addCaseForm.validateFields();
            // 第一步：创建用例
            let body = {
                groupId: currentGroup?.groupId,
                nodeName: caseValues.name,
                nodeType: 2,
                osType: +caseValues.osType,
                signType: 0,
                signStage: [1, 2]
            };

            if (parentId) {
                body.parentId = parentId;
            }
            if (preSibId) {
                body.preSibId = preSibId;
            }
            // 创建树节点
            const res = await createTreeNode(body);
            // 第二步：添加需求
            // 需求绑定所需表单值
            let demandFormValues = await demandBindRef?.current?.handleSubmit();
            let params = {
                treeNodeId: res.nodeId,
                generateTaskKey: demandFormValues?.generateTaskKey,
                docNodeTreeList: demandFormValues?.docNodeTreeList
            };
            // 创建文档关联
            await createTreeNodeDoc(params);
            messageApi.success('创建成功');
            EventBus.emit('refreshTreeNodeList');
            navigate(
                stringifyUrl({
                    url: '/' + currentModule + '/detail',
                    query: {
                        treeNodeId: res.nodeId,
                        groupId: currentGroup?.groupId,
                        moduleId: currentSpace?.id
                    }
                })
            );

            // 关闭弹窗
            hideModal();
        } catch (err) {
            console.log(err?.message ?? err);
            setLoading(false);
            messageApi.warning('请填写完整表单');
        } finally {
            setLoading(false);
        }
    };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    return (
        <>
            {contextHolder}
            <Modal
                title={currentGroup?.groupType === 6 ? '创建智能测试' : '创建指令测试'}
                autoComplete="off"
                open={open}
                destroyOnClose
                transitionName=""
                onCancel={hideModal}
                footer={
                    <div>
                        <Space>
                            <Button onClick={onReset}>取消</Button>
                            <Button type="primary" loading={loading} onClick={onClick}>
                                确定
                            </Button>
                        </Space>
                    </div>
                }
                mask="true"
                maskClosable="false"
                width={window.innerWidth * 0.6}
            >
                <br />
                <Form form={addCaseForm} colon={false} preserve={false} requiredMark={false}>
                    <Form.Item
                        label="测试名称"
                        name="name"
                        rules={[
                            {
                                required: true,
                                message: '请输入测试名称'
                            }
                        ]}
                        labelCol={{
                            span: 3
                        }}
                    >
                        <Input placeholder="请输入测试名称" autocomplete="off" />
                    </Form.Item>
                    <Form.Item
                        name="osType"
                        label={'端类型'}
                        rules={[
                            {
                                required: true,
                                message: '请选择端类型'
                            }
                        ]}
                        labelCol={{
                            span: 3
                        }}
                        initialValue={(creationConfig?.caseConfig?.osType || '3').toString()}
                    >
                        <Radio.Group>
                            <Radio value="1">Android 端</Radio>
                            <Radio value="2">iOS 端</Radio>
                            <Radio value="3">Android 与 iOS 双端</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <DemandBind ref={demandBindRef} labelCol={{ span: 3 }} showLevel={false} />
                </Form>
            </Modal>
            <SettingModal ref={settingModalRef} />
        </>
    );
}

export default connectModel([baseModel, commonModel, demandModel], (state) => ({
    showModal: state.common.base.showModal,
    currentModule: state.common.base.currentModule,
    currentSpace: state.common.base.currentSpace,
    creationConfig: state.common.base.creationConfig,
    currentCase: state.common.case.currentCase,
    currentGroup: state.common.case.currentGroup,
    caseTemplateList: state.common.case.caseTemplateList,
    groupList: state.common.case.groupList,
    docList: state.demand.doc.docList
}))(forwardRef(AddIntelligentCaseModal));
