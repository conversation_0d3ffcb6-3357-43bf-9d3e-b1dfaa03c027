import { useEffect, useState } from 'react';
import { Checkbox, Divider, Select, Form, Input, message, Modal, Switch, Spin } from 'antd';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { createSnippet, getSnippetList } from 'COMMON/api/front_qe_tools/config';
import { batchCopyStepList } from 'COMMON/api/front_qe_tools/step';
import {
    createSystemStepInitConfig,
    handleCreateStep
} from 'COMMON/components/TreeComponents/Step/StepInit/utils';
import styles from './BatchStepSwitch.module.less';

function BatchStepSwitch(props) {
    const {
        showBatchStepSwitch,
        className,
        nodeId,
        curOsType,
        currentSpace,
        stepList,
        checkStepList,
        setCheckStepList,
        snippetList,
        setSnippetList,
        handleDeleteStep,
        handleUpdateStepList,
        onChange,
        treeData,
        setTreeData
    } = props;
    const [saveStepListToTemplateModalOpen, setSaveStepListToTemplateModalOpen] = useState(false); // 保存步骤为测试片段弹窗是否打开
    const [checkAll, setCheckAll] = useState(false); // 是否全选步骤
    const [keyList, setKeyList] = useState([]); // 测试片段下拉列表
    const [createLoading, setCreateLoading] = useState(false); // 创建测试片段加载状态
    const [messageApi, contextHolder] = message.useMessage();
    const [addForm] = Form.useForm();

    useEffect(() => {
        setCheckAll(checkStepList.length === stepList.length);
    }, [checkStepList, stepList]);

    // 全选 / 取消全选步骤
    const onCheckAllStepList = (checked) => {
        if (checked) {
            let stepIdList = [];
            stepList.forEach((item) => {
                stepIdList.push(parseInt(item.stepId, 10));
            });
            setCheckStepList(stepIdList);
        } else {
            setCheckStepList([]);
        }
    };

    // 批量删除步骤
    const onDeleteStepList = () => {
        if (isEmpty(checkStepList)) {
            messageApi.warning('请选择要删除的步骤');
            return;
        }
        Modal.confirm({
            title: '批量删除',
            okText: '删除',
            cancelText: '取消',
            content: (
                <>
                    确定要删除选中的&nbsp;
                    <span style={{ color: 'red' }}>{checkStepList.length}</span>
                    &nbsp;个步骤吗？
                </>
            ),
            onOk() {
                handleDeleteStep(checkStepList);
                setCheckStepList([]);
            }
        });
    };

    // 打开保存步骤为测试片段弹窗
    const onOpenSaveStepListToTemplateModal = () => {
        if (isEmpty(checkStepList)) {
            messageApi.warning('请选择要保存为测试片段的步骤');
            return;
        }
        for (let step of stepList) {
            if (
                checkStepList.includes(step?.stepId) &&
                [101, 102, 412, 420, 421]?.includes(step?.stepType)
            ) {
                messageApi.warning(
                    '所选步骤不能包含测试片段、屏幕操作、人工等特殊类型步骤，请重新选择'
                );
                return;
            }
        }
        let tabNames = [...new Set((snippetList?.[curOsType] ?? [])?.map((item) => item.tabName))];
        let newTabNames = ['默认'];
        tabNames.forEach((element) => {
            if (element !== '默认') {
                newTabNames.push(element);
            }
        });
        setKeyList(newTabNames?.map((name) => ({ key: name, label: name })));
        setSaveStepListToTemplateModalOpen(true);
    };

    // 保存步骤为测试片段
    const onSaveStepListToTemplate = () => {
        addForm
            .validateFields()
            .then(async (values) => {
                try {
                    // 创建模版
                    setCreateLoading(true);
                    let tabName = values?.tabName?.[0] ?? values?.tabName;
                    if (!tabName || tabName === null || tabName === '') {
                        tabName = '默认';
                    }
                    let createRes = await createSnippet({
                        moduleId: currentSpace?.id,
                        osType: curOsType,
                        tabName: tabName,
                        snippetName: values.snippetName
                    });
                    // 更新模版库 服务端仅返回snipptId，前端无法直接缓存需要 再次获取caseNodeId
                    let getSnippetListRes = await getSnippetList({
                        moduleId: currentSpace?.id,
                        osType: curOsType
                    });
                    setSnippetList({
                        ...snippetList,
                        [curOsType]: getSnippetListRes
                    });
                    // 新建模版步骤
                    let newSnippet = getSnippetListRes.find(
                        (item) => item.templateId === createRes?.snippetId
                    );
                    await batchCopyStepList({
                        stepIdList: checkStepList,
                        targetCaseNodeId: newSnippet?.caseNodeId,
                        targetOsType: curOsType
                    });
                    // 新建模版步骤，并引用参数
                    let { step, stepType, stepDesc } = createSystemStepInitConfig({
                        type: 'runTemplate',
                        curOsType: curOsType,
                        commonParams: props?.commonParams
                    });
                    let title = '执行【' + values?.snippetName + '】测试片段';
                    step.desc = title;
                    step.params.params.id = createRes?.snippetId;

                    await handleCreateStep({
                        caseNodeId: nodeId,
                        stepDesc: title,
                        step,
                        curOsType,
                        stepType,
                        treeData,
                        setTreeData,
                        stepList,
                        currentStep: stepList?.find(
                            (item) => item.stepId === checkStepList[checkStepList?.length - 1]
                        ),
                        handleUpdateStepList,
                        currentSpace
                    });
                    messageApi.success(
                        '新增测试片段成功，对应测试片段步骤同步创建。如有需要，可手动删除既有步骤'
                    );
                    // 关闭弹窗
                    setSaveStepListToTemplateModalOpen(false);
                    setCreateLoading(false);
                } catch (err) {
                    setCreateLoading(false);
                }
            })
            .catch((errorInfo) => {
                console.log('Failed:', errorInfo);
                messageApi.error('表单填写不完整');
            });
    };

    return (
        <>
            <div className={classnames(styles.stepListSwitch, className)}>
                {contextHolder}
                <span className={styles.operaTitle}>批量操作</span>
                <Switch
                    size="small"
                    checked={showBatchStepSwitch}
                    onChange={onChange}
                    tabIndex={-1}
                />
            </div>
            <div className={classnames(styles.stepListSwitch, className)}>
                {showBatchStepSwitch && (
                    <>
                        <span className={styles.operaCheckAllBtn}>
                            <Checkbox
                                checked={checkAll}
                                onChange={(e) => {
                                    setCheckAll(e.target.checked);
                                    onCheckAllStepList(e.target.checked);
                                }}
                            >
                                <span className={styles.operaCheckAllTitle}>全选</span>
                            </Checkbox>
                        </span>
                    </>
                )}
                {showBatchStepSwitch && checkStepList?.length > 0 && (
                    <>
                        <Divider type="vertical" />
                        <a
                            className={classnames(styles.operaBtn)}
                            onClick={onOpenSaveStepListToTemplateModal}
                        >
                            保存为测试片段
                        </a>
                    </>
                )}
                {showBatchStepSwitch && checkStepList?.length > 0 && (
                    <>
                        <Divider type="vertical" />
                        <a
                            className={classnames(styles.operaBtn, styles.operaBtnDel)}
                            onClick={onDeleteStepList}
                        >
                            删除
                        </a>
                    </>
                )}
            </div>
            <Modal
                title="保存为测试片段"
                okText="保存"
                open={saveStepListToTemplateModalOpen}
                cancelText="取消"
                onCancel={() => {
                    setSaveStepListToTemplateModalOpen(false);
                }}
                onOk={onSaveStepListToTemplate}
            >
                <Spin spinning={createLoading}>
                    <Form form={addForm} colon={false} requiredMark={false}>
                        <Form.Item
                            label="片段名称"
                            name="snippetName"
                            rules={[
                                {
                                    required: true,
                                    message: '请输入片段名称'
                                }
                            ]}
                        >
                            <Input placeholder="请输入片段名称" />
                        </Form.Item>
                        <Form.Item label="片段分类" name="tabName">
                            <Select
                                mode="tags"
                                placeholder="请选择"
                                options={keyList.map((item) => ({
                                    value: item.label,
                                    label: item.label
                                }))}
                                onChange={(value) => {
                                    if (value.length === 1) {
                                        addForm.setFieldValue('tabName', value);
                                        return;
                                    }
                                    let data = addForm?.getFieldValue('tabName')?.[0] ?? [];
                                    let selectData = value?.filter((item) => item !== data) ?? [];
                                    addForm.setFieldValue('tabName', selectData);
                                }}
                            />
                        </Form.Item>
                    </Form>
                </Spin>
            </Modal>
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    snippetList: state.common.case.snippetList,
    treeData: state.common.case.treeData
}))(BatchStepSwitch);
