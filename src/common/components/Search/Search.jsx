import classnames from 'classnames';
import {Input, Typography} from 'antd';
import {SearchOutlined} from '@ant-design/icons';
import styles from './Search.module.less';

function Search(props) {
    const {value, onSearch, searchLabel, onBlur,
        onChange, className, style, ...restProps} = props;

    return (
        <Input
            {...restProps}
            size='small'
            prefix={<SearchOutlined className={styles.searchIcon} />}
            placeholder={searchLabel ?? '关键字 + Enter'}
            className={classnames(styles.input, styles.search, 'inputEditor', className)}
            style={{...style}}
            defaultValue={value}
            onChange={(e) => {
                e.stopPropagation();
                onChange && onChange(e.target.value);
            }}
            onPressEnter={(e) => {
                e.stopPropagation();
                onSearch && onSearch(e.target.value);
            }}
            onBlur={(e) => {
                e.stopPropagation();
                onBlur && onBlur(e.target.value);
            }}
        />
    );
}

export default Search;