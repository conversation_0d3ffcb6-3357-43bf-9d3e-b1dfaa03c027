/**
 * @file mock.jsx mock操作
 * @create 李爽@2023.03.13
 */
import { Tag, Col, Input } from 'antd';

export default ({ query, type, styles, matchStatus }) => {
    let queryJsx = [];
    for (let index in query) {
        queryJsx.push(
            <Col
                span='24'
                style={{
                    marginTop: 3
                }}
            >
                <Input
                    style={{ width: '50%' }}
                    value={query[index].key}
                />
                <Input
                    style={{ width: '50%' }}
                    value={query[index].value}
                />
            </Col>
        );
    }
    let tagJsx = [];
    if (type === 'requestVerify') {
        tagJsx.push(
            matchStatus.query ?
                <Tag color='success'>通过</Tag> : <Tag color='error'>不通过</Tag>
        );
    }
    let jsx = [];
    jsx.push(
        <span className={styles.step_divider}>
            Query 校验&nbsp;{tagJsx}
        </span>
    );
    jsx.push(queryJsx);
    return jsx;
};