import {Col, Input, Tag} from 'antd';
import {formatRequest} from 'COMMON/components/TreeComponents/Step/utils';
const {TextArea} = Input;

export default ({request, styles, type, matchStatus}) => {
    let body = {
        type: 'text',
        data: ''
    };
    if (-1 !== Object.keys(request).indexOf('body') && '' !== request.body) {
        if (-1 !== ['application/text-plain', 'application/json', 'text/plain;charset=UTF-8'].indexOf(
            request.headers['Content-Type'])
        ) {
            body = {
                type: 'text',
                data: request.body
            };
        } else if (-1 !== ['application/x-www-form-urlencoded'].indexOf(
            request.headers['Content-Type'])
        ) {
            let bodyQuery = [];
            let _body = JSON.parse(request.body);
            for (let item in _body) {
                bodyQuery.push({
                    key: item,
                    value: _body[item],
                    isChecked: false
                });
            }
            body = {
                type: 'json',
                data: bodyQuery
            };
        }
    }
    if (-1 !== Object.keys(request).indexOf('body') && '' === request.body &&
        -1 !== ['application/x-www-form-urlencoded'].indexOf(
            request.headers[
            'Content-Type'
            ])) {
        body = {
            type: 'json',
            data: []
        };
    }
    let jsx = [];
    let bodyJsx = [];
    if (body.type === 'json') {
        for (let index in body.data) {
            bodyJsx.push(
                <Col
                    span='24'
                    style={{
                        marginTop: 3
                    }}
                >
                    <Input
                        style={{width: '50%'}}
                        value={body.data[index].key}
                    />
                    <Input
                        style={{width: '50%'}}
                        value={body.data[index].value}
                    />
                </Col>
            );
        }
    }
    let tagJsx = [];
    if (type === 'requestVerify') {
        tagJsx.push(
            matchStatus.body ?
                <Tag color='success'>通过</Tag> : <Tag color='error'>不通过</Tag>
        );
    }
    if ('json' !== body.type) {
        jsx.push(
            <span
                className={styles.step_divider}
            >
                Body 校验&nbsp;{tagJsx}
            </span>
        );
        jsx.push(
            <Col span='24'>
                <TextArea
                    style={{width: '100%'}}
                    value={formatRequest(body.data)}
                    rows={'' === body.data ? 2 : 5}
                />
            </Col>
        );
    } else {
        jsx.push(
            <span className={styles.step_divider}>
                Body 校验&nbsp;{tagJsx}
            </span>
        );
        jsx.push(bodyJsx);
    }
    return jsx;
};