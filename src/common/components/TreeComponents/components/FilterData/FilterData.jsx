import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import {Select} from 'antd';
import styles from './FilterData.module.less';

function FilterData(props) {
    const {
        filterItemWidth = '50%',
        filterPadding = '9px 15px 8px 15px',
        values = [],
        selectMode = 'multiple',
        filterClearStyle = {},
        filterSelectStyle = {},
        border = true,
        showTitle = true, filterStyle = {}, tagList,
    } = props;
    const onSelectChange = (value, setValue) => {
        setValue(value);
    };
    // 筛选项
    const typeOptions = [
        {
            key: 'type1',
            value: 2,
            label: '自动化'
        },
        {
            key: 'type2',
            value: 1,
            label: '手动'
        },
        {
            key: 'type4',
            value: 4,
            label: '半自动化'
        }
    ];
    const priorityOptions = [
        {
            key: 'level0',
            value: 0,
            label: 'P0'
        },
        {
            key: 'level1',
            value: 1,
            label: 'P1'
        },
        {
            key: 'level2',
            value: 2,
            label: 'P2'
        }
    ];
    const tagOptions = tagList.map((item, index) => ({
        key: 'tag' + index,
        value: item.id,
        label: item.tagName
    }));
    const tagTextOptions = tagList.map((item, index) => ({
        key: 'tag' + index,
        value: item.tagName,
        label: item.tagName
    }));
    const executeOptions = [
        {
            key: 'execute1',
            value: 2,
            label: '成功'
        },
        {
            key: 'execute2',
            value: 6,
            label: '失败'
        }
    ];
    const recordOptions = [
        {
            key: 'record1',
            value: 1,
            label: '已录制'
        },
        {
            key: 'record0',
            value: 0,
            label: '未录制'
        }
    ];
    const changeOptions = [
        {
            key: 'change1',
            value: 1,
            label: '用例已变更'
        },
        {
            key: 'change0',
            value: 0,
            label: '用例未变更'
        }
    ];
    const functionOptions = [
        {
            key: 'func1',
            value: 1,
            label: '异常函数'
        },
        {
            key: 'func0',
            value: 0,
            label: '非异常函数'
        }
    ];
    const admissionOptions = [
        {
            key: 'admission3',
            value: 2,
            label: '全部'
        },
        {
            key: 'admission1',
            value: 0,
            label: '非准入'
        },
        {
            key: 'admission2',
            value: 1,
            label: '准入'
        }
    ];
    const optionLists = {
        type: {
            options: typeOptions,
            name: '类型'
        },
        priority: {
            options: priorityOptions,
            name: '等级'
        },
        tag: {
            options: tagOptions,
            name: '标签'
        },
        tagText: {
            options: tagTextOptions,
            name: '标签'
        },
        execute: {
            options: executeOptions,
            name: '签章'
        },
        record: {
            options: recordOptions,
            name: '录制'
        },
        change: {
            options: changeOptions,
            name: '变更'
        },
        function: {
            options: functionOptions,
            name: '函数'
        },
        admission: {
            options: admissionOptions,
            name: '准入'
        }
    };
    const getSelectItem = (values) => {
        let jsx = [];
        for (let item in values) {
            jsx.push(
                <SelectOperator
                    mode={selectMode}
                    key={'filter_select_' + item}
                    options={optionLists[item].options}
                    onSelectChange={onSelectChange}
                    filterSelectStyle={filterSelectStyle}
                    itemWidth={filterItemWidth}
                    setValue={values[item].setValue}
                    name={optionLists[item].name}
                    showTitle={showTitle}
                    border={border}
                    value={values[item].value}
                />
            );
        }
        return jsx;
    };
    return (
        <div
            className={classnames(styles.caseOperator, styles.caseFilter,
                {[styles.showBorder]: border}
            )}
            style={{...filterStyle, padding: filterPadding}}
        >
            {getSelectItem(values)}
            <div
                className={styles.filterClear}
                style={{...filterClearStyle}}
            >
                <span
                    onClick={() => {
                        for (let item in values) {
                            if (item === 'admission') {
                                values[item].setValue(2);
                            } else {
                                values[item].setValue([]);
                            }
                        }
                    }}
                >
                    清空
                </span>
            </div>
        </div>
    );
}

function SelectOperator({
    name,
    setValue,
    mode,
    selectWidth,
    showTitle, itemWidth,
    className = null,
    filterSelectStyle,
    onSelectChange, ...restProps}) {
    let extraProps = {};
    if (name !== '准入') {
        extraProps = {
            mode: name === '标签' ? 'tags' : mode
        };
    }
    return (
        <div
            className={classnames(styles.filterItem, className)}
            style={{
                width: itemWidth,
                marginTop: !showTitle ? 0 : 5
            }}
        >
            {
                showTitle ?
                    <span className={styles.filterTitle}>{name}</span> : null
            }
            <Select
                size="small"
                className={styles.filterSelect}
                style={filterSelectStyle}
                placeholder={`选择${!showTitle ? name : ''}`}
                onChange={(value) => onSelectChange(value, setValue)}
                {...restProps}
                {...extraProps}
                maxTagCount={2}
                maxTagTextLength={5}
            />
        </div>
    );
};

export default connectModel([baseModel], state => ({
    tagList: state.common.base.tagList
}))(FilterData);