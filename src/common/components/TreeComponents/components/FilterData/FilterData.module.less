@import "RESOURCES/css/common.less";

.caseFilter {
    z-index: 99;
    position: relative;
    background-color: var(--background-color1);
    color: var(--color);
    overflow: hidden;

    :global {
        .ant-select-selector {
            height: 22px;
            line-height: 22px;
            overflow: auto;
        }

        .ant-select-multiple.ant-select-sm .ant-select-selection-overflow {
            overflow: scroll;
            flex-wrap: nowrap;
        }
    }
}

.filterItem,
.filterLongItem {
    float: left;
    padding-right: 5px;
}


.filterLongItem {
    margin-top: 5px;
}

.filterTitle {
    float: left;
    height: 22px;
    line-height: 22px;
    width: 35px;
}

.filterSelect {
    float: left;
    height: 22px;
    width: calc(100% - 45px);
}

.filterClear {
    float: left;
    width: 100%;
    font-size: 14px;
    margin-top: 2px;
    height: 16px;

    span {
        position: absolute;
        bottom: 5px;
        right: 20px;
        text-align: right;
        color: #777;
        cursor: pointer;
    }

    span:hover {
        color: #166cff;
    }
}

.showBorder {
    border-bottom: 1px solid var(--border-color);
}