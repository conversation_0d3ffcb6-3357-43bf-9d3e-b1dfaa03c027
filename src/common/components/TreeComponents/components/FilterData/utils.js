import {isEmpty} from 'lodash';

// 过滤筛选条件后的树
export function getFilterTreeData(data, factor) {
    let newData = data;
    if (data) {
        if (!isEmpty(factor?.priority?.value)) {
            newData = getFilterPriorityTree(newData, factor.priority.value);
        }
        if (!isEmpty(factor?.type?.value)) {
            newData = getFilterTypeTree(newData, factor.type.value);
        }
        if (!isEmpty(factor?.tag?.value)) {
            newData = getFilterTagTree(newData, factor.tag.value);
        }
        if (!isEmpty(factor?.execute?.value)) {
            newData = getFilterStatusTree(newData, factor.execute.value);
        }
        if (!isEmpty(factor?.record?.value)) {
            newData = getFilterRecordTree(newData, factor.record.value);
        }
        if (!isEmpty(factor?.change?.value)) {
            newData = getFilterChangeTree(newData, factor.change.value);
        }
        if (!isEmpty(factor?.function?.value)) {
            newData = getFilterFunctionTree(newData, factor.function.value);
        }
        return newData;
    } else {
        return data;
    }
};

// 过滤筛选优先级条件后的树
export function getFilterPriorityTree(data, priority, newData = []) {
    if (!data) {
        return newData;
    }
    data.forEach(item => {
        let isChild = false;
        if (!item.children && item.child) {
            isChild = true;
        }
        let ele = {...item};
        ele = {...ele, [!isChild ? 'children' : 'child']: []};
        newData.push(ele);
        getFilterPriorityTree(item[!isChild ? 'children' : 'child'],
            priority, newData[newData.length - 1][!isChild ? 'children' : 'child']);
        if (priority && !priority.includes(ele?.priority) &&
            newData[newData.length - 1][!isChild ? 'children' : 'child'].length === 0) {
            newData.pop();
        }
    });
    return newData;
};

// 过滤筛选类型条件后的树
export function getFilterTypeTree(data, type, newType = []) {
    let newData = [];
    data.forEach(item => {
        let isChild = false;
        if (!item.children && item.child) {
            isChild = true;
        }
        let ele = {...item, [!isChild ? 'children' : 'child']: []};
        newType.push(item.nodeType);
        if (!item[!isChild ? 'children' : 'child'] || item[!isChild ? 'children' : 'child'].length === 0) {
            if (type.includes(1) && newType.length > 0 && [0, 1].includes(newType.at(-1))) {
                newData.push(item);
            } else if (type.includes(2) && !newType.includes(1) &&
                !newType.includes(4) && newType.includes(2) && ![0, 1].includes(newType.at(-1))) {
                // 自动化： 全为自动化节点
                newData.push(item);
            } else if (type.includes(4) && !newType.includes(1) &&
                newType.includes(4) && ![0, 1].includes(newType.at(-1))) {
                // 半自动化： 没有手动节点 有半自动化节点
                newData.push(item);
            } else if (type.includes(1) && (newType.includes(1) ||
                newType.length === newType.filter(_item => _item === 0).length)) {
                // 手动: 有手动节点或者全为空节点
                newData.push(item);
            }
        } else {
            let _child = getFilterTypeTree(item[!isChild ? 'children' : 'child'],
                type, newType);
            if (_child.length !== 0) {
                ele[!isChild ? 'children' : 'child'] = _child;
                newData.push(ele);
            }
        }
        newType.pop();
    });
    return newData;
};

// 过滤筛选标签条件后的树
export function getFilterTagTree(data, tag, newData = []) {
    if (!data) {
        return newData;
    }
    data.forEach(item => {
        let isChild = false;
        if (!item.children && item.child) {
            isChild = true;
        }
        let ele = {...item};
        ele = {...ele, [!isChild ? 'children' : 'child']: []};
        newData.push(ele);
        getFilterTagTree(item[!isChild ? 'children' : 'child'],
            tag, newData[newData.length - 1][!isChild ? 'children' : 'child']);
        let flag = false;
        for (let _item of tag) {
            if (ele?.tagList?.includes(_item)) {
                flag = true;
                return;
            }
        }
        if (newData && !flag && newData[newData.length - 1][!isChild ? 'children' : 'child']?.length === 0) {
            newData.pop();
        }
    });
    return newData;
};

// 过滤筛选执行结果条件后的树
export function getFilterStatusTree(data, status, newData = []) {
    if (!data) {
        return newData;
    }
    data.forEach(item => {
        let isChild = false;
        if (!item.children && item.child) {
            isChild = true;
        }
        let ele = {...item};
        ele = {...ele, [!isChild ? 'children' : 'child']: []};
        newData.push(ele);
        getFilterStatusTree(item[!isChild ? 'children' : 'child'],
            status, newData[newData.length - 1][!isChild ? 'children' : 'child']);
        let flag = false;
        for (let _statusItem of status) {
            if (item?.info.taskId &&
                (((!item?.info?.manualStatus || item?.info?.manualStatus === 0) && item?.info?.status === _statusItem) ||
                    (_statusItem === 6 && [2].includes(item?.info?.manualStatus)) ||
                    (_statusItem === 2 && [1].includes(item?.info?.manualStatus))
                    ||
                    ((!item?.info?.manualStatus || item?.info?.manualStatus === 0) &&
                        [3, 6].includes(item?.info?.status) && [3, 6].includes(_statusItem))
                )
            ) {
                flag = true;
                break;
            }
        }
        if (!flag && newData[newData.length - 1][!isChild ? 'children' : 'child']?.length === 0) {
            newData.pop();
        }
    });
    return newData;
};

// 过滤筛选精准是否录制条件后的树
export function getFilterRecordTree(data, record, newData = []) {
    if (!data) {
        return newData;
    }
    data.forEach(item => {
        let isChild = false;
        if (!item.children && item.child) {
            isChild = true;
        }
        let ele = {...item};
        ele = {...ele, [!isChild ? 'children' : 'child']: []};
        newData.push(ele);
        getFilterRecordTree(item[!isChild ? 'children' : 'child'],
            record, newData[newData.length - 1][!isChild ? 'children' : 'child']);
        if (!record.includes(ele?.isRecord) &&
            newData[newData.length - 1][!isChild ? 'children' : 'child']?.length === 0) {
            newData.pop();
        }
    });
    return newData;
};

// 过滤筛选精准是否录制条件后的树
export function getFilterChangeTree(data, change, newData = []) {
    if (!data) {
        return newData;
    }
    data.forEach(item => {
        let isChild = false;
        if (!item.children && item.child) {
            isChild = true;
        }
        let ele = {...item};
        ele = {...ele, [!isChild ? 'children' : 'child']: []};
        newData.push(ele);
        getFilterChangeTree(item[!isChild ? 'children' : 'child'],
            change, newData[newData.length - 1][!isChild ? 'children' : 'child']);
        if (!change.includes(ele?.isChanged) &&
            newData[newData.length - 1][!isChild ? 'children' : 'child']?.length === 0) {
            newData.pop();
        }
    });
    return newData;
};

// 过滤筛选精准是否函数异常条件后的树
export function getFilterFunctionTree(data, func, newData = []) {
    if (!data) {
        return newData;
    }
    data.forEach(item => {
        let isChild = false;
        if (!item.children && item.child) {
            isChild = true;
        }
        let ele = {...item};
        ele = {...ele, [!isChild ? 'children' : 'child']: []};
        newData.push(ele);
        getFilterFunctionTree(item[!isChild ? 'children' : 'child'],
            func, newData[newData.length - 1][!isChild ? 'children' : 'child']);
        if (!func.includes(ele?.isFunctionCountAbnormal)
            && newData[newData.length - 1][!isChild ? 'children' : 'child']?.length === 0) {
            newData.pop();
        }
    });
    return newData;
};

export function getFilterIntelligentTree(data, newData = []) {
    if (!data) {
        return newData;
    }
    data.forEach(item => {
        if (item?.adoptStatus === null || ![0, 2].includes(item?.adoptStatus)) {
            let ele = {...item};
            ele = {...ele, children: [], child: []};
            newData.push(ele);
            getFilterIntelligentTree(item.children, newData[newData.length - 1]?.children);
        }
    });
    return newData;
};

const levelStatus = (item, factor) => {
    if (isEmpty(factor.priority)) {
        return true;
    }
    if (!isEmpty(factor.priority) && factor.priority.includes(item.priority)) {
        return true;
    }
    return false;
};

const typeStatus = (item, factor) => {
    if (isEmpty(factor.type)) {
        return true;
    }
    if (0 === item.nodeType ||
        factor.type.includes(item.nodeType)) {
        return true;
    }
    return false;
};


const tagStatus = (item, factor) => {
    if (isEmpty(factor.tag)) {
        return true;
    }
    for (let _tagItem of factor.tag) {
        if (item?.tagList?.includes(_tagItem)) {
            return true;
        }
    }
    return false;
};

const statusStatus = (item, factor) => {
    if (!factor.status || isEmpty(factor.status)) {
        return true;
    }
    for (let _statusItem of factor.status) {
        if (!item?.info.taskId || item?.info?.status === _statusItem ||
            ([3, 6].includes(item?.info?.status) && [3, 6].includes(_statusItem))) {
            return true;
        }
    }
    return false;
};

//  单个元素过滤筛选条件后的树
export function hasFilterTreeData(data, factor) {
    return levelStatus(data, factor) &&
        typeStatus(data, factor) &&
        tagStatus(data, factor);
};