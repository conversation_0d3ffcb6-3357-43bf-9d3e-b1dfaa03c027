import { Modal, Form, Button, Input, message } from 'antd';
import { useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { createScheme } from 'COMMON/api/front_qe_tools/config';
import styles from './SchemeModal.module.less';

function SchemeModal(props, ref) {
    const { onChange, currentSpace, schemeList, setSchemeList, curOsType } = props;
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();

    const showModal = useCallback(() => {
        setOpen(true);
    }, []);

    const hideModal = useCallback(() => {
        form.resetFields();
        setOpen(false);
    }, []);

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    const handleSubmit = () => {
        form.validateFields()
            .then(async (values) => {
                if (
                    schemeList?.[curOsType]?.find((item) => item.schemeName === values.schemeName)
                ) {
                    message.warning('该 Scheme 描述已存在');
                    return;
                }
                if (
                    schemeList?.[curOsType]?.find(
                        (item) => item.schemeContent === values.schemeContent
                    )
                ) {
                    message.warning('该 Scheme 内容已存在');
                    return;
                }
                if (!values?.tabName || values?.tabName === null || values?.tabName === '') {
                    values.tabName = '默认';
                }
                let res = await createScheme({
                    moduleId: currentSpace?.id,
                    osType: curOsType,
                    tabName: values?.tabName,
                    schemeName: values.schemeName,
                    schemeContent: values.schemeContent
                });
                message.success('新增成功');
                setSchemeList({
                    ...schemeList,
                    [curOsType]: [
                        ...schemeList?.[curOsType],
                        {
                            schemeId: res?.schemeId,
                            tabName: values?.tabName,
                            schemeName: values.schemeName,
                            schemeContent: values.schemeContent
                        }
                    ]
                });
                onChange && onChange(res.schemeId, values.schemeName, values.schemeContent);
                setOpen(false);
            })
            .catch((errorInfo) => {
                console.log('Failed:', errorInfo);
                messageApi.error('表单填写不完整');
            });
    };
    return (
        <Modal
            title="新增 Scheme 模版"
            open={open}
            onCancel={hideModal}
            destroyOnClose
            footer={null}
        >
            {contextHolder}
            <br />
            <Form form={form} colon={false} requiredMark={false}>
                <Form.Item
                    label="描述"
                    name="schemeName"
                    rules={[
                        {
                            required: true,
                            message: '请输入 Scheme 描述'
                        }
                    ]}
                >
                    <Input placeholder="请输入 Scheme 描述" />
                </Form.Item>
                <Form.Item
                    label="链接"
                    name="schemeContent"
                    rules={[
                        {
                            required: true,
                            message: '请输入 Scheme 链接'
                        }
                    ]}
                >
                    <Input placeholder="请输入 Scheme 链接" />
                </Form.Item>
                <Form.Item label="分类" name="tabName" initialValue="默认">
                    <Input placeholder="选填" />
                </Form.Item>
                <Form.Item>
                    <Button className={styles.submitBtn} onClick={handleSubmit} type="primary">
                        确认
                    </Button>
                </Form.Item>
            </Form>
        </Modal>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    recording: state.common.base.recording,
    currentDevice: state.common.base.currentDevice,
    currentSpace: state.common.base.currentSpace,
    schemeList: state.common.case.schemeList
}))(forwardRef(SchemeModal));
