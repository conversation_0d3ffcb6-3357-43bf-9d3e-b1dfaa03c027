import { Popover, Form, Button, Input, message } from 'antd';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { createScheme, getSchemeList } from 'COMMON/api/front_qe_tools/config';
import styles from './SchemePopver.module.less';

function SchemePopver(props) {
    const { onChange, children, currentSpace, schemeList, setSchemeList, curOsType } = props;
    const [form] = Form.useForm();

    const handleSubmit = () => {
        form.validateFields()
            .then(async (values) => {
                try {
                    if (
                        schemeList?.[curOsType]?.find(
                            (item) => item.schemeName === values.schemeName
                        )
                    ) {
                        message.warning('该 Scheme 描述已存在');
                        return;
                    }
                    if (
                        schemeList?.[curOsType]?.find(
                            (item) => item.schemeContent === values.schemeContent
                        )
                    ) {
                        message.warning('该 Scheme 内容已存在');
                        return;
                    }
                    if (!values?.tabName || values?.tabName === null || values?.tabName === '') {
                        values.tabName = '默认';
                    }
                    let res = await createScheme({
                        moduleId: currentSpace?.id,
                        osType: curOsType,
                        tabName: values?.tabName,
                        schemeName: values.schemeName,
                        schemeContent: values.schemeContent
                    });
                    message.success('新增成功');
                    let list = await getSchemeList({
                        moduleId: currentSpace?.id,
                        osType: curOsType
                    });
                    setSchemeList({
                        ...schemeList,
                        [curOsType]: list
                    });
                    onChange && onChange();
                } catch (err) {
                    console.log(err?.message ?? err);
                }
            })
            .catch((errorInfo) => {
                console.log('Failed:', errorInfo);
                message.error('表单填写不完整');
            });
    };
    return (
        <Popover
            trigger="click"
            content={
                <div className={styles.addOpera}>
                    <Form form={form} colon={false} requiredMark={false}>
                        <Form.Item
                            label="描述"
                            name="schemeName"
                            rules={[
                                {
                                    required: true,
                                    message: '请输入 Scheme 描述'
                                }
                            ]}
                        >
                            <Input placeholder="请输入 Scheme 描述" />
                        </Form.Item>
                        <Form.Item
                            label="链接"
                            name="schemeContent"
                            rules={[
                                {
                                    required: true,
                                    message: '请输入 Scheme 链接'
                                }
                            ]}
                        >
                            <Input placeholder="请输入 Scheme 链接" />
                        </Form.Item>
                        <Form.Item label="分类" name="tabName" initialValue="默认">
                            <Input placeholder="选填" />
                        </Form.Item>
                        <Form.Item>
                            <Button
                                className={styles.submitBtn}
                                onClick={handleSubmit}
                                type="primary"
                            >
                                确认
                            </Button>
                        </Form.Item>
                    </Form>
                </div>
            }
        >
            {children}
        </Popover>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    recording: state.common.base.recording,
    currentDevice: state.common.base.currentDevice,
    currentSpace: state.common.base.currentSpace,
    schemeList: state.common.case.schemeList
}))(SchemePopver);
