import {Modal, Input, Tooltip, Empty} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
const {TextArea} = Input;

function ApiModal({title, testRes, extra, open, setOpen, setShowModal}) {
    return (
        <>
            <Tooltip title={title} placement='topRight'>
                {extra}
            </Tooltip>
            <Modal
                open={open}
                title="请求返回"
                centered
                width={window.innerWidth * 0.8}
                onCancel={() => {
                    setOpen(false);
                    setShowModal(false);
                }}
                footer={null}
            >
                {!testRes?.data?.extra ||
                    0 === Object.keys(testRes.data.extra).length ? <Empty
                    description='暂无返回数据'
                /> : <TextArea
                    value={JSON.stringify(testRes.data.extra.requestResponse, undefined, 2)}
                    rows='12'
                />}
            </Modal>
        </>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    currentDevice: state.common.base.currentDevice,
    showModal: state.common.base.showModal,
    mockList: state.common.case.mockList,
}))(ApiModal);