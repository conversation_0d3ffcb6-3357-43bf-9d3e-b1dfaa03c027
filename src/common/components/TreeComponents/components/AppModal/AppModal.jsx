import { useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { Modal, Form, Tooltip, Button, Input, message, Spin } from 'antd';
import { connectModel } from 'COMMON/middleware';
import electron from 'COMMON/utils/electron';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { updateApp, createApp, getAppList, getEnvDetail } from 'COMMON/api/front_qe_tools/config';
import styles from './AppModal.module.less';
import { PlusOutlined, MinusCircleOutlined, VideoCameraTwoTone } from '@ant-design/icons';

const formItemLayout = {
    labelCol: {
        span: 4
    },
    wrapperCol: {
        span: 20
    }
};
const formItemLayoutWithOutLabel = {
    wrapperCol: {
        offset: 4
    }
};

function AppModal(props, ref) {
    const {
        onChange,
        currentSpace,
        curOsType,
        currentDevice,
        currentEnv,
        setCurrentEnv,
        appList,
        setAppList
    } = props;
    const [loading, setLoading] = useState(false);
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();

    const showModal = () => {
        setOpen(true);
    };

    const hideModal = useCallback(() => {
        form.resetFields();
        setOpen(false);
    }, []);

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    const handleSubmit = () => {
        form.validateFields()
            .then((values) => {
                let _app = appList?.[curOsType]?.find((item) => item.appName === values.appName);
                if (_app) {
                    messageApi.warning('该 APP 包名已存在');
                    return;
                }
                if (_app) {
                    updateApp({
                        appId: _app?.appId,
                        appName: _app.appName,
                        packageList: [...values.packageList, ..._app?.packageList]
                    })
                        .then(() => {
                            messageApi.success('新增成功');
                            let newAppList = appList?.[curOsType]?.map((item) => {
                                return {
                                    appId: item?.appId,
                                    appName: item.appName,
                                    packageList:
                                        item?.appId === _app?.appId
                                            ? item?.packageList
                                            : [...item?.packageList, ...values.packageList]
                                };
                            });
                            setAppList({
                                ...appList,
                                [curOsType]: newAppList
                            });
                            // 默认更新当前选择的环境
                            if (currentEnv?.envId) {
                                getEnvDetail({ envId: currentEnv?.envId })
                                    .then((res) => {
                                        setCurrentEnv({
                                            envId: currentEnv?.envId,
                                            ...res.envDetail
                                        });
                                    })
                                    .catch();
                            }
                            onChange &&
                                onChange(_app?.appId, values.appName, values.packageList[0]);
                            form.resetFields();
                            setOpen(false);
                        })
                        .catch((err) => {
                            console.log(err?.message ?? err);
                        });
                } else {
                    createApp({
                        moduleId: currentSpace?.id,
                        osType: curOsType,
                        appName: values.appName,
                        packageList: values.packageList
                    })
                        .then((res) => {
                            messageApi.success('新增成功');
                            setAppList({
                                ...appList,
                                [curOsType]: [
                                    ...appList?.[curOsType],
                                    {
                                        appId: res?.appId,
                                        appName: values.appName,
                                        packageList: values.packageList
                                    }
                                ]
                            });
                            // 默认更新当前选择的环境
                            if (currentEnv?.envId) {
                                getEnvDetail({ envId: currentEnv?.envId })
                                    .then((res) => {
                                        setCurrentEnv({
                                            envId: currentEnv?.envId,
                                            ...res.envDetail
                                        });
                                    })
                                    .catch();
                            }
                            onChange && onChange(res?.appId, values.appName, values.packageList[0]);
                            form.resetFields();
                            setOpen(false);
                        })
                        .catch((err) => {
                            console.log(err?.message ?? err);
                        });
                }
            })
            .catch((errorInfo) => {
                console.log('Failed:', errorInfo);
                messageApi.error('表单填写不完整');
            });
    };

    // 获取当前设备所在 APP 包名
    const getDeviceCurrent = (index) => {
        setLoading(true);
        electron
            .send('device.current', { deviceType: curOsType, deviceId: currentDevice?.deviceId })
            .then((res) => {
                let newPackage = form.getFieldValue('packageList');
                newPackage[index] = res?.packageName ?? '';
                form.setFieldValue('packageList', newPackage);
                setLoading(false);
            })
            .catch((err) => {
                setLoading(false);
            });
    };
    return (
        <Modal title="新增 APP 模版" open={open} onCancel={hideModal} destroyOnClose footer={null}>
            {contextHolder}
            <Spin spinning={loading}>
                <div className={styles.addOpera}>
                    <Form form={form} colon={false} requiredMark={false}>
                        <Form.Item
                            label="APP 名称"
                            name="appName"
                            {...formItemLayout}
                            rules={[
                                {
                                    required: true,
                                    message: '请输入 APP 名称'
                                }
                            ]}
                        >
                            <Input placeholder="请输入 APP 名称" />
                        </Form.Item>
                        <Form.List name="packageList" initialValue={['']}>
                            {(fields, { add, remove }, { errors }) => (
                                <>
                                    {fields.map((field, index) => (
                                        <Form.Item
                                            key="package"
                                            {...(index === 0
                                                ? formItemLayout
                                                : formItemLayoutWithOutLabel)}
                                            label={index === 0 ? 'APP 包名' : ''}
                                        >
                                            <Form.Item
                                                {...field}
                                                validateTrigger={['onChange', 'onBlur']}
                                                rules={[
                                                    {
                                                        required: true,
                                                        whitespace: true,
                                                        message: '请输入 APP 包名'
                                                    }
                                                ]}
                                                noStyle
                                            >
                                                <Input
                                                    placeholder="请输入 APP 包名"
                                                    style={{
                                                        width: '100%'
                                                    }}
                                                    allowClear
                                                    suffix={
                                                        isElectron() && (
                                                            <Tooltip
                                                                placement="right"
                                                                title={'获取设备当前APP 包名'}
                                                            >
                                                                <VideoCameraTwoTone
                                                                    twoToneColor="#acb1b9"
                                                                    onClick={() =>
                                                                        getDeviceCurrent(index)
                                                                    }
                                                                />
                                                            </Tooltip>
                                                        )
                                                    }
                                                />
                                            </Form.Item>
                                            {fields.length > 1 ? (
                                                <MinusCircleOutlined
                                                    className={styles.dynamicDeleteBtn}
                                                    onClick={() => remove(field.name)}
                                                />
                                            ) : (
                                                <MinusCircleOutlined
                                                    className={styles.dynamicDeleteBtn}
                                                    style={{ color: '#ccc' }} // 灰色图标表示不可点击
                                                />
                                            )}
                                        </Form.Item>
                                    ))}
                                    <Form.Item {...formItemLayoutWithOutLabel}>
                                        <Button
                                            type="dashed"
                                            onClick={() => add()}
                                            block
                                            icon={<PlusOutlined />}
                                        >
                                            新增
                                        </Button>
                                    </Form.Item>
                                </>
                            )}
                        </Form.List>
                        <Form.Item>
                            <Button
                                className={styles.submitBtn}
                                onClick={handleSubmit}
                                type="primary"
                            >
                                确认
                            </Button>
                        </Form.Item>
                    </Form>
                </div>
            </Spin>
        </Modal>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentDevice: state.common.base.currentDevice,
    currentSpace: state.common.base.currentSpace,
    currentEnv: state.common.base.currentEnv,
    appList: state.common.case.appList
}))(forwardRef(AppModal));
