import {Modal, Row, Col, Tooltip, Layout, Tabs, Badge} from 'antd';
import {useState} from 'react';
import {isEmpty} from 'lodash';
import StepListWithCommon from 'COMMON/components/TreeComponents/StepDetail/StepList/StepListWithCommon';
import NoContent from 'COMMON/components/common/NoContent';
import ParamsSwitch from 'COMMON/components/ParamsSwitch/ParamsSwitch';
import StepInfo from 'COMMON/components/TreeComponents/Step/StepInfo';
import StepRes from 'COMMON/components/TreeComponents/Step/StepRes/StepRes';
import {connectModel} from 'COMMON/middleware';
import InterfaceStepRes from 'COMMON/components/TreeComponents/Step/InterfaceStepRes';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import styles from './TemplateExecuteInfoModal.module.less';

function TemplateExecuteInfoModal(props) {
    const {
        title,
        testRes,
        extra,
        open,
        curOsType,
        setOpen,
        setShowModal
    } = props;
    const [currentStep, setCurrentStep] = useState(
        testRes?.data?.extra?.loop?.[0]?.stepResult?.[0]
    );
    const [openVerfiyModal, setOpenVerfiyModal] = useState(false);
    const [openApiModal, setOpenApiModal] = useState(false);
    const {TabPane} = Tabs;
    const getStatusColor = (status) => {
        switch (status) {
            case 0:
                return 'gray'; // 未循环
            case 1:
                return 'green'; // 成功
            case 2:
            case 3:
                return 'red'; // 条件失败 或 循环体失败
            default:
                return 'gray';
        }
    };
    const getStyles = (step, currentStep) => {
        let bgc = '#fff';
        let bd = '1px solid #eee';
        let tc = '#000';
        let tagC = '#bbbbbc';
        if (3 === step?.stepInfo?.type) {
            switch (step?.stepInfo?.params?.type) {
                case 'assest':
                    tagC = '#ea722b';
                    break;
                default:
                    tagC = '#f9a52d';
                    break;
            }
        }
        let boxS = '';
        let isClick = currentStep?.stepId === step?.stepId;
        if (isClick) {
            bd = '1px solid #5191f0';
            boxS = '0 0 0 3px rgb(24 144 255 / 12%)';
        }
        switch (step?.result?.status) {
            case 2:
                // 执行中
                bgc = '#def0ff';
                bd = '1px solid #3686f6';
                tagC = '#3686f6';
                boxS = '0 0 0 3px rgb(54 134 246 / 12%)';
                break;
            case 0:
                bgc = '#f5ffea';
                bd = '1px solid #aee884';
                tagC = '#48bc19';
                boxS = '0 0 0 3px rgb(105 185 61 / 12%)';
                if (3 === step.stepInfo.type && 'assest' === step.stepInfo.params.type) {
                    bgc = '#fae1d0';
                    bd = '1px solid #ea722b';
                    tagC = '#ea722b';
                    boxS = '0 0 0 3px rgb(236 84 78 / 12%)';
                }
                break;
            case -1:
                bgc = '#fff0ee';
                bd = '1px solid #ffc5c0';
                tagC = '#ff4446';
                boxS = '0 0 0 3px rgb(236 84 78 / 12%)';
                break;
            default:
                break;
        }
        return {bgc, tc, bd, boxS, tagC};
    };

    const items = testRes?.data?.extra?.loop?.map((loop, index) => {
        let steps = [];
        if ([7, 8, 9, 10]?.includes(loop?.conditionResult?.stepInfo?.type)) {
            steps = [
                {
                    ...loop?.conditionResult,
                    // 兼容Id， 控件类型会判断id 是否存在
                    stepId: 'template-if-condition',
                    // 兼容步骤描述
                    stepDesc: loop?.conditionResult?.stepInfo?.desc
                },
                ...(loop?.stepResult ?? [])
            ];
        } else {
            steps = [...(loop?.stepResult ?? [])];
        }
        return {
            title: (
                <Badge
                    size="small"
                    color={getStatusColor(loop.status)}
                    text={`循环第${index + 1}次`}
                />
            ),
            key: index,
            content: (
                <>
                    <Row>
                        <Col span={6}>
                            <div
                                className={styles.stepList}
                                style={{height: window.innerHeight * 0.7, overflow: 'scroll', paddingLeft: 5}}
                            >
                                {
                                    curOsType !== 4 &&
                                    <ParamsSwitch />
                                }
                                <StepListWithCommon
                                    {...props}
                                    editType='execute'
                                    onChangeStep={(step) => {
                                        setCurrentStep(step);
                                    }}
                                    currenStep={currentStep}
                                    setCurrentStep={setCurrentStep}
                                    stepList={steps}
                                    moduleStepList={steps}
                                />
                            </div>
                        </Col>
                        <Col span={9}>
                            <div className={styles.stepDetail}>
                                <StepInfo
                                    editType={'execute'}
                                    currentStep={currentStep}
                                    curOsType={curOsType}
                                    stepList={
                                        !isEmpty(loop?.conditionResult?.stepInfo)
                                            ? [loop?.conditionResult, ...(loop?.stepResult ?? [])]
                                            : [...(loop?.stepResult ?? [])]
                                    }
                                />
                            </div>
                        </Col>
                        <Col span={9}>
                            {null !== currentStep && currentStep?.result?.data?.screenshot && (
                                <div className={styles.stepRes}>
                                    <StepRes
                                        editType="debug"
                                        curOsType={curOsType}
                                        currentStep={currentStep}
                                        testRes={currentStep?.result}
                                    />
                                </div>
                            )}
                            {null !== currentStep && currentStep?.result?.data?.extra?.screenshot && (
                                <div className={styles.stepRes}>
                                    <StepRes
                                        editType="debug"
                                        curOsType={curOsType}
                                        currentStep={currentStep}
                                        testRes={currentStep?.result}
                                    />
                                </div>
                            )}
                            {curOsType === 4 && (
                                <div className={styles.stepRes}>
                                    <InterfaceStepRes
                                        editType="debug"
                                        key={currentStep?.id}
                                        currentStepDetail={currentStep}
                                    />
                                </div>
                            )}
                        </Col>
                    </Row>
                </>
            )
        };
    });

    const onChange = (activeKey) => {
        console.log('Active tab changed to: ', activeKey);
    };
    return (
        <>
            <Tooltip title={title} placement="topRight">
                {extra}
            </Tooltip>
            <Modal
                open={open}
                title="测试片段执行详情"
                centered
                width={window.innerWidth * 0.85}
                onCancel={() => {
                    setOpen(false);
                    setShowModal(false);
                }}
                destroyOnClose
                footer={null}
            >
                <Layout className={styles.container} style={{height: window.innerHeight * 0.8, overflow: 'scroll'}}>
                    {isEmpty(testRes?.data?.extra?.loop[0]?.conditionResult?.stepInfo) &&
                        isEmpty(testRes?.data?.extra?.loop[0]?.stepResult) ? (
                        <NoContent text="暂无执行详情" />
                    ) : (
                        <Tabs defaultActiveKey="1" onChange={onChange} style={{width: '100%'}}>
                            {items.map((item) => (
                                <TabPane
                                    tab={item.title}
                                    key={item.key}
                                    style={{overflow: 'scroll'}}
                                >
                                    {item.content}
                                </TabPane>
                            ))}
                        </Tabs>
                    )}
                </Layout>
            </Modal>
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentDevice: state.common.base.currentDevice,
    showModal: state.common.base.showModal,
    mockList: state.common.case.mockList
}))(TemplateExecuteInfoModal);
