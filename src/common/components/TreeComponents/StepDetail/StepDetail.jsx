import { useEffect, useState, useRef } from 'react';
import { Dropdown, Layout, Tooltip, Button, Spin, message } from 'antd';
import { isEmpty } from 'lodash';
import { CodeSandboxOutlined } from '@ant-design/icons';
import classnames from 'classnames';
import { useLocation, useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import { getQueryParams } from 'COMMON/utils/utils';
import { connectModel } from 'COMMON/middleware';
import NoContent from 'COMMON/components/common/NoContent';
import Loading from 'COMMON/components/common/Loading';
import Splitter from 'COMMON/components/common/SplitterPlus';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import StepInfo from 'COMMON/components/TreeComponents/Step/StepInfo';
import StepInit from 'COMMON/components/TreeComponents/Step/StepInit';
import { getStepType } from 'COMMON/components/TreeComponents/Step/StepInit/utils';
import { getStepList, createStep, updateStep } from 'COMMON/api/front_qe_tools/step';
import { createPoint } from 'COMMON/api/front_qe_tools/points';
import { treeAction } from 'FEATURES/front_qe_tools/case/edit/EditPage/CaseDetailView/LayoutSider/utils';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import TestStep from '../Step/TestStep';
import DebugInfo from '../Step/DebugInfo';
import ServerCaseDebug from './ServerCaseDebug';
import styles from './StepDetail.module.less';
import StepList from './StepList';
const { Content } = Layout;

function StepDetail(props) {
    const {
        leftExtra,
        testStep,
        stepListWidth,
        showDebug = true,
        currentNode,
        caseNodeId,
        view = 'detail',
        treeData,
        setTreeData,
        handleCurrentNode = () => {},
        handleUpdateNode = () => {},
        editType = 'edit',
        setRecording,
        copyStep,
        currentModule,
        curOsType,
        currentSpace,
        serverTestRes,
        handleShowRelationCaseOpen,
        snippetId,
        renderStepList
    } = props;
    const [nodeId, setNodeId] = useState(null);
    const [currentStep, setCurrentStep] = useState(null);
    const [stepList, setStepList] = useState([]);
    const [loading, setLoading] = useState(true);
    const [hasProxy, setHasProxy] = useState(false);

    // 批量操作开关
    const templateManagerRef = useRef();
    const [messageApi, contextHolder] = message.useMessage();
    const [openTestDetail, setOpenTestDetail] = useState(false);
    const query = getQueryParams();
    const navigate = useNavigate();
    const location = useLocation();
    const latestStepId = useRef(null);

    // 渲染脑图步骤
    useEffect(() => {
        renderStepList && renderStepList(stepList);
    }, [stepList]);

    useEffect(() => {
        setNodeId(caseNodeId);
    }, [caseNodeId]);

    // 记录最新步骤id
    useEffect(() => {
        if (currentStep?.stepId) {
            latestStepId.current = currentStep?.stepId;
        }
    }, [currentStep?.stepId]);

    useEffect(() => {
        if (nodeId && -1 !== Number(nodeId)) {
            // 获取步骤信息
            refreshStepList(nodeId, curOsType);
        }
    }, [nodeId, curOsType, view]);

    const refreshStepList = (updateExecutionType = false) => {
        if (!curOsType || !nodeId) {
            return;
        }
        setLoading(true);
        getStepList({
            caseNodeId: +nodeId,
            osType: curOsType
        })
            .then((res) => {
                let stepList = res.stepList || [];
                let setupStepList = res.setupStepList || [];
                let teardownStepList = res.teardownStepList || [];
                // step转为纯对象，不包含字符串对象
                stepList.forEach((item) => {
                    if (item?.stepType === 1401) {
                        item.stepChildren?.forEach((element) => {
                            element.parentStepId = item.stepId;
                        });
                    }
                    if (
                        !hasProxy &&
                        -1 !==
                            ['mock', 'requestVerify', 'requestRedirect', 'networkConnect'].indexOf(
                                item?.stepInfo?.params?.type
                            )
                    ) {
                        setHasProxy(true);
                    }
                });
                const activeStep = 0 === stepList?.length ? null : stepList[0];
                setCurrentStep({ ...activeStep });
                // 添加类型
                setupStepList.forEach((item) => {
                    item.hookType = 'setup';
                    item.hookId = currentNode?.extra?.setupInfo[convertOsTypeToType(curOsType)];
                });
                teardownStepList.forEach((item) => {
                    item.hookType = 'teardown';
                    item.hookId = currentNode?.extra?.teardownInfo[convertOsTypeToType(curOsType)];
                });
                stepList.forEach((item) => {
                    if (item?.stepType === 1401) {
                        item.stepChildren?.forEach((element) => {
                            element.parentStepId = item.stepId;
                        });
                    }
                    item.hookType = 'step';
                });
                // 只有server才展示所有的
                if (curOsType === 4) {
                    setStepList([...setupStepList, ...stepList, ...teardownStepList]);
                } else {
                    setStepList([...stepList]);
                }
                if (updateExecutionType === true) {
                    setTreeData(
                        treeAction([...treeData], 'children', +caseNodeId, (node, item, index) => {
                            let type = getStepType(stepList);
                            if (!item?.extra) {
                                item.extra = {
                                    executionType: {
                                        [convertOsTypeToType(curOsType)]: type
                                    }
                                };
                            }
                            if (!item?.extra?.executionType) {
                                item.extra.executionType = {
                                    [convertOsTypeToType(curOsType)]: type
                                };
                            }
                            item.extra.executionType[convertOsTypeToType(curOsType)] = type;
                            item.executionType = type;
                        })
                    );
                }
            })
            .catch((err) => {
                console.log(err);
                setStepList([]);
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const handleUpdateStepList = (stepList, step) => {
        setCurrentStep(step);
        setStepList([...stepList]);
    };

    const updateStepInList = (updatedStep) => {
        setStepList((prevStepList) => {
            return prevStepList.map((stepItem) => {
                if (
                    stepItem.stepId === updatedStep.stepId &&
                    updatedStep.stepId === latestStepId.current
                ) {
                    // 顶层 step 替换
                    setCurrentStep({ ...updatedStep });
                    return updatedStep;
                }
                if (stepItem.stepType === 1401 && Array.isArray(stepItem.stepChildren)) {
                    const newStepChildren = stepItem.stepChildren.map((child) => {
                        if (
                            child.stepId === updatedStep.stepId &&
                            updatedStep.stepId === latestStepId.current
                        ) {
                            setCurrentStep({ ...updatedStep });
                            return updatedStep;
                        }
                        return child;
                    });

                    // 如果 children 有变才替换
                    const changed = newStepChildren.some(
                        (child, i) => child !== stepItem.stepChildren[i]
                    );
                    if (changed) {
                        return { ...stepItem, stepChildren: newStepChildren };
                    }
                }
                // 否则保留原引用
                return stepItem;
            });
        });
    };

    const handleUpdateStep = (step, attr) => {
        updateStepInList(step);
        let newActionInfo = JSON.parse(JSON.stringify(step.stepInfo));
        if (
            [9]?.includes(newActionInfo?.type) &&
            newActionInfo?.params?.deviceInfo?.screenshot &&
            !newActionInfo?.params?.deviceInfo?.screenshot?.startsWith('http')
        ) {
            newActionInfo.params.deviceInfo.screenshot = '';
        }
        if (
            [10]?.includes(newActionInfo?.type) &&
            newActionInfo?.params?.recordInfo?.deviceInfo?.screenshot &&
            !newActionInfo?.params?.recordInfo?.deviceInfo?.screenshot?.startsWith('http')
        ) {
            newActionInfo.params.recordInfo.deviceInfo.screenshot = '';
        }
        let body = {
            stepId: step.stepId,
            stepDesc: step.stepDesc
        };
        if (attr !== 'desc') {
            body.stepInfo = newActionInfo;
        }
        updateStep(body).catch((e) => {});
        createPoint({
            moduleId: currentSpace?.id, // 业务模块id；int；必填
            caseNodeId: caseNodeId, // 用例节点Id；int；必填
            stepId: currentStep?.stepId, // 步骤Id；int；必填
            osType: curOsType, // 端类型，int；必填 1-Android 2-iOS 3-Android&iOS 4-server 5-web
            pointType: 1100, // 打点类型；int；必填 1000-创建步骤 1001-选中步骤 1100-update
            pointInfo: {}, // 点位内容；json；选填（预留字段，当前传空 json 就行）
            createTime: Math.floor(new Date().getTime() / 1000) // 打点时间；int；必填
        }).catch(() => {});
    };

    let items = [];
    if (isEmpty(stepList)) {
        items = [
            {
                label: '粘贴',
                key: '2'
            }
        ];
    }

    const onClick = async ({ key }) => {
        switch (key) {
            case '2':
                if (null === copyStep) {
                    messageApi.warning('请先复制步骤');
                    return false;
                }
                try {
                    let body = {
                        caseNodeId: nodeId,
                        stepType: copyStep.stepType,
                        stepInfo: copyStep.stepInfo,
                        stepDesc: copyStep?.stepDesc ?? '新建步骤',
                        osType: curOsType
                    };
                    // 获取步骤添加位置
                    setRecording(true);
                    let res = await createStep(body);
                    const newStep = {
                        stepInfo: copyStep.stepInfo,
                        stepType: copyStep.stepType,
                        stepId: res.stepId,
                        index: 0,
                        state: 0,
                        type: 0
                    };
                    let newStepList = [...stepList];
                    newStepList = [newStep];
                    handleUpdateStepList(newStepList, newStep);
                    setRecording(false);
                } catch (err) {
                    setRecording(false);
                }
                break;
            default:
                break;
        }
    };

    return (
        <Layout hasSider className={styles.layoutContainer}>
            {contextHolder}
            <Splitter layout="vertical">
                <Splitter.Panel min="100">
                    <Dropdown
                        menu={{
                            items,
                            onClick
                        }}
                        placement="right"
                        trigger={['contextMenu']}
                    >
                        <Layout className={styles.layoutContainer}>
                            {/* 编辑态 且 竖图下展示 */}
                            {editType === 'edit' &&
                                localStorage.getItem('common_view') === 'vertical' &&
                                leftExtra}
                            <StepInit
                                editType={editType}
                                nodeId={nodeId}
                                curOsType={curOsType}
                                currentStep={currentStep}
                                setCurrentStep={setCurrentStep}
                                stepList={stepList}
                                currentNode={currentNode}
                                handleCurrentNode={handleCurrentNode}
                                handleUpdateNode={handleUpdateNode}
                                handleUpdateStep={handleUpdateStep}
                                handleUpdateStepList={handleUpdateStepList}
                                refreshStepList={refreshStepList}
                            />
                            {/* 仅移动端支持切换调试页面去执行 */}
                            {showDebug &&
                                !location.pathname.includes('/backup') &&
                                isElectron() &&
                                [1, 2]?.includes(curOsType) &&
                                editType === 'edit' &&
                                global.params.CASE_STATUS === 'edit' && (
                                    <div className={styles.runSetting}>
                                        <div
                                            onClick={(e) => {
                                                if (!e.target.closest('.ant-dropdown-trigger')) {
                                                    return;
                                                }
                                                let url;
                                                const baseQuery = {
                                                    ...query,
                                                    view: '222',
                                                    mode: 'default'
                                                };
                                                if (location.pathname.includes('/traffic')) {
                                                    url = stringifyUrl({
                                                        url:
                                                            '/' + currentModule + '/traffic/detail',
                                                        query: baseQuery
                                                    });
                                                } else {
                                                    url = stringifyUrl({
                                                        url: '/' + currentModule + '/edit',
                                                        query: baseQuery
                                                    });
                                                }
                                                navigate(url);
                                            }}
                                            className={classnames(styles.runSettingBtn)}
                                        >
                                            <CodeSandboxOutlined />
                                            <span style={{ fontSize: '12px' }}> 用例调试 </span>
                                        </div>
                                    </div>
                                )}

                            {[4]?.includes(curOsType) && editType === 'edit' && <ServerCaseDebug />}
                            {isEmpty(stepList) && curOsType !== 4 ? (
                                <NoContent text="暂无步骤" className={styles.noContent} />
                            ) : (
                                <Content>
                                    <Splitter>
                                        <Splitter.Panel
                                            min="20%"
                                            defaultSize={
                                                stepListWidth
                                                    ? stepListWidth
                                                    : curOsType === 4
                                                    ? '25%'
                                                    : '40%'
                                            }
                                            style={{ borderRight: '1px solid #f0f0f0' }}
                                        >
                                            <Loading loading={loading}>
                                                <StepList
                                                    nodeId={nodeId}
                                                    caseNodeId={caseNodeId}
                                                    setOpenTestDetail={setOpenTestDetail}
                                                    editType={editType}
                                                    curOsType={curOsType}
                                                    currentStep={currentStep}
                                                    setCurrentStep={setCurrentStep}
                                                    handleUpdateStep={handleUpdateStep}
                                                    stepList={stepList}
                                                    hasProxy={hasProxy}
                                                    handleUpdateNode={handleUpdateNode}
                                                    handleCurrentNode={handleCurrentNode}
                                                    currentNode={currentNode}
                                                    handleUpdateStepList={handleUpdateStepList}
                                                    handleShowRelationCaseOpen={
                                                        handleShowRelationCaseOpen
                                                    }
                                                    refreshStepList={refreshStepList}
                                                    isSnippetCase={snippetId && true}
                                                />
                                            </Loading>
                                        </Splitter.Panel>
                                        <Splitter.Panel min="38%">
                                            <StepInfo
                                                key={'step_edit_stepInfo'}
                                                nodeId={nodeId}
                                                editType={editType}
                                                curOsType={curOsType}
                                                currentStep={currentStep}
                                                setCurrentStep={setCurrentStep}
                                                handleUpdateStep={handleUpdateStep}
                                                stepList={stepList}
                                                hasProxy={hasProxy}
                                                handleUpdateNode={handleUpdateNode}
                                                handleCurrentNode={handleCurrentNode}
                                                currentNode={currentNode}
                                                handleUpdateStepList={handleUpdateStepList}
                                            />
                                        </Splitter.Panel>
                                    </Splitter>
                                </Content>
                            )}
                        </Layout>
                    </Dropdown>
                </Splitter.Panel>
                {!isEmpty(serverTestRes) && (
                    <Splitter.Panel
                        min="100"
                        style={{
                            borderTop: '4px solid #f0f0f0'
                        }}
                    >
                        <DebugInfo
                            curOsType={curOsType}
                            currentStep={currentStep}
                            serverTestRes={serverTestRes}
                            testStepList={serverTestRes?.stepList}
                            handleUpdateStep={handleUpdateStep}
                        />
                    </Splitter.Panel>
                )}
            </Splitter>
            {/* 移动端单步调试 */}
            {null !== testStep && [1, 2]?.includes(curOsType) && (
                <TestStep
                    {...props}
                    open={openTestDetail}
                    setOpen={setOpenTestDetail}
                    curOsType={curOsType}
                />
            )}
            <SettingModal ref={templateManagerRef} />
        </Layout>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    defaultConfig: state.common.base.defaultConfig,
    pageSourceSwitch: state.common.case.pageSourceSwitch,
    caseConfig: state.common.case.caseConfig,
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace,
    currentModule: state.common.base.currentModule,
    showModal: state.common.base.showModal,
    testStep: state.common.case.testStep,
    testRes: state.common.case.testRes,
    serverTestRes: state.common.case.serverTestRes,
    copyStep: state.common.case.copyStep,
    treeData: state.common.case.treeData,
    envList: state.common.case.envList,
    snippetList: state.common.case.snippetList,
    appList: state.common.case.appList,
    schemeList: state.common.case.schemeList,
    currentNode: state.common.case.currentNode
}))(StepDetail);
