import { useState, useRef } from 'react';
import classnames from 'classnames';
import { Tooltip, message } from 'antd';
import { handleCreateStep } from 'COMMON/components/TreeComponents/Step/StepInit/utils';
import StepItem from 'COMMON/components/TreeComponents/Step/StepItem';
import RenderStepGroup from './RenderStepGroup';
import {
    moveSingleStep,
    moveSingleStepIntoGroup,
    moveSingleStepIntoGroupStep,
    moveSingleStepOutGroup,
    moveGroupStepToGroup,
    moveGroupStepToGroupStep
} from './utils';
import styles from './StepList.module.less';

function RenderStep(props) {
    const {
        step,
        index,
        editType = 'edit', //  编辑类型
        currentStep,
        setCurrentStep = () => {},
        onChangeStep,
        defaultConfig,
        showBatchStepSwitch,
        checkStepList,
        handleCheckStepList,
        stepList,
        showAddGroup = true, // 是否显示添加组按钮
        isDebug = false,
        nodeId, // 节点id
        curOsType, // 端类型
        treeData,
        setTreeData,
        setRecording,
        currentSpace,
        handleUpdateStepList,
        dragging,
        setDragging,
        draggingStep,
        setDraggingStep
    } = props;
    const [expanded, setExpanded] = useState(false);
    const [dragOverPosition, setDragOverPosition] = useState({
        targetId: null,
        position: 'top'
    });

    const common_params = {
        commonAlertClear: defaultConfig?.mobileConfig?.stepConfig?.commonAlertClear ?? false,
        stepInterval: defaultConfig?.mobileConfig?.stepConfig?.stepInterval
            ? +defaultConfig?.mobileConfig?.stepConfig?.stepInterval
            : 2
    };

    // 添加步骤组
    const handleAddStepGroup = async (step) => {
        try {
            if (['readonly', 'debug', 'execute'].includes(editType)) {
                message.error('当前状态无法添加步骤组');
                return false;
            }
            setRecording(true);
            const stepInfo = {
                type: 1,
                desc: '',
                params: {
                    type: 'stepGroup',
                    params: {
                        stepIdList: [],
                        step: [],
                        assert: {
                            type: 0 // 0-不校验 1-智能校验；默认 0
                        }
                    }
                }
            };
            await handleCreateStep({
                caseNodeId: nodeId,
                stepDesc: '未命名步骤组',
                step: stepInfo,
                curOsType,
                stepType: 1401,
                treeData,
                setTreeData,
                stepList: stepList,
                currentStep: step,
                handleUpdateStepList,
                currentSpace
            });
            setRecording(false);
        } catch (err) {
            console.log(err);
            setRecording(false);
        }
    };

    // 工具端且自动化步骤
    const isStepTest = (step) => {
        if (isElectron() && [1, 2]?.includes(curOsType)) {
            if ([504, 602, 601]?.includes(step?.stepType)) {
                return true;
            }
            if (3 !== step?.stepInfo?.type) {
                return true;
            }
        }
        return false;
    };

    // 勾选操作
    const showCheckbox = showBatchStepSwitch;
    // 删除操作
    const showDelete =
        ['edit', 'template']?.includes(editType) && step?.stepId === currentStep?.stepId;
    // 添加操作
    const showGroupAttr =
        ['edit', 'template']?.includes(editType) &&
        step?.stepType === 1401 &&
        step?.stepId === currentStep?.stepId;
    // 单步调试操作
    const showTest = !isDebug && isStepTest(step);
    // AI 智能校验是否通过展示
    const showExecuteAIAssertRes =
        [0].indexOf(step?.result?.status) !== -1 && [504, 601, 602]?.includes(step.stepType);

    function checkCanDropInside(hoverStep) {
        // 例如，只有 dragNode.type === 'step' 才能放进 group 内部
        if (hoverStep?.stepType !== 1401) {
            return false;
        }
        if (draggingStep?.stepType === 1401) {
            return false;
        }
        return true;
    }

    const getDropPosition = (e, step) => {
        const rect = e.currentTarget.getBoundingClientRect();
        const offsetY = e.clientY - rect.top;
        // 步骤组不允许 放入组里的步骤下
        if (step.parentStepId && draggingStep?.stepType === 1401) {
            return {
                position: '',
                canDropInside: false,
                finalPosition: ''
            };
        }
        // 简单三分区判断
        if (offsetY < rect.height * 0.2) {
            // 上四分之一，放前面
            return { position: 'before', canDropInside: false, finalPosition: 'before' };
        }
        if (offsetY > rect.height * 0.8) {
            // 下四分之一，放后面
            return { position: 'after', canDropInside: false, finalPosition: 'after' };
        }

        // 中间区域，判断是否允许放入子层级
        const canDropInside = step.stepType === 1401 && checkCanDropInside(step);
        // 中间 50%
        return {
            position: 'inside',
            canDropInside,
            finalPosition: canDropInside ? 'inside' : 'after' // 不允许 inside 时默认放后面
        };
    };

    const handleDragStart = (e, step) => {
        e.stopPropagation();
        setDraggingStep(step);
    };

    const handleDragEnter = (e, step) => {
        e.stopPropagation();
        const { finalPosition } = getDropPosition(e, step);

        setDragOverPosition({
            targetId: step.stepId,
            position: finalPosition
        });
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();

        const toElement = e.relatedTarget;
        if (e.currentTarget.contains(toElement)) {
            // 鼠标是移到子元素，不算离开，忽略
            return;
        }
        setDragOverPosition(null);
    };

    const handleDrop = (e, targetStep) => {
        e.preventDefault();
        e.stopPropagation();

        if (!draggingStep || draggingStep.stepId === targetStep.stepId) {
            return;
        }

        // 拖拽位置
        const pos = dragOverPosition?.position;
        // 被移动的步骤Id
        const dragStepId = draggingStep.stepId;
        // 移动到的步骤Id
        const targetStepId = targetStep.stepId;

        // 具体操作
        // 单步骤(非组里的)移动 after
        if (pos === 'after' && !draggingStep?.parentStepId && !targetStep?.parentStepId) {
            console.log('单步骤移动 after');
            moveSingleStep(dragStepId, targetStepId, stepList, handleUpdateStepList);
        }
        // 单步骤(非组里的)移动 before
        else if (pos === 'before' && !draggingStep?.parentStepId && !targetStep?.parentStepId) {
            console.log('单步骤移动 before');
            let targetStepIndex = stepList.findIndex((item) => item.stepId === targetStepId);
            let newTargetStepId =
                targetStepIndex - 1 < 0 ? -1 : stepList[targetStepIndex - 1].stepId;
            moveSingleStep(dragStepId, newTargetStepId, stepList, handleUpdateStepList);
        }
        // 单步骤(非组里的)移入步骤组1
        else if (pos === 'inside' && !draggingStep?.parentStepId && targetStep.stepType === 1401) {
            console.log('单步骤移入步骤组1的第一个步骤');
            moveSingleStepIntoGroup(dragStepId, targetStepId, stepList, handleUpdateStepList);
        }
        // 单步骤(非组里的)移入步骤组中某步骤下 after
        else if (pos === 'after' && !draggingStep?.parentStepId && targetStep?.parentStepId) {
            console.log('单步骤移入步骤组中某步骤下 after');
            moveSingleStepIntoGroupStep(
                dragStepId,
                targetStepId,
                targetStep.parentStepId,
                stepList,
                handleUpdateStepList
            );
        }

        // 单步骤(非组里的)移入步骤组中某步骤下 before
        else if (pos === 'before' && !draggingStep?.parentStepId && targetStep?.parentStepId) {
            console.log('单步骤移入步骤组中某步骤下 before');
            let targetParentStep = stepList.find((item) => item.stepId === targetStep.parentStepId);
            let targetStepIndex = targetParentStep.stepChildren.findIndex(
                (item) => item.stepId === targetStepId
            );
            let newTargetStepId =
                targetStepIndex - 1 < 0
                    ? -1
                    : targetParentStep.stepChildren[targetStepIndex - 1].stepId;
            moveSingleStepIntoGroupStep(
                dragStepId,
                newTargetStepId,
                targetStep.parentStepId,
                stepList,
                handleUpdateStepList
            );
        }
        // 单步骤(组里的)从步骤组1移出到步骤列表里 after
        else if (pos === 'after' && draggingStep?.parentStepId && !targetStep?.parentStepId) {
            console.log('单步骤从步骤组1移出到步骤列表里 after');
            moveSingleStepOutGroup(
                dragStepId,
                targetStepId,
                draggingStep.parentStepId,
                stepList,
                handleUpdateStepList
            );
        }
        // 单步骤(组里的)从步骤组1移出到步骤列表里 before
        else if (pos === 'before' && draggingStep?.parentStepId && !targetStep?.parentStepId) {
            console.log('单步骤从步骤组1移出到步骤列表里 before');
            let targetStepIndex = stepList.findIndex((item) => item.stepId === targetStepId);
            let newTargetStepId =
                targetStepIndex - 1 < 0 ? -1 : stepList[targetStepIndex - 1].stepId;
            moveSingleStepOutGroup(
                dragStepId,
                newTargetStepId,
                draggingStep.parentStepId,
                stepList,
                handleUpdateStepList
            );
        }
        // 单步骤(组内)从步骤组1移动到步骤组2第一个步骤
        else if (pos === 'inside' && draggingStep?.parentStepId && targetStep.stepType === 1401) {
            console.log('单步骤从步骤组1移动到步骤组2的第一个步骤');
            moveGroupStepToGroup(
                dragStepId,
                draggingStep.parentStepId,
                targetStep?.stepId,
                stepList,
                handleUpdateStepList
            );
        }
        // 单步骤(组内)从步骤组1移动到步骤组2中某步骤下 after
        else if (pos === 'after' && draggingStep?.parentStepId && targetStep?.parentStepId) {
            console.log('单步骤从步骤组1移动到步骤组2中某步骤下 after');
            moveGroupStepToGroupStep(
                dragStepId,
                targetStep?.stepId,
                draggingStep.parentStepId,
                targetStep.parentStepId,
                stepList,
                handleUpdateStepList
            );
        }
        // 单步骤(组内)从步骤组1移动到步骤组2中某步骤下 before
        else if (pos === 'before' && draggingStep?.parentStepId && targetStep?.parentStepId) {
            console.log('单步骤从步骤组1移动到步骤组2中某步骤下 before');
            let targetParentStep = stepList.find((item) => item.stepId === targetStep.parentStepId);
            let targetStepIndex = targetParentStep.stepChildren.findIndex(
                (item) => item.stepId === targetStepId
            );
            let newTargetStepId =
                targetStepIndex - 1 < 0
                    ? -1
                    : targetParentStep.stepChildren[targetStepIndex - 1].stepId;
            moveGroupStepToGroupStep(
                dragStepId,
                newTargetStepId,
                draggingStep.parentStepId,
                targetStep.parentStepId,
                stepList,
                handleUpdateStepList
            );
        }
        setDragOverPosition(null);
        setDraggingStep(null);
    };

    return (
        <>
            <div
                className={styles.dragOverTop}
                style={{
                    opacity:
                        dragOverPosition?.targetId === step.stepId &&
                        dragOverPosition.position === 'before'
                            ? 1
                            : 0
                }}
            />
            <div
                className={styles.stepItemContainer}
                draggable={!['readonly', 'debug', 'execute']?.includes(editType) && dragging}
                onDragEnter={(e) => handleDragEnter(e, step)}
                onDragStart={(e) => handleDragStart(e, step)}
                onDragLeave={(e) => handleDragLeave(e, step)}
                onDragOver={(e) => e.preventDefault()}
                onDrop={(e) => handleDrop(e, step)}
                onClick={(e) => {
                    e.stopPropagation();
                    onChangeStep && onChangeStep(step);
                    setCurrentStep(step);
                }}
            >
                <StepItem
                    {...props}
                    step={step}
                    hoverDragInside={
                        dragOverPosition?.targetId === step.stepId &&
                        dragOverPosition.position === 'inside'
                    }
                    expanded={expanded}
                    setExpanded={setExpanded}
                    commonParams={common_params}
                    currentStep={currentStep}
                    isChecked={checkStepList?.includes(step?.stepId)}
                    onCheck={(e) => {
                        handleCheckStepList(step?.stepId, e?.target?.checked);
                    }}
                    stepInGroup={!!step?.parentStepId}
                    showCheckbox={showCheckbox}
                    showDelete={showDelete}
                    showGroupAttr={showGroupAttr}
                    showTest={showTest}
                    showExecuteAIAssertRes={showExecuteAIAssertRes}
                    showDebugRes={!isDebug}
                    index={index}
                    onFocus={() => setDragging(false)}
                    onBlur={() => setDragging(true)}
                />
                {showAddGroup &&
                    !['readonly', 'debug', 'execute'].includes(editType) &&
                    index !== 0 &&
                    !step?.parentStepId && (
                        <Tooltip title="添加步骤组" placement="left">
                            <span
                                className={styles.addStepGroup}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    let preStepIndex = stepList?.findIndex(
                                        (item) => item.stepId === step?.stepId
                                    );
                                    handleAddStepGroup(
                                        stepList[preStepIndex - 1 < 0 ? 0 : preStepIndex - 1]
                                    );
                                }}
                            >
                                <span className={styles.icon}>+</span>
                            </span>
                        </Tooltip>
                    )}
            </div>
            <div
                className={styles.dragOverBottom}
                style={{
                    opacity:
                        dragOverPosition?.targetId === step.stepId &&
                        dragOverPosition.position === 'after'
                            ? 1
                            : 0
                }}
            />
            <RenderStepGroup
                {...props}
                step={step}
                index={index}
                expanded={expanded}
                setExpanded={setExpanded}
            />
        </>
    );
}

export default RenderStep;
