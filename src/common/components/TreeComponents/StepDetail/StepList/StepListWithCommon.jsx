import { useEffect, useState, useMemo, useCallback } from 'react';
import { Tooltip, Input, message } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ParamsSwitch from 'COMMON/components/ParamsSwitch';
import BatchStepSwitch from 'COMMON/components/BatchStepSwitch';
import AddOperation from 'COMMON/components/AddOperation';
import { updateStepAttrInIsDel } from 'COMMON/api/front_qe_tools/step';
import { createPoint } from 'COMMON/api/front_qe_tools/points';
import { treeAction } from 'FEATURES/front_qe_tools/case/edit/EditPage/CaseDetailView/LayoutSider/utils';
import { getExecutionType } from '../../Step/StepItem/utils';
import RenderStep from './RenderStep';
import styles from './StepList.module.less';

function StepList(props) {
    const { moduleStepList } = props;
    const [draggingStep, setDraggingStep] = useState(null);
    const [dragging, setDragging] = useState(true); // 是否拖拽

    return (
        <div className={styles.stepListDetail}>
            {(moduleStepList ?? [])?.map((step, index) => (
                <RenderStep
                    {...props}
                    key={step?.stepId}
                    step={step}
                    index={index}
                    dragging={dragging}
                    setDragging={setDragging}
                    draggingStep={draggingStep}
                    setDraggingStep={setDraggingStep}
                />
            ))}
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    defaultConfig: state.common.base.defaultConfig,
    pageSourceSwitch: state.common.case.pageSourceSwitch,
    caseConfig: state.common.case.caseConfig,
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace,
    currentModule: state.common.base.currentModule,
    showModal: state.common.base.showModal,
    testStep: state.common.case.testStep,
    testRes: state.common.case.testRes,
    serverTestRes: state.common.case.serverTestRes,
    copyStep: state.common.case.copyStep,
    treeData: state.common.case.treeData,
    envList: state.common.case.envList,
    snippetList: state.common.case.snippetList,
    appList: state.common.case.appList,
    schemeList: state.common.case.schemeList
}))(StepList);
