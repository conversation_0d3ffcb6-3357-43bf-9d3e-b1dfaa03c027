@import "RESOURCES/css/common.less";
@import "RESOURCES/css/resizableBox.less";
@import "COMMON/components/TreeComponents/Step/TestStep/common.less";

.layoutContainer {
    position: relative;
    height: 100%;
    background-color: var(--background-color);
}

.noContent {
    margin-top: 200px;
}

.caseDesc {
    float: left;
    width: 50%;
}

.caseStep {
    width: 100%;
}

.stepList {
    position: relative;
    width: 100%;
    padding: 10px;
    overflow: scroll;
    max-height: calc(100vh - 80px);

    :global {
        .ant-input {
            width: 100%;
            font-size: 12px !important;
            color: rgb(119, 119, 119);
        }
    }

    .stepInfo {
        margin-bottom: 5px;
        padding: 10px 5px;
        overflow: hidden;
        border: 1px solid var(--border-color);
        border-radius: 5px;
    }

    .activedStepInfo {
        border: 1px solid #5191f0;
        box-shadow: 0 0 0 3px rgb(24 144 255 / 12%);
    }

    .stepInfoLeft {
        height: 20px;
        line-height: 20px;
    }
}

.stepItemContainer {
    position: relative;
    // margin: 4px 0;
}

// 新增步骤组icon（出现：步骤下方）
.addStepGroup {
    position: absolute;
    left: -8px;
    top: -15px;
    width: 20px;
    height: 20px;
    box-shadow: 0 0 3px 2px rgba(157, 157, 157, 0.12);
    background-color: #fff;
    border: 1px solid #e1e6f0;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    z-index: 999;
    opacity: 0;
    user-select: none;

    .icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 16px;
        color: #777;
    }
}

.stepItemContainer:hover {
    .addStepGroup {
        opacity: 1;
        user-select: auto;
    }
}

// 步骤组
.stepGroupContainer {
    position: relative;
    width: 100%;
    padding-left: 10px;
    margin-bottom: 5px;
    border-radius: 4px;
    border: 1px dashed var(--primary-color);
}

.stepGroupContainerEmpty {
    height: 40px;
    line-height: 40px;
}

.stepGroupIcon,
.stepGroupCount,
.stepGroupName {
    color: #777;
    font-size: 12px;
}

.stepGroupName {

    :global {
        .ant-input-sm {
            padding: 0 !important;
        }
    }
}

.stepGroupOperation {
    position: absolute;
    right: 11px;
    top: 0px;
    opacity: 0;
    user-select: none;
}

.stepGroupContainerActived {
    box-shadow: 0 0 0 3px var(--primary-background-color);

    .stepGroupOperation {
        opacity: 1;
    }
}

// 步骤详情
.stepDetail {
    position: relative;
    width: 100%;
    // height: 100%;
    border-radius: 5px;
}

.debugInfo {
    height: calc(100% - 150px);
}

.delete {
    color: red;
}

.operatorCut {
    color: var(--border-color);
    margin: 0 0 0 -4px;
}

.testIcon {
    margin-right: 0;
}

.layoutFooter {
    :global {

        .ant-select-selection-item,
        .ant-select,
        .custom-default-select,
        .custom-dark-select {
            padding: 0 !important;
            font-size: 12px !important;
            color: #777 !important;
        }

        .ant-select-single.ant-select-sm .ant-select-selector {
            font-size: 12px;
        }

        .ant-select-selection-overflow,
        .custom-default-select-selection-overflow,
        .custom-dark-select-selection-overflow {
            padding: 0 !important;
            font-size: 12px !important;
            color: #777 !important;
        }
    }
}


.paramsSelect {
    :global {
        .ant-select-selection-placeholder {
            font-size: 12px !important;
        }
    }
}

.runSettingBtn {
    position: absolute;
    right: 15px;
    top: 15px;
    background-color: #000;
    cursor: pointer;
}

.reRunSettingBtn,
.runSettingBtn {
    margin-left: 5px;
    padding: 3px 5px;
    font-size: 12px;
    background-color: var(--primary-color);
    border-radius: 3px;
    color: #fff;
}

.reRunSettingBtn {
    background-color: var(--error-color);
}

.batchStepSwitch {
    float: left;
}


.stepGroup {
    margin-bottom: 10px;
    background-color: #ffffff;
    border-radius: 4px;
    overflow: hidden;
}

.stepGroupHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 12px;
    background-color: rgba(247, 247, 247, 0.965);
    cursor: pointer;
    user-select: none;
    margin-bottom: 8px;
}

.headerLeft {
    display: flex;
    align-items: center;
}

.headerRight {
    display: flex;
    margin-left: auto;
}

.collapseIcon {
    margin-right: 6px;
    font-size: 12px;
    color: #333;
}

.stepGroupTitle {
    font-weight: bold;
    color: #333;
}

.stepGroupContent {
    /* 移除边框 */
    border: none;
    margin-left: 5px;
}

.addBtn,
.deleteBtn {
    font-size: 12px;
    height: 24px;
    padding: 0 8px;
    margin-left: 4px;
}

.deleteBtn {
    color: #ff4d4f;
}

.relationList {
    font-size: 12px;
    font-weight: normal;
    color: #777;

    &:hover {
        color: var(--primary-color);
        text-decoration: underline;
        cursor: pointer;
    }
}

.stepListHeaderWithFlex {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
}


// new
.stepInfo {
    margin-left: -3px;
    padding: 10px 5px;
    overflow: hidden;
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 5px;
}

.stepGroupInfo {
    background-color: transparent;
    border: none;
    border-radius: 0;
}

.stepInfo:hover {
    background-color: var(--background-color-hover);
}

.activedStepInfo {
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-background-color);
}

.activedStepGroupInfo {
    border: none;
    border-bottom: 1px solid var(--primary-color);
    box-shadow: none;
}

.disabledStepInfo {
    opacity: .5;
}

.stepNameInfo {
    position: relative;
    display: flex;
    width: 100%;
}

.left {
    max-width: 125px;
    overflow: hidden;

    .stepCheckbox {
        margin-right: 4px;
    }

    // 步骤索引
    .stepIndex {
        float: left;
        display: block;
        width: 20px;
        text-align: center;
        color: #777;
        font-size: 12px;
    }

    .groupWithStepIndex {
        opacity: 0;
    }

}


.stepDescInfo {
    flex: 1;
    position: relative;
    height: 100%;
    overflow: hidden;

    :global {
        .ant-input {
            font-size: 12px !important;
            color: var(--color2) !important;
        }
    }
}

.stepCount {
    float: left;
    text-align: center;
    color: #777;
    font-size: 12px;
}

.defaultStepName {
    display: inline-block;
    min-width: 60px;
    max-width: 140px;
    height: 22px;
    line-height: 22px;
    color: #1890ff;
    font-size: 12px;
    font-weight: 800;
    cursor: pointer;
}

.groupStepName {
    color: gray;
}

.manualStepName {
    color: var(--maunal-color);
}

.assestStepName {
    color: var(--assest-color);
}

.aiAssertStepName {
    color: var(--ai-color);
}

.errorStepName {
    color: var(--error-color);
}

.clearStepName {
    background-color: #e5f1fe;
    color: #1890ff;
}


.stepParamsDivider {
    width: 100%;
    margin: 10px 0 10px 0;
    border-bottom: 1px solid var(--border-color);
    height: 1px;
}

.operationGroup {
    padding-right: 3px;
    background-color: transparent;
    border-radius: 4px;

    .addIcon,
    .divier {
        color: #eee;
        font-size: 12px;
    }
}

.stepChildrenInfo {
    margin-left: 20px;
}

// drag
.dragOverTop {
    border-top: 2px solid var(--primary-color);
}

.dragOverBottom {
    border-bottom: 2px solid var(--primary-color);
}

.dragOverMiddle {
    background-color: var(--primary-background-color);
}