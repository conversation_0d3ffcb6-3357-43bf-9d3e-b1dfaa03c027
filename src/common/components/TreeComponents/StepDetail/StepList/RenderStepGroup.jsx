import RenderStep from './RenderStep';
import styles from './StepList.module.less';

function RenderStepGroup(props) {
    const {step, currentStep, expanded} = props;

    if (expanded) {
        return null;
    }

    return (
        <>
            {step?.stepChildren?.map((stepChild, _index) => {
                if (currentStep?.stepId === stepChild?.stepId) {
                    stepChild = currentStep;
                }
                stepChild.parentStepId = step?.stepId;
                return (
                    <div key={stepChild?.stepId} className={styles.stepChildrenInfo}>
                        <RenderStep {...props} step={stepChild} index={_index} />
                    </div>
                );
            })}
        </>
    );
};

export default RenderStepGroup;