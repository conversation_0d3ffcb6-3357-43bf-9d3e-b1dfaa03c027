import { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import { Tooltip } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ParamsSwitch from 'COMMON/components/ParamsSwitch';
import BatchStepSwitch from 'COMMON/components/BatchStepSwitch';
import AddOperation from 'COMMON/components/AddOperation';
import { updateStepAttrInIsDel } from 'COMMON/api/front_qe_tools/step';
import { createPoint } from 'COMMON/api/front_qe_tools/points';
import { treeAction } from 'FEATURES/front_qe_tools/case/edit/EditPage/CaseDetailView/LayoutSider/utils';
import { getExecutionType } from '../../Step/StepItem/utils';
import StepListWithServer from './StepListWithServer';
import StepListWithCommon from './StepListWithCommon';
import styles from './StepList.module.less';

function StepList(props) {
    const {
        nodeId, // 节点id
        stepListView = 'list', // 步骤列表视图
        editType = 'edit', //  编辑类型
        curOsType, // 端类型
        currentStep,
        stepList,
        setTestStep,
        caseNodeId,
        treeData,
        setTreeData,
        setRecording,
        currentSpace,
        defaultConfig,
        handleUpdateStepList,
        handleShowRelationCaseOpen,
        isSnippetCase = false
    } = props;
    // 批量操作开关
    const [showBatchStepSwitch, setShowBatchStepSwitch] = useState(false);
    const [checkStepList, setCheckStepList] = useState([]);

    const common_params = {
        commonAlertClear: defaultConfig?.mobileConfig?.stepConfig?.commonAlertClear ?? false,
        stepInterval: defaultConfig?.mobileConfig?.stepConfig?.stepInterval
            ? +defaultConfig?.mobileConfig?.stepConfig?.stepInterval
            : 2
    };

    useEffect(() => {
        if (!currentStep?.stepId) {
            return;
        }
        createPoint({
            moduleId: currentSpace?.id, // 业务模块id；int；必填
            caseNodeId: caseNodeId, // 用例节点Id；int；必填
            stepId: currentStep?.stepId, // 步骤Id；int；必填
            osType: curOsType, // 端类型，int；必填 1-Android 2-iOS 3-Android&iOS 4-server 5-web
            pointType: 1001, // 打点类型；int；必填 1000-创建步骤 1001-选中步骤 1100-update
            pointInfo: {}, // 点位内容；json；选填（预留字段，当前传空 json 就行）
            createTime: Math.floor(new Date().getTime() / 1000) // 打点时间；int；必填
        }).catch(() => {});
    }, [currentStep?.stepId]);

    useEffect(() => {
        setCheckStepList([]);
        setShowBatchStepSwitch(false);
    }, [caseNodeId]);

    // 批量选择步骤
    const handleCheckStepList = (stepId, checked) => {
        if (checked && !checkStepList?.includes(stepId)) {
            // 选中
            setCheckStepList([...checkStepList, stepId]);
        } else if (!checked && checkStepList?.includes(stepId)) {
            // 取消选中
            setCheckStepList(checkStepList.filter((item) => item !== stepId));
        }
    };

    // 删除步骤
    const handleDeleteStep = async (stepIdList) => {
        try {
            let _stepList = [];
            // 构造新stepList, 删除当前选中的步骤
            for (let step of stepList) {
                if (!stepIdList?.includes(step.stepId)) {
                    _stepList.push(step);
                }
            }
            // 找到第一个删除步骤，删除后展示它的上一个步骤
            let curStepIndex = stepList?.findIndex((item) => stepIdList?.includes(item.stepId));

            setRecording(true);
            await updateStepAttrInIsDel({
                stepIdList: stepIdList,
                isDel: true
            });
            setTestStep(null);
            // 获取新步骤展示
            if (isEmpty(_stepList)) {
                handleUpdateStepList([], null);
            } else {
                handleUpdateStepList(
                    _stepList,
                    0 === +curStepIndex ? _stepList[curStepIndex] : _stepList[curStepIndex - 1]
                );
            }
            setTreeData(
                treeAction([...treeData], 'children', +nodeId, (node, item, index) => {
                    item.executionType = getExecutionType(item, _stepList);
                })
            );
        } catch (err) {
        } finally {
            setRecording(false);
        }
    };

    return (
        <div className={styles.stepList}>
            <div className={`${styles.stepListHeader} ${styles.stepListHeaderWithFlex}`}>
                <div style={{ display: stepListView === 'list' ? 'flex' : 'none' }}>
                    {curOsType !== 4 && (
                        <ParamsSwitch
                            className={
                                ['edit', 'template']?.includes(editType)
                                    ? styles.batchStepSwitch
                                    : null
                            }
                        />
                    )}
                    {['edit', 'template']?.includes(editType) && (
                        <BatchStepSwitch
                            {...props}
                            curOsType={curOsType}
                            nodeId={nodeId}
                            showBatchStepSwitch={showBatchStepSwitch}
                            onChange={setShowBatchStepSwitch}
                            checkStepList={checkStepList}
                            setCheckStepList={setCheckStepList}
                            stepList={stepList}
                            handleDeleteStep={handleDeleteStep}
                            handleUpdateStepList={handleUpdateStepList}
                        />
                    )}
                    {['edit']?.includes(editType) && +curOsType === 4 && (
                        <AddOperation
                            {...props}
                            curOsType={curOsType}
                            nodeId={nodeId}
                            showBatchStepSwitch={showBatchStepSwitch}
                            onChange={setShowBatchStepSwitch}
                            checkStepList={checkStepList}
                            han
                            setCheckStepList={setCheckStepList}
                            stepList={stepList}
                            handleDeleteStep={handleDeleteStep}
                            handleUpdateStepList={handleUpdateStepList}
                        />
                    )}
                </div>
                {isSnippetCase && (
                    <Tooltip title="点击查询该测试片段关联的用例信息">
                        <span
                            className={styles.relationList}
                            onClick={() => handleShowRelationCaseOpen(true)}
                        >
                            相关用例
                        </span>
                    </Tooltip>
                )}
            </div>
            <div className={styles.stepListDetail}>
                {curOsType === 4 ? (
                    // 对于osType=4，使用分组显示
                    <StepListWithServer
                        {...props}
                        commonParams={common_params}
                        showBatchStepSwitch={showBatchStepSwitch}
                        checkStepList={checkStepList}
                        handleDeleteStep={handleDeleteStep}
                        handleCheckStepList={handleCheckStepList}
                    />
                ) : (
                    // 对于其他osType，保持原有显示方式
                    <StepListWithCommon
                        {...props}
                        stepList={stepList}
                        moduleStepList={stepList}
                        commonParams={common_params}
                        showBatchStepSwitch={showBatchStepSwitch}
                        checkStepList={checkStepList}
                        handleDeleteStep={handleDeleteStep}
                        handleCheckStepList={handleCheckStepList}
                    />
                )}
            </div>
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    defaultConfig: state.common.base.defaultConfig,
    pageSourceSwitch: state.common.case.pageSourceSwitch,
    caseConfig: state.common.case.caseConfig,
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace,
    currentModule: state.common.base.currentModule,
    showModal: state.common.base.showModal,
    testStep: state.common.case.testStep,
    testRes: state.common.case.testRes,
    serverTestRes: state.common.case.serverTestRes,
    copyStep: state.common.case.copyStep,
    treeData: state.common.case.treeData,
    envList: state.common.case.envList,
    snippetList: state.common.case.snippetList,
    appList: state.common.case.appList,
    schemeList: state.common.case.schemeList
}))(StepList);
