import { updateStep } from 'COMMON/api/front_qe_tools/step';

// 单步骤移动
export async function moveSingleStep(curStepId, sibStepId, stepList, handleUpdateStepList) {
    let body = {
        stepId: curStepId
    };
    body.preSibId = sibStepId;
    // 获取需要变动的步骤
    let step = stepList.find((item) => item.stepId === curStepId);
    // 过滤掉变动的步骤
    let filterStepList = stepList.filter((item) => item.stepId !== curStepId);
    // 获取目标步骤的位置
    const targetIndex = filterStepList.findIndex((item) => item.stepId === sibStepId);
    // 重新排序步骤列表
    const newStepList = [...filterStepList.slice(0, targetIndex + 1), step, ...filterStepList.slice(targetIndex + 1)];
    handleUpdateStepList(newStepList, step);
    await updateStep(body);
}

// 单步骤移入步骤组1的第一个步骤
export async function moveSingleStepIntoGroup(curStepId, parentStepId, stepList, handleUpdateStepList) {
    let body = {
        stepId: curStepId,
        preStepId: -1,
        stepAttr: {
            parentStepId: parentStepId
        }
    };
    // 获取需要变动的步骤
    let step = stepList.find((item) => item.stepId === curStepId);
    // 更新步骤父亲信息
    step.parentStepId = parentStepId;
    step.stepAttr = {
        parentStepId
    };
    // 过滤掉变动的步骤
    let filterStepList = stepList.filter((item) => item.stepId !== curStepId);
    // 获取目标步骤
    let targetStep = filterStepList.find((item) => item.stepId === parentStepId);
    // 更新目标步骤的子步骤列表
    targetStep.stepInfo.params.params.stepIdList = [
        step?.stepId,
        ...(targetStep.stepInfo.params.params.stepIdList ?? [])
    ];
    targetStep.stepChildren = [step, ...(targetStep.stepChildren ?? [])];
    // 重新排序步骤列表
    const newStepList = filterStepList.map((item) => {
        if (item.stepId === parentStepId) {
            return targetStep;
        }
        return item;
    });
    await updateStep(body);
    handleUpdateStepList(newStepList, step);
}

// 单步骤移入步骤组中某步骤下
export async function moveSingleStepIntoGroupStep(curStepId, sibStepId, parentStepId, stepList, handleUpdateStepList) {
    let body = {
        stepId: curStepId,
        preSibId: sibStepId,
        stepAttr: {
            parentStepId: parentStepId
        }
    };

    // 获取需要变动的步骤
    let step = stepList.find((item) => item.stepId === curStepId);
    // 过滤掉变动的步骤
    let filterStepList = stepList.filter((item) => item.stepId !== curStepId);
    // 获取目标步骤组
    let targetParentStep = filterStepList.find((item) => item.stepId === parentStepId);
    // 更新步骤父亲信息
    step.parentStepId = parentStepId;
    step.stepAttr = {
        parentStepId
    };
    // 获取目标步骤的位置
    const targetIndex = targetParentStep?.stepChildren.findIndex((item) => item.stepId === sibStepId);
    // 重新排序步骤列表
    const newParentStepList = [
        ...targetParentStep?.stepChildren.slice(0, targetIndex + 1),
        step,
        ...targetParentStep?.stepChildren.slice(targetIndex + 1)
    ];
    // 更新步骤组的子步骤列表
    targetParentStep.stepInfo.params.params.stepIdList = newParentStepList.map((item) => item.stepId);
    targetParentStep.stepChildren = newParentStepList;
    // 重新组装步骤列表
    const newStepList = filterStepList.map((item) => {
        if (item.stepId === parentStepId) {
            return targetParentStep;
        }
        return item;
    });
    await updateStep(body);
    handleUpdateStepList(newStepList, step);
}

// 单步骤从步骤组1移出到步骤列表里
export async function moveSingleStepOutGroup(curStepId, sibStepId, parentStepId, stepList, handleUpdateStepList) {
    let body = {
        stepId: curStepId,
        preSibId: sibStepId,
        stepAttr: {
            parentStepId: -1
        }
    };
    // 获取对应步骤组
    let curParentStep = stepList.find((item) => item.stepId === parentStepId);
    // 获取需要变动的步骤
    let step = curParentStep?.stepChildren?.find((item) => item.stepId === curStepId);
    // 更新步骤父亲信息
    step.parentStepId = null;
    step.stepAttr.parentStepId = null;
    // 更新步骤组的子步骤列表
    let filterStepChildren = curParentStep?.stepChildren?.filter((item) => item.stepId !== curStepId);

    curParentStep.stepInfo.params.params.stepIdList = filterStepChildren.map((item) => item.stepId);
    curParentStep.stepChildren = filterStepChildren;

    // 获取目标步骤的位置
    const targetIndex = stepList.findIndex((item) => item.stepId === sibStepId);

    // 重新排序步骤列表
    const newStepList = [...stepList.slice(0, targetIndex + 1), step, ...stepList.slice(targetIndex + 1)];

    // 重新组装步骤列表
    const newStepListWithGroup = newStepList.map((item) => {
        if (item.stepId === parentStepId) {
            return curParentStep;
        }
        return item;
    });
    await updateStep(body);
    handleUpdateStepList(newStepListWithGroup, step);
}

// 单步骤从步骤组1移动到步骤组2的第一个步骤
export async function moveGroupStepToGroup(curStepId, oldParentStepId, parentStepId, stepList, handleUpdateStepList) {
    let body = {
        stepId: curStepId,
        preSibId: -1,
        stepAttr: {
            parentStepId: parentStepId
        }
    };
    // 获取旧步骤组
    let oldParentStep = stepList.find((item) => item.stepId === oldParentStepId);
    // 获取新步骤组
    let curParentStep = stepList.find((item) => item.stepId === parentStepId);
    // 获取需要变动的步骤
    let step = oldParentStep?.stepChildren?.find((item) => item.stepId === curStepId);
    // 更新步骤父亲信息
    step.parentStepId = parentStepId;
    step.stepAttr = {
        parentStepId
    };
    // 更新旧步骤组的子步骤列表
    let filterOldStepChildren = oldParentStep?.stepChildren?.filter((item) => item.stepId !== curStepId);
    oldParentStep.stepInfo.params.params.stepIdList = filterOldStepChildren.map((item) => item.stepId);
    oldParentStep.stepChildren = filterOldStepChildren;

    // 更新新步骤组的子步骤列表
    let filterCurStepChildren = [step, ...(curParentStep?.stepChildren ?? [])];
    curParentStep.stepInfo.params.params.stepIdList = filterCurStepChildren.map((item) => item.stepId);
    curParentStep.stepChildren = filterCurStepChildren;

    // 重新组装步骤列表
    const newStepListWithGroup = stepList.map((item) => {
        if (item.stepId === oldParentStepId) {
            return oldParentStep;
        }
        if (item.stepId === parentStepId) {
            return curParentStep;
        }
        return item;
    });
    await updateStep(body);
    handleUpdateStepList(newStepListWithGroup, step);
}

// 单步骤从步骤组1移动到步骤组2中某步骤下
export async function moveGroupStepToGroupStep(
    curStepId,
    sibStepId,
    oldParentStepId,
    parentStepId,
    stepList,
    handleUpdateStepList
) {
    let body = {
        stepId: curStepId,
        preSibId: sibStepId,
        stepAttr: {
            parentStepId: parentStepId
        }
    };
    // 获取旧步骤组
    let oldParentStep = stepList.find((item) => item.stepId === oldParentStepId);
    // 获取新步骤组
    let curParentStep = stepList.find((item) => item.stepId === parentStepId);
    // 获取需要变动的步骤
    let step = oldParentStep?.stepChildren?.find((item) => item.stepId === curStepId);

    // 更新步骤父亲信息
    step.parentStepId = parentStepId;
    step.stepAttr = {
        parentStepId
    };
    // 更新旧步骤组的子步骤列表
    let filterOldStepChildren = oldParentStep?.stepChildren?.filter((item) => item.stepId !== curStepId);
    oldParentStep.stepInfo.params.params.stepIdList = filterOldStepChildren.map((item) => item.stepId);
    oldParentStep.stepChildren = filterOldStepChildren;

    // 更新新步骤组的子步骤列表
    // 获取目标步骤的位置
    const targetIndex = curParentStep?.stepChildren.findIndex((item) => item.stepId === sibStepId);
    // 重新排序步骤列表
    const newParentStepList = [
        ...curParentStep?.stepChildren.slice(0, targetIndex + 1),
        step,
        ...curParentStep?.stepChildren.slice(targetIndex + 1)
    ];
    // 更新步骤组的子步骤列表
    curParentStep.stepInfo.params.params.stepIdList = newParentStepList.map((item) => item.stepId);
    curParentStep.stepChildren = newParentStepList;

    // 重新组装步骤列表
    const newStepListWithGroup = stepList.map((item) => {
        if (item.stepId === oldParentStepId) {
            return oldParentStep;
        }
        if (item.stepId === parentStepId) {
            return curParentStep;
        }
        return item;
    });
    await updateStep(body);
    handleUpdateStepList(newStepListWithGroup, step);
}
