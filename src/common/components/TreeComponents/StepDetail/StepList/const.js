// server 端步骤配置

export const AddServiceOptions = [
    {
        key: 'http',
        label: 'HTTP'
    },
    {
        key: 'stargateRPC',
        label: 'StargateRPC'
    },
    {
        key: 'sql',
        label: 'SQL'
    },
    {
        key: 'redis',
        label: 'Redis'
    }
];

export const HOOK_TYPE = {
    setup: 1,
    teardown: 2,
    step: -1
};

export const HOOK_TYPE_TEXT = {
    [HOOK_TYPE.setup]: 'setup',
    [HOOK_TYPE.teardown]: 'teardown',
    [HOOK_TYPE.step]: 'step'
};