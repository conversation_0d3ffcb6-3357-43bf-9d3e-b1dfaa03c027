import { useState, useEffect } from 'react';
import { Dropdown, Button } from 'antd';
import { isEmpty } from 'lodash';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import NoContent from 'COMMON/components/common/NoContent';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import InterfaceStep from 'COMMON/components/TreeComponents/Step/StepInit/InitInterfaceStep.jsx';
import { deleteSetupTeardownNode } from 'COMMON/api/front_qe_tools/step';
import { updateTreeDataHook } from 'FEATURES/front_qe_tools/case/edit/EditPage/CaseDetailView/LayoutSider/utils';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import StepListWithCommon from './StepListWithCommon';
import { AddServiceOptions, HOOK_TYPE, HOOK_TYPE_TEXT } from './const';
import styles from './StepList.module.less';

function StepList(props) {
    const {
        commonParams,
        nodeId, // 节点id
        editType = 'edit', //  编辑类型
        curOsType, // 端类型
        currentStep,
        stepList,
        caseNodeId,
        currentNode,
        setCurrentNode,
        treeData,
        setTreeData,
        handleUpdateStepList,
        setCurrentStep
    } = props;

    // 在组件函数内部添加状态管理折叠状态
    const [collapsedGroups, setCollapsedGroups] = useState({
        setup: false,
        step: false,
        teardown: false
    });

    // 切换折叠状态函数
    const toggleCollapse = (groupType) => {
        setCollapsedGroups((prev) => ({
            ...prev,
            [groupType]: !prev[groupType]
        }));
    };

    const handleDeleteHook = async (hookType) => {
        try {
            await deleteSetupTeardownNode({
                caseNodeId: +nodeId,
                osType: curOsType,
                hookType: hookType
            });
            updateTreeDataHook(
                treeData,
                hookType,
                null,
                setTreeData,
                +nodeId,
                curOsType,
                setCurrentNode
            );
        } catch (err) {}
    };

    // 修改渲染步骤列表的辅助函数
    const renderStepGroup = (hookType, steps, showHeader = false, stepTemplateId = null) => {
        if (!stepTemplateId) {
            return null;
        }
        // 测试片段不展示setup和teardown
        if (editType === 'template' && hookType !== HOOK_TYPE.step) {
            return null;
        }
        const groupType = HOOK_TYPE_TEXT[hookType];
        const isCollapsed = collapsedGroups[groupType];

        return (
            <div className={styles.stepGroup}>
                {showHeader && editType !== 'template' && (
                    <div className={styles.stepGroupHeader}>
                        <div
                            className={styles.headerLeft}
                            onClick={() => toggleCollapse(groupType)}
                        >
                            <span className={styles.collapseIcon}>
                                {isCollapsed ? <RightOutlined /> : <DownOutlined />}
                            </span>
                            <span className={styles.stepGroupTitle}>{groupType.toUpperCase()}</span>
                        </div>
                        <div className={styles.headerRight}>
                            {[HOOK_TYPE.setup, HOOK_TYPE.teardown]?.includes(hookType) && (
                                <Button
                                    type="text"
                                    size="small"
                                    color="danger"
                                    onClick={(e) => {
                                        e.stopPropagation(); /* 阻止点击事件冒泡到header */
                                        /* 删除对应类型步骤的逻辑 */
                                        handleDeleteHook(hookType);
                                    }}
                                >
                                    删除
                                </Button>
                            )}
                            <Dropdown
                                menu={{
                                    items: AddServiceOptions.map((v) => {
                                        return {
                                            key: v.key,
                                            label: (
                                                <div
                                                    style={{
                                                        width: '100%'
                                                    }}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                    }}
                                                >
                                                    <InterfaceStep
                                                        {...props}
                                                        type={v.key}
                                                        commonParams={commonParams}
                                                        curOsType={curOsType}
                                                        nodeId={stepTemplateId}
                                                        currentStep={currentStep}
                                                        stepList={stepList}
                                                        treeData={treeData}
                                                        setTreeData={setTreeData}
                                                        hookType={groupType}
                                                        handleUpdateStepList={handleUpdateStepList}
                                                        extra={
                                                            <div className={styles.textIconContent}>
                                                                {v.label}
                                                            </div>
                                                        }
                                                    />
                                                </div>
                                            )
                                        };
                                    })
                                }}
                            >
                                <Button
                                    type="text"
                                    size="small"
                                    color="primary"
                                    onClick={(e) => {
                                        e.stopPropagation(); /* 阻止点击事件冒泡到header */
                                        /* 添加对应类型步骤的逻辑 */
                                    }}
                                >
                                    添加
                                </Button>
                            </Dropdown>
                        </div>
                    </div>
                )}
                {!isCollapsed && (
                    <div className={styles.stepGroupContent}>
                        {isEmpty(steps) ? (
                            <NoContent text="暂无步骤" />
                        ) : (
                            <StepListWithCommon
                                {...props}
                                showAddGroup={false}
                                onChangeStep={(step) => {
                                    setCurrentStep(step);
                                }}
                                currenStep={currentStep}
                                setCurrentStep={setCurrentStep}
                                stepList={stepList}
                                moduleStepList={steps}
                                hookType={HOOK_TYPE_TEXT[HOOK_TYPE.step]}
                            />
                        )}
                    </div>
                )}
            </div>
        );
    };

    const setupId = (currentNode?.extra?.setupInfo || {})?.[convertOsTypeToType(curOsType)];
    const teardownId = (currentNode?.extra?.teardownInfo || {})?.[convertOsTypeToType(curOsType)];

    return (
        <>
            {renderStepGroup(
                HOOK_TYPE.setup,
                stepList.filter((step) => step.hookType === HOOK_TYPE_TEXT[HOOK_TYPE.setup]),
                true,
                setupId
            )}
            {renderStepGroup(
                HOOK_TYPE.step,
                stepList.filter(
                    (step) => step.hookType === HOOK_TYPE_TEXT[HOOK_TYPE.step] || !step.hookType
                ),
                setupId || teardownId,
                caseNodeId
            )}
            {renderStepGroup(
                HOOK_TYPE.teardown,
                stepList.filter((step) => step.hookType === HOOK_TYPE_TEXT[HOOK_TYPE.teardown]),
                true,
                teardownId
            )}
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    defaultConfig: state.common.base.defaultConfig,
    pageSourceSwitch: state.common.case.pageSourceSwitch,
    caseConfig: state.common.case.caseConfig,
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace,
    currentModule: state.common.base.currentModule,
    showModal: state.common.base.showModal,
    testStep: state.common.case.testStep,
    testRes: state.common.case.testRes,
    serverTestRes: state.common.case.serverTestRes,
    copyStep: state.common.case.copyStep,
    treeData: state.common.case.treeData,
    envList: state.common.case.envList,
    snippetList: state.common.case.snippetList,
    appList: state.common.case.appList,
    schemeList: state.common.case.schemeList,
    currentNode: state.common.case.currentNode
}))(StepList);
