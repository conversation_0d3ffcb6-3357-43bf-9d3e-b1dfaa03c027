export const debugData = {
    "envDetail": { // 环境配置
        "envId": 3,
        "appList": [{
            "appId": 1157,
            "appName": "1",
            "envValue": "1",
            "packageList": ["1"]
        }],
        "paramList": [{
            "envValue": "8853105343",
            "paramId": 106,
            "paramKey": "roomId",
            "paramValue": "8853105343"
        }],
        "serverList": [{
            "addressList": ["http://127"],
            "envValue": "http://127",
            "serverId": 1,
            "serverName": "测试环境"
        }],
        "dbList": [{ // db环境配置
            "dbId": 123, // serverId
            "dbName": "tet", // db名称
            "envValue": "tab1", // 当前环境值 
            "dbConfig": {
                "tabl": {
                    "remark": "xxx", // 备注
                    "type": 1, // 数据库类型 1-mysql 2-BaikaiDB
                    "version": 1, // 数据库版本 1-mysql5 2-mysql8
                    "host": "0.0.0.0",
                    "port": 8000,
                    "username": "", // 用户名
                    "password": "", // 密码 （注意加密）
                    "dataBase": "", // 数据库名称
                    "encoding": 1, // 1-ASCII 2-ISO-8859 3-UTF-8 4-GBK
                }
            }
        }],
        "redisList": [{ // redis环境配置
            "redisId": 123, // serverId
            "redisName": "tet", // db名称  
            "envValue": "tab1", // 当前环境值
            "redisConfig": {
                "tabl": {
                    "remark": "xxx", // 备注
                    "host": "0.0.0.0",
                    "port": 8000,
                    "username": "", // 用户名
                    "password": "" // 密码 （注意加密）
                }
            }
        }],
        "updateTime": 1736220850,
        "updateUser": "maxiaolong01"
    },
    "userServerList": [ // 个人服务配置
        {
            "serverId": 1, // serverId
            "envValue": "https://qamate.baidu-int.com" // 当前环境值       
        }
    ],
    "caseList": [{ // 用例内容
        "caseNodeId": 111,
        "nodeName": "setup节点",
        "nodeType": 1,
        "creationType": 0,
        "adoptionType": 0,
        "creationFrom": 0,
        "isDescription": false,
        "parentId": 0,
        "extra": {},
        "children": [],
        "debugNodeType": 0,
        "step": [{
            "stepType": 1001,
            "stepDesc": "新建步骤",
            "stepId": 227889,
            "stepInfo": {
                method: 1
            },
            "stepAttr": { // 步骤额外信息
                "disabledInfo": {
                    "status": false, // 是否禁用；boolean
                    "type": 1, // 1-全局禁用，当disabled为true时关注
                    "message": "test" // // 禁用描述；当disabled为true时关注
                }
            }
        }]
    },
    {
        "caseNodeId": 39327102,
        "nodeName": "马小龙的测试用例",
        "nodeType": 1,
        "creationType": 0,
        "adoptionType": 0,
        "creationFrom": 0,
        "isDescription": false,
        "parentId": 0,
        "extra": {
            "setup": {
                "server": 111
            },
            "teardown": { // afterAll
                "server": 456
            },
        },
        "children": [],
        "step": [{
            "stepType": 1001,
            "stepDesc": "新建步骤",
            "stepId": 227890,
            "stepInfo": {
                method: 1
            },
            "stepAttr": { // 步骤额外信息
                "disabledInfo": {
                    "status": false, // 是否禁用；boolean
                    "type": 1, // 1-全局禁用，当disabled为true时关注
                    "message": "test" // // 禁用描述；当disabled为true时关注
                }
            }
        }, {
            "stepType": 1101,
            "stepDesc": "新建步骤",
            "stepId": 227891,
            "stepInfo": {
                method: 1
            },
            "stepAttr": { // 步骤额外信息
                "disabledInfo": {
                    "status": true, // 是否禁用；boolean
                    "type": 1, // 1-全局禁用，当disabled为true时关注
                    "message": "test" // // 禁用描述；当disabled为true时关注
                }
            }
        }, {
            "stepType": 1201,
            "stepDesc": "新建步骤",
            "stepId": 227892,
            "stepInfo": {
                method: 1
            },
            "stepAttr": { // 步骤额外信息
                "disabledInfo": {
                    "status": true, // 是否禁用；boolean
                    "type": 1, // 1-全局禁用，当disabled为true时关注
                    "message": "test" // // 禁用描述；当disabled为true时关注
                }
            }
        }]
    }, {
        "caseNodeId": 456,
        "nodeName": "teardown节点",
        "nodeType": 2,
        "creationType": 0,
        "adoptionType": 0,
        "creationFrom": 0,
        "isDescription": false,
        "parentId": 39327102,
        "extra": {},
        "updateUser": "maxiaolong01",
        "updateTime": 1737599548,
        "children": [],
        "step": []
    }]
};

export const debugResMockHTTP = {
    'debugId': '123123', // 前端的debugId
    'status': 2, // 任务状态, 0-执行中 1-执行失败 2-执行成功 3-执行异常 4-主动取消
    'errorInfo': {
        'code': 100, // 执行端的code；int
        'message': '描述信息', // 任务描述信息；string；（ 250个字符内）
        'errorStack': '' // 错误堆栈信息
    },
    'stepList': [ // 步骤列表
        {
            'stepId': 227889, // 步骤id；int
            'status': 0, // 任务状态； 0-成功 -1-执行失败 1-执行异常
            'result': {
                // 'resultInfo': 'https://bj.bcebos.com' // 步骤的结果bos文件
                resultInfo: {
                    'stepId': 227889,
                    'stepType': 1001,
                    'stepDesc': '',
                    'stepInfo': {},
                    // 上述结构是步骤结构 步骤的结果绑定到用例结构中
                    // 下述结构是步骤结果结构
                    'result': {
                        'code': 0, // 具体错误码
                        'status': 0, // 步骤执行状态  0:成功 1:失败 2:异常 3:运行中
                        'stepResult': { // 步骤结果
                            'startTime': 12, // 步骤开始时间，毫秒
                            'endTime': 12, // 步骤结束时间，毫秒
                            'duration': 12, // 步骤耗时，秒
                            'result': 1, // 步骤执行状态 0-成功 1-失败 2-异常
                            'errorMsg': '', // 步骤错误码概述信息
                            'errorStack': '', // 步骤错误堆栈
                            'errorCode': '', // 步骤错误码 默认 -1
                            'assertResult': { // 步骤断言结果
                                'result': 1, // 断言结果 0-成功 1-失败 2-异常
                                'assertResult': [
                                    {
                                        'assertType': 1,
                                        assertResult: {
                                            jsonPath: '123123',
                                            'isPass': 1, // 断言状态 0-通过 1-不通过
                                            'data': '222', // 预期内容
                                            'assertedContent': '123', // 断言内容
                                            'type': 1 // 断言类型 1-包含 2-正则
                                        }
                                    },
                                    {
                                        'assertType': 2,
                                        assertResult: {
                                            'isPass': 1, // 断言状态 0-通过 1-不通过
                                            'data': '222', // 预期内容
                                            'assertedContent': '123', // 断言内容
                                            'type': 1 // 断言类型 1-包含 2-正则
                                        }
                                    }
                                ]
                            },
                            'invocationResult': [{ // 步骤执行结果
                                request: {
                                    'host': 'http://127.0.01', // 请求IP
                                    'path': '/api/v1', // 请求路径
                                    'method': 1, // 请求方法
                                    'headers': [ // 请求头
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        }
                                    ],
                                    'query': [ // 请求参数
                                        {
                                            'name': 'data',
                                            'value': '1'
                                        }
                                    ],
                                    'body': {
                                        'type': 2, // 请求体类型 1-none 2-FormData 3-fromUrlEncode 4-json 5-raw
                                        'json': '', // 请求体 JSON 字符
                                        'formData': [
                                            {
                                                'name': 'data',
                                                'value': '1'
                                            }
                                        ]
                                    }
                                },
                                response: {
                                    'headers': [ // 请求头
                                        {
                                            'name': 'Content-Type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-typecontent-typecontent-typecontent-typecontent-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        },
                                        {
                                            'name': 'content-type',
                                            'value': 'application/json'
                                        }
                                    ],
                                    'body': { // 响应体
                                        reponseData: "{\n'type': 1, \n'json': '123', \n'children': {\n'a': 1\n}\n}"
                                    },
                                    'statusCode': 200, // 状态码
                                    'size': 13, // 响应体大小(MB)
                                    'remoteIp': '', // 响应IP
                                    duration: 2 // 耗时(ms)
                                },
                                duration: 201
                            }],
                            'postOperationResult': [{ // 后置操作结果
                                // 后置操作结果
                            }],
                        }
                    }
                }
            },
            'createTime': 1730101260 // 创建时间
        }
    ]
};

export const debugResMockSQL = {
    'debugId': '123123', // 前端的debugId
    'status': 2, // 任务状态, 0-执行中 1-执行失败 2-执行成功 3-执行异常 4-主动取消
    'errorInfo': {
        'code': 100, // 执行端的code；int
        'message': '描述信息', // 任务描述信息；string；（ 250个字符内）
        'errorStack': '' // 错误堆栈信息
    },
    'stepList': [ // 步骤列表
        {
            'stepId': 227889, // 步骤id；int
            'status': 0, // 任务状态； 0-成功 -1-执行失败 1-执行异常
            'result': {
                // 'resultInfo': 'https://bj.bcebos.com' // 步骤的结果bos文件
                resultInfo: {
                    'stepId': 227889,
                    'stepType': 1101,
                    'stepDesc': '',
                    'stepInfo': {},
                    // 上述结构是步骤结构 步骤的结果绑定到用例结构中
                    // 下述结构是步骤结果结构
                    'result': {
                        'code': 0, // 具体错误码
                        'status': 0, // 步骤执行状态  0:成功 1:失败 2:异常 3:运行中
                        'stepResult': { // 步骤结果
                            'startTime': 12, // 步骤开始时间，毫秒
                            'endTime': 12, // 步骤结束时间，毫秒
                            'duration': 12, // 步骤耗时，秒
                            'result': 1, // 步骤执行状态 0-成功 1-失败 2-异常
                            'errorMsg': '', // 步骤错误码概述信息
                            'errorStack': '', // 步骤错误堆栈
                            'errorCode': '', // 步骤错误码 默认 -1
                            'assertResult': { // 步骤断言结果
                                'result': 1, // 断言结果 0-成功 1-失败 2-异常
                                'assertResult': [
                                    {
                                        'assertType': 1,
                                        assertResult: {
                                            jsonPath: '123123',
                                            'isPass': 1, // 断言状态 0-通过 1-不通过
                                            'data': '222', // 预期内容
                                            'assertedContent': '123', // 断言内容
                                            'type': 1 // 断言类型 1-包含 2-正则
                                        }
                                    },
                                    {
                                        'assertType': 2,
                                        assertResult: {
                                            'isPass': 1, // 断言状态 0-通过 1-不通过
                                            'data': '222', // 预期内容
                                            'assertedContent': '123', // 断言内容
                                            'type': 1 // 断言类型 1-包含 2-正则
                                        }
                                    }
                                ]
                            },
                            'invocationResult': [{ // 步骤执行结果
                                request: {
                                    'type': 1, // 数据库类型 1-mysql 2-BaikaiDB
                                    'version': 1, // 数据库版本 1-mysql5 2-mysql8
                                    'host': '0.0.0.0',
                                    'port': 8000,
                                    'username': '', // 用户名
                                    'dataBase': '', // 数据库名称
                                    'encoding': 1, // 1-ASCII 2-ISO-8859 3-UTF-8 4-GBK
                                },
                                response: {
                                    'selectResult': "{\n'a': 1\n}", // jsonstring, sql 查询结果
                                    'affectedRows': 1, // 写 SQL 影响的行数
                                    'msg': 'success' //  SQL执行成功是 success， 否则是错误信息
                                },
                            }],
                            'postOperationResult': [{ // 后置操作结果
                                // 后置操作结果
                            }],
                        }
                    }
                }
            },
            'createTime': 1730101260 // 创建时间
        }
    ]
};

export const debugResMocRedis = {
    'debugId': '123123', // 前端的debugId
    'status': 2, // 任务状态, 0-执行中 1-执行失败 2-执行成功 3-执行异常 4-主动取消
    'errorInfo': {
        'code': 100, // 执行端的code；int
        'message': '描述信息', // 任务描述信息；string；（ 250个字符内）
        'errorStack': '' // 错误堆栈信息
    },
    'stepList': [ // 步骤列表
        {
            'stepId': 227889, // 步骤id；int
            'status': 0, // 任务状态； 0-成功 -1-执行失败 1-执行异常
            'result': {
                // 'resultInfo': 'https://bj.bcebos.com' // 步骤的结果bos文件
                resultInfo: {
                    'stepId': 227889,
                    'stepType': 1101,
                    'stepDesc': '',
                    'stepInfo': {},
                    // 上述结构是步骤结构 步骤的结果绑定到用例结构中
                    // 下述结构是步骤结果结构
                    'result': {
                        'code': 0, // 具体错误码
                        'status': 0, // 步骤执行状态  0:成功 1:失败 2:异常 3:运行中
                        'stepResult': { // 步骤结果
                            'startTime': 12, // 步骤开始时间，毫秒
                            'endTime': 12, // 步骤结束时间，毫秒
                            'duration': 12, // 步骤耗时，秒
                            'result': 1, // 步骤执行状态 0-成功 1-失败 2-异常
                            'errorMsg': '', // 步骤错误码概述信息
                            'errorStack': '', // 步骤错误堆栈
                            'errorCode': '', // 步骤错误码 默认 -1
                            'assertResult': { // 步骤断言结果
                                'result': 1, // 断言结果 0-成功 1-失败 2-异常
                                'assertResult': [
                                    {
                                        'assertType': 1,
                                        assertResult: {
                                            jsonPath: '123123',
                                            'isPass': 1, // 断言状态 0-通过 1-不通过
                                            'data': '222', // 预期内容
                                            'assertedContent': '123', // 断言内容
                                            'type': 1 // 断言类型 1-包含 2-正则
                                        }
                                    },
                                    {
                                        'assertType': 2,
                                        assertResult: {
                                            'isPass': 1, // 断言状态 0-通过 1-不通过
                                            'data': '222', // 预期内容
                                            'assertedContent': '123', // 断言内容
                                            'type': 1 // 断言类型 1-包含 2-正则
                                        }
                                    }
                                ]
                            },
                            'invocationResult': [{ // 步骤执行结果
                                request: {
                                    'host': '0.0.0.0', // redis-server 地址
                                    'port': 8000, // redis-server 端口
                                    'username': '', // 用户名
                                    'redisStatement': 'SELECT * FROM user' // redis 语句
                                },
                                response: {
                                    'exeResult': '', // redis 语句执行结果
                                    'msg': 'success' //  redis 执行信息，成功是 success， 否则是错误信息
                                },
                            }],
                            'postOperationResult': [{ // 后置操作结果
                                // 后置操作结果
                            }],
                        }
                    }
                }
            },
            'createTime': 1730101260 // 创建时间
        }
    ]
};