@import "RESOURCES/css/common.less";
@import "RESOURCES/css/resizableBox.less";

.layoutContainer {
    position: relative;
    height: 100%;
    background-color: transparent;
}

.noContent {
    margin-top: 200px;
}

.caseDesc {
    float: left;
    width: 50%;
}

.caseStep {
    width: 100%;
}

.stepDetail {
    position: relative;
    width: 100%;
    // height: 100%;
    border-radius: 5px;
}

.debugInfo {
    height: calc(100% - 150px);
}

.delete {
    color: red;
}

.operatorCut {
    color: var(--border-color);
    margin: 0 0 0 -4px;
}

.testIcon {
    margin-right: 0;
}

.layoutFooter {
    :global {

        .ant-select-selection-item,
        .ant-select,
        .custom-default-select,
        .custom-dark-select {
            padding: 0 !important;
            font-size: 12px !important;
            color: #777 !important;
        }

        .ant-select-single.ant-select-sm .ant-select-selector {
            font-size: 12px;
        }

        .ant-select-selection-overflow,
        .custom-default-select-selection-overflow,
        .custom-dark-select-selection-overflow {
            padding: 0 !important;
            font-size: 12px !important;
            color: #777 !important;
        }
    }
}


.paramsSelect {
    :global {
        .ant-select-selection-placeholder {
            font-size: 12px !important;
        }
    }
}

.runSettingBtn {
    position: absolute;
    right: 15px;
    top: 15px;
    background-color: #000;
    cursor: pointer;
}

.reRunSettingBtn,
.runSettingBtn {
    margin-left: 5px;
    padding: 3px 5px;
    font-size: 12px;
    background-color: var(--primary-color);
    border-radius: 3px;
    color: #fff;
}

.reRunSettingBtn {
    background-color: var(--error-color);
}

.batchStepSwitch {
    float: left;
}


.stepGroup {
    margin-bottom: 10px;
    background-color: #ffffff;
    border-radius: 4px;
    overflow: hidden;
}

.stepGroupHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 12px;
    background-color: rgba(247, 247, 247, 0.965);
    cursor: pointer;
    user-select: none;
    margin-bottom: 8px;
}

.headerLeft {
    display: flex;
    align-items: center;
}

.headerRight {
    display: flex;
    margin-left: auto;
}

.collapseIcon {
    margin-right: 6px;
    font-size: 12px;
    color: #333;
}

.stepGroupTitle {
    font-weight: bold;
    color: #333;
}

.stepGroupContent {
    /* 移除边框 */
    border: none;
}

.addBtn,
.deleteBtn {
    font-size: 12px;
    height: 24px;
    padding: 0 8px;
    margin-left: 4px;
}

.deleteBtn {
    color: #ff4d4f;
}

.contentItem {
    padding: 0 15px;
}