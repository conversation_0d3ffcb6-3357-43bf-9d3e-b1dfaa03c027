import { message } from 'antd';
import electron from 'COMMON/utils/electron';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { base64ImageUpload } from 'COMMON/utils/utils';
import { createAIStepInitConfig, handleCreateStep } from './utils';
import styles from './StepInit.module.less';

function InitAIStep(props) {
    const {
        nodeId,
        extra,
        curOsType,
        currentDevice,
        deviceList,
        editType,
        setRecording,
        treeData,
        setTreeData,
        stepList,
        currentStep,
        type,
        handleUpdateStepList,
        currentSpace
    } = props;
    const [messageApi, contextHolder] = message.useMessage();

    const addStep = async () => {
        let timer = null;
        try {
            // 确定有设备连接
            let gotDevice = false;
            for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                if (device.deviceId === currentDevice?.deviceId) {
                    gotDevice = true;
                    if (![2].includes(device?.status)) {
                        messageApi.error('请确保设备状态正常');
                        return false;
                    }
                    break;
                }
            }
            if (!gotDevice) {
                messageApi.error('请确保有设备连接');
                return false;
            }
            // 开启录制模式
            setRecording(true);
            let { screenshot, screenSize } = await electron.send('device.record.screenshot', {
                deviceType: 2 === +curOsType ? 'ios' : 'android',
                deviceId: currentDevice?.deviceId
            });

            let { step, stepType, stepDesc } = createAIStepInitConfig({
                type: type,
                widgetParams: props.widgetParams,
                commonParams: props.commonParams
            });
            // 上传图片
            base64ImageUpload(screenshot)
                .then(async (res) => {
                    let bosUrl = res?.url;
                    for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                        let recordInfo = {
                            deviceInfo: {
                                type: 2 === +curOsType ? 'ios' : 'android',
                                screenSize,
                                screenshot: bosUrl
                            }
                        };
                        if (device.deviceId === currentDevice?.deviceId) {
                            step.params.recordInfo = recordInfo;
                            break;
                        }
                    }
                    await handleCreateStep({
                        caseNodeId: nodeId,
                        stepDesc,
                        step,
                        curOsType,
                        stepType,
                        treeData,
                        setTreeData,
                        stepList,
                        currentStep,
                        handleUpdateStepList,
                        currentSpace
                    });
                })
                .catch((err) => {
                    console.log(err?.message ?? err);
                    messageApi.error(err?.message ?? err);
                });
        } catch (err) {
            message.error(err?.message ?? err);
        } finally {
            setRecording(false);
            clearInterval(timer);
        }
    };

    return (
        <>
            {contextHolder}
            <span
                onClick={(e) => {
                    e.stopPropagation();
                    if (['readonly']?.includes(editType)) {
                        messageApi.warning('当前模式下，仅只读');
                        return false;
                    }
                    if (!isElectron()) {
                        messageApi.warning('Web 不支持步骤创建');
                        return false;
                    }
                    addStep();
                }}
                className={styles.stepInitName}
            >
                {extra}
            </span>
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    deviceList: state.common.base.deviceList,
    appList: state.common.case.appList,
    recording: state.common.base.recording,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    treeData: state.common.case.treeData,
    currentSpace: state.common.base.currentSpace
}))(InitAIStep);
