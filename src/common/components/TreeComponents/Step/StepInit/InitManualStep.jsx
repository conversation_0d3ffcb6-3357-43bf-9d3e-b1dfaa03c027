import {useState, useEffect} from 'react';
import {message} from 'antd';
import {getQueryParams} from 'COMMON/utils/utils';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {createManualStepInitConfig, handleCreateStep} from './utils';
import styles from './StepInit.module.less';

function InitManualStep(props) {
    const {nodeId, extra, curOsType, editType,
        setRecording, treeData, setTreeData,
        stepList, currentStep, type,
        handleUpdateStepList, currentSpace} = props;
    const [messageApi, contextHolder] = message.useMessage();

    const addStep = async () => {
        try {
            setRecording(true);
            let {step, stepType, stepDesc} = createManualStepInitConfig({
                type: type,
                commonParams: props.commonParams,
            });
            await handleCreateStep({
                caseNodeId: nodeId,
                stepDesc,
                step, curOsType, stepType,
                treeData, setTreeData,
                stepList, currentStep,
                handleUpdateStepList,
                currentSpace
            });
        } catch (err) {
        } finally {
            setRecording(false);
        }
    };

    return (
        <span
            onClick={(e) => {
                e.stopPropagation();
                if (['readonly']?.includes(editType)) {
                    messageApi.warning('当前模式下，仅只读');
                    return false;
                }
                addStep();
            }}
            className={styles.stepInitName}
        >
            {contextHolder}
            {extra}
        </span>
    );
};

export default connectModel([baseModel, commonModel], state => ({
    deviceList: state.common.base.deviceList,
    appList: state.common.case.appList,
    recording: state.common.base.recording,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    treeData: state.common.case.treeData,
    currentSpace: state.common.base.currentSpace,
}))(InitManualStep);