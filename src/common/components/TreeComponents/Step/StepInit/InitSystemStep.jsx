import {message} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {createSystemStepInitConfig, handleCreateStep} from './utils';
import styles from './StepInit.module.less';

function InitSystemStep(props) {
    const {nodeId, type, extra, curOsType, setRecording,
        treeData, setTreeData, editType,
        stepList, currentStep, appList,
        handleUpdateStepList, currentSpace} = props;
    const [messageApi, contextHolder] = message.useMessage();

    const addStep = async () => {
        try {
            // 开启录制模式
            setRecording(true);
            let {step, stepType, stepDesc} = createSystemStepInitConfig({
                type: type,
                curOsType: curOsType,
                appList: appList,
                commonParams: props.commonParams,
            });
            await handleCreateStep({
                caseNodeId: nodeId,
                stepDesc,
                step, curOsType, stepType,
                treeData, setTreeData,
                stepList, currentStep,
                handleUpdateStepList,
                currentSpace
            });
        } catch (err) {
        } finally {
            setRecording(false);
        }
    };
    return (
        <span
            className={styles.stepInitName}
            onClick={(e) => {
                e.stopPropagation();
                if (['readonly']?.includes(editType)) {
                    messageApi.warning('当前模式下，仅只读');
                    return false;
                }

                if (!isElectron() && curOsType !== 4) {
                    messageApi.warning('当前环境不支持步骤创建');
                    return false;
                }
                addStep();
            }}
        >
            {contextHolder}
            {extra}
        </span>
    );
};

export default connectModel([baseModel, commonModel], state => ({
    deviceList: state.common.base.deviceList,
    appList: state.common.case.appList,
    recording: state.common.base.recording,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    treeData: state.common.case.treeData,
    currentSpace: state.common.base.currentSpace,
}))(InitSystemStep);
