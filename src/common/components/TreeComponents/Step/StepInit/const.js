export const getStepInitActionParamms = (type, widgetParams) => {
    // 初始参数
    let _stepInfo = {
        before: {
            sleep: widgetParams?.beforeActionWait * 1000 ?? 0
        }
    };
    let _findParams = {
        times: widgetParams?.retryTimes ?? 0,
        before: {
            wait: widgetParams?.beforeReplayWait * 1000 ?? 0
        }
    };
    let _findInfo = {
        screenCount: widgetParams?.screenCount ?? 1
    };
    if (widgetParams?.screenCount > 1) {
        _findInfo = {
            ..._findInfo,
            findType: 2,
            scrollRatio: 25,
            scrollDirection: 1,
            scrollType: 2
        };
    }
    if ('tap' === type) {
        _stepInfo = {
            ..._stepInfo,
            type: 'tap',
            params: {},
        };
    } else if ('addTap' === type) {
        _stepInfo = {
            ..._stepInfo,
            type: 'tap',
            params: {
                duration: 300,
            },
        };
    } else if ('input' === type) {
        _stepInfo = {
            ..._stepInfo,
            type: 'input',
            params: {
                text: '',
            },
        };
    } else if ('nope' === type) {
        _stepInfo = {
            ..._stepInfo,
            type: 'nope',
            params: {},
        };
    } else if ('absence' === type) {
        _stepInfo = {
            ..._stepInfo,
            type: 'absence',
            params: {},
        };
    } else if ('swipe' === type) {
        _stepInfo = {
            ..._stepInfo,
            type: 'swipe',
            params: {
                direction: 1, // 滑动专用 1 从下到上；2 从上到下；3 从左到右；4 从右到左
                duration: 500, // 单位 ms（ios 中 500ms以下都是短按），默认 100
                times: 1,
            },
        };
    }
    return {_stepInfo, _findParams, _findInfo};
};