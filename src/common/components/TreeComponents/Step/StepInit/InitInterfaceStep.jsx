import { useState, useEffect } from 'react';
import { message } from 'antd';
import { getQueryParams } from 'COMMON/utils/utils';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { createInterfaceStepInitConfig, handleCreateStep } from './utils';
import styles from './StepInit.module.less';

function InitInterfaceStep(props) {
    const {
        nodeId,
        extra,
        curOsType,
        setRecording,
        treeData,
        setTreeData,
        stepList,
        currentStep,
        type,
        handleUpdateStepList,
        serverList,
        currentSpace,
        hookType,
        dbList,
        redisList,
        editType
    } = props;
    const [messageApi, contextHolder] = message.useMessage();

    const addStep = async () => {
        try {
            setRecording(true);
            let { step, stepType, stepDesc } = createInterfaceStepInitConfig({
                type: type,
                commonParams: props.commonParams,
                serverList: serverList,
                dbList: dbList?.dbList,
                redisList: redisList?.redisList
            });
            await handleCreateStep({
                caseNodeId: nodeId,
                stepDesc,
                step,
                curOsType,
                stepType,
                treeData,
                setTreeData,
                stepList,
                currentStep,
                handleUpdateStepList,
                currentSpace,
                hookType
            });
        } catch (err) {
            console.log('err', err);
        } finally {
            setRecording(false);
        }
    };

    return (
        <span
            onClick={(e) => {
                e.stopPropagation();
                if (['readonly']?.includes(editType)) {
                    messageApi.warning('当前模式下，仅只读');
                    return false;
                }
                if (global.params.CASE_STATUS !== 'edit') {
                    messageApi.warning('暂无权限创建');
                    return false;
                }
                addStep();
            }}
            className={styles.stepInitName}
        >
            {contextHolder}
            {extra}
        </span>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    deviceList: state.common.base.deviceList,
    appList: state.common.case.appList,
    recording: state.common.base.recording,
    currentDevice: state.common.base.currentDevice,
    treeData: state.common.case.treeData,
    serverList: state.common.case.serverList,
    currentSpace: state.common.base.currentSpace,
    dbList: state.common.case.dbList,
    redisList: state.common.case.redisList
}))(InitInterfaceStep);
