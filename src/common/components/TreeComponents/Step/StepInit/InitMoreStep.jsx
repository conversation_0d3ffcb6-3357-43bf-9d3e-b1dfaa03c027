import {message, Popconfirm} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {createMoreStepInitConfig, handleCreateStep} from './utils';
import styles from './StepInit.module.less';

function InitMoreStep(props) {
    const {nodeId, type, extra, curOsType, setRecording,
        treeData, setTreeData, editType,
        stepList, currentStep, appList, currentSpace,
        handleUpdateStepList} = props;
    const [messageApi, contextHolder] = message.useMessage();

    const addStep = async () => {
        try {
            // 开启录制模式
            setRecording(true);
            let {step, stepType, stepDesc} = createMoreStepInitConfig({
                type: type,
                curOsType: curOsType,
                appList: appList,
                commonParams: props.commonParams,
            });
            await handleCreateStep({
                caseNodeId: nodeId,
                stepDesc,
                step, curOsType, stepType,
                treeData, setTreeData,
                stepList, currentStep,
                handleUpdateStepList,
                currentSpace
            });
        } catch (err) {
        } finally {
            setRecording(false);
        }
    };
    const getStepType = () => {
        switch (type) {
            case 'installApp':
                return '安装 APP';
            case 'login':
                return '登录账号';
            default:
                return '';
        }
    };
    return (
        <Popconfirm
            title={`新增【${getStepType()}】步骤`}
            description="该步骤地添加可能会对用例执行时长产生很大的影响，确认添加?"
            onConfirm={(e) => {
                e.stopPropagation();
                if (['readonly']?.includes(editType)) {
                    messageApi.warning('当前模式下，仅只读');
                    return false;
                }

                if (!isElectron() && curOsType !== 4) {
                    messageApi.warning('当前环境不支持步骤创建');
                    return false;
                }
                addStep();
            }}
            okText="添加"
            cancelText="取消"
        >
            <span
                className={styles.stepInitName}
            >
                {contextHolder}
                {extra}
            </span>
        </Popconfirm>
    );
};

export default connectModel([baseModel, commonModel], state => ({
    deviceList: state.common.base.deviceList,
    appList: state.common.case.appList,
    recording: state.common.base.recording,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    treeData: state.common.case.treeData,
    currentSpace: state.common.base.currentSpace,
}))(InitMoreStep);
