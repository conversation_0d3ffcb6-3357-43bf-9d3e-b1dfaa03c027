@import "RESOURCES/css/common.less";

.layoutSider {
    width: 100%;
    height: 50px !important;
    padding: 5px;
    background-color: transparent;
    border-bottom: 1px solid var(--border-color);
}

.stepInitList {
    margin-left: 120px;
}

.stepInitItem {
    float: left;
    width: 50px;
    height: 40px;
    position: relative;
    padding: 5px;
    margin: 0 1px;
    color: rgb(149, 146, 146);
    text-align: center;
    cursor: pointer;
    border-radius: 5px;

    .stepInitName {
        display: block;
        font-size: 11px;
    }

    .stepInitNameWithAI {
        color: rgba(65, 69, 216, 0.7);
    }
}

.stepInitAllowItem {
    &:hover {
        color: #3686f6;
        background-color: #e6f5fe;

        .stepInitSteps,
        .stepInfoSteps {
            display: block;
        }
    }
}

.stepInitNotAllowItem {
    color: #878d8e70;
}

.icon {
    display: block;
    font-size: 14px;
    margin-bottom: 5px;
}

.imgIcon {
    display: block;
    font-size: 14px;
    margin-bottom: 3px;
}

.textIconContent {
    display: flex;
    flex-direction: column;
    align-items: center;

    .textIcon {
        // display: block;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 14px;
        height: 14px;
        font-size: 14px;
        margin-bottom: 5px;
    }

    .text {
        font-size: 14px;
    }

}

.apiConent {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    // gap: 50px;
    // border: 1px solid red;
    font-size: 14px;
}

.stepPart {
    position: relative;
}

.stepInitSteps,
.stepInfoSteps {
    display: none;
    width: 240px;
    position: absolute;
    top: 38px;
    left: 0px;
    background-color: var(--background-color);
    z-index: 999;
    box-shadow: 0 0 3px 2px rgba(157, 157, 157, 0.12);
    border-radius: 8px;
}

.stepList {
    display: none;
}

.stepInitStep {
    margin: 5px;
    width: 70px;
    float: left;
    font-size: 12px;
    color: rgb(149, 146, 146);
    padding: 5px;
    text-align: center;
    cursor: pointer;

    &:hover {
        color: #3686f6;
        background-color: #e6f5fe;
        border-radius: 5px;
    }
}

.stepInfoSteps {
    width: 140px;
}


.stepInfo {
    margin: 5px;
    float: left;
    font-size: 12px;
    color: rgb(149, 146, 146);
    padding: 5px;
    text-align: center;
    cursor: pointer;
}

.aiStep {
    color: rgba(65, 69, 216, 0.7);

    .imgIcon {
        margin-top: -3px;
        margin-bottom: 0;
    }
}