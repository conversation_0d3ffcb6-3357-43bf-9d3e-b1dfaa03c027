import { useState, useEffect, useMemo } from 'react';
import { message, Tooltip } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { copyStepList } from 'COMMON/api/front_qe_tools/step';
import styles from './StepInit.module.less';

function InitCopyStep(props) {
    const { extra, nodeId, curOsType, refreshStepList, editType, setRecording } = props;
    const [messageApi, contextHolder] = message.useMessage();

    return (
        <Tooltip
            title={`拷贝${2 === curOsType ? ' Android 用例的' : ' iOS 用例'}的所有步骤到当前用例中`}
            placement="left"
        >
            {contextHolder}
            <span
                onClick={async (e) => {
                    try {
                        e.stopPropagation();
                        if (['readonly']?.includes(editType)) {
                            messageApi.warning('当前模式下，仅只读');
                            return false;
                        }
                        if (!isElectron()) {
                            messageApi.warning('Web 不支持步骤创建');
                            return false;
                        }
                        e.stopPropagation();
                        message.success('复制成功');
                        setRecording(true);
                        await copyStepList({
                            caseNodeId: nodeId,
                            osType: 2 === curOsType ? 1 : 2,
                            targetOsType: curOsType
                        });
                        refreshStepList(true);
                    } catch (error) {
                    } finally {
                        setRecording(false);
                    }
                }}
                className={styles.stepInitName}
            >
                {extra}
            </span>
        </Tooltip>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    caseConfig: state.common.case.caseConfig
}))(InitCopyStep);
