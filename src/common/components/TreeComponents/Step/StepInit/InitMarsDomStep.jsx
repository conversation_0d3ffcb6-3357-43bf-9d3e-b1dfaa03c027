import {message} from 'antd';
import electron from 'COMMON/utils/electron';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {clickThrottle} from 'COMMON/utils/utils';
import {checkDevice} from 'COMMON/config/device';
import {handleCreateStep, createDomStepInitConfig} from './utils';
import styles from './StepInit.module.less';

// 通用建模步骤
function InitMarsDomStep(props) {
    const {nodeId, extra, currentDevice, deviceList, curOsType, editType,
        setRecording, setRecordId, pageSourceSwitch, currentSpace,
        stepList, type, currentStep, treeData, setTreeData,
        handleUpdateStepList} = props;
    const [messageApi, contextHolder] = message.useMessage();

    const addStep = async () => {
        try {
            let step = createDomStepInitConfig({
                type: type,
                widgetParams: props.widgetParams,
                commonParams: props.commonParams,
                pageSourceSwitch: pageSourceSwitch
            });
            if (!clickThrottle(3000)) {
                messageApi.info('操作太频繁啦！');
                return false;
            }
            let newStep = await handleCreateStep({
                caseNodeId: nodeId,
                stepDesc: '新建步骤',
                step, curOsType, stepType: 201,
                treeData, setTreeData,
                stepList, currentStep,
                handleUpdateStepList,
            });
            // 确定有设备连接
            let gotDevice = false;
            for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                if (device.deviceId === currentDevice?.deviceId) {
                    gotDevice = true;
                    if (![2].includes(device?.status)) {
                        messageApi.error('请确保设备状态正常');
                        return false;
                    }
                    break;
                }
            }
            if (!gotDevice) {
                messageApi.error('请确保有设备连接');
                return false;
            }
            let data = await electron.send('device.record.dumpWidgetModel', {
                deviceType: 2 === +curOsType ? 'ios' : 'android',
                deviceId: currentDevice?.deviceId,
                stepId: newStep?.stepId,
                nodeId: nodeId,
                needUi: true,
                needPagesource: pageSourceSwitch,
                productId: currentSpace?.id,
                versionType: '3.0'
            });
            let {
                recordId
            } = data;
            setRecordId(recordId);
        } catch (err) {
            console.log(err?.message ?? err);
        } finally {
            setRecording(false);
        }
    };

    return (
        <span
            onClick={(e) => {
                e.stopPropagation();
                if (['readonly']?.includes(editType)) {
                    messageApi.warning('当前模式下，仅只读');
                    return false;
                }
                if (!isElectron()) {
                    messageApi.warning('Web 不支持步骤创建');
                    return false;
                }
                if (!checkDevice(deviceList, currentDevice, curOsType)) {
                    return;
                }
                addStep();
            }}
            className={styles.stepInitName}
        >
            {contextHolder}
            {extra}
        </span>
    );
};

export default connectModel([baseModel, commonModel], state => ({
    recording: state.common.base.recording,
    treeData: state.common.case.treeData,
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recordId: state.common.case.recordId,
    showModal: state.common.base.showModal,
    pageSourceSwitch: state.common.case.pageSourceSwitch,
}))(InitMarsDomStep);