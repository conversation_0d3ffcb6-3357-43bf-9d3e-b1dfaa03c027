import { isEmpty } from 'lodash';
import { createStep } from 'COMMON/api/front_qe_tools/step';
import { createPoint } from 'COMMON/api/front_qe_tools/points';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import { treeAction } from 'FEATURES/front_qe_tools/case/edit/EditPage/CaseDetailView/LayoutSider/utils';

// 获取步骤类型 A M R
export const getStepType = (stepList) => {
    let stepInfo = stepList.map((item) => {
        if (item.stepType === 101) {
            return 'M';
        } else if (item.stepType === 102) {
            return 'R';
        } else {
            return 'A';
        }
    });
    if (stepList.length === 0 || stepInfo.filter((i) => i === 'M').length > 0) {
        return 1;
    }
    if (stepInfo.filter((i) => i === 'R').length > 0) {
        return 4;
    }
    return 2;
};

const getActionInfo = (type) => {
    switch (type) {
        case 'tap':
            return {
                type: 'tap',
                params: {}
            };
        case 'addTap':
            return {
                type: 'tap',
                params: {
                    duration: 300
                }
            };
        case 'input':
            return {
                type: 'input',
                params: {
                    text: ''
                }
            };
        case 'nope':
            return {
                type: 'nope',
                params: {}
            };
        case 'absence':
            return {
                type: 'absence',
                params: {}
            };
        case 'swipe':
            return {
                type: 'swipe',
                params: {
                    direction: 1, // 滑动专用 1 从下到上；2 从上到下；3 从左到右；4 从右到左
                    duration: 500, // 单位 ms（ios 中 500ms以下都是短按），默认 100
                    times: 1
                }
            };
        default:
            break;
    }
};

// 生成控件步骤配置、
export const createDomStepInitConfig = (props) => {
    const { type, widgetParams, commonParams, pageSourceSwitch } = props;
    let step = {
        desc: '',
        type: 10,
        params: {},
        common: commonParams
    };
    step.params.findType = 0; // 0: 通用建模
    // recordInfo 录制结果
    step.params.recordInfo = {};
    // findParams 参数
    step.params.findParams = {
        // 滑动参数
        scroll: {
            screenCount: widgetParams?.screenCount ?? 1,
            ...(widgetParams?.screenCount > 1
                ? {
                      scrollRatio: 25,
                      scrollDirection: 1,
                      scrollType: 2
                  }
                : {})
        },
        // 重试参数
        retry: {
            times: widgetParams?.retryTimes ?? 1, // 寻找几次，默认 1 次
            interval: 1000 // 每次间隔多少，单位 ms，默认 1000ms
        },
        before: {
            wait: widgetParams?.beforeReplayWait * 1000 ?? 0 // 建模前等待多久，单位 0ms，默认 0ms
        },
        until: {
            enable: false, // 是否启用，默认 false
            times: 1, // 点击次数，默认 1 次
            interval: 2000 // 间隔，单位 ms，默认 2000ms
        },
        allowFail: false // 是否允许执行失败，默认 false
    };

    // findInfo 查找区域 和 控件定位配置
    step.params.findInfo = {
        baseInfo: {
            findType: 0,
            useFastSAM: false,
            chosenTag: ['visual', 'single'],
            modelType: pageSourceSwitch ? 2 : 0,
            findNode: []
        },
        widgetInfo: {
            findType: 0,
            useFastSAM: false,
            chosenTag: ['visual', 'single'],
            modelType: pageSourceSwitch ? 2 : 0,
            findNode: []
        }
    };

    // actionInfo 操作信息
    step.params.actionInfo = {
        before: {
            sleep: widgetParams?.beforeActionWait * 1000 ?? 0
        },
        ...getActionInfo(type)
    };
    return step;
};

// 生成屏幕步骤配置
export function createScreenStepInitConfig(props) {
    const { commonParams, type } = props;
    let step = {
        type: 4,
        desc: '',
        params: {},
        common: commonParams
    };
    let stepDesc = '新建步骤';
    let stepType = -1;
    switch (type) {
        case 'tap':
            step.params.actionInfo = {
                type: 'tap',
                params: {
                    x: 0,
                    y: 0,
                    duration: 300,
                    times: 1,
                    interval: 500
                }
            };
            stepType = 421;
            break;
        case 'swipe':
            step.params.actionInfo = {
                type: 'swipe',
                params: {
                    startX: 0,
                    startY: 0,
                    targetX: 0,
                    targetY: 0
                }
            };
            stepType = 420;
            break;
        default:
            break;
    }
    return { step, stepType, stepDesc };
}

// 生成AI步骤配置
export function createAIStepInitConfig(props) {
    const { type, widgetParams, commonParams } = props;
    let step = {
        desc: '',
        type: 3,
        params: {},
        common: commonParams
    };
    let stepDesc = '新建步骤';
    let stepType = -1;
    switch (type) {
        case 'aiLocate':
            // https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/LWyELLqriZ/bUcSHWYlCkUl2M
            step.type = 11;
            step.params.findType = 0; // 0: 智能定位
            // recordInfo 录制结果
            step.params.recordInfo = {};
            // findParams 参数
            step.params.findParams = {
                // 滑动参数
                scroll: {
                    screenCount: widgetParams?.screenCount ?? 1,
                    ...(widgetParams?.screenCount > 1
                        ? {
                              scrollRatio: 25,
                              scrollDirection: 1,
                              scrollType: 2
                          }
                        : {})
                },
                // 重试参数
                retry: {
                    times: widgetParams?.retryTimes ?? 1, // 寻找几次，默认 1 次
                    interval: 1000 // 每次间隔多少，单位 ms，默认 1000ms
                },
                before: {
                    wait: widgetParams?.beforeReplayWait * 1000 ?? 0 // 建模前等待多久，单位 0ms，默认 0ms
                },
                allowFail: false // 是否允许执行失败，默认 false
            };

            // findInfo 查找区域 和 控件定位配置
            step.params.findInfo = {
                findDesc: '',
                modelName: 'doubao-1.5-thinking-vision-pro-250428'
            };

            // actionInfo 操作信息
            step.params.actionInfo = {
                before: {
                    sleep: widgetParams?.beforeActionWait * 1000 ?? 0
                },
                ...getActionInfo('tap')
            };
            stepType = 601;
            break;
        case 'aiAssert':
            step.params = {
                type: 'aiAssert',
                params: {
                    inputAssert: '',
                    withScreenRecord: false, // 是否开启录屏校验，默认 false
                    modelName: 'doubao-1.5-vision-pro-250328',
                    imgInfo: {
                        img: '',
                        width: 0,
                        height: 0
                    }
                }
            };
            stepType = 602;
            break;
        default:
            break;
    }
    return { step, stepType, stepDesc };
}

// 生成人工步骤配置
export function createManualStepInitConfig(props) {
    const { type } = props;
    let step = {
        type: 3,
        desc: '',
        params: {}
    };
    let stepDesc = '新建步骤';
    let stepType = -1;
    switch (type) {
        case 'operator':
            step.common = {
                commonAlertClear: false
            };
            step.params = {
                type: 'operator',
                params: {
                    img: ''
                }
            };
            stepType = 101;
            break;
        case 'assest':
            step.common = props.commonParams;
            step.params = {
                type: 'assest',
                params: {
                    img: ''
                }
            };
            stepType = 102;
            break;
        default:
            break;
    }
    return { step, stepType, stepDesc };
}

// 生成接口步骤配置
export function createInterfaceStepInitConfig(props) {
    const { type, serverList = [], dbList = [], redisList = [] } = props;
    let step = {};
    let stepDesc = '新建步骤';
    let stepType = -1;
    switch (type) {
        case 'http':
            step = {
                type: 'http',
                method: 2,
                // 请求接口
                requestInterface: {
                    serverId: serverList[0]?.serverId || null,
                    path: ''
                },
                // 请求参数
                requestParams: {
                    query: [],
                    headers: [],
                    body: {
                        type: 1,
                        none: ''
                    }
                },
                // 变量提取
                variableExtract: [],
                // 返回断言
                responseAssert: [],
                // 后置操作
                postOperation: [],
                // 更多配置
                moreConfig: {
                    isRedirect: true, // 重定向，默认是true,
                    timeout: 60 // 超时时间 （s）
                }
            };
            stepType = 1001;
            break;
        case 'sql':
            step = {
                type: 'sql',
                dbId: dbList[0]?.dbId || null,
                sqlStatement: '',
                // 变量提取
                variableExtract: [],
                responseAssert: [],
                postOperation: []
            };
            stepType = 1101;
            break;
        case 'redis':
            step = {
                type: 'redis',
                redisId: redisList?.[0]?.redisId || null,
                redisStatement: '',
                // 变量提取
                variableExtract: [],
                responseAssert: [],
                postOperation: []
            };
            stepType = 1201;
            break;
        default:
            break;
    }
    return { step, stepType, stepDesc };
}

// 生成系统步骤配置
export function createSystemStepInitConfig(props) {
    const { commonParams, type } = props;
    let appName = undefined;
    let appId = undefined;
    let autoAuthApp = true;
    let step = {
        type: 1,
        desc: '',
        params: {},
        common: commonParams
    };
    let stepDesc = '新建步骤';
    let stepType = -1;

    switch (type) {
        case 'whiteScreen':
            step.params = {
                type: 'whiteScreen',
                params: {
                    ratio: 80
                }
            };
            stepType = 501;
            break;
        case 'logCheck':
            step.params = {
                type: 'logCheck',
                params: {
                    id: null, // 规则 ID
                    ubcId: null, // 点位 ID
                    name: '', // 规则名称
                    productName: '' // 产品线名称
                }
            };
            stepType = 502;
            break;
        case 'clearMock':
            step.params = {
                type: 'clearMock',
                params: {}
            };
            stepType = 415;
            break;
        case 'clearRequest':
            step.params = {
                type: 'clearRequest',
                params: {}
            };
            stepType = 416;
            break;
        case 'scheme':
            step.params = {
                type: 'scheme',
                params: {
                    id: undefined,
                    desc: '',
                    schemeContent: '',
                    wait: 3
                }
            };
            stepType = 402;
            break;
        case 'wait':
            step.params = {
                type: 'wait',
                params: {
                    seconds: 3
                }
            };
            stepType = 417;
            break;
        case 'launchApp':
            stepType = 401;
            step.params = {
                type: 'launchApp',
                params: {
                    id: appId,
                    appName,
                    isColdStart: true
                }
            };
            step.desc = !isEmpty(appName) ? '冷启动【' + appName + '】' : '';
            stepDesc = !isEmpty(appName) ? '冷启动【' + appName + '】' : '';
            break;
        // 授权APP
        case 'authApp':
            step.params = {
                type: 'authApp',
                params: {
                    id: appId,
                    appName
                }
            };
            stepType = 405;
            step.desc = !isEmpty(appName) ? '授权【' + appName + '】' : '';
            stepDesc = !isEmpty(appName) ? '授权【' + appName + '】' : '';
            break;
        case 'swipe':
            step.params = {
                type: 'swipe',
                params: {
                    interval: 1,
                    times: 1,
                    direction: 1,
                    duration: 500
                }
            };
            stepType = 410;
            break;
        case 'mock':
            step.params = {
                type: 'mock',
                params: {
                    timestamp: 0,
                    mockRequest: {
                        matchRequest: {
                            headers: {},
                            protocol: '',
                            hostname: '',
                            port: '',
                            method: '',
                            path: ''
                        },
                        responseDetail: {}
                    }
                }
            };
            stepType = 408;
            break;
        case 'requestVerify':
            step.params = {
                type: 'requestVerify',
                params: {
                    timestamp: 0,
                    mockRequest: {
                        matchRequest: {
                            headers: {},
                            protocol: '',
                            hostname: '',
                            port: '',
                            method: '',
                            path: ''
                        },
                        responseDetail: {},
                        verifyDetail: {
                            verifyType: 1,
                            query: {},
                            body: {},
                            response: {}
                        }
                    }
                }
            };
            stepType = 503;
            break;
        case 'request':
            step.params = {
                type: 'request',
                params: {
                    url: '',
                    body: '',
                    method: 'GET',
                    timeout: 3000
                }
            };
            stepType = 419;
            break;
        case 'clearPop':
            // 弹窗点除
            step.params = {
                type: 'clearPop',
                params: {
                    popList: [],
                    popDetail: []
                }
            };
            stepType = 411;
            break;
        case 'requestRedirect':
            step.params = {
                type: 'requestRedirect',
                params: {
                    timestamp: 0,
                    url: '',
                    mockRequest: {
                        matchRequest: {
                            headers: {},
                            protocol: '',
                            hostname: '',
                            port: '',
                            method: '',
                            path: ''
                        },
                        requestDetail: {
                            headers: {},
                            protocol: '',
                            hostname: '',
                            port: '',
                            path: ''
                        }
                    }
                }
            };
            stepType = 409;
            break;
        case 'runTemplate':
            // 测试片段 无点除通用弹窗能力
            if (!step.common) {
                step.common = {};
            }
            step.common.commonAlertClear = false;
            step.params = {
                type: 'runTemplate',
                params: {
                    id: null
                }
            };
            stepType = 412;
            break;
        case 'disconnect':
            step.params = {
                type: 'networkConnect',
                params: {
                    type: 0
                }
            };
            stepType = 407;
            break;
        case 'connect':
            step.params = {
                type: 'networkConnect',
                params: {
                    type: 1
                }
            };
            stepType = 407;
            break;
        case 'home':
            step.params = {
                type: 'home',
                params: {}
            };
            stepType = 403;
            break;
        case 'back':
            step.params = {
                type: 'back',
                params: {}
            };
            stepType = 423;
            break;
        case 'clearApp':
            step.params = {
                type: 'clearApp',
                params: {
                    id: appId,
                    appName,
                    autoAuthApp
                }
            };
            step.desc = !isEmpty(appName) ? '清理【' + appName + '】' : '';
            stepDesc = !isEmpty(appName) ? '清理【' + appName + '】' : '';
            stepType = 404;
            break;
        case 'pushFile':
            step.params = {
                type: 'pushFile',
                params: {
                    fileLink: '',
                    targetPath: '/sdcard/'
                }
            };
            stepType = 406;
            break;
        case 'shell':
            step.params = {
                type: 'shell',
                params: {
                    shell: ''
                }
            };
            stepType = 422;
            break;
        default:
            break;
    }
    return { step, stepType, stepDesc };
}

// 生成更多步骤配置
export function createMoreStepInitConfig(props) {
    const { commonParams, type, appList = [], curOsType } = props;
    let appName = '';
    let appId = null;
    let step = {
        type: 1,
        desc: '',
        params: {},
        common: commonParams
    };
    let stepDesc = '新建步骤';
    let stepType = -1;

    if (['installApp', 'login'].includes(type)) {
        if (0 < appList?.[curOsType]?.length) {
            appName = appList?.[curOsType]?.[0].appName ?? '';
            appId = appList?.[curOsType]?.[0].appId ?? null;
        }
    }
    switch (type) {
        // 安装 APP
        case 'installApp':
            stepType = 424;
            step.params = {
                type: 'installApp',
                params: {
                    id: appId,
                    appName,
                    fileLink: '',
                    installType: 1
                }
            };
            let desc = `卸载安装【${appName}】`;
            step.desc = desc;
            stepDesc = desc;
            break;
        // 登录账号
        case 'login':
            stepType = 425;
            step.params = {
                type: 'login',
                params: {
                    id: appId,
                    appName
                }
            };
            step.desc = '登录账号';
            stepDesc = '登录账号';
            break;
        default:
            break;
    }
    return { step, stepType, stepDesc };
}

// 创建步骤
export async function handleCreateStep(props) {
    const {
        caseNodeId,
        step,
        stepType,
        stepDesc,
        curOsType,
        stepList,
        currentStep,
        handleUpdateStepList,
        treeData,
        setTreeData,
        currentSpace,
        hookType
    } = props;
    let stepIndex = {
        preSibId: null,
        index: -1
    };
    let stepInGroupIndex = {
        preSibId: null,
        groupId: null,
        groupIndex: -1,
        index: -1
    };

    // 获取当前步骤索引
    let hasStep = false;
    stepList.forEach((item, index) => {
        if (item.stepId === currentStep?.stepId) {
            hasStep = true;
            stepIndex = {
                preSibId: currentStep?.stepId ?? 0,
                index: index
            };
        }
    });
    // 如无索引，判断是否在步骤组中
    if (!hasStep) {
        stepList.forEach((item, index) => {
            if (item?.stepType === 1401) {
                let _index = item.stepChildren.findIndex((_item) => _item.stepId === currentStep?.stepId);
                if (_index !== -1) {
                    stepInGroupIndex = {
                        groupId: item.stepId,
                        groupIndex: index,
                        preSibId: item.stepChildren[_index]?.stepId ?? 0,
                        index: _index
                    };
                }
            }
        });
    }
    let body = {
        caseNodeId: caseNodeId,
        stepDesc: stepDesc,
        stepType: stepType,
        stepInfo: step,
        osType: curOsType
    };

    // setup / teardown 步骤
    if ((currentStep?.hookType === 'setup' || currentStep?.hookType === 'teardown') && !hookType) {
        body.caseNodeId = +currentStep?.hookId;
    }
    // 获取步骤添加位置
    //  步骤组中
    if (stepInGroupIndex.index !== -1) {
        body.preSibId = stepInGroupIndex.preSibId;
        body.stepAttr = {
            parentStepId: stepInGroupIndex.groupId
        };
    }
    // 普通步骤
    if (stepInGroupIndex.index === -1 && stepIndex.index !== -1) {
        body.preSibId = stepIndex.preSibId;
    }
    let res = await createStep(body);
    const newStep = {
        stepId: res.stepId,
        stepType: stepType,
        stepInfo: step,
        stepDesc: stepDesc,
        stepChildren: []
    };
    if (hookType || currentStep?.hookType) {
        newStep.hookType = hookType || currentStep?.hookType;
    }
    if (currentSpace) {
        createPoint({
            moduleId: currentSpace?.id, // 业务模块id；int；必填
            caseNodeId: caseNodeId, // 用例节点Id；int；必填
            stepId: currentStep?.stepId, // 步骤Id；int；必填
            osType: curOsType, // 端类型，int；必填 1-Android 2-iOS 3-Android&iOS 4-server 5-web
            pointType: 1000, // 打点类型；int；必填 1000-创建步骤 1001-选中步骤 1100-update
            pointInfo: {}, // 点位内容；json；选填（预留字段，当前传空 json 就行）
            createTime: Math.floor(new Date().getTime() / 1000) // 打点时间；int；必填
        }).catch(() => {});
    }
    if (stepInGroupIndex.groupIndex !== -1) {
        stepList[stepInGroupIndex.groupIndex].stepChildren.splice(
            -1 === stepInGroupIndex.index ? 0 : stepInGroupIndex.index + 1,
            0,
            newStep
        );
    } else {
        stepList.splice(-1 === stepIndex.index ? 0 : stepIndex.index + 1, 0, newStep);
    }
    await handleUpdateStepList(stepList, newStep);
    setTreeData(
        treeAction([...treeData], 'children', +caseNodeId, (node, item, index) => {
            let type = getStepType(stepList);
            if (!item?.extra) {
                item.extra = {
                    executionType: {
                        [convertOsTypeToType(curOsType)]: type
                    },
                    stepInfo: {
                        [convertOsTypeToType(curOsType)]: stepList
                    }
                };
            }
            if (!item?.extra?.executionType) {
                item.extra.executionType = {
                    [convertOsTypeToType(curOsType)]: type
                };
            }
            if (!item?.extra?.stepInfo) {
                item.extra.stepInfo = {
                    [convertOsTypeToType(curOsType)]: stepList
                };
            }
            item.extra.executionType[convertOsTypeToType(curOsType)] = type;
            item.extra.stepInfo[convertOsTypeToType(curOsType)] = stepList;
            item.executionType = type;
        })
    );
    return newStep;
}
