/**
 * @file mock.jsx mock操作
 * @create 李爽@2023.03.13
 */
import { Tag, Col, Input } from 'antd';
import {formatRequest} from 'COMMON/components/TreeComponents/Step/utils';
const { TextArea } = Input;

export default ({ responseDetail, styles, type, matchStatus }) => {
    let body = '';
    let jsx = [];
    if (-1 !== Object.keys(responseDetail).indexOf('body')) {
        body = responseDetail.body;
    } else {
        body = responseDetail;
    }
    let tagJsx = [];
    if (type === 'requestVerify') {
        tagJsx.push(
            matchStatus.responseData ?
                <Tag color='success'>通过</Tag> : <Tag color='error'>不通过</Tag>
        );
    }
    jsx.push(
        <span
            className={styles.step_divider}
        >
            Response 校验&nbsp;{tagJsx}
        </span>
    );
    jsx.push(
        <Col span='24'>
            <TextArea
                rows={'' === body ? 2 : 15}
                style={{ width: '100%' }}
                value={formatRequest(body)}
            />
        </Col>
    );
    return jsx;
};