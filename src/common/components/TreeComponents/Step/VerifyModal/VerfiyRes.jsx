import {Row, Col, Input, Tag, Tooltip, Divider} from 'antd';
import {isEmpty} from 'lodash';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import {getUrl, getSmallDate, getQuery, getNewVertifyQueryArry} from 'COMMON/components/TreeComponents/Step/utils';
import ResData from './VerifyOperator/ResData';
import ResBody from './VerifyOperator/ResBody';
import ResQuery from './VerifyOperator/ResQuery';
import styles from './VerifyModal.module.less';

function getWholeUrl(record, whole = true) {
    let url = '';
    if (record.protocol && '' !== record.protocol) {
        url += record.protocol + '://';
    }
    if (record.hostname && '' !== record.hostname) {
        url += record.hostname;
    }
    if (record.port && '' !== record.port) {
        url += ':' + record.port;
    }
    if (whole && record.path && '' !== record.path) {
        url += record.path;
    }
    return url;
};

function VerifyInfo({testRes, currentStep}) {
    let matchId = testRes.data && testRes.data.extra && testRes.data.extra.matchId ?
        testRes.data.extra.matchId : '';
    let requestList = testRes.data && testRes.data.extra ? testRes.data.extra.requestList : [];
    let verifyResult = testRes.data && testRes.data.extra && -1 === testRes.status ?
        testRes.data.extra.verifyResult : {queryRes: true, bodyRes: true, responseRes: true};
    let urlVertifyList = {};
    let matchUrl = {};
    let matchStatus = {query: false, body: false, responseData: false};

    if (requestList) {
        for (let data of requestList) {
            let mainUrl = getUrl(data.requestDetail, false);
            if (-1 === Object.keys(urlVertifyList).indexOf(mainUrl)) {
                urlVertifyList[mainUrl] = [];
            }
            urlVertifyList[mainUrl].unshift(
                {
                    id: data.id,
                    protocol: data.requestDetail.protocol,
                    hostname: data.requestDetail.hostname,
                    key: mainUrl + String(urlVertifyList[mainUrl].length),
                    time: getSmallDate(data.timestamp),
                    port: data.requestDetail.port,
                    url: data.requestDetail.path,
                    query: getQuery(data.requestDetail.path)
                }
            );
            if (data.id === matchId) {
                matchStatus = {
                    query: verifyResult.queryRes,
                    body: verifyResult.bodyRes,
                    responseData: verifyResult.responseRes
                };
                matchUrl = data;
            }
        }
    }

    // 处理 query
    let rawQuery = [];
    let matchQuery = [];
    let vertifyQuery = [];
    if (
        -1 !== Object.keys(
            currentStep.stepInfo.params.params.mockRequest.matchRequest
        ).indexOf('query') &&
        0 !== Object.keys(currentStep.stepInfo.params.params.mockRequest.matchRequest.query).length
    ) {
        rawQuery = getNewVertifyQueryArry(
            currentStep.stepInfo.params.params.mockRequest.matchRequest.query
        );
        matchQuery = 0 === Object.keys(matchUrl).length ?
            [] : getQuery(matchUrl.requestDetail.path);
        // 找到相同query展示
        matchQuery.forEach((result, index) => {
            rawQuery.forEach((item, idx) => {
                if (result.key === item.key) {
                    vertifyQuery.push(result);
                }
            });
        });
    }
    return (
        <div
            style={{
                maxHeight: window.innerHeight - 200,
                overflow: 'scroll',
                padding: '10px 25px'
            }}
        >
            <Divider size='small' orientation='left'><span style={{fontSize: 12}}>URL</span></Divider>
            <Row>
                <Col span='24' style={{marginBottom: 15}}>
                    {
                        0 !== Object.keys(matchUrl).length ?
                            <Tooltip
                                title={getWholeUrl(matchUrl.requestDetail)}
                                overlayStyle={{maxWidth: window.innerWidth * 0.7}}
                            >
                                <Input
                                    size='small'
                                    value={getWholeUrl(matchUrl.requestDetail)}
                                />
                            </Tooltip> : <Input
                                addonBefore='URL'
                                value='暂未获取到有效请求'
                            />
                    }
                </Col>
            </Row>
            {
                -1 !== Object.keys(
                    currentStep.stepInfo.params.params.mockRequest.matchRequest
                ).indexOf('query') &&
                0 !== Object.keys(
                    currentStep.stepInfo.params.params.mockRequest.matchRequest.query
                ).length && 0 !== Object.keys(matchUrl).length &&
                <>
                    <Row>
                        <Col span='24' style={{padding: '0 5px'}}>
                            <ResQuery
                                query={vertifyQuery}
                                styles={styles}
                                type={'requestVerify'}
                                matchStatus={matchStatus}
                            />
                        </Col>
                    </Row>
                </>
            }
            {
                -1 !== Object.keys(
                    currentStep.stepInfo.params.params.mockRequest.matchRequest
                ).indexOf('body') &&
                '' !== currentStep.stepInfo.params.params.mockRequest.matchRequest.body &&
                '{}' !== currentStep.stepInfo.params.params.mockRequest.matchRequest.body &&
                0 !== Object.keys(matchUrl).length &&
                <>
                    <Row>
                        <Col span='24' style={{padding: '0 5px'}}>
                            <ResBody
                                request={0 === Object.keys(matchUrl).length ?
                                    {} : matchUrl.requestDetail}
                                styles={styles}
                                matchStatus={matchStatus}
                                type={'requestVerify'}
                            />
                        </Col>
                    </Row>
                </>
            }
            {
                !isEmpty(currentStep.stepInfo.params.params.mockRequest.verifyDetail.response) &&
                0 !== Object.keys(matchUrl).length && <>
                    <Row>
                        <Col span='24' style={{padding: '0 5px'}}>
                            <ResData
                                responseDetail={0 === Object.keys(matchUrl).length ?
                                    {} : matchUrl.responseDetail}
                                styles={styles}
                                matchStatus={matchStatus}
                                type={'requestVerify'}
                            />
                        </Col>
                    </Row>
                </>
            }
        </div>
    );
};

export default connectModel([baseModel], (state) => ({
}))(VerifyInfo);

