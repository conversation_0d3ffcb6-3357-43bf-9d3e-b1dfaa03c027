import {Modal} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import VerifyInfo from './VerfiyInfo';

function VertifyModal(props) {
    const {open, setOpen, setShowModal} = props;
    return (
        <Modal
            open={open}
            title="校验结果"
            centered
            width={window.innerWidth * 0.8}
            onCancel={() => {
                setOpen(false);
                setShowModal(false);
            }}
            footer={null}
        >
            <VerifyInfo {...props} />
        </Modal>
    );
};

export default connectModel([baseModel], (state) => ({
    showModal: state.common.base.showModal,
}))(VertifyModal);

