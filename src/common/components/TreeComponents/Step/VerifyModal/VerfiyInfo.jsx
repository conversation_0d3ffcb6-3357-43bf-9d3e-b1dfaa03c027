import {Row, Col, Collapse, Table, Input, Tag, Tooltip, Divider, Modal} from 'antd';
import {isEmpty} from 'lodash';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import {getUrl, getSmallDate, getQuery, getNewVertifyQueryArry} from 'COMMON/components/TreeComponents/Step/utils';
import ResData from './VerifyOperator/ResData';
import ResBody from './VerifyOperator/ResBody';
import ResQuery from './VerifyOperator/ResQuery';
import styles from './VerifyModal.module.less';

const {Panel} = Collapse;

function getWholeUrl(record, whole = true) {
    let url = '';
    if (record.protocol && '' !== record.protocol) {
        url += record.protocol + '://';
    }
    if (record.hostname && '' !== record.hostname) {
        url += record.hostname;
    }
    if (record.port && '' !== record.port) {
        url += ':' + record.port;
    }
    if (whole && record.path && '' !== record.path) {
        url += record.path;
    }
    return url;
};

function VerifyInfo({testRes, currentStep}) {
    let matchId = testRes.data && testRes.data.extra && testRes.data.extra.matchId ?
        testRes.data.extra.matchId : '';
    let requestList = testRes.data && testRes.data.extra ? testRes.data.extra.requestList : [];
    let verifyResult = testRes.data && testRes.data.extra && -1 === testRes.status ?
        testRes.data.extra.verifyResult : {queryRes: true, bodyRes: true, responseRes: true};
    let urlVertifyList = {};
    let matchUrl = {};
    let matchStatus = {query: false, body: false, responseData: false};

    if (requestList) {
        for (let data of requestList) {
            let mainUrl = getUrl(data.requestDetail, false);
            if (-1 === Object.keys(urlVertifyList).indexOf(mainUrl)) {
                urlVertifyList[mainUrl] = [];
            }
            urlVertifyList[mainUrl].unshift(
                {
                    id: data.id,
                    protocol: data.requestDetail.protocol,
                    hostname: data.requestDetail.hostname,
                    key: mainUrl + String(urlVertifyList[mainUrl].length),
                    time: getSmallDate(data.timestamp),
                    port: data.requestDetail.port,
                    url: data.requestDetail.path,
                    query: getQuery(data.requestDetail.path)
                }
            );
            if (data.id === matchId) {
                matchStatus = {
                    query: verifyResult.queryRes,
                    body: verifyResult.bodyRes,
                    responseData: verifyResult.responseRes
                };
                matchUrl = data;
            }
        }
    }

    let jsx = [];
    const columns = [
        {
            title: '时间',
            dataIndex: 'time',
            width: 100
        },
        Table.EXPAND_COLUMN,
        {
            title: 'URL',
            key: 'url',
            ellipsis: true,
            onCell: (record) => {
                return {rowSpan: record.rowSpan};
            },
            render: (record) => {
                let jsx = [];
                jsx.push(
                    <span
                        style={{
                            color: matchUrl.id === record.id ? 'red' : '#000'
                        }}
                    >
                        {record.url}
                    </span>
                );
                return jsx;
            },
        }
    ];
    let index = 0;
    for (let host in urlVertifyList) {
        jsx.push(
            <Panel header={host} key={index + 1}>
                <Table
                    style={{
                        cursor: 'pointer'
                    }}
                    size='small'
                    columns={columns}
                    dataSource={[...urlVertifyList[host]]}
                    scroll={{
                        y: 340,
                    }}
                    expandable={{
                        expandedRowRender: (record) => (
                            <p
                                style={{
                                    margin: 0,
                                }}
                            >
                                {getUrl(record)}
                            </p>
                        ),
                    }}
                />
            </Panel>
        );
        index++;
    }
    let statusJsx = [];
    if (0 !== Object.keys(matchUrl).length) {
        statusJsx.push(
            matchStatus.query && matchStatus.body && matchStatus.responseData ?
                <Tag color='success'>校验通过</Tag> : <Tag color='error'>校验不通过</Tag>
        );
    }
    // 处理 query
    let rawQuery = [];
    let matchQuery = [];
    let vertifyQuery = [];
    if (
        -1 !== Object.keys(
            currentStep.stepInfo.params.params.mockRequest.matchRequest
        ).indexOf('query') &&
        0 !== Object.keys(currentStep.stepInfo.params.params.mockRequest.matchRequest.query).length
    ) {
        rawQuery = getNewVertifyQueryArry(
            currentStep.stepInfo.params.params.mockRequest.matchRequest.query
        );
        matchQuery = 0 === Object.keys(matchUrl).length ?
            [] : getQuery(matchUrl.requestDetail.path);
        // 找到相同query展示
        matchQuery.forEach((result, index) => {
            rawQuery.forEach((item, idx) => {
                if (result.key === item.key) {
                    vertifyQuery.push(result);
                }
            });
        });
    }
    return (
        <div
            style={{
                maxHeight: window.innerHeight - 200,
                overflow: 'scroll'
            }}
        >
            <Row>
                <Col span='24' style={{marginBottom: 15}}>
                    {
                        0 !== Object.keys(matchUrl).length ?
                            <Tooltip
                                title={getWholeUrl(matchUrl.requestDetail)}
                                overlayStyle={{maxWidth: window.innerWidth * 0.7}}
                            >
                                <Input
                                    addonBefore='URL'
                                    value={getWholeUrl(matchUrl.requestDetail)}
                                />
                            </Tooltip> : <Input
                                addonBefore='URL'
                                value='暂未获取到有效请求'
                            />
                    }
                </Col>
            </Row>
            {
                0 !== Object.keys(matchUrl).length ?
                    <Row>
                        <Col span='12' style={{padding: '0 5px'}}>
                            <Divider style={{padding: 0, margin: 0}}>期望</Divider>
                        </Col>
                        <Col span='12' style={{padding: '0 5px'}}>
                            <Divider style={{padding: 0, margin: 0}}>
                                实际
                            </Divider>
                        </Col>
                    </Row> : null
            }
            {
                0 !== Object.keys(matchUrl).length ?
                    <Row>
                        <Col span='12' style={{padding: '0 5px'}}>
                            {
                                -1 !== Object.keys(
                                    currentStep.stepInfo.params.params.mockRequest.matchRequest
                                ).indexOf('query') &&
                                    0 !== Object.keys(
                                        currentStep.stepInfo.params.params.mockRequest.matchRequest.query
                                    ).length ?
                                    <ResQuery
                                        query={rawQuery}
                                        type={'normal'}
                                        styles={styles}
                                        matchStatus={matchStatus}
                                    /> : <span className={styles.step_divider}>
                                        无 Query 校验
                                    </span>
                            }
                        </Col>
                        <Col span='12' style={{padding: '0 5px'}}>
                            {
                                -1 !== Object.keys(
                                    currentStep.stepInfo.params.params.mockRequest.matchRequest
                                ).indexOf('query') &&
                                    0 !== Object.keys(
                                        currentStep.stepInfo.params.params.mockRequest.matchRequest.query
                                    ).length ?
                                    <ResQuery
                                        query={vertifyQuery}
                                        styles={styles}
                                        type={'requestVerify'}
                                        matchStatus={matchStatus}
                                    /> : <span className={styles.step_divider}>
                                        无 Query 校验
                                    </span>
                            }
                        </Col>
                    </Row> : null
            }
            {
                0 !== Object.keys(matchUrl).length ?
                    <Row>
                        <Col span='12' style={{padding: '0 5px'}}>
                            {
                                -1 !== Object.keys(
                                    currentStep.stepInfo.params.params.mockRequest.matchRequest
                                ).indexOf('body') &&
                                    '' !== currentStep.stepInfo.params.params.mockRequest.matchRequest.body &&
                                    '{}' !== currentStep.stepInfo.params.params.mockRequest.matchRequest.body ?
                                    <ResBody
                                        request={
                                            currentStep.stepInfo.params.params.mockRequest.matchRequest
                                        }
                                        type={'normal'}
                                        styles={styles}
                                        matchStatus={matchStatus}
                                    /> : <span className={styles.step_divider}>
                                        无 Body 校验
                                    </span>
                            }
                        </Col>
                        <Col span='12' style={{padding: '0 5px'}}>
                            {
                                -1 !== Object.keys(
                                    currentStep.stepInfo.params.params.mockRequest.matchRequest
                                ).indexOf('body') &&
                                    '' !== currentStep.stepInfo.params.params.mockRequest.matchRequest.body &&
                                    '{}' !== currentStep.stepInfo.params.params.mockRequest.matchRequest.body ?
                                    <ResBody
                                        request={0 === Object.keys(matchUrl).length ?
                                            {} : matchUrl.requestDetail}
                                        styles={styles}
                                        matchStatus={matchStatus}
                                        type={'requestVerify'}
                                    /> : <span className={styles.step_divider}>
                                        无 Body 校验
                                    </span>
                            }
                        </Col>
                    </Row> : null
            }
            {
                0 !== Object.keys(matchUrl).length ?
                    <Row>
                        <Col span='12' style={{padding: '0 5px'}}>
                            {
                                !isEmpty(currentStep.stepInfo.params.params.mockRequest.verifyDetail.response) ?
                                    <ResData
                                        responseDetail={
                                            JSON.stringify(currentStep.stepInfo.params.params.
                                                mockRequest.verifyDetail.response)
                                        }
                                        styles={styles}
                                        type={'normal'}
                                        matchStatus={matchStatus}
                                    /> : <span className={styles.step_divider}>
                                        无 Response 校验
                                    </span>
                            }
                        </Col>
                        <Col span='12' style={{padding: '0 5px'}}>
                            {
                                !isEmpty(currentStep.stepInfo.params.params.mockRequest.verifyDetail.response) ?
                                    <ResData
                                        responseDetail={0 === Object.keys(matchUrl).length ?
                                            {} : matchUrl.responseDetail}
                                        styles={styles}
                                        matchStatus={matchStatus}
                                        type={'requestVerify'}
                                    /> : <span className={styles.step_divider}>
                                        无 Response 校验
                                    </span>
                            }
                        </Col>
                    </Row> : null
            }
            {
                requestList && 0 !== requestList.length ?
                    <Collapse accordion style={{marginTop: 30}}>
                        {jsx}
                    </Collapse> : null
            }
        </div>
    );
};

export default connectModel([baseModel], (state) => ({
}))(VerifyInfo);

