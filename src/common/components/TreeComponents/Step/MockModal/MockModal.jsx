import {Modal, Table, Tooltip, Empty, message} from 'antd';
import {useImperativeHandle, forwardRef, useState} from 'react';
import {ClearOutlined, DeleteOutlined} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import electron from 'COMMON/utils/electron';
import {getSmallDate} from 'COMMON/components/TreeComponents/Step/utils';

function MockModal(props, ref) {
    const {currentDevice, mockList, setModalOpen, setShowModal, setMockList} = props;
    const [curOsType, setCurOsType] = useState(1);
    const [open, setOpen] = useState(false);
    const columns = [
        {
            title: '时间',
            width: 100,
            key: 'timestamp',
            onCell: (record) => {
                return {rowSpan: record.rowSpan};
            },
            render: (record) => {
                return getSmallDate(record.timestamp);
            }
        },
        {
            title: 'URL',
            key: 'url',
            ellipsis: true,
            onCell: (record) => {
                return {rowSpan: record.rowSpan};
            },
            render: (record) => {
                let {hostname, protocol, port, path} = record.mockRequest.matchRequest;
                return protocol + '://' + hostname + ':' + port + path;
            }
        },
        {
            title: '操作',
            width: 200,
            key: 'status',
            onCell: (record) => {
                return {rowSpan: record.rowSpan};
            },
            render: (record) => {
                let jsx = [];
                jsx.push(
                    <DeleteOutlined
                        style={{
                            color: 'red'
                        }}
                        onClick={async () => {
                            try {
                                await electron.send('proxy.mock.del', {
                                    deviceType: curOsType,
                                    deviceId: currentDevice?.deviceId, mockId: record.id
                                });
                                if (currentDevice) {
                                    await electron.send('proxy.mock.get',
                                        {
                                            deviceType: curOsType,
                                            deviceId: currentDevice?.deviceId
                                        }).then(res => {
                                            setMockList(res);
                                        }).catch((error) => {
                                            message.error(error.message ? error.message : error);
                                        });
                                }
                            } catch (error) {
                                message.error(error.message ? error.message : error);
                            }
                        }}
                    />
                );
                return jsx;
            }
        }
    ];

    const showModal = (osType) => {
        setCurOsType(osType);
        setShowModal(true);
        setOpen(true);
    };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(ref, () => {
        return {
            show: showModal
        };
    }, [showModal]);

    return (
        <Modal
            open={open}
            title={
                <>
                    数据模拟
                    <Tooltip title='清空'>
                        <ClearOutlined
                            style={{
                                marginLeft: 5,
                                color: '#777'
                            }}
                            onClick={async () => {
                                try {
                                    if (null === currentDevice) {
                                        return false;
                                    }
                                    if (2 !== currentDevice.status) {
                                        return false;
                                    }
                                    await electron.send('proxy.mock.clear',
                                        {deviceType: curOsType, deviceId: currentDevice.deviceId});
                                    setMockList([]);
                                } catch (err) {
                                    message.error(err.message ? err.message : err);
                                }
                            }}
                        />
                    </Tooltip>
                </>
            }
            centered
            width={window.innerWidth * 0.8}
            onCancel={() => {
                setOpen(false);
                setModalOpen(false);
                setShowModal(false);
            }}
            footer={null}
        >

            {
                0 === mockList.length ?
                    <Empty description='暂无 Mock 请求' /> :
                    <Table
                        style={{
                            cursor: 'pointer'
                        }}
                        size='small'
                        columns={columns}
                        dataSource={[...mockList]}
                        scroll={{
                            y: 340,
                        }}
                    />
            }
        </Modal>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    showModal: state.common.base.showModal,
    currentDevice: state.common.base.currentDevice,
    mockList: state.common.case.mockList,
}))(forwardRef(MockModal));