import { useState, useEffect } from 'react';
import { Collapse, Select, Input, Form, Empty } from 'antd';
import { isEmpty } from 'lodash';
import ReactJson from 'react-json-view';
import {CardTitle} from 'COMMON/components/common/Card';
import { safeJsonParse } from 'COMMON/utils/utils';
import { v4 } from 'uuid';
import { operationList } from './const';
import JsonRenderer from './JsonRenderer';
import styles from './InterfaceStepDiffRes.module.less';
const { Panel } = Collapse;

const ResponseAssertItem = (props) => {
    const { onBlurUpdateStep, currentStep } = props;
    const [value, setValue] = useState({});

    useEffect(() => {
        if (!isEmpty(props.defaultValue)) {
            setValue(props.defaultValue);
        } else if (!isEmpty(props.value)) {
            setValue(props.value);
        } else {
            setValue({});
        }
    }, [props.defaultValue, props.value]);

    const onBlur = (field, fieldValue) => {
        props.onChange({
            ...value,
            [field]: fieldValue
        });
    };

    return (
        <>
            <div style={{ display: 'flex', marginTop: 8, alignItems: 'center' }}>
                <div
                    style={{
                        width: 120,
                        marginRight: 6,
                        flexShrink: 0,
                        textAlign: 'center',
                        display: 'inline-block',
                        background: '#f5f7fa',
                        padding: '4px 8px',
                        borderRadius: 2
                    }}
                >
                    JSONPath
                </div>
                <Input
                    disabled
                    defaultValue={props.value?.jsonPath}
                    placeholder="请输入JSONPath"
                    onBlur={(e) => {
                        onBlur('jsonPath', e.target.value);
                    }}
                />
            </div>
            <div style={{ display: 'flex', marginTop: 8 }}>
                <Select
                    style={{
                        marginRight: 6,
                        width: 120,
                        flexShrink: 0
                    }}
                    disabled
                    placeholder="请选择操作符"
                    options={operationList}
                    defaultValue={props.value?.type}
                    onChange={(selectvalue) => {
                        onBlur('type', selectvalue);
                    }}
                />
                <Input
                    defaultValue={props.value?.data}
                    onBlur={(e) => {
                        onBlur('data', e.target.value);
                    }}
                    placeholder="请输入断言值"
                    disabled
                />
            </div>
        </>
    );
};

const AssertionFormItem = ({ index, assertion }) => {
    return (
        <>
            <div style={{ display: 'flex', marginTop: 8 }}>
                <Select
                    style={{
                        marginRight: 6,
                        width: 120,
                        flexShrink: 0
                    }}
                    placeholder="请选择操作符"
                    options={[
                        { value: 1, label: '包含' },
                        { value: 2, label: '正则' }
                    ]}
                    defaultValue={assertion.type}
                    disabled
                />
                <Input
                    style={{ flex: 2 }}
                    placeholder="请输入断言条件"
                    defaultValue={assertion.data}
                    disabled
                />
            </div>
        </>
    );
};

const InterfaceDiffAssertItem = ({ title, data, redPath, isPass }) => {
    const borderColor = isPass === 0 ? 'green' : 'red';
    return (
        <div
            style={{
                flex: 1,
                marginTop: '5px'
            }}
        >
            <h4 style={{ textAlign: 'center' }}>{title}</h4>
            <div
                style={{
                    width: '100%',
                    padding: '0 5px',
                    overflow: 'auto'
                }}
            >
                <JsonRenderer
                    style={{
                        border: 'solid 1px #ccc',
                        borderRadius: 5,
                        padding: 5
                    }}
                    data={safeJsonParse(data)}
                    redPaths={redPath}
                />
            </div>
        </div>
    );
};

const InterfaceDiffAssert = ({ src }) => {
    const errPath = src.errorKeyList.map((v) => v.split('.').slice(1));
    return (
        <>
            <div
                style={{
                    color: src?.isPass === 0 ? 'green' : 'red',
                    fontSize: 14,
                    fontWeight: 'bold',
                    margin: '10px 5px'
                }}
            >
                断言结果: {src?.errorMsg ?? '通过'}
            </div>
            <div
                style={{
                    width: '100%',
                    display: 'flex',
                    overflowX: 'auto',
                    overflowY: 'auto'
                }}
            >
                <InterfaceDiffAssertItem
                    title={'稳定版'}
                    data={src.stableEnvResponse}
                    redPath={errPath}
                    isPass={src.isPass}
                />
                <InterfaceDiffAssertItem
                    title={'待测版'}
                    data={src.testEnvResponse}
                    redPath={errPath}
                    isPass={src.isPass}
                />
            </div>
        </>
    );
};

//  0 表示全量diff， 1表示checkKeyDiff， 2表示ignoreKeyDiff
const DIFF_TITLE = {
    0: '全量 Diff  断言',
    1: '指定 Key Diff  断言',
    2: '忽略 Key Diff  断言'
};
const InterfaceAssert = ({ assertResult }) => {
    const renderAssertions = (assertions = [], type, title) => {
        if (!Array.isArray(assertions)) {
            // 如果 assertions 不是数组，直接处理单个对象
            assertions = [assertions];
        }

        return assertions.map((assertion, index) => {
            // isPass // 0-通过 1-不通过
            const versionType =
                assertion?.envType === 'stable'
                    ? '- 稳定版'
                    : assertion?.envType === 'test'
                    ? '- 待测版'
                    : '';
            const fullTitle = `${title} ${versionType}`;
            return (
                <Panel
                    style={{
                        border: `solid 1px ${assertion.isPass ? '#ff4446' : '#48bc19'}`,
                        opacity: 0.8,
                        borderRadius: 5,
                        margin: 0.5
                    }}
                    key={v4()}
                    header={
                        <div style={{ color: assertion.isPass ? '#ff4446' : '#48bc19' }}>
                            {type} [{index + 1}]
                        </div>
                    }
                >
                    <CardTitle text={fullTitle} style={{ margin: 0 }} />
                    {type === 'assertText' && (
                        <AssertionFormItem index={index} assertion={assertion} />
                    )}
                    {type === 'assertJson' && (
                        <ResponseAssertItem
                            value={{
                                jsonPath: assertion.jsonPath,
                                type: assertion.type,
                                data: assertion.data
                            }}
                        />
                    )}
                    {type === 'assertJsonSchema' && (
                        <ReactJson
                            name={false}
                            displayDataTypes={false}
                            src={safeJsonParse(assertion.data)}
                        />
                    )}

                    {type === 'assertJsonDiff' ? (
                        <InterfaceDiffAssert src={assertion} />
                    ) : (
                        <>
                            <CardTitle text="请求的响应" style={{ margin: '10px 0' }} />
                            <ReactJson
                                name={false}
                                displayDataTypes={false}
                                src={safeJsonParse(assertion.assertedContent)}
                            />
                        </>
                    )}
                </Panel>
            );
        });
    };

    return isEmpty(assertResult?.assertText) &&
        isEmpty(assertResult?.assertJson) &&
        isEmpty(assertResult?.assertJsonSchema) &&
        isEmpty(assertResult?.assertJsonDiff) ? (
        <Empty
            style={{ marginTop: 200 }}
            description="暂无断言结果"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
    ) : (
        <div className={styles.tabsContentNoFlex}>
            <div>
                <Collapse accordion bordered={false} size="small" ghost>
                    {renderAssertions(assertResult?.assertJson, 'assertJson', 'Response JSON 断言')}
                </Collapse>
            </div>
            <div>
                <Collapse accordion bordered={false} size="small" ghost>
                    {renderAssertions(assertResult?.assertText, 'assertText', 'Response Text 断言')}
                </Collapse>
            </div>
            <div>
                <Collapse accordion bordered={false} size="small" ghost>
                    {renderAssertions(
                        assertResult?.assertJsonSchema,
                        'assertJsonSchema',
                        'Response JSON Schema 断言'
                    )}
                </Collapse>
            </div>
            <div
                style={{
                    width: '100%'
                }}
            >
                <Collapse accordion bordered={false} size="small" ghost>
                    {renderAssertions(
                        assertResult?.assertJsonDiff,
                        'assertJsonDiff',
                        DIFF_TITLE[assertResult?.assertJsonDiff?.type]
                    )}
                </Collapse>
            </div>
        </div>
    );
};

export default InterfaceAssert;
