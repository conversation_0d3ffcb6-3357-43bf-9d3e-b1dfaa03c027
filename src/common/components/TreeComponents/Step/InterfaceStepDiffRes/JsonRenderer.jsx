import React from 'react';

const isRedPath = (path, redPaths) => {
    return redPaths.some((redPath) => JSON.stringify(redPath) === JSON.stringify(path));
};

// 递归组件，用于渲染 JSON 对象
const JsonRenderer = ({ data, path = [], redPaths = [] }) => {
    // 检查当前路径是否在需要变红的路径中
    if (Array.isArray(data)) {
        return (
            <span>
                [
                <ul style={{ listStyleType: 'none', paddingLeft: '20px', margin: 0 }}>
                    {data.map((item, index) => (
                        <li
                            key={index}
                            style={{
                                color: isRedPath(path.concat(index + ''), redPaths)
                                    ? 'red'
                                    : 'black'
                            }}
                        >
                            <JsonRenderer
                                data={item}
                                path={path.concat(index + '')}
                                redPaths={redPaths}
                            />
                            {index < data.length - 1 && ','}
                        </li>
                    ))}
                </ul>
                ]
            </span>
        );
    }

    if (typeof data === 'object' && data !== null) {
        return (
            <span>
                {'{'}
                <ul style={{ listStyleType: 'none', paddingLeft: '20px', margin: 0 }}>
                    {Object.keys(data).map((key, index, arr) => (
                        <li
                            key={key}
                            style={{
                                color: isRedPath(path.concat(key), redPaths) ? 'red' : 'black'
                            }}
                        >
                            <strong>{key}:</strong>{' '}
                            <JsonRenderer
                                data={data[key]}
                                path={path.concat(key)}
                                redPaths={redPaths}
                            />
                            {index < arr.length - 1 && ','}
                        </li>
                    ))}
                </ul>
                {'}'}
            </span>
        );
    }

    // 对于基本数据类型，直接显示值
    return (
        <span style={{ color: isRedPath(path, redPaths) ? 'red' : 'black', whiteSpace: 'nowrap' }}>
            {JSON.stringify(data)}
        </span>
    );
};

export default ({style,...otherProps}) => {
    return (
        <div style={style}>
            <JsonRenderer {...otherProps} />
        </div>
    );
};
