import React, { useMemo, useEffect } from 'react';
import React<PERSON><PERSON> from 'react-json-view';
import { Collapse, Divider } from 'antd';
import { safeJsonParse } from 'COMMON/utils/utils';
import {CardTitle} from 'COMMON/components/common/Card';
import InterfaceHeader from './InterfaceHeader';
import JSONMonacoEditor from '../StepInfo/InterfaceAction/Postman/JSONMonacoEditor';
import styles from './InterfaceStepDiffRes.module.less';
const CONTENT_TYPE = {
    'application/json': 'json',
    'application/xml': 'xml',
    'text/html': 'html',
    'text/plain': 'txt',
    'application/x-www-form-urlencoded': 'json',
    'multipart/form-data': 'json',
    'application/graphql': 'txt',
    'application/graphql+json': 'txt',
    'text/javascript': 'javascript',
    'application/ld+json': 'txt',
    'text/css': 'css',
    'application/x-yaml': 'txt',
    'text/vnd.graphviz': 'txt'
};

const InterfaceResponseComparison = ({ response, version }) => {
    const contentType = useMemo(() => {
        return response?.headers.find((item) => item.key === 'Content-Type');
    }, [response]);

    const language = useMemo(() => {
        const find = Object.keys(CONTENT_TYPE).find((item) => contentType?.value?.includes(item));
        if (find) {
            return CONTENT_TYPE[find];
        }
        return 'txt';
    }, [contentType]);

    const [editor, setEditor] = React.useState(null);

    const items = [
        {
            key: 'header',
            label: <a>response header</a>,
            children: <InterfaceHeader list={response?.headers} />
        },
        {
            key: 'body',
            label: <a>response body</a>,
            children:
                language === 'json' ? (
                    <ReactJson
                        name={false}
                        displayDataTypes={false}
                        src={safeJsonParse(response?.body?.data)}
                    />
                ) : (
                    <JSONMonacoEditor
                        viewValue={response?.body?.data}
                        readOnly
                        language={language}
                        height={300}
                        setEditor={setEditor}
                    />
                )
        }
    ];

    return (
        <div style={{ width: '50%' }}>
            <div style={{ fontSize: 16, color: '#598fe9', marginLeft: 10, fontWeight: 'bold' }}>
                {version === 'stable' ? '稳定版' : '待测版'}
            </div>
            <Collapse defaultActiveKey={items.map((item) => item.key)} ghost items={items} />
        </div>
    );
};

const InterfaceResponse = ({ data }) => {
    return (
        <div className={styles.tabsContent}>
            {/* {JSON.stringify(data?.data)} */}
            <InterfaceResponseComparison response={data?.[0]?.response} version="stable" />
            <Divider type="vertical" style={{ height: '78vh' }} dashed />
            <InterfaceResponseComparison response={data?.[1]?.response} version="test" />
        </div>
    );
};

export default InterfaceResponse;
