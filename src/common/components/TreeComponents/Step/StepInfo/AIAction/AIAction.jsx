import { useState } from 'react';
import Loading from 'COMMON/components/common/Loading';
import AIAssert from './AIAssert';
import AILocate from './AILocate';
import styles from './AIAction.module.less';

function AIAction(props) {
    const { currentStep, descExtra } = props;
    const [loading, setLoading] = useState(false);

    if (loading) {
        return <Loading />;
    }

    return (
        <div className={styles.action}>
            {descExtra}
            {[504, 602]?.includes(currentStep?.stepType) && (
                <AIAssert {...props} setLoading={setLoading} />
            )}
            {[601]?.includes(currentStep?.stepType) && (
                <AILocate {...props} setLoading={setLoading} />
            )}
        </div>
    );
}

export default AIAction;
