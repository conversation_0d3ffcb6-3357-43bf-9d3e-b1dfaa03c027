import { useState, useEffect } from 'react';
import { message, Tooltip } from 'antd';
import { CameraOutlined } from '@ant-design/icons';
import electron from 'COMMON/utils/electron';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { base64ImageUpload } from 'COMMON/utils/utils';
import { CommonConfig, ImageCanvas, InputContent } from '../../components';
import styles from './AIAssert.module.less';

const AIAssert = (props) => {
    const {
        currentStep,
        editType,
        setRecording,
        currentDevice,
        deviceList,
        curOsType,
        handleUpdateStep
    } = props;
    const [imgSrc, setImgSrc] = useState('');
    const [messageApi, contextHolder] = message.useMessage();

    useEffect(() => {
        let newImg = currentStep?.stepInfo.params.params?.imgInfo?.img;
        setImgSrc(newImg);
    }, [currentStep?.stepInfo.params.params?.imgInfo?.img]);

    const uploadImg = async () => {
        try {
            // 确定有设备连接
            let gotDevice = false;
            for (let device of deviceList?.[2 === +curOsType ? 'iOS' : 'android'] ?? []) {
                if (device.deviceId === currentDevice?.deviceId) {
                    gotDevice = true;
                    if (![2].includes(device?.status)) {
                        messageApi.error('请确保设备状态正常');
                        return false;
                    }
                    break;
                }
            }
            if (!gotDevice) {
                messageApi.error('请确保有设备连接');
                return false;
            }
            // 开启录制模式
            setRecording(true);
            let { screenshot, screenSize } = await electron.send('device.record.screenshot', {
                deviceType: 2 === +curOsType ? 'ios' : 'android',
                deviceId: currentDevice?.deviceId
            });

            // 上传图片
            base64ImageUpload(screenshot)
                .then(async (res) => {
                    let bosUrl = res?.url;
                    for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                        if (device.deviceId === currentDevice?.deviceId) {
                            currentStep.stepInfo.params.params.imgInfo = {
                                width: screenSize.width,
                                height: screenSize.height,
                                img: bosUrl
                            };
                            handleUpdateStep(currentStep);
                        }
                    }
                })
                .catch((err) => {
                    console.log(err?.message ?? err);
                    messageApi.error(err?.message ?? err);
                });
        } catch (err) {
            console.log(err?.message ?? err);
            messageApi.error(err?.message ?? err);
        } finally {
            setRecording(false);
        }
    };

    return (
        <div>
            {contextHolder}
            <InputContent
                {...props}
                data={currentStep?.stepInfo.params.params.inputAssert}
                title="校验内容"
                placeholder="请输入校验内容"
                onBlur={(data) => {
                    currentStep.stepInfo.params.params.inputAssert = data;
                    handleUpdateStep(currentStep);
                }}
            />
            <div className={styles.imgContent}>
                {isElectron() && !['readonly', 'debug', 'execute'].includes(editType) && (
                    <Tooltip title="重新获取图片">
                        <span className={styles.imgUpload} onClick={() => uploadImg()}>
                            <CameraOutlined />
                        </span>
                    </Tooltip>
                )}
                <ImageCanvas
                    {...props}
                    imgSrc={imgSrc}
                    stepWidth={currentStep?.stepInfo?.params?.params?.imgInfo?.width}
                    stepHeight={currentStep?.stepInfo?.params?.params?.imgInfo?.height}
                />
            </div>
            <CommonConfig {...props} />
        </div>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace
}))(AIAssert);
