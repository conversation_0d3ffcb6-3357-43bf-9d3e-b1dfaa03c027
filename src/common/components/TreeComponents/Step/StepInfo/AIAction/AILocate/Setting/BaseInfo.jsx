import { useState, useEffect } from 'react';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { ImageCanvas } from '../../../components';
import ElementType from './ElementType';
import StepOperator from '../StepOperator';
import styles from './Setting.module.less';

function BaseInfo(props) {
    const { currentStep, editType, handleUpdateStep } = props;
    const [isCanvas, setIsCanvas] = useState(false);
    const [imgSrc, setImgSrc] = useState('');

    useEffect(() => {
        let newImg = currentStep?.stepInfo.params?.recordInfo?.deviceInfo?.screenshot;
        setImgSrc(newImg);
    }, [currentStep?.stepInfo.params?.recordInfo?.deviceInfo?.screenshot]);

    return (
        <>
            <div className={styles.settingLeft}>
                <StepOperator
                    {...props}
                    imgSrc={imgSrc}
                    setIsCanvas={setIsCanvas}
                    operaOptions={{
                        showActionType: true,
                        showDrawOpera: !['debug', 'execute']?.includes(editType),
                        showSettingOpera: false
                    }}
                />
                <ImageCanvas
                    {...props}
                    rect={currentStep.stepInfo.params.findInfo.rect}
                    isCanvas={isCanvas}
                    imgSrc={imgSrc}
                    stepWidth={
                        currentStep?.stepInfo?.params?.recordInfo?.deviceInfo?.screenSize?.width
                    }
                    stepHeight={
                        currentStep?.stepInfo?.params?.recordInfo?.deviceInfo?.screenSize?.height
                    }
                    onFinish={({ x, y, width, height }) => {
                        currentStep.stepInfo.params.findInfo.rect = {
                            x,
                            y,
                            w: width,
                            h: height
                        };
                        handleUpdateStep && handleUpdateStep(currentStep);
                    }}
                />
            </div>
            <div className={styles.settingRight}>
                <ElementType {...props} />
            </div>
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace
}))(BaseInfo);
