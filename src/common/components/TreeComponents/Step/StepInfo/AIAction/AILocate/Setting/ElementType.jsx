import classnames from 'classnames';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import ParamsTitle from 'COMMON/components/ParamsTitle';
import ParamsSmallTitle from 'COMMON/components/ParamsSmallTitle';
import { SelectOperator } from 'COMMON/components/TreeComponents/Step/StepInfo/components';
import MulitParams from 'COMMON/components/TreeComponents/Step/StepItem/Params/NewMulitParams';
import styles from './Setting.module.less';
import { InputContent } from '../../../components';

function ElementType(props) {
    const { currentStep, handleUpdateStep, operationType, sizeType = 'normal' } = props;
    const Title = sizeType === 'normal' ? ParamsTitle : ParamsSmallTitle;
    return (
        <div
            className={classnames(styles.singleParams, {
                [styles.modelMarginLeft]: sizeType === 'small'
            })}
        >
            <div
                className={classnames(
                    { [styles.paramsItem]: sizeType === 'normal' },
                    { [styles.paramsItemSmall]: sizeType === 'small' }
                )}
                style={{
                    marginTop: sizeType === 'normal' ? 15 : 10,
                    fontSize: sizeType === 'normal' ? 14 : 12
                }}
            >
                <Title text="操作行为" />
                <div
                    className={classnames({
                        [styles.rowMarginLeft]: sizeType === 'small'
                    })}
                >
                    <ParamsInfo text="操作动作" sizeType={sizeType} width={85} />
                    <SelectOperator
                        {...props}
                        styles={{
                            width: 'calc(100% - 100px)',
                            marginBottom: 5
                        }}
                        sizeType={sizeType}
                        includeOptions={operationType === 'ifThen' ? ['nope', 'absence'] : []}
                    />
                    <MulitParams type={sizeType} step={currentStep} {...props} />
                </div>
            </div>
            {sizeType === 'normal' && (
                <div
                    className={classnames(
                        { [styles.paramsItem]: sizeType === 'normal' },
                        { [styles.paramsItemSmall]: sizeType === 'small' }
                    )}
                    style={{
                        marginTop: sizeType === 'normal' ? 15 : 10,
                        fontSize: sizeType === 'normal' ? 14 : 12
                    }}
                >
                    <Title text="定位描述" />
                    <InputContent
                        {...props}
                        isTextArea
                        title="定位描述"
                        data={currentStep?.stepInfo.params.findInfo.findDesc}
                        onBlur={(data) => {
                            currentStep.stepInfo.params.findInfo.findDesc = data;
                            handleUpdateStep && handleUpdateStep(currentStep);
                        }}
                    />
                </div>
            )}
        </div>
    );
}

function ParamsInfo({ text, sizeType, width = '100%' }) {
    return (
        <span
            className={styles.paramsInfo}
            style={{
                fontSize: sizeType === 'normal' ? 14 : 12,
                width: width
            }}
        >
            {text}
        </span>
    );
}
export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal
}))(ElementType);
