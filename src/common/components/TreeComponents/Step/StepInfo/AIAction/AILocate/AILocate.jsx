import { useState, useEffect } from 'react';
import { Spin } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { ImageCanvas, InputContent } from '../../components';
import StepOperator from './StepOperator';
import styles from './AILocate.module.less';

function AILocate(props) {
    const { curOsType, currentStep, editType, handleUpdateStep } = props;
    const [isCanvas, setIsCanvas] = useState(false);
    const [imgSrc, setImgSrc] = useState('');
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        let newImg = currentStep?.stepInfo.params?.recordInfo?.deviceInfo?.screenshot;
        setImgSrc(newImg);
    }, [currentStep?.stepInfo.params?.recordInfo?.deviceInfo?.screenshot]);

    return (
        <div>
            <InputContent
                {...props}
                title="定位描述"
                placeholder="请输入定位描述"
                data={currentStep?.stepInfo.params.findInfo.findDesc}
                onBlur={(data) => {
                    currentStep.stepInfo.params.findInfo.findDesc = data;
                    handleUpdateStep && handleUpdateStep(currentStep);
                }}
            />
            <div className={styles.imgContent}>
                <StepOperator
                    {...props}
                    imgSrc={imgSrc}
                    setLoading={setLoading}
                    isCanvas={isCanvas}
                    setIsCanvas={setIsCanvas}
                    operaOptions={{
                        showActionType: true,
                        showCameraOpera:
                            !['readOnly', 'debug', 'execute']?.includes(editType) &&
                            [1, 2]?.includes(curOsType) &&
                            isElectron(),
                        showDrawOpera: !['readOnly', 'debug', 'execute']?.includes(editType),
                        showSettingOpera: true
                    }}
                />
                <Spin spinning={loading} tip="图片加载中...">
                    <ImageCanvas
                        {...props}
                        rect={currentStep.stepInfo.params.findInfo.rect}
                        isCanvas={isCanvas}
                        imgSrc={imgSrc}
                        stepWidth={
                            currentStep?.stepInfo?.params?.recordInfo?.deviceInfo?.screenSize?.width
                        }
                        stepHeight={
                            currentStep?.stepInfo?.params?.recordInfo?.deviceInfo?.screenSize
                                ?.height
                        }
                        onFinish={({ x, y, width, height }) => {
                            currentStep.stepInfo.params.findInfo.rect = {
                                x,
                                y,
                                w: width,
                                h: height
                            };
                            handleUpdateStep && handleUpdateStep(currentStep);
                        }}
                    />
                </Spin>
            </div>
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace
}))(AILocate);
