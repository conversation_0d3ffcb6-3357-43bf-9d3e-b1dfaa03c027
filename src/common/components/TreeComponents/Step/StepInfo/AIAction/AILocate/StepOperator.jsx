import { useRef } from 'react';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import {
    SelectOperator,
    SettingOperator,
    CameraOperator,
    DrawOperator
} from 'COMMON/components/TreeComponents/Step/StepInfo/components';
import Setting from './Setting';
import styles from 'COMMON/components/TreeComponents/Step/StepInfo/StepInfo.module.less';

function StepOperator(props) {
    const { operaOptions, isCanvas, setIsCanvas, currentStep, curOsType, handleUpdateStep } = props;
    const settingRef = useRef();

    return (
        <div className={styles.stepOperator}>
            {operaOptions?.showActionType && (
                <SelectOperator
                    {...props}
                    border={false}
                    findInfoType="widgetInfo"
                    includeOptions={[]}
                />
            )}
            {operaOptions?.showDrawOpera && (
                <DrawOperator
                    {...props}
                    isActived={isCanvas}
                    onClick={() => setIsCanvas(!isCanvas)}
                    onClear={() => {
                        let newCurrentStep = JSON.parse(JSON.stringify(currentStep));
                        delete newCurrentStep.stepInfo.params.findInfo.rect;
                        handleUpdateStep && handleUpdateStep(newCurrentStep);
                    }}
                />
            )}
            {operaOptions?.showDrawOpera && (
                <CameraOperator
                    {...props}
                    onChange={(url, screenSize) => {
                        let newCurrentStep = JSON.parse(JSON.stringify(currentStep));
                        newCurrentStep.stepInfo.params.recordInfo.deviceInfo = {
                            deviceType: curOsType === 2 ? 'ios' : 'android',
                            screenshot: url,
                            screenSize
                        };
                        delete newCurrentStep.stepInfo.params.findInfo.rect;
                        handleUpdateStep && handleUpdateStep(newCurrentStep);
                    }}
                />
            )}
            {operaOptions?.showSettingOpera && (
                <SettingOperator
                    {...props}
                    onClick={() => {
                        settingRef.current?.show();
                    }}
                />
            )}
            <Setting {...props} ref={settingRef} />
        </div>
    );
}

export default connectModel([commonModel], (state) => ({
    recordId: state.common.case.recordId,
    pageSourceSwitch: state.common.case.pageSourceSwitch
}))(StepOperator);
