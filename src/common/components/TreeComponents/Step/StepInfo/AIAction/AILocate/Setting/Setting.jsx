import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Drawer, Tabs } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import ParamsTitle from 'COMMON/components/ParamsTitle';
import {
    ClickWait,
    DomWait,
    BeforeSysAlertClear,
    StepInterval,
    Retry,
    ScreenFind,
    IfThen
} from 'COMMON/components/TreeComponents/Step/StepInfo/components';
import BaseInfo from './BaseInfo';

function Setting(props, ref) {
    const { setShowModal, currentStep, curOsType, innerHeight, innerWidth, handleUpdateStep } =
        props;
    const [open, setOpen] = useState(false);
    const [settingWidth, setSettingWidth] = useState(0);
    const [recordStep, setRecordStep] = useState(null);

    useEffect(() => {
        updateSettingWidth(window.innerHeight, window.innerWidth);
    }, [innerHeight, innerWidth]);

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    const updateSettingWidth = (innerHeight, innerWidth) => {
        let screenWidth = currentStep?.stepInfo.params.recordInfo.deviceInfo.screenSize.width;
        let screenHeight = currentStep?.stepInfo.params.recordInfo.deviceInfo.screenSize.height;
        if (screenWidth < screenHeight || curOsType === 2) {
            let imgHeight = innerHeight - 200;
            let imageScale = imgHeight / screenHeight;
            let imgWidth = imageScale * screenWidth;
            setSettingWidth(imgWidth + 700);
        } else {
            let imgWidth = innerWidth - 200;
            let imageScale = imgWidth / screenWidth;
            let imgHeight = imageScale * screenHeight;
            setSettingWidth(imgHeight + 350);
        }
    };

    const showModal = () => {
        setShowModal(true);
        setOpen(true);
        setRecordStep(JSON.stringify(currentStep.stepInfo));
    };

    const hideModal = async () => {
        let newActionInfo = JSON.parse(JSON.stringify(currentStep.stepInfo));
        if (JSON.stringify(newActionInfo) === recordStep) {
            setShowModal(false);
            setRecordStep(null);
            setOpen(false);
            return;
        }
        handleUpdateStep &&
            (await handleUpdateStep({
                ...currentStep,
                stepInfo: newActionInfo
            }));
        setShowModal(false);
        setOpen(false);
    };

    return (
        <Drawer
            title="智能定位"
            open={open}
            centered
            width={settingWidth}
            onClose={hideModal}
            footer={null}
        >
            <Tabs
                tabPosition="left"
                defaultActiveKey={2}
                items={[
                    {
                        label: '基础配置',
                        key: 1,
                        children: (
                            <div style={{ margin: '0 10%' }}>
                                <ParamsTitle text="查找失败重试配置" />
                                <Retry {...props} />
                                <ParamsTitle text="操作配置" />
                                <IfThen {...props} />
                                <DomWait {...props} />
                                <ClickWait {...props} />
                                <ParamsTitle text="查找配置" />
                                <ScreenFind {...props} />
                                <ParamsTitle text="步骤参数" />
                                <BeforeSysAlertClear {...props} />
                                <StepInterval {...props} />
                            </div>
                        )
                    },
                    {
                        label: '查找方式',
                        key: 2,
                        children: <BaseInfo {...props} />
                    }
                ]}
            />
        </Drawer>
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal
}))(forwardRef(Setting));
