import { isEmpty } from 'lodash';
import Loading from 'COMMON/components/common/Loading';
import NoContent from 'COMMON/components/common/NoContent';
import { STEP_TYPE } from 'COMMON/components/TreeComponents/Step/const';
import SystemAction from './SystemAction';
import ScreenAction from './ScreenAction';
import ManualAction from './ManualAction';
import AIAction from './AIAction';
import ApiAction from './ApiAction';
import DomAction from './DomAction';
import GroupAction from './GroupAction';
import styles from './StepInfo.module.less';

function StepInfoWithList(props) {
    const { currentStep, loading, handleUpdateStep = () => {} } = props;

    if (loading) {
        return <Loading />;
    }

    return (
        <div className={styles.stepDetail}>
            {isEmpty(currentStep) ? (
                <NoContent text="未选择步骤" className={styles.noContent} />
            ) : (
                <>
                    <div className={styles.stepDetailInfo}>
                        {STEP_TYPE.GROUP?.includes(currentStep?.stepType) && (
                            <GroupAction {...props} handleUpdateStep={handleUpdateStep} />
                        )}
                        {[...STEP_TYPE.SYSTEM, ...STEP_TYPE.ASSERT]?.includes(
                            currentStep?.stepType
                        ) && <SystemAction {...props} handleUpdateStep={handleUpdateStep} />}
                        {STEP_TYPE.MANUAL?.includes(currentStep?.stepType) && (
                            <ManualAction {...props} handleUpdateStep={handleUpdateStep} />
                        )}
                        {STEP_TYPE?.SCREEN?.includes(currentStep?.stepType) && (
                            <ScreenAction {...props} handleUpdateStep={handleUpdateStep} />
                        )}
                        {STEP_TYPE.DOM?.includes(currentStep?.stepType) ? (
                            <DomAction {...props} handleUpdateStep={handleUpdateStep} />
                        ) : null}
                        {STEP_TYPE.AI?.includes(currentStep?.stepType) ? (
                            <AIAction {...props} />
                        ) : null}
                        {STEP_TYPE.API?.includes(currentStep?.stepType) ? (
                            <ApiAction {...props} />
                        ) : null}
                    </div>
                </>
            )}
        </div>
    );
}

export default StepInfoWithList;
