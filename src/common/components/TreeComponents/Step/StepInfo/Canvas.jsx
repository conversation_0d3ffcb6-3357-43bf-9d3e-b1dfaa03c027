import {useState, useRef, useEffect} from 'react';
import {Spin} from 'antd';

function CanvasScreenshot(props) {
    const {width, height, draw, src, ratio, classText, step, needImgSize = false} = props;
    const [loading, setLoading] = useState(true);
    const [imgDimensions, setImgDimensions] = useState({width: 0, height: 0});
    const canvas = useRef({
        width: 0,
        height: 0
    });

    useEffect(() => {
        setLoading(true);
        const context = canvas.current.getContext('2d');
        const img = new Image();
        let url = src;
        if (src.startsWith('http://')) {
            let res = url.split(':');
            url = 'https:' + res[1];
        }
        img.src = url;
        img.onload = () => {
            let canvasWidth = width * ratio;
            let canvasHeight = height * ratio;
            // 涉及到长图片长宽，所以需要先拿到图片的宽高
            if (needImgSize) {
                canvasWidth = img.width;
                canvasHeight = img.height;
                // 拿到图片的宽高
                const containerWidth = width ? width - 20 : window.innerWidth * 0.35;
                // 适配容器宽度
                const scale = containerWidth / canvasWidth;
                setImgDimensions({
                    width: canvasWidth * scale,
                    height: canvasHeight * scale
                });
            } else {
                canvasWidth = width * ratio;
                canvasHeight = height * ratio;
                setImgDimensions({
                    width: canvasWidth,
                    height: canvasHeight
                });
            }
            if (!canvas.current) {
                return;
            }
            canvas.current.width = canvasWidth;
            canvas.current.height = canvasHeight;
            context.drawImage(img, 0, 0, canvasWidth, canvasHeight);
            draw(context);
            setLoading(false);
        };
    }, [src, step]);

    return (
        <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
            <Spin spinning={loading}>
                <div
                    style={{
                        width,
                        height,
                        overflow: 'auto'
                    }}
                >
                    <canvas
                        ref={canvas}
                        style={{
                            width: !needImgSize ? width : imgDimensions.width,
                            height: !needImgSize ? height : imgDimensions.height,
                            border: '1px solid #eee'
                        }}
                        className={classText}
                    />
                </div>
            </Spin>
        </div>
    );
}

export default CanvasScreenshot;
