import { CloudUploadOutlined, VideoCameraAddOutlined } from '@ant-design/icons';
import { Upload, Tooltip, Space, message } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import electron from 'COMMON/utils/electron';
import { fileImageUpload } from 'COMMON/utils/utils';
import commonModel from 'COMMON/models/commonModel';
import NoContent from 'COMMON/components/common/NoContent';
import { updateStep } from 'COMMON/api/front_qe_tools/step';
import { createPoint } from 'COMMON/api/front_qe_tools/points';
import { getQueryParams } from 'COMMON/utils/utils';
import styles from './ManualAction.module.less';

function InitOperator(props) {
    const {
        currentDevice,
        curOsType,
        currentStep,
        editType,
        setLoading,
        setRecording,
        deviceList,
        handleUpdateStep,
        currentSpace
    } = props;
    const [messageApi, contextHolder] = message.useMessage();
    const query = getQueryParams();
    const beforeUpload = (file) => {
        const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
        if (!isJpgOrPng) {
            messageApi.error('请上传 JPG/PNG 格式图片!');
        }
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isLt2M) {
            messageApi.error('图片必须小于 2MB!');
        }
        return isJpgOrPng && isLt2M;
    };

    const customRequest = async (options) => {
        return new Promise(function (resolve, reject) {
            const fileReader = new FileReader();
            fileReader.onload = (e) => {
                const src = e.target.result;
                const image = new Image();
                image.onload = async function () {
                    uploadImageAction({
                        file: options.file,
                        url: e.target.result,
                        screenSize: { width: this.width, height: this.height }
                    });
                };
                image.onerror = reject;
                image.src = src;
            };
            fileReader.readAsDataURL(options.file);
        });
    };

    const uploadImageAction = async ({ file, url, screenSize }) => {
        setLoading(true);
        fileImageUpload(file)
            .then(async (res) => {
                let newCurrentStep = JSON.parse(JSON.stringify(currentStep));
                newCurrentStep.stepInfo.params.params.img = res?.url;
                newCurrentStep.stepInfo.params.params.width = screenSize.width;
                newCurrentStep.stepInfo.params.params.height = screenSize.height;
                newCurrentStep.stepInfo.params.params.rect = {
                    x: 0,
                    y: 0,
                    width: 0,
                    height: 0
                };
                let body = {
                    stepId: newCurrentStep.stepId,
                    stepInfo: newCurrentStep.stepInfo
                };
                await updateStep(body);
                createPoint({
                    moduleId: currentSpace?.id, // 业务模块id；int；必填
                    caseNodeId: query?.caseNodeId, // 用例节点Id；int；必填
                    stepId: currentStep?.stepId, // 步骤Id；int；必填
                    osType: curOsType, // 端类型，int；必填 1-Android 2-iOS 3-Android&iOS 4-server 5-web
                    pointType: 1100, // 打点类型；int；必填 1000-创建步骤 1001-选中步骤 1100-update
                    pointInfo: {}, // 点位内容；json；选填（预留字段，当前传空 json 就行）
                    createTime: Math.floor(new Date().getTime() / 1000) // 打点时间；int；必填
                }).catch(() => {});
                handleUpdateStep(newCurrentStep);
                setLoading(false);
            })
            .catch((err) => {
                console.log(err?.message ?? err);
                setLoading(false);
            });
    };

    const options = {
        customRequest,
        maxCount: 1,
        beforeUpload
    };
    return (
        <div className={styles.initOperator}>
            {contextHolder}
            {['readonly', 'debug', 'execute'].includes(editType) ? (
                <NoContent text={'未添加图片'} className={styles.noContent} />
            ) : (
                <Space direction="vertical" size="middle" className={styles.uploadBtnList}>
                    <Upload {...options}>
                        <Tooltip title="上传截屏">
                            <span className={styles.uploadBtn}>
                                <CloudUploadOutlined className={styles.uploadBtnIcon} />
                            </span>
                        </Tooltip>
                    </Upload>
                    {isElectron() && [1, 2]?.includes(curOsType) ? (
                        <Tooltip title="设备截屏">
                            <span
                                className={styles.uploadBtn}
                                onClick={async () => {
                                    try {
                                        if (!isElectron()) {
                                            return false;
                                        }
                                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                                            return false;
                                        }
                                        // 确定有设备连接
                                        let gotDevice = false;
                                        for (let device of deviceList[
                                            2 === +curOsType ? 'iOS' : 'android'
                                        ]) {
                                            if (device.deviceId === currentDevice?.deviceId) {
                                                gotDevice = true;
                                                if (![2].includes(device?.status)) {
                                                    message.error('请确保设备状态正常');
                                                    return false;
                                                }
                                                break;
                                            }
                                        }
                                        if (!gotDevice) {
                                            message.error('请确保有设备连接');
                                            return false;
                                        }
                                        setRecording(true);
                                        let { screenshot, screenSize } = await electron.send(
                                            'device.record.screenshot',
                                            {
                                                deviceType: curOsType,
                                                deviceId: currentDevice?.deviceId
                                            }
                                        );
                                        uploadImageAction({
                                            file: { name: 'screenShot.png' },
                                            url: screenshot,
                                            screenSize
                                        });
                                    } catch (err) {
                                        message.error(err.message ? err.message : err);
                                    } finally {
                                        setRecording(false);
                                    }
                                }}
                            >
                                <VideoCameraAddOutlined className={styles.uploadBtnIcon} />
                            </span>
                        </Tooltip>
                    ) : null}
                </Space>
            )}
        </div>
    );
}

export default connectModel([commonModel, baseModel], (state) => ({
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace
}))(InitOperator);
