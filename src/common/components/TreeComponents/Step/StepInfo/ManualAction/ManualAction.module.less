@import "RESOURCES/css/common.less";

.noContent {
    margin-top: 100px;
}

.imgContent {
    position: relative;
    padding-top: 60px;
    text-align: center;
}

.initOperator {
    position: absolute;
    top: 200px;
    left: 50%;
    transform: translate(-50%, -50%);

    :global {

        .ant-upload-list,
        .custom-default-upload-list,
        .custom-dark-upload-list {
            display: none;
        }
    }

    .uploadBtnList {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .uploadBtn {
        position: relative;
        display: inline-block;
        width: 100px;
        height: 100px;
        line-height: 100px;
        border: 1px solid var(--border-color);
        border-radius: 100%;
        background-color: var(--background-color);
    }

    .uploadBtnIcon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-size: 50px;
        color: #bfbfbf;
    }

    .uploadBtn:hover .uploadBtnIcon {
        color: #709cf9;
    }
}