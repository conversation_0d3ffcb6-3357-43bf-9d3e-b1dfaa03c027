import { useState, useEffect } from 'react';
import { Spin, message } from 'antd';
import { getBosToken } from 'COMMON/api/base/common';
import { ImageCanvas } from '../components';
import InitOperator from './InitOperator';
import StepOperator from './StepOperator';
import styles from './ManualAction.module.less';

function ManualAction(props) {
    const { curOsType, currentStep, editType, handleUpdateStep } = props;
    const [isCanvas, setIsCanvas] = useState(false);
    const [imgSrc, setImgSrc] = useState('');
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        let newImg = currentStep?.stepInfo.params.params?.img;
        if (newImg?.startsWith('http') && -1 !== newImg.indexOf('https://hydra.bj.bcebos.com')) {
            getBosToken({ bosLink: newImg }).then((res) => {
                if (!res?.token) {
                    message.error('身份校验失败，获取图片失败');
                }
                newImg += '?authorization=' + res.token;
                setImgSrc(newImg);
                setLoading(false);
            });
        } else {
            setImgSrc(newImg);
            setLoading(false);
        }
    }, [currentStep]);

    return (
        <div className={styles.imgContent}>
            {'' === currentStep?.stepInfo?.params?.params?.img ? (
                <InitOperator {...props} setLoading={setLoading} setImgSrc={setImgSrc} />
            ) : (
                <>
                    <StepOperator
                        {...props}
                        setLoading={setLoading}
                        isCanvas={isCanvas}
                        setIsCanvas={setIsCanvas}
                        setImgSrc={setImgSrc}
                        operaOptions={{
                            showUploadOpera: !['readOnly', 'debug', 'execute']?.includes(editType),
                            showDrawOpera: !['readOnly', 'debug', 'execute']?.includes(editType),
                            showCameraOpera:
                                !['readOnly', 'debug', 'execute']?.includes(editType) &&
                                [1, 2]?.includes(curOsType) &&
                                isElectron()
                        }}
                    />
                    <Spin spinning={loading} tip="图片加载中...">
                        <ImageCanvas
                            {...props}
                            rect={currentStep.stepInfo.params.params.rect}
                            isCanvas={isCanvas}
                            imgSrc={imgSrc}
                            stepWidth={currentStep?.stepInfo?.params?.params?.width}
                            stepHeight={currentStep?.stepInfo?.params?.params?.height}
                            onFinish={({ x, y, width, height }) => {
                                currentStep.stepInfo.params.params.rect = {
                                    x,
                                    y,
                                    width,
                                    height
                                };
                                handleUpdateStep(currentStep);
                            }}
                        />
                    </Spin>
                </>
            )}
        </div>
    );
}

export default ManualAction;
