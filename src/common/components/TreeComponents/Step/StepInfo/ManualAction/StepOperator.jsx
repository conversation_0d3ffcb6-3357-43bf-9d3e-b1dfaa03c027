import {
    CameraOperator,
    DrawOperator,
    UploadOperator
} from 'COMMON/components/TreeComponents/Step/StepInfo/components';
import styles from 'COMMON/components/TreeComponents/Step/StepInfo/StepInfo.module.less';

function StepOperator(props) {
    const { currentStep, operaOptions, isCanvas, setIsCanvas, handleUpdateStep } = props;
    const handleUpdateImg = (url, screenSize) => {
        let newCurrentStep = JSON.parse(JSON.stringify(currentStep));
        newCurrentStep.stepInfo.params.params = {
            ...newCurrentStep.stepInfo.params.params,
            img: url,
            width: screenSize.width,
            height: screenSize.height,
            rect: {
                x: 0,
                y: 0,
                width: 0,
                height: 0
            }
        };
        handleUpdateStep(newCurrentStep);
    };

    return (
        <div className={styles.stepOperator}>
            {operaOptions?.showDrawOpera && (
                <DrawOperator
                    {...props}
                    isActived={isCanvas}
                    onClick={() => setIsCanvas(!isCanvas)}
                    onClear={() => {
                        let newCurrentStep = JSON.parse(JSON.stringify(currentStep));
                        delete newCurrentStep.stepInfo.params.params.rect;
                        handleUpdateStep && handleUpdateStep(newCurrentStep);
                    }}
                />
            )}
            {operaOptions?.showUploadOpera ? (
                <UploadOperator {...props} onChange={handleUpdateImg} />
            ) : null}
            {operaOptions?.showCameraOpera && (
                <CameraOperator {...props} onChange={handleUpdateImg} />
            )}
        </div>
    );
}

export default StepOperator;
