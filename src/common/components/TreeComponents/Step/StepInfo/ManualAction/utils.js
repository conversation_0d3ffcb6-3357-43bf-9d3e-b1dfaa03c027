export function getScreenPos(step, stepWidth, stepHeight) {
    let screenPos = {
        x: 0,
        y: 0,
        targetX: 0,
        targetY: 0,
    };
    if (
        3 === step?.stepInfo.type &&
        step.stepInfo.params.params &&
        step.stepInfo.params.params.rect
    ) {
        let {x, y, width, height} = step.stepInfo.params.params.rect;
        screenPos.x = x > 1 ? x / stepWidth : x;
        screenPos.y = y > 1 ? y / stepHeight : y;
        screenPos.targetX = x > 1 ? (x + width) / stepWidth : x + width;
        screenPos.targetY = y > 1 ? (y + height) / stepHeight : y + height;
    }
    return screenPos;
};