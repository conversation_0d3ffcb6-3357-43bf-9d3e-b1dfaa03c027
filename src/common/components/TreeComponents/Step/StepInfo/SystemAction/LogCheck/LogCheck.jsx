import {useState, useEffect} from 'react';
import {isEmpty} from 'lodash';
import {Button, Select, Input, message, Space} from 'antd';
import electron from 'COMMON/utils/electron';
import {CardTitle} from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function LogCheck(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [logNo, setLogNo] = useState(null);
    const [rules, setRules] = useState([]);
    const [ruleId, setRuleId] = useState(null);
    const [ruleName, setRuleName] = useState('');
    const [productName, setProductName] = useState('');

    useEffect(() => {
        setLogNo(currentStep?.stepInfo.params.params.ubcId);
        setRuleId(currentStep?.stepInfo.params.params.id);
        setRuleName(currentStep?.stepInfo.params.params.name);
        setProductName(currentStep?.stepInfo.params.params.productName);
        if (currentStep?.stepInfo.params.params.id !== null) {
            setRules([{
                id: currentStep?.stepInfo.params.params.id,
                name: currentStep?.stepInfo.params.params.name,
                productName: currentStep?.stepInfo.params.params.productName
            }]);
        } else {
            setRules([]);
        }
    }, [currentStep?.stepId]);

    const onChange = async (productName, ruleName, ruleId) => {
        let newCurrentStep = {...currentStep};
        newCurrentStep.stepInfo.params.params.id = ruleId;
        newCurrentStep.stepInfo.params.params.name = ruleName;
        newCurrentStep.stepInfo.params.params.productName = productName;
        newCurrentStep.stepInfo.params.params.ubcId = logNo;
        await handleUpdateStep(newCurrentStep);
    };

    return (
        <div>
            <CardTitle text='操作参数' />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>
                    点位编号
                </span>
                <Space.Compact className={styles.paramsSelectData}>
                    <Input
                        placeholder={'输入点位编号'}
                        className='inputEditor'
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        value={logNo}
                        onChange={async (e) => {
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return false;
                            }
                            setLogNo(e.target.value);
                        }}
                        onBlur={async (e) => {
                            let newCurrentStep = {...currentStep};
                            newCurrentStep.stepInfo.params.params.ubcId = e.target.value;
                            await handleUpdateStep(newCurrentStep);
                        }}
                    />
                    <Button
                        type='primary'
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        onClick={async (e) => {
                            if (logNo === '' || logNo === undefined || logNo === null) {
                                message.error('请输入点位编号');
                                return false;
                            }
                            let res = await electron.send('logcheck.get', {ubcId: logNo});
                            let newRules = res.map(item => ({
                                id: item.id,
                                productName: item.productName,
                                name: item.name
                            }));
                            if (isEmpty(newRules)) {
                                message.error('该点位编号下无规则，请重新输入');
                                return false;
                            }
                            setRules(newRules);
                            setProductName(newRules[0].productName);
                            setRuleId(newRules[0].id);
                            setRuleName(newRules[0].name);
                            onChange(newRules[0].productName, newRules[0].name, newRules[0].id);

                            message.success('获取成功');
                        }}
                    >
                        获取规则
                    </Button>
                </Space.Compact>
            </div>
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>
                    业务线
                </span>
                <Select
                    showSearch
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    className={styles.paramsSelectData}
                    placeholder="请设置业务线"
                    options={[...new Set(rules.map(item => item.productName))].map(item => ({
                        key: item,
                        value: item,
                        label: item
                    }))}
                    filterOption={(input, option) => (option?.label ?? '').includes(input)}
                    value={productName === '' ? undefined : productName}
                    onChange={(value) => {
                        setProductName(value);
                        let arr = rules.filter(item => item.productName === value);
                        if (isEmpty(arr)) {
                            setRuleId(null);
                            setRuleName('');
                            onChange(value, '', null);
                        } else {
                            setRuleId(arr[0].id);
                            setRuleName(arr[0].name);
                            onChange(value, arr[0].name, arr[0].id);
                        }

                    }}
                />
            </div>
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>点位内容</span>
                <Select
                    showSearch
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    className={styles.paramsSelectData}
                    placeholder="请设置点位内容"
                    options={rules.filter(item => item.productName === productName).map(item => ({
                        key: item.id,
                        id: item.id,
                        value: item.name,
                        label: item.name,
                    }))}
                    value={ruleName === '' ? undefined : ruleName}
                    filterOption={(input, option) => (option?.label ?? '').includes(input)}
                    onChange={(value, option) => {
                        setRuleId(option?.id);
                        setRuleName(value);
                        onChange(productName, value, option?.id);
                    }}
                />
            </div>
            <CommonConfig {...props} />
        </div>
    );
};

export default LogCheck;
