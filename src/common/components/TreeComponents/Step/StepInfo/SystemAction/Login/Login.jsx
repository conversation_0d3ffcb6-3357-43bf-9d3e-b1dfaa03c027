import { useState, useEffect, useRef } from 'react';
import { Alert, Tooltip, message } from 'antd';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { CardTitle } from 'COMMON/components/common/Card';
import SelectWithDropdownRender from 'COMMON/components/Select/SelectWithDropdownRender';
import RunCaseSettingModal from 'FEATURES/front_qe_tools/case/edit/EditPage/Modal/RunCaseSettingModal/RunCaseSettingModal';
import AppModal from 'COMMON/components/TreeComponents/components/AppModal/AppModal';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function Login(props) {
    const { currentStep, curOsType, appList, editType, setShowModal, handleUpdateStep } = props;

    const [appId, setAppId] = useState(null);
    const [appName, setAppName] = useState('');
    const appModalRef = useRef();
    const appAddModalRef = useRef();

    useEffect(() => {
        setAppName(currentStep?.stepInfo.params.params.appName);
        setAppId(
            currentStep?.stepInfo.params.params.id ? currentStep?.stepInfo.params.params.id : null
        );
    }, [currentStep]);

    let appNameOptions = [];
    appNameOptions = appList?.[curOsType]?.map((item) => {
        return {
            label: item.appName,
            value: item.appName
        };
    });

    const showAppModal = () => {
        setShowModal(true);
        appModalRef?.current.show({ key: 'app-config' });
    };

    const showAppAddModal = () => {
        setShowModal(true);
        appAddModalRef?.current?.show(curOsType);
    };

    return (
        <div>
            <br />
            <Alert message="该步骤地添加可能会对用例执行时长产生很大的影响" type="info" showIcon />
            <CardTitle text="操作参数" />
            <div className={styles.paramsSelect}>
                <Tooltip title={`ID: ${appId}`} placement="left">
                    <span className={styles.paramsSelectTitle}>APP 名称</span>
                </Tooltip>
                <SelectWithDropdownRender
                    className={styles.paramsSelectData}
                    editType={editType}
                    placeholder="请选择 APP 名称"
                    text="APP 选择"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    showSearch
                    showSearchIcon={false}
                    filterOption={(input, option) => option.label.includes(input)}
                    value={appName}
                    options={appNameOptions}
                    onChange={async (value) => {
                        const selectApp = appList?.[curOsType]?.find(
                            (item) => item.appName === value
                        );
                        let newId = selectApp?.appId;
                        let newCurrentStep = { ...currentStep };
                        newCurrentStep.stepInfo.params.params.id = newId;
                        newCurrentStep.stepInfo.params.params.appName = value;
                        newCurrentStep.stepDesc = '登录【' + value + '】';
                        newCurrentStep.stepInfo.desc = newCurrentStep.stepDesc;
                        await handleUpdateStep(newCurrentStep);
                        setAppName(value);
                        setAppId(newId);
                    }}
                    addChange={() => showAppAddModal()}
                    settingChange={() => showAppModal()}
                />
            </div>
            <CommonConfig {...props} />
            <RunCaseSettingModal ref={appModalRef} />
            <AppModal
                ref={appAddModalRef}
                curOsType={curOsType}
                onChange={async (appId, appName, packageName) => {
                    let newCurrentStep = { ...currentStep };
                    newCurrentStep.stepInfo.params.params.id = appId;
                    newCurrentStep.stepInfo.params.params.appName = appName;
                    newCurrentStep.stepInfo.params.params.packageName = packageName;
                    newCurrentStep.stepInfo.desc = '登录【' + appName + '】';
                    await handleUpdateStep(newCurrentStep);
                }}
            />
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    showModal: state.common.base.showModal,
    appList: state.common.case.appList
}))(Login);
