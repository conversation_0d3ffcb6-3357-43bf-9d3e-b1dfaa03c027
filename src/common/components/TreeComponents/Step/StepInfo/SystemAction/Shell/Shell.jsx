import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {Button, Upload, Input, message, Space} from 'antd';
import electron from 'COMMON/utils/electron';
import {CardTitle} from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function Shell(props) {
    const {currentStep, handleUpdateStep,
        editType} = props;
    const [shell, setShell] = useState(null);

    useEffect(() => {
        setShell(currentStep?.stepInfo.params.params?.shell);
    }, [currentStep]);

    const beforeUpload = (file) => {
        const isLt1M = file.size / 1024 / 1024 < 1;
        if (!isLt1M) {
            message.error('文件需要小于 1MB!');
        }
        return isLt1M;
    };

    const customRequest = (options) => {
        const reader = new FileReader();
        reader.readAsDataURL(options.file);
        reader.onloadend = function (e) {
            uploadImageAction({file: options.file, url: e.target.result});
        };
    };

    const uploadImageAction = async ({file}) => {
        try {
            let bosUrl = '';
            setFileLink('');
            message.info('上传中，请稍后');
            if (isElectron()) {
                bosUrl = await electron.send('file.uploadFileToBos', {
                    filePath: file.path
                });
            }
            let newCurrentStep = {...currentStep};
            newCurrentStep.stepInfo.params.params.fileLink = bosUrl;
            await handleUpdateStep(newCurrentStep);
            message.success('上传成功');
        } catch (err) {
            console.log(err?.message ?? err);
        }
    };

    const options = {
        customRequest,
        maxCount: 1,
        beforeUpload
    };

    return (
        <div>
            <CardTitle text='操作参数' />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>Shell 命令</span>
                <Input
                    className={classnames(styles.paramsSelectData, 'inputEditor')}
                    placeholder='请输入'
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    value={shell}
                    onChange={(e) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        setShell(e.target.value);
                    }}
                    onBlur={async (e) => {
                        try {
                            let newCurrentStep = {...currentStep};
                            newCurrentStep.stepInfo.params.params.shell = e.target.value.trim();
                            await handleUpdateStep(newCurrentStep);
                        } catch (err) {
                        }
                    }}
                />
            </div>
            <CommonConfig {...props} />
        </div>
    );
};

export default Shell;
