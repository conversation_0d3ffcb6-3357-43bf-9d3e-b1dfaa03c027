import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {UploadOutlined} from '@ant-design/icons';
import {Button, Upload, Input, message, Space} from 'antd';
import electron from 'COMMON/utils/electron';
import {CardTitle} from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function PushFile(props) {
    const {currentStep, handleUpdateStep,
        editType} = props;
    const [fileLink, setFileLink] = useState(null);
    const [targetPath, setTargetPath] = useState([]);

    useEffect(() => {
        setFileLink(currentStep?.stepInfo.params.params?.fileLink);
        setTargetPath(currentStep?.stepInfo.params.params?.targetPath.slice(8,
            currentStep?.stepInfo.params.params?.targetPath.length));
    }, [currentStep]);

    const beforeUpload = (file) => {
        const isLt1M = file.size / 1024 / 1024 < 1;
        if (!isLt1M) {
            message.error('文件需要小于 1MB!');
        }
        return isLt1M;
    };

    const customRequest = (options) => {
        const reader = new FileReader();
        reader.readAsDataURL(options.file);
        reader.onloadend = function (e) {
            uploadImageAction({file: options.file, url: e.target.result});
        };
    };

    const uploadImageAction = async ({file}) => {
        try {
            let bosUrl = '';
            setFileLink('');
            message.info('上传中，请稍后');
            if (isElectron()) {
                bosUrl = await electron.send('file.uploadFileToBos', {
                    filePath: file.path
                });
            }
            let newCurrentStep = {...currentStep};
            newCurrentStep.stepInfo.params.params.fileLink = bosUrl;
            await handleUpdateStep(newCurrentStep);
            message.success('上传成功');
        } catch (err) {
            console.log(err?.message ?? err);
        }
    };

    const options = {
        customRequest,
        maxCount: 1,
        beforeUpload
    };

    return (
        <div>
            <CardTitle text='操作参数' />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>文件链接</span>
                <Space.Compact className={styles.paramsSelectData}>
                    <Input
                        className='inputEditor'
                        placeholder='暂无文件链接'
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        value={fileLink}
                    />
                    <Upload {...options}>
                        <Button
                            type='primary'
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            icon={<UploadOutlined />}
                        >
                            上传
                        </Button>
                    </Upload>
                </Space.Compact>
            </div>
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>推送路径</span>
                <Input
                    className={classnames(styles.paramsSelectData, 'inputEditor')}
                    placeholder='请输入具体路径'
                    addonBefore='/sdcard/'
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    value={targetPath}
                    onChange={(e) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        setTargetPath(e.target.value);
                    }}
                    onBlur={async (e) => {
                        try {
                            let newCurrentStep = {...currentStep};
                            newCurrentStep.stepInfo.params.params.targetPath = '/sdcard/' + e.target.value.trim();
                            await handleUpdateStep(newCurrentStep);
                        } catch (err) {
                        }
                    }}
                />
            </div>
            <CommonConfig {...props} />
        </div>
    );
};

export default PushFile;
