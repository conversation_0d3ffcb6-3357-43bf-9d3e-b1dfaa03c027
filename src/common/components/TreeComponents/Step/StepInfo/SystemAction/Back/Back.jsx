import {Input} from 'antd';
import {CardTitle} from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function Back(props) {
    return (
        <div>
            <CardTitle text='操作参数' />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>返回操作</span>
                <Input
                    disabled
                    className={styles.paramsSelectData}
                    value={'Back'}
                />
            </div>
            <CommonConfig {...props} />
        </div>
    );
};

export default Back;
