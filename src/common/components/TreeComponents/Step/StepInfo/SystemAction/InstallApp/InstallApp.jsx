import { useState, useEffect, useRef } from 'react';
import classnames from 'classnames';
import { isEmpty } from 'lodash';
import { Tooltip, Input, Radio, Alert, Spin } from 'antd';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { CardTitle } from 'COMMON/components/common/Card';
import SelectWithDropdownRender from 'COMMON/components/Select/SelectWithDropdownRender';
import RunCaseSettingModal from 'FEATURES/front_qe_tools/case/edit/EditPage/Modal/RunCaseSettingModal/RunCaseSettingModal';
import AppModal from 'COMMON/components/TreeComponents/components/AppModal/AppModal';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function InstallApp(props) {
    const {
        currentStep,
        currentSpace,
        appList,
        setAppList,
        curOsType,
        editType,
        setShowModal,
        handleUpdateStep
    } = props;
    const [appId, setAppId] = useState(null);
    const [appName, setAppName] = useState(null);
    const [installType, setInstallType] = useState(1);
    const [fileLink, setFileLink] = useState('');
    const appModalRef = useRef();
    const appAddModalRef = useRef();

    // 初始化安装 App 步骤
    const initInstallAppStep = (step, app) => {
        let newCurrentStep = { ...step };
        newCurrentStep.stepInfo.params.params.id = app?.appId;
        newCurrentStep.stepInfo.params.params.appName = app?.appName;
        newCurrentStep.stepInfo.params.params.packageName = app?.packageName;
        let desc =
            1 === newCurrentStep.stepInfo.params.params.installType
                ? '卸载安装' + app?.appName + '】'
                : '覆盖安装' + app?.appName + '】';
        newCurrentStep.stepInfo.desc = desc;
        newCurrentStep.stepDesc = desc;
        setAppId(app?.appId);
        setAppName(app?.appName);
        setInstallType(newCurrentStep?.stepInfo.params.params?.installType ?? 1);
        setFileLink(newCurrentStep?.stepInfo.params.params?.fileLink ?? '');
        handleUpdateStep(newCurrentStep);
    };

    // 监听当前步骤 stepId 变化
    useEffect(() => {
        if (currentStep?.stepInfo.params.params.id) {
            setAppId(currentStep?.stepInfo?.params?.params?.id);
            setAppName(currentStep?.stepInfo?.params?.params?.appName);
            setInstallType(currentStep?.stepInfo.params.params?.installType ?? 1);
            setFileLink(currentStep?.stepInfo.params.params?.fileLink ?? '');
        } else {
            let _app = appList?.[curOsType]?.[0];
            if (['template', 'edit'].includes(editType)) {
                if (!currentStep?.stepInfo.params.params.id && _app) {
                    initInstallAppStep(currentStep, _app);
                }
            }
        }
    }, [currentStep?.stepId, curOsType]);

    let appNameOptions = [];
    appNameOptions = appList?.[curOsType]?.map((item) => {
        return {
            label: item.appName,
            value: item.appName
        };
    });

    const showAppModal = () => {
        setShowModal(true);
        appModalRef?.current.show({ key: 'app-config' });
    };

    const showAppAddModal = () => {
        setShowModal(true);
        appAddModalRef?.current?.show(curOsType);
    };

    const installTypeOptions = [
        {
            label: '卸载安装',
            value: 1
        },
        {
            label: '覆盖安装',
            value: 2
        }
    ];

    return (
        <>
            <br />
            <Alert message="该步骤地添加可能会对用例执行时长产生很大的影响" type="info" showIcon />
            <CardTitle text="操作参数" />
            <div className={styles.paramsSelect}>
                <Tooltip title={`ID: ${appId}`} placement="left">
                    <span className={styles.paramsSelectTitle}>APP 名称</span>
                </Tooltip>
                <SelectWithDropdownRender
                    className={styles.paramsSelectData}
                    placeholder="请选择 APP 名称"
                    editType={editType}
                    text="APP 选择"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    showSearch
                    showSearchIcon={false}
                    filterOption={(input, option) => option.label.includes(input)}
                    value={appName}
                    options={appNameOptions}
                    onChange={(value) => {
                        const selectApp = appList?.[curOsType]?.find(
                            (item) => item.appName === value
                        );
                        let newId = selectApp?.appId;
                        let newCurrentStep = { ...currentStep };
                        newCurrentStep.stepInfo.params.params.id = newId;
                        newCurrentStep.stepInfo.params.params.appName = value;
                        newCurrentStep.stepDesc =
                            newCurrentStep.stepInfo.params.params.installType === 1
                                ? '卸载安装【' + value + '】'
                                : '覆盖安装【' + value + '】';
                        newCurrentStep.stepInfo.desc = newCurrentStep.stepDesc;
                        setAppName(value);
                        setAppId(newId);
                        handleUpdateStep(newCurrentStep);
                    }}
                    addChange={() => showAppAddModal()}
                    settingChange={() => showAppModal()}
                />
            </div>
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>包地址</span>
                <Input
                    className={classnames(styles.paramsSelectData, 'inputEditor')}
                    placeholder="请输入下载链接"
                    disabled={
                        installType === 2 || ['readonly', 'debug', 'execute'].includes(editType)
                    }
                    value={
                        installType === 2 ? '覆盖安装默认使用自动化小助手配置中的地址' : fileLink
                    }
                    onChange={(e) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        setFileLink(e.target.value);
                    }}
                    onBlur={(e) => {
                        let newCurrentStep = { ...currentStep };
                        newCurrentStep.stepInfo.params.params.fileLink = e.target.value.trim();
                        handleUpdateStep(newCurrentStep);
                    }}
                />
            </div>
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>安装方式</span>
                <Radio.Group
                    className={styles.paramsSelectData}
                    options={installTypeOptions}
                    value={installType}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    optionType="button"
                    buttonStyle="solid"
                    onChange={(e) => {
                        let value = e.target.value;
                        let newCurrentStep = { ...currentStep };
                        newCurrentStep.stepInfo.desc =
                            1 === value
                                ? '卸载安装【' +
                                  newCurrentStep.stepInfo.params.params.appName +
                                  '】'
                                : '覆盖安装【' +
                                  newCurrentStep.stepInfo.params.params.appName +
                                  '】';
                        newCurrentStep.stepInfo.params.params.installType = value;
                        newCurrentStep.stepDesc = newCurrentStep.stepInfo.desc;
                        setInstallType(value);
                        handleUpdateStep(newCurrentStep);
                    }}
                />
            </div>
            <CommonConfig {...props} />
            <RunCaseSettingModal ref={appModalRef} />
            <AppModal
                ref={appAddModalRef}
                curOsType={curOsType}
                onChange={(appId, appName, packageName) => {
                    let newCurrentStep = { ...currentStep };
                    newCurrentStep.stepInfo.params.params.id = appId;
                    newCurrentStep.stepInfo.params.params.appName = appName;
                    newCurrentStep.stepInfo.params.params.packageName = packageName;
                    newCurrentStep.stepInfo.desc =
                        1 === newCurrentStep.stepInfo.params.params.installType
                            ? '卸载安装【' + appName + '】'
                            : '覆盖安装【' + appName + '】';
                    setAppId(appId);
                    setAppName(appName);
                    handleUpdateStep(newCurrentStep);
                }}
            />
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    showModal: state.common.base.showModal,
    appList: state.common.case.appList
}))(InstallApp);
