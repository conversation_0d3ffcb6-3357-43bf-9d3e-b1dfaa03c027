import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {InputNumber, Divider, message} from 'antd';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './Params.module.less';

function ParamsTimes(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [timeout, setTimeout] = useState('POST');

    useEffect(() => {
        setTimeout(currentStep?.stepInfo.params.params.timeout);
    }, [currentStep?.stepId]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                最大请求时长
            </Divider>
            <InputNumber
                className={classnames(styles.paramsEdit, 'inputEditor')}
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                style={{
                    width: '100%'
                }}
                min={0}
                placeholder='请输入最大请求时长'
                addonAfter='ms'
                value={timeout}
                onChange={(value) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    setTimeout(value);
                }}
                onBlur={async (e) => {
                    try {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        let value = 0;
                        if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                            value = parseInt(e.target.value.trim(), 10);
                        }
                        let newCurrentStep = {...currentStep};
                        newCurrentStep.stepInfo.params.params.timeout = e.target.value.trim();
                        await handleUpdateStep(newCurrentStep);
                    } catch (err) {
                    }
                }}
            />
        </>
    );
};

export default ParamsTimes;