import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {Input, Divider, message} from 'antd';
import styles from './Params.module.less';

function ParamsRequestMethod(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [method, setMethod] = useState('POST');

    useEffect(() => {
        setMethod(currentStep?.stepInfo.params.params.method);
    }, [currentStep?.stepId]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                请求方法
            </Divider>
            <Input
                className={classnames(styles.paramsEdit, 'inputEditor')}
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                placeholder='请输入请求方法'
                value={method}
                onChange={(e) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    setMethod(e.target.value);
                }}
                onBlur={async (e) => {
                    try {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        let newCurrentStep = {...currentStep};
                        newCurrentStep.stepInfo.params.params.method = e.target.value.trim();
                        await handleUpdateStep(newCurrentStep);
                    }
                    catch (err) {
                    }
                }}
            />
        </>
    );
};

export default ParamsRequestMethod;