import classnames from 'classnames';
import {useState, useEffect} from 'react';
import {Input, Divider, message, Tooltip} from 'antd';
import {contactUrl} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './Params.module.less';


function ParamsPath(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [url, setUrl] = useState('');
    const [urlPath, setUrlPath] = useState('');

    useEffect(() => {
        if (0 !== Object.keys(currentStep.stepInfo.params.params).length) {
            let {path} = currentStep.stepInfo.params.params.mockRequest.matchRequest;
            setUrl(contactUrl(currentStep.stepInfo.params.params.mockRequest.matchRequest));
            setUrlPath(path);
        }
    }, [currentStep]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                URL
            </Divider>
            {
                -1 === ['readonly', 'debug', 'execute'].indexOf(editType) ?
                    <div className={styles.urlPath}>
                        <Tooltip title={url}>
                            <span className={styles.addBeforePath}>{url}</span>
                        </Tooltip>
                        <Input
                            className={classnames(styles.paramsEdit, styles.pathInput, 'inputEditor')}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            placeholder='请输入 Path'
                            value={urlPath}
                            onChange={(e) => {
                                if (!isElectron() || ['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                setUrlPath(e.target.value);
                            }}
                            onBlur={async (e) => {
                                if (!isElectron() || ['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.path =
                                    e.target.value.trim();
                                await handleUpdateStep(newCurrentStep);
                                setUrlPath(e.target.value.trim());
                            }}
                        />
                    </div> :
                    <Input
                        className={classnames(styles.paramsEdit, 'inputEditor')}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        placeholder='请输入 Path'
                        value={url + urlPath}
                    />
            }
        </>
    );
};

export default ParamsPath;