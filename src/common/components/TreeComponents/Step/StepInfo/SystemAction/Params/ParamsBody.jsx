import classnames from 'classnames';
import {useState, useEffect} from 'react';
import {DeleteOutlined, PlusOutlined} from '@ant-design/icons';
import {Tag, Checkbox, Input, Divider, message} from 'antd';
import {getBody, formatRequest, getNewBody, getVerifyNewBody, isJSON} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './Params.module.less';

const {TextArea} = Input;

function ParamsBody(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [bodyData, setBodyData] = useState({});
    const [allSelect, setAllSelect] = useState(false);
    useEffect(() => {
        setBodyData(getBody(currentStep?.stepInfo.params.params.mockRequest.matchRequest));
    }, [currentStep]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                Body 校验
            </Divider>
            {'json' === bodyData.type ?
                <>
                    <div>
                        <Checkbox
                            className={styles.paramsIcon}
                            checked={allSelect}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onClick={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                setAllSelect(e.target.checked);
                                let newBodyData = {...bodyData};
                                for (let item of newBodyData.data) {
                                    item.isChecked = e.target.checked;
                                }
                                setBodyData(newBodyData);
                            }}
                        />
                        {!allSelect && bodyData.data.filter(item => item.isChecked).length === 0 ?
                            <>
                                <span className={styles.paramsTitle}>Key</span>
                                <span className={styles.paramsTitle}>Value</span>
                                {!['readonly', 'debug', 'execute'].includes(editType) ?
                                    <PlusOutlined
                                        className={styles.paramsIcon}
                                        onClick={() => {
                                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                                return false;
                                            }
                                            let newBodyData = {...bodyData};
                                            newBodyData.data.push({
                                                key: '',
                                                value: '',
                                                isChecked: false,
                                            });
                                            setBodyData(newBodyData);
                                        }}
                                    /> : null}
                            </> : <>
                                <span className={styles.selectInfo}>
                                    已选择 {bodyData.data.filter(item =>
                                        item.isChecked).length} / {bodyData.data.length}
                                </span>
                                {!['readonly', 'debug', 'execute'].includes(editType) ?
                                    <Tag
                                        className={styles.paramsSelectedDelete}
                                        color='error'
                                        onClick={async () => {
                                            let newBodyData = {
                                                type: bodyData.type,
                                                data: []
                                            };
                                            for (let index in bodyData.data) {
                                                if (!bodyData.data[index].isChecked) {
                                                    newBodyData.data.push(bodyData.data[index]);
                                                }
                                            }
                                            let newCurrentStep = {...currentStep};
                                            newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.body =
                                                getNewBody(
                                                    newBodyData.data
                                                );
                                            if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                                '' !== newCurrentStep.stepInfo.params.params.
                                                    mockRequest.matchRequest.hostname) {
                                                newCurrentStep.stepInfo.params
                                                    .params.mockRequest.verifyDetail.body =
                                                    JSON.parse(newCurrentStep.stepInfo.
                                                        params.params.mockRequest.matchRequest.body) ?
                                                        getVerifyNewBody(
                                                            newBodyData.data
                                                        ) : {};
                                            }
                                            await handleUpdateStep(newCurrentStep);
                                            setBodyData(newBodyData);
                                            setAllSelect(false);
                                        }}
                                    >
                                        <DeleteOutlined />
                                        &nbsp; 批量删除
                                    </Tag> : null}
                            </>}
                    </div>
                    {
                        bodyData.data.map((item, index) => (
                            <div key={'body_' + String(index)}>
                                <Checkbox
                                    className={styles.paramsIcon}
                                    checked={item.isChecked}
                                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                    onClick={(e) => {
                                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                                            return false;
                                        }
                                        let newBodyData = {...bodyData};
                                        newBodyData.data[index].isChecked = e.target.checked;
                                        setBodyData(newBodyData);
                                    }}
                                />
                                <Input
                                    size='small'
                                    className={classnames(styles.paramsInput, 'inputEditor')}
                                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                    value={item.key}
                                    onChange={(e) => {
                                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                                            return false;
                                        }
                                        let newBodyData = {...bodyData};
                                        newBodyData.data[index].key = e.target.value;
                                        setBodyData(newBodyData);
                                    }}
                                    onBlur={async (e) => {
                                        let newBodyData = {...bodyData};
                                        newBodyData.data[index].key = e.target.value.trim();
                                        let newCurrentStep = {...currentStep};
                                        newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.body =
                                            getNewBody(
                                                newBodyData.data
                                            );
                                        if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                            '' !== newCurrentStep.stepInfo.params.params.
                                                mockRequest.matchRequest.hostname) {
                                            newCurrentStep.stepInfo.params
                                                .params.mockRequest.verifyDetail.body =
                                                JSON.parse(newCurrentStep.stepInfo.params.
                                                    params.mockRequest.matchRequest.body) ?
                                                    getVerifyNewBody(
                                                        newBodyData.data
                                                    ) : {};
                                        }
                                        await handleUpdateStep(newCurrentStep);
                                    }}
                                />
                                <Input
                                    size='small'
                                    className={classnames(styles.paramsInput, 'inputEditor')}
                                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                    value={item.value}
                                    onChange={(e) => {
                                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                                            return false;
                                        }
                                        let newBodyData = {...bodyData};
                                        newBodyData.data[index].value = e.target.value;
                                        setBodyData(newBodyData);
                                    }}
                                    onBlur={async (e) => {
                                        let newBodyData = {...bodyData};
                                        newBodyData.data[index].value = e.target.value.trim();
                                        let newCurrentStep = {...currentStep};
                                        newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.body =
                                            getNewBody(
                                                newBodyData.data
                                            );
                                        if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                            '' !== newCurrentStep.stepInfo.params.params.
                                                mockRequest.matchRequest.hostname) {
                                            newCurrentStep.stepInfo.params.params.
                                                mockRequest.verifyDetail.body =
                                                JSON.parse(newCurrentStep.stepInfo.
                                                    params.params.mockRequest.matchRequest.body) ?
                                                    getVerifyNewBody(
                                                        newBodyData.data
                                                    ) : {};
                                        }
                                        await handleUpdateStep(newCurrentStep);
                                    }}
                                />
                                {!['readonly', 'debug', 'execute'].includes(editType) ?
                                    <DeleteOutlined
                                        className={classnames(styles.paramsIcon, styles.paramsDelete)}
                                        onClick={async () => {
                                            let newBodyData = {...bodyData};
                                            newBodyData.data.splice(index, 1);
                                            let newCurrentStep = {...currentStep};
                                            newCurrentStep.stepInfo.params.params.
                                                mockRequest.matchRequest.body = getNewBody(
                                                    newBodyData.data
                                                );
                                            if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                                '' !== newCurrentStep.stepInfo.params.params.
                                                    mockRequest.matchRequest.hostname) {
                                                newCurrentStep.stepInfo.params.params.
                                                    mockRequest.verifyDetail.body =
                                                    JSON.parse(newCurrentStep.stepInfo.params.params.
                                                        mockRequest.matchRequest.body) ?
                                                        getVerifyNewBody(
                                                            newBodyData.data
                                                        ) : {};
                                            }
                                            await handleUpdateStep(newCurrentStep);
                                            setBodyData(newBodyData);
                                        }}
                                    /> : null}
                            </div>
                        ))
                    }
                </> :
                <TextArea
                    style={{width: '100%'}}
                    className={classnames(styles.textArea, 'inputEditor')}
                    value={formatRequest(
                        bodyData.data
                    )}
                    rows={
                        bodyData?.data === ''
                            ? 2
                            : 20
                    }
                    onChange={(e) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        let newBodyData = {...bodyData};
                        newBodyData.data = JSON.parse(JSON.stringify(e.target.value).replace(/\r|\n/g, ''));
                        setBodyData(currentStep);
                    }}
                    onBlur={async (e) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        let data = JSON.parse(JSON.stringify(e.target.value).replace(/\r|\n/g, ''));
                        let newBodyData = {...bodyData};
                        newBodyData.data = data;
                        if (isJSON(data.trim()) || '' === data.trim()) {
                            if (currentStep.stepInfo.params.params.mockRequest
                                .matchRequest.body !== data.trim()) {
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.body = data.trim();
                                if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                    '' !== newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.hostname) {
                                    newCurrentStep.stepInfo.params
                                        .params.mockRequest.verifyDetail.body =
                                        newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.body ?
                                            newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.body : {};
                                }
                                await handleUpdateStep(newCurrentStep);
                                setBodyData(newBodyData);
                            }
                        } else {
                            message.info('请输入 JSON 格式');
                            setBodyData(getBody(currentStep?.stepInfo.params.params.mockRequest.matchRequest));
                        }
                    }}
                />}
        </>
    );
};

export default ParamsBody;