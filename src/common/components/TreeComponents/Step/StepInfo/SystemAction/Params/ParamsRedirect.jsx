import {useState, useEffect} from 'react';
import {Divider, message, Checkbox} from 'antd';
import styles from './Params.module.less';

function ParamsRedirect(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [redirect, setRedirect] = useState(1);

    useEffect(() => {
        setRedirect(currentStep?.stepInfo.params.params.mockRequest.requestDetail?.pageRedirect ?? false);
    }, [currentStep?.stepId]);

    const selectVerifyMethod = async (currentStep, value) => {
        try {
            if (['readonly', 'debug', 'execute'].includes(editType)) {
                return false;
            }
            let newCurrentStep = {...currentStep};
            newCurrentStep.stepInfo.params.params.mockRequest.requestDetail.pageRedirect = value;
            await handleUpdateStep(newCurrentStep);
            setRedirect(value);
        } catch (err) {
        }
    };
    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                配置
            </Divider>
            <Checkbox
                checked={redirect}
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                onChange={(e) => selectVerifyMethod(currentStep, e.target.checked)}
            >
                <span>开启 302 重定向转发（仅页面跳转使用）</span>
            </Checkbox>
        </>
    );
};

export default ParamsRedirect;
