import classnames from 'classnames';
import {useState, useEffect} from 'react';
import {DeleteOutlined, PlusOutlined} from '@ant-design/icons';
import {Checkbox, Input, Divider, message, Tag, Tooltip} from 'antd';
import {contactUrl, getQuery, getNewUrl} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './Params.module.less';

function ParamsQuery(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [allSelect, setAllSelect] = useState(false);
    const [querySelect, setQuerySelect] = useState([]);
    const [queryList, setQueryList] = useState([]);
    const [url, setUrl] = useState('');
    const [urlPath, setUrlPath] = useState('');

    useEffect(() => {
        if (0 !== Object.keys(currentStep.stepInfo.params.params).length) {
            let {path} = currentStep.stepInfo.params.params.mockRequest.matchRequest;
            setUrl(contactUrl(currentStep.stepInfo.params.params.mockRequest.matchRequest));
            setUrlPath(path ?? '');
        }
    }, [currentStep]);

    useEffect(() => {
        if (currentStep?.stepInfo.params.params.mockRequest.matchRequest.path) {
            let query = getQuery(currentStep?.stepInfo.params.params.mockRequest.matchRequest.path);
            setQueryList(query);
            setQuerySelect(query.map(() => false));
        } else {
            setQueryList([]);
        }
    }, [currentStep]);
    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                URL
            </Divider>
            {
                -1 === ['readonly', 'debug', 'execute'].indexOf(editType) ?
                    <div className={styles.urlPath}>
                        <Tooltip title={url}>
                            <span className={styles.addBeforePath}>{url}</span>
                        </Tooltip>
                        <Input
                            className={classnames(styles.paramsEdit, styles.pathInput, 'inputEditor')}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            placeholder='请输入 Path'
                            value={urlPath}
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                setUrlPath(e.target.value);
                                let newQuery = getQuery(e.target.value);
                                setQueryList(newQuery);
                            }}
                            onBlur={async (e) => {
                                if (!isElectron() ||
                                    -1 !== ['readonly', 'debug', 'execute'].indexOf(editType)) {
                                    return false;
                                }
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.path =
                                    e.target.value.trim();
                                await handleUpdateStep(newCurrentStep);
                            }}
                        />
                    </div> :
                    <Input
                        className={classnames(styles.paramsEdit, 'inputEditor')}
                        disabled
                        placeholder='请输入 Path'
                        value={url + urlPath}
                    />
            }
            {'mock' === currentStep?.stepInfo.params.type ?
                <Divider orientation="left" plain className={styles.paramsName}>
                    Query
                </Divider> : null}
            <div>
                <Checkbox
                    className={styles.paramsIcon}
                    checked={allSelect}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    onClick={(e) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        setAllSelect(e.target.checked);
                        let newQuerySelect = [...Array(queryList.length).fill(e.target.checked)];
                        setQuerySelect(newQuerySelect);
                    }}
                />
                {!allSelect && querySelect.filter(item => item).length === 0 ?
                    <>
                        <span className={styles.paramsTitle}>Key</span>
                        <span className={styles.paramsTitle}>Value</span>
                        {isElectron() ?
                            <PlusOutlined
                                className={styles.paramsIcon}
                                style={{
                                    display: ['readonly', 'debug', 'execute'].includes(editType) ?
                                        'none' : 'inline-block'
                                }}
                                onClick={() => {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    let newQueryList = [...queryList, {
                                        key: '',
                                        value: ''
                                    }];
                                    let newQuerySelect = [...querySelect, false];
                                    const newPath = getNewUrl(
                                        currentStep.stepInfo.params.params.mockRequest.matchRequest.path,
                                        newQueryList
                                    );
                                    currentStep.stepInfo.params.params.mockRequest.matchRequest.path = newPath;
                                    setQueryList(newQueryList);
                                    setQuerySelect(newQuerySelect);
                                    setUrlPath(newPath);
                                }}
                            /> : null}
                    </> : <>
                        <span className={styles.selectInfo}>
                            已选择 {querySelect.filter(item => item).length} / {queryList.length}
                        </span>
                        {isElectron() && !['readonly', 'debug', 'execute'].includes(editType) ?
                            <Tag
                                className={styles.paramsSelectedDelete}
                                color='error'
                                onClick={async () => {
                                    let newQueryList = [];
                                    let newQuerySelect = [];
                                    for (let index in querySelect) {
                                        if (!querySelect[index]) {
                                            newQueryList.push(queryList[index]);
                                            newQuerySelect.push(false);
                                        }
                                    }
                                    const newPath = getNewUrl(
                                        currentStep.stepInfo.params.params.mockRequest.matchRequest.path,
                                        newQueryList
                                    );
                                    let newCurrentStep = {...currentStep};
                                    newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.path = newPath;
                                    await handleUpdateStep(newCurrentStep);
                                    setQueryList(newQueryList);
                                    setQuerySelect(newQuerySelect);
                                    setAllSelect(false);
                                    setUrlPath(newPath);
                                }}
                            >
                                <DeleteOutlined />
                                &nbsp; 批量删除
                            </Tag> : null}
                    </>}
            </div>
            {
                queryList?.map((item, index) => (
                    <div key={'query_' + String(index)}>
                        <Checkbox
                            className={styles.paramsIcon}
                            checked={querySelect[index]}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onClick={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newQuerySelect = [...querySelect];
                                newQuerySelect[index] = e.target.checked;
                                setQuerySelect(newQuerySelect);
                            }}
                        />
                        <Input
                            size='small'
                            className={classnames(styles.paramsInput, 'inputEditor')}
                            value={item.key}
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newQueryList = [...queryList];
                                newQueryList[index].key = e.target.value;
                                const newPath = getNewUrl(
                                    currentStep.stepInfo.params.params.mockRequest.matchRequest.path,
                                    newQueryList
                                );
                                setUrlPath(newPath);
                                setQueryList(newQueryList);
                            }}
                            onBlur={async (e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newQueryList = [...queryList];
                                newQueryList[index].key = e.target.value.trim();
                                const newPath = getNewUrl(
                                    currentStep.stepInfo.params.params.mockRequest.matchRequest.path,
                                    newQueryList
                                );
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.path = newPath;
                                await handleUpdateStep(newCurrentStep);
                                setQueryList(newQueryList);
                            }}
                        />
                        <Input
                            size='small'
                            className={classnames(styles.paramsInput, 'inputEditor')}
                            value={item.value}
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newQueryList = [...queryList];
                                newQueryList[index].value = e.target.value;
                                const newPath = getNewUrl(
                                    currentStep.stepInfo.params.params.mockRequest.matchRequest.path,
                                    newQueryList
                                );
                                setUrlPath(newPath);
                                setQueryList(newQueryList);
                            }}
                            onBlur={async (e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newQueryList = [...queryList];
                                newQueryList[index].value = e.target.value.trim();
                                const newPath = getNewUrl(
                                    currentStep.stepInfo.params.params.mockRequest.matchRequest.path,
                                    newQueryList
                                );
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.path = newPath;
                                await handleUpdateStep(newCurrentStep);
                                setQueryList(newQueryList);
                            }}
                        />
                        {isElectron() ?
                            <DeleteOutlined
                                className={classnames(styles.paramsIcon, styles.paramsDelete)}
                                style={{
                                    display: ['readonly', 'debug', 'execute'].includes(editType) ?
                                        'none' : 'inline-block'
                                }}
                                onClick={async () => {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    let newQueryList = [...queryList];
                                    let newQuerySelect = [...querySelect];
                                    newQueryList.splice(index, 1);
                                    newQuerySelect.splice(index, 1);
                                    const newPath = getNewUrl(
                                        currentStep.stepInfo.params.params.mockRequest.matchRequest.path,
                                        newQueryList
                                    );
                                    let newCurrentStep = {...currentStep};
                                    newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.path = newPath;
                                    await handleUpdateStep(newCurrentStep);
                                    setUrlPath(newPath);
                                    setQueryList(newQueryList);
                                    setQuerySelect(newQuerySelect);
                                }}
                            /> : null}
                    </div>
                ))
            }
        </>
    );
};

export default ParamsQuery;