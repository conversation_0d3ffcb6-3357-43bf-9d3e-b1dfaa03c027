import classnames from 'classnames';
import {useState, useEffect} from 'react';
import {DeleteOutlined, PlusOutlined} from '@ant-design/icons';
import {Tag, Checkbox, Input, Divider, message} from 'antd';
import {getObjToArray, changeKeyValue} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './Params.module.less';

function ParamsVerifyQuery(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [queryList, setQueryList] = useState([]);
    const [allSelect, setAllSelect] = useState(false);
    const [querySelect, setQuerySelect] = useState([]);

    useEffect(() => {
        let query = getObjToArray(currentStep?.stepInfo.params.params.mockRequest.matchRequest.query);
        setQueryList(query);
        setQuerySelect(query.map(() => false));
    }, [currentStep]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                Query 校验
            </Divider>
            <div>
                <Checkbox
                    className={styles.paramsIcon}
                    checked={allSelect}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    onClick={(e) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        setAllSelect(e.target.checked);
                        let newQuerySelect = [...Array(queryList.length).fill(e.target.checked)];
                        setQuerySelect(newQuerySelect);
                    }}
                />
                {!allSelect && querySelect.filter(item => item).length === 0 ?
                    <>
                        <span className={styles.paramsTitle}>Key</span>
                        <span className={styles.paramsTitle}>Value</span>
                        {!['readonly', 'debug', 'execute'].includes(editType) ?
                            <PlusOutlined
                                className={styles.paramsIcon}
                                onClick={() => {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    let newQueryList = [...queryList, {
                                        key: '',
                                        value: ''
                                    }];
                                    let newQuerySelect = [...querySelect, false];
                                    currentStep.stepInfo.params.params.mockRequest.matchRequest.query =
                                        changeKeyValue(newQueryList);
                                    setQueryList(newQueryList);
                                    setQuerySelect(newQuerySelect);
                                }}
                            /> : null}
                    </> : <>
                        <span className={styles.selectInfo}>
                            已选择 {querySelect.filter(item => true === item).length} / {queryList.length}
                        </span>
                        {!['readonly', 'debug', 'execute'].includes(editType) ?
                            <Tag
                                className={styles.paramsSelectedDelete}
                                color='error'
                                onClick={async () => {
                                    let newQueryList = [];
                                    let newQuerySelect = [];
                                    for (let index in querySelect) {
                                        if (!querySelect[index]) {
                                            newQueryList.push(queryList[index]);
                                            newQuerySelect.push(false);
                                        }
                                    }
                                    let newCurrentStep = {...currentStep};
                                    newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.query =
                                        changeKeyValue(newQueryList);
                                    if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                        '' !== newCurrentStep.stepInfo
                                            .params.params.mockRequest.matchRequest.hostname) {
                                        newCurrentStep.stepInfo.params.params.mockRequest.verifyDetail.query =
                                            newCurrentStep.stepInfo.params.params.mockRequest.matchRequest ?
                                                newCurrentStep.stepInfo.params.params.
                                                    mockRequest.matchRequest.query : {};
                                    }
                                    await handleUpdateStep(newCurrentStep);
                                    setQueryList(newQueryList);
                                    setQuerySelect(newQuerySelect);
                                    setAllSelect(false);
                                }}
                            >
                                <DeleteOutlined />
                                &nbsp; 批量删除
                            </Tag> : null}
                    </>}
            </div>
            {
                queryList.map((item, index) => (
                    <div key={'verifyquery_' + String(index)}>
                        <Checkbox
                            className={styles.paramsIcon}
                            checked={querySelect[index]}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onClick={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newQuerySelect = [...querySelect];
                                newQuerySelect[index] = e.target.checked;
                                setQuerySelect(newQuerySelect);
                            }}
                        />
                        <Input
                            size='small'
                            className={classnames(styles.paramsInput, 'inputEditor')}
                            value={item.key}
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newQueryList = [...queryList];
                                newQueryList[index].key = e.target.value;
                                setQueryList(newQueryList);
                            }}
                            onBlur={async (e) => {
                                try {
                                    let newQueryList = [...queryList];
                                    newQueryList[index].key = e.target.value.trim();
                                    let newCurrentStep = {...currentStep};
                                    newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.query =
                                        changeKeyValue(newQueryList);
                                    if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                        '' !== newCurrentStep.stepInfo.params.params.
                                            mockRequest.matchRequest.hostname) {
                                        newCurrentStep.stepInfo.params.params.mockRequest.verifyDetail.query =
                                            newCurrentStep.stepInfo.params.params.mockRequest.matchRequest ?
                                                newCurrentStep.stepInfo.params.params.
                                                    mockRequest.matchRequest.query : {};
                                    }
                                    await handleUpdateStep(newCurrentStep);
                                    setQueryList(newQueryList);
                                } catch (err) {
                                }
                            }}
                        />
                        <Input
                            size='small'
                            className={classnames(styles.paramsInput, 'inputEditor')}
                            value={item.value}
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newQueryList = [...queryList];
                                newQueryList[index].value = e.target.value;
                                setQueryList(newQueryList);
                            }}
                            onBlur={async (e) => {
                                try {
                                    let newQueryList = [...queryList];
                                    newQueryList[index].value = e.target.value.trim();
                                    let newCurrentStep = {...currentStep};
                                    newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.query =
                                        changeKeyValue(newQueryList);
                                    if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                        '' !== newCurrentStep.stepInfo.params.params.
                                            mockRequest.matchRequest.hostname) {
                                        newCurrentStep.stepInfo.params.params.mockRequest.verifyDetail.query =
                                            newCurrentStep.stepInfo.params.params.mockRequest.matchRequest ?
                                                newCurrentStep.stepInfo.params.params.
                                                    mockRequest.matchRequest.query : {};
                                    }
                                    await handleUpdateStep(newCurrentStep);
                                    setQueryList(newQueryList);
                                } catch (err) {
                                }
                            }}
                        />
                        {!['readonly', 'debug', 'execute'].includes(editType) ?
                            <DeleteOutlined
                                className={classnames(styles.paramsIcon, styles.paramsDelete)}
                                onClick={async (e) => {
                                    try {
                                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                                            return false;
                                        }
                                        let newQueryList = [...queryList];
                                        let newQuerySelect = [...querySelect];
                                        newQueryList.splice(index, 1);
                                        newQuerySelect.splice(index, 1);
                                        let newCurrentStep = {...currentStep};
                                        newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.query =
                                            changeKeyValue(newQueryList);
                                        if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                            '' !== newCurrentStep.stepInfo.params.params.
                                                mockRequest.matchRequest.hostname) {
                                            newCurrentStep.stepInfo.params.params.mockRequest.verifyDetail.query =
                                                newCurrentStep.stepInfo.params.params.mockRequest.matchRequest ?
                                                    newCurrentStep.stepInfo.params.params.
                                                        mockRequest.matchRequest.query : {};
                                        }
                                        await handleUpdateStep(newCurrentStep);
                                        setQueryList(newQueryList);
                                        setQuerySelect(newQuerySelect);
                                    } catch (err) {
                                    }
                                }}
                            /> : null}
                    </div>
                ))
            }

        </>
    );
};

export default ParamsVerifyQuery;