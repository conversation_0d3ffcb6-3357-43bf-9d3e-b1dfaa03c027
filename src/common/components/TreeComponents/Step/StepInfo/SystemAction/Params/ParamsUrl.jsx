import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {Input, Divider, message} from 'antd';
import {contactUrl} from 'COMMON/components/TreeComponents/Step/utils';
import {parseURL} from './utils';
import styles from './Params.module.less';
import {connectModel} from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';

function ParamsUrl(props) {
    const {currentStep, editType, handleUpdateStep, paramList} = props;
    const [url, setUrl] = useState('');
    // 校验逻辑

    const getUrlParams = (url) => {
        let replacedUrl = url;
         // 检查URL是否包含`${...}`这样的参数
         if (/\$\{\w+\}/.test(url)) {
            replacedUrl = url.replace(/\$\{(\w+)\}/g, (_, key) => {
                const param = paramList.find(param => param.paramKey === key);
                return param ? param.paramValue : _;
            });
        }
        try {
            new URL(replacedUrl);
            let params = parseURL(replacedUrl);
            let requestDetail = {};
            if (undefined !== params.protocol && '' !== params.protocol) {
                requestDetail.protocol =
                    params.protocol;
            } else {
                message.warning('请填写协议 http 或 https');
                return false;
            }
            if (undefined !== params.host && '' !== params.host) {
                requestDetail.hostname =
                    params.host;
            } else {
                message.warning('请填写域名');
                return false;
            }
            if (!isNaN(params.port) && '' !== params.port) {
                requestDetail.port =
                    params.port;
            } else if (undefined !== params.protocol && 'file' !== params.protocol) {
                if (params.protocol === 'https') {
                    requestDetail.port = 443;
                } else {
                    requestDetail.port = 80;
                }
            } else {
                message.warning('请填写端口号');
                return false;
            }
            if (undefined === params.path) {
                requestDetail.path = '';
            }
            if (undefined !== params.path && '' !== params.path) {
                requestDetail.path =
                    params.path;
            }
            if (undefined !== params.query && '' !== params.query) {
                if (
                    -1 ===
                    Object.keys(
                        requestDetail
                    ).indexOf('path')
                ) {
                    requestDetail.path =
                        '';
                }
                requestDetail.path +=
                    params.query;
            }
            return requestDetail;
        } catch (err) {
            message.error('无效URL');
        }
    };
    // const getUrlParams = (url) => {
    //     const urlPattern = /^(https?:\/\/)?([\w-]+(\.[\w-]+)+)(:\d+)?(\/\S*)?$/;
    //     let requestDetail = {
    //         protocol: '',
    //         hostname: '',
    //         port: null,
    //         path: ''
    //     };
    //     // 非标准 url
    //     if (!urlPattern.test(url)) {
    //         return requestDetail; // or return an error object, or handle it appropriately
    //     } else {
    //         let params = parseURL(url);
    //         requestDetail.protocol = params?.protocol || '';
    //         requestDetail.hostname = params?.host || '';
    //         requestDetail.port = params?.port || '';
    //         requestDetail.path = params?.path || '';
    //         if (params.query) {
    //             requestDetail.path += params?.query;
    //         }
    //         return requestDetail;
    //     }
    // };

    const getUrl = (currentStep) => {
        if (currentStep.stepInfo?.params?.type === 'requestRedirect') {
            setUrl(currentStep.stepInfo.params.params.mockRequest.url);
        }
        if (currentStep.stepInfo?.params?.type !== 'request' &&
            currentStep.stepInfo.params.params.mockRequest?.requestDetail &&
            currentStep.stepInfo.params.params.mockRequest?.requestDetail?.hostname !== '') {
            let curUrl = contactUrl(currentStep?.stepInfo.params.params.mockRequest.requestDetail);
            let {path} = currentStep.stepInfo.params.params.mockRequest.requestDetail;
            if (/\$\{\w+\}/.test(currentStep.stepInfo.params.params.mockRequest.url)) {
                setUrl(currentStep.stepInfo.params.params.mockRequest.url);
            } else {
                setUrl(curUrl + (path ?? ''));
            }
        }
        if (currentStep.stepInfo?.params?.type === 'request') {
            setUrl(currentStep.stepInfo.params.params.url);
        }
    };

    useEffect(() => {
        getUrl(currentStep);
    }, [currentStep]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                URL
            </Divider>
            <Input
                className={classnames(styles.paramsEdit, 'inputEditor')}
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                placeholder='请输入 URL'
                value={url}
                onChange={(e) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    setUrl(e.target.value);
                }}
                onBlur={async (e) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    try {
                        if ('' !== e.target.value.trim()) {
                            new URL(e.target.value.trim());
                        }
                    } catch (err) {
                        message.error('无效 URL');
                        getUrl(currentStep);
                        return false;
                    }
                    try {
                        let newCurrentStep = {...currentStep};
                        newCurrentStep.stepInfo.params.params.url = e.target.value.trim();
                        if (newCurrentStep.stepInfo?.params?.type === 'requestRedirect') {
                            let urlParams = getUrlParams(e.target.value.trim());
                            newCurrentStep.stepInfo.params.params.mockRequest.url = e.target.value.trim();
                            newCurrentStep.stepInfo.params.params.mockRequest.requestDetail = {
                                pageRedirect: newCurrentStep.stepInfo.params.params.
                                    mockRequest.requestDetail?.pageRedirect ?? false,
                                ...urlParams
                            };
                        }
                        await handleUpdateStep(newCurrentStep);
                    } catch (err) {
                    }
                }}
            />
        </>
    );
};

export default connectModel([commonModel], (state) => ({
    paramList: state.common.case.paramList,
}))(ParamsUrl);