import classnames from 'classnames';
import {useState, useEffect} from 'react';
import {DeleteOutlined, PlusOutlined} from '@ant-design/icons';
import {Tag, Checkbox, Input, Divider, message} from 'antd';
import {getObjToArray, changeKeyValue} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './Params.module.less';

function ParamsHeader(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [headerList, setHeaderList] = useState([]);
    const [allSelect, setAllSelect] = useState(false);
    const [headerSelect, setHeaderSelect] = useState([]);

    useEffect(() => {
        let headers = getObjToArray(currentStep?.stepInfo.params.params.mockRequest.requestDetail?.headers || {});
        setHeaderList(headers);
        setHeaderSelect(headers.map(() => false));
    }, [currentStep?.stepId]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                Header
            </Divider>
            <div>
                <Checkbox
                    className={styles.paramsIcon}
                    checked={allSelect}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    onClick={(e) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        setAllSelect(e.target.checked);
                        let newHeaderSelect = [...Array(headerList.length).fill(e.target.checked)];
                        setHeaderSelect(newHeaderSelect);
                    }}
                />
                {
                    !allSelect && headerSelect.filter(item => item).length === 0 ?
                        <>
                            <span className={styles.paramsTitle}>Key</span>
                            <span className={styles.paramsTitle}>Value</span>
                            {isElectron() ?
                                <PlusOutlined
                                    className={styles.paramsIcon}
                                    onClick={() => {
                                        if (!isElectron() ||
                                            -1 !== ['readonly', 'debug', 'execute'].indexOf(editType)) {
                                            return false;
                                        }
                                        let newHeaderList = [...headerList, {
                                            key: '',
                                            value: ''
                                        }];
                                        let newHeaderSelect = [...headerSelect, false];
                                        currentStep.stepInfo.params.params.mockRequest.requestDetail.headers =
                                            changeKeyValue(newHeaderSelect);
                                        setHeaderList(newHeaderList);
                                        setHeaderSelect(newHeaderSelect);
                                    }}
                                /> : null}
                        </> : <>
                            <span className={styles.selectInfo}>
                                已选择 {headerSelect.filter(item => true === item).length} / {headerList.length}
                            </span>
                            {isElectron() && !['readonly', 'debug', 'execute'].includes(editType) ?
                                <Tag
                                    className={styles.paramsSelectedDelete}
                                    color='error'
                                    onClick={async () => {
                                        let newHeaderList = [];
                                        let newHeaderSelect = [];
                                        for (let index in headerSelect) {
                                            if (!headerSelect[index]) {
                                                newHeaderList.push(headerList[index]);
                                                newHeaderSelect.push(false);
                                            }
                                        }
                                        let newCurrentStep = {...currentStep};
                                        newCurrentStep.stepInfo.params.params.mockRequest.requestDetail.headers =
                                            changeKeyValue(newHeaderList);
                                        await handleUpdateStep(newCurrentStep);
                                        setHeaderList(newHeaderList);
                                        setHeaderSelect(newHeaderSelect);
                                        setAllSelect(false);
                                    }}
                                >
                                    <DeleteOutlined />
                                    &nbsp; 批量删除
                                </Tag> : null}
                        </>
                }
            </div>
            {
                headerList.map((item, index) => (
                    <div key={'header_' + String(index)}>
                        <Checkbox
                            className={styles.paramsIcon}
                            checked={headerSelect[index]}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onClick={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newHeaderSelect = [...headerSelect];
                                newHeaderSelect[index] = e.target.checked;
                                setHeaderSelect(newHeaderSelect);
                            }}
                        />
                        <Input
                            size='small'
                            className={classnames(styles.paramsInput, 'inputEditor')}
                            value={item.key}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newHeaderList = [...headerList];
                                newHeaderList[index].key = e.target.value;
                                setHeaderList(newHeaderList);
                            }}
                            onBlur={async (e) => {
                                let newHeaderList = [...headerList];
                                newHeaderList[index].key = e.target.value.trim();
                                if (e.target.value.trim() === 'Cookie') {
                                    message.warning('Key 不允许为 Cookie 字段');
                                    newHeaderList[index].key = '';
                                    setHeaderList(newHeaderList);
                                    return;
                                }
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.mockRequest.requestDetail.headers =
                                    changeKeyValue(newHeaderList);
                                await handleUpdateStep(newCurrentStep);
                                setHeaderList(newHeaderList);
                            }}
                        />
                        <Input
                            size='small'
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            className={classnames(styles.paramsInput, 'inputEditor')}
                            value={item.value}
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newHeaderList = [...headerList];
                                newHeaderList[index].value = e.target.value;
                                setHeaderList(newHeaderList);
                            }}
                            onBlur={async (e) => {
                                let newHeaderList = [...headerList];
                                newHeaderList[index].value = e.target.value.trim();
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.mockRequest.requestDetail.headers =
                                    changeKeyValue(newHeaderList);
                                await handleUpdateStep(newCurrentStep);
                                setHeaderList(newHeaderList);
                            }}
                        />
                        {isElectron() ?
                            <DeleteOutlined
                                className={classnames(styles.paramsIcon, styles.paramsDelete)}
                                onClick={async (e) => {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    let newHeaderList = [...headerList];
                                    let newHeaderSelect = [...headerSelect];
                                    newHeaderList.splice(index, 1);
                                    newHeaderSelect.splice(index, 1);
                                    let newCurrentStep = {...currentStep};
                                    newCurrentStep.stepInfo.params.params.mockRequest.requestDetail.headers =
                                        changeKeyValue(newHeaderList);
                                    await handleUpdateStep(newCurrentStep);
                                    setHeaderList(newHeaderList);
                                    setHeaderSelect(newHeaderSelect);
                                }}
                            /> : null}
                    </div>
                ))
            }

        </>
    );
};

export default ParamsHeader;