import classnames from 'classnames';
import {useState, useEffect} from 'react';
import {DeleteOutlined, PlusOutlined} from '@ant-design/icons';
import {Tag, Checkbox, Input, Divider, Select} from 'antd';
import styles from './Params.module.less';

// arr 转 cookie 格式
const cookieArrayToString = (arr = []) => {
    return arr
        .map(({key, value}) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('; ');
};

// cookie 转 arr 格式
const cookieToArray = (cookieStr = '') => {
    return cookieStr
        .split('; ')
        .filter(Boolean)
        .map(item => {
            const [key, val] = item.split('=');
            return {key: decodeURIComponent(key), value: decodeURIComponent(val || '')};
        });
};


function ParamsCookie(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [cookieList, setCookieList] = useState([]);
    const [allSelect, setAllSelect] = useState(false);
    const [cookieSelect, setCookieSelect] = useState([]);

    useEffect(() => {
        let cookies = cookieToArray(currentStep?.stepInfo.params.params
            .mockRequest.requestDetail?.headers?.Cookie || '');
        setCookieList(cookies);
        setCookieSelect(cookies.map(() => false));
    }, [currentStep?.stepId]);


    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                Cookie
            </Divider>
            <div>
                <Checkbox
                    className={styles.paramsIcon}
                    checked={allSelect}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    onClick={(e) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        setAllSelect(e.target.checked);
                        let newCookieSelect = [...Array(cookieList.length).fill(e.target.checked)];
                        setCookieSelect(newCookieSelect);
                    }}
                />
                {
                    !allSelect && cookieSelect.filter(item => item).length === 0 ?
                        <>
                            <span className={styles.paramsTitle}>Key</span>
                            <span className={styles.paramsTitle}>Value</span>
                            {isElectron() && cookieList.length < 2 ?
                                <PlusOutlined
                                    className={styles.paramsIcon}
                                    onClick={() => {
                                        if (!isElectron() ||
                                            -1 !== ['readonly', 'debug', 'execute'].indexOf(editType)) {
                                            return false;
                                        }
                                        let initKey = 'orp_preview';
                                        if (cookieList.find(item => item.key === initKey)) {
                                            initKey = 'pandora_prevoew';
                                        }
                                        let newCookieList = [...cookieList, {
                                            key: initKey,
                                            value: ''
                                        }];
                                        let newCookieSelect = [...cookieSelect, false];
                                        currentStep.stepInfo.params.params.
                                            mockRequest.requestDetail.headers.Cookie =
                                            cookieArrayToString(newCookieList);
                                        setCookieList(newCookieList);
                                        setCookieSelect(newCookieSelect);
                                    }}
                                /> : null}
                        </> : <>
                            <span className={styles.selectInfo}>
                                已选择 {cookieSelect.filter(item => true === item).length} / {cookieList.length}
                            </span>
                            {isElectron() &&
                                !['readonly', 'debug', 'execute'].includes(editType) && <Tag
                                    className={styles.paramsSelectedDelete}
                                    color='error'
                                    onClick={async () => {
                                        let newCookieList = [];
                                        let newCookieSelect = [];
                                        for (let index in cookieSelect) {
                                            if (!cookieSelect[index]) {
                                                newCookieList.push(cookieList[index]);
                                                newCookieSelect.push(false);
                                            }
                                        }
                                        let newCurrentStep = {...currentStep};
                                        newCurrentStep.stepInfo.params.params.
                                            mockRequest.requestDetail.headers.Cookie =
                                            cookieArrayToString(newCookieList);
                                        setCookieList(newCookieList);
                                        setCookieSelect(newCookieSelect);
                                        setAllSelect(false);
                                    }}
                                >
                                    <DeleteOutlined />
                                    &nbsp; 批量删除
                                </Tag>}
                        </>
                }
            </div>
            {
                cookieList.map((item, index) => (
                    <div key={'cookie_' + String(index)}>
                        <Checkbox
                            className={styles.paramsIcon}
                            checked={cookieSelect[index]}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onClick={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newCookieSelect = [...cookieSelect];
                                newCookieSelect[index] = e.target.checked;
                                setCookieSelect(newCookieSelect);
                            }}
                        />
                        <Select
                            size='small'
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            className={classnames(styles.paramsInput, 'inputEditor')}
                            value={item.key}
                            options={[{
                                value: 'orp_preview',
                                disabled: cookieList.find(cookie => cookie.key === 'orp_preview'),
                                label: 'orp_preview'
                            }, {
                                value: 'pandora_prevoew',
                                disabled: cookieList.find(cookie => cookie.key === 'pandora_prevoew'),
                                label: 'pandora_prevoew'
                            }]}
                            onChange={async (value) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newCookieList = [...cookieList];
                                newCookieList[index].key = value;
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.
                                    mockRequest.requestDetail.headers.Cookie = cookieArrayToString(newCookieList);
                                await handleUpdateStep(newCurrentStep);
                                setCookieList(newCookieList);
                            }}
                        />
                        <Input
                            size='small'
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            className={classnames(styles.paramsInput, 'inputEditor')}
                            value={item.value}
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newCookieList = [...cookieList];
                                newCookieList[index].value = e.target.value;
                                setCookieList(newCookieList);
                            }}
                            onBlur={async (e) => {
                                let newCookieList = [...cookieList];
                                newCookieList[index].value = e.target.value.trim();
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.
                                    mockRequest.requestDetail.headers.Cookie = cookieArrayToString(newCookieList);
                                await handleUpdateStep(newCurrentStep);
                                setCookieList(newCookieList);
                            }}
                        />
                        {isElectron() ?
                            <DeleteOutlined
                                className={classnames(styles.paramsIcon, styles.paramsDelete)}
                                onClick={async (e) => {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    let newCookieList = [...cookieList];
                                    let newCookieSelect = [...cookieSelect];
                                    newCookieList.splice(index, 1);
                                    newCookieSelect.splice(index, 1);
                                    let newCurrentStep = {...currentStep};
                                    newCurrentStep.stepInfo.params.params.
                                        mockRequest.requestDetail.headers.Cookie = cookieArrayToString(newCookieList);
                                    await handleUpdateStep(newCurrentStep);
                                    setCookieList(newCookieList);
                                    setCookieSelect(newCookieSelect);
                                }}
                            /> : null}
                    </div>
                ))
            }

        </>
    );
};

export default ParamsCookie;