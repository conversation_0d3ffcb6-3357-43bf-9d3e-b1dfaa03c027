// 解析url
export function parseURL(url) {
    let parse_url =
        /^(?:([A-Za-z]+):)?(\/{0,3})([0-9_.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/;
    let result = parse_url.exec(url);
    let params = {};
    let names = ['url', 'protocol', 'slash', 'host', 'port', 'path', 'query', 'hash'];
    for (let i = 0; i < names.length; i++) {
        params[names[i]] = result[i];
        if ('port' === names[i]) {
            params.port = Number(result[i]);
        }
        if ('path' === names[i] && undefined !== result[i]) {
            params.path = '/' + result[i];
        }
        if ('query' === names[i] && undefined !== result[i]) {
            params.query = '?' + result[i];
        }
    }

    return params;
}
