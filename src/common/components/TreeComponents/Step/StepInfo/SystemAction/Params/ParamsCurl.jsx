import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {Input, Divider, message} from 'antd';
import {connectModel} from 'COMMON/middleware';
import styles from './Params.module.less';

const {TextArea} = Input;

function ParamsCurl(props) {
    const {currentStep, editType, currentNode, handleUpdateStep} = props;
    const [curl, setCurl] = useState('');

    useEffect(() => {
        if (currentStep?.stepInfo.params.params?.curl) {
            setCurl(currentStep?.stepInfo.params.params?.curl);
        }
    }, [currentStep?.stepId]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                CURL
            </Divider>
            <TextArea
                className={classnames(styles.paramsEdit, 'inputEditor')}
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                placeholder='请输入 CURL'
                value={curl}
                autoSize={{
                    minRows: 10
                }}
                onChange={(e) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    setCurl(e.target.value);
                }}
                onBlur={async (e) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    let newCurrentStep = {...currentStep};
                    newCurrentStep.stepInfo.params.params.curl = e.target.value.trim();
                    await handleUpdateStep(newCurrentStep);
                }}
            />
        </>
    );
};

export default ParamsCurl;