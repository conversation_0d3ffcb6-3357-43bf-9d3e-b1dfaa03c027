import {useState} from 'react';
import {<PERSON><PERSON>E<PERSON><PERSON> as Editor} from 'jsoneditor-react';
import {Tooltip, Modal, message} from 'antd';
import {FormOutlined} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import styles from './Params.module.less';

function JsonEditor(props) {
    const {currentStep, resData,
        setResData, editType, setShowModal, handleUpdateStep} = props;
    const [open, setOpen] = useState(false);
    const [resJsonData, setResJsonData] = useState('{}');

    const handleOk = async () => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            setOpen(false);
            setShowModal(false);
            return false;
        }
        let newCurrentStep = {...currentStep};
        newCurrentStep.stepInfo.params.params.mockRequest.responseDetail.body = resJsonData;
        if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
            '' !== newCurrentStep.stepInfo
                .params.params.mockRequest.matchRequest.hostname) {
            newCurrentStep.stepInfo.params.params.mockRequest.verifyDetail.response =
                JSON.parse(newCurrentStep.stepInfo.params.params.mockRequest.responseDetail.body) ?
                    JSON.parse(newCurrentStep.stepInfo.params.params.mockRequest.responseDetail.body) : {};
        }
        await handleUpdateStep(newCurrentStep);
        setOpen(false);
        setShowModal(false);
        setResData(resJsonData);
        setResJsonData(resJsonData);
    };

    return (
        <>
            <Tooltip title='JSON 编辑器'>
                <FormOutlined
                    style={{
                        marginLeft: 5,
                    }}
                    onClick={() => {
                        setResJsonData('' === resData ? '{}' : resData);
                        setOpen(true);
                        setShowModal(true);
                    }}
                />
            </Tooltip>
            <Modal
                title="JSON 编辑器"
                open={open}
                centered
                className="inputEditor"
                onCancel={handleOk}
                onOk={handleOk}
                width={window.innerWidth * 0.8}
                footer={null}
            >
                <div className={styles.jsonEditor}>
                    <Editor
                        value={JSON.parse(resJsonData?.replace(/\r|\n/g, ''))}
                        className="inputEditor"
                        onChange={(value) => {
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return false;
                            }
                            setResJsonData(JSON.stringify(value));
                        }}
                        onError={(error) => {
                            message.error('发生了报错: ', error);
                        }}
                    />
                </div>
            </Modal>
        </>
    );
};

export default connectModel([baseModel], (state) => ({
    showModal: state.common.base.showModal,
}))(JsonEditor);
