import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {Input, Divider, Checkbox, Row, Col} from 'antd';
import styles from './Params.module.less';

function VariableExtraction(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [extractType, setExtractType] = useState(1);
    const [jsonPath, setJsonPath] = useState('');

    useEffect(() => {
        setExtractType(currentStep?.stepInfo.params?.params?.extract?.type ?? 1);
        setJsonPath(currentStep?.stepInfo.params?.params?.extract?.params?.jsonPath ?? '');
    }, [currentStep?.stepId]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                变量提取
            </Divider>
            <Row>
                <Col flex='130px'>
                    <Checkbox
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        checked={extractType === 2}
                        onChange={async (e) => {
                            let checked = e.target.checked;
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return false;
                            }
                            let newCurrentStep = {...currentStep};
                            if (checked) {
                                newCurrentStep.stepInfo.params.params.extract = {
                                    type: 2,
                                    params: {
                                        jsonPath: ''
                                    }
                                };
                            } else {
                                newCurrentStep.stepInfo.params.params.extract = {
                                    type: 1
                                };
                            }
                            setExtractType(checked ? 2 : 1);
                            await handleUpdateStep(newCurrentStep);
                        }}
                    >
                        JSON Path
                    </Checkbox>
                </Col>
                {extractType === 2 && (
                    <Col flex='auto'>
                        <Input
                            className={classnames(styles.paramsEdit, 'inputEditor')}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            placeholder='请输入JSON Path'
                            value={jsonPath}
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                setJsonPath(e.target.value);
                            }}
                            onBlur={async (e) => {
                                try {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    let newCurrentStep = {...currentStep};
                                    newCurrentStep.stepInfo.params.params.extract.params.jsonPath =
                                        e.target.value.trim();
                                    await handleUpdateStep(newCurrentStep);
                                } catch (err) {
                                    console.log(err);

                                }
                            }}
                        />
                    </Col>)}
            </Row>
        </>
    );
};

export default VariableExtraction;