import classnames from 'classnames';
import {useState, useEffect} from 'react';
import {Input, Divider, message} from 'antd';
import {formatRequest, isJSON} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './Params.module.less';

const {TextArea} = Input;

function ParamsBodyDefined(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [bodyData, setBodyData] = useState('');

    useEffect(() => {
        setBodyData(currentStep?.stepInfo?.params?.params?.body || '');
    }, [currentStep]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                Body
            </Divider>
            <TextArea
                style={{width: '100%'}}
                className={classnames(styles.textArea, 'inputEditor')}
                value={formatRequest(bodyData)}
                rows={bodyData === '' ? 2 : 10}
                onChange={(e) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    let newBodyData = bodyData;
                    newBodyData = JSON.parse(JSON.stringify(e.target.value).replace(/\r|\n/g, ''));
                    setBodyData(newBodyData);
                }}
                onBlur={async (e) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    let data = JSON.parse(JSON.stringify(e.target.value).replace(/\r|\n/g, ''));
                    if (isJSON(data.trim()) || '' === data.trim()) {
                        let newCurrentStep = {...currentStep};
                        if (newCurrentStep.stepInfo.params.params.body !== data.trim()) {
                            newCurrentStep.stepInfo.params.params.body = data.trim();
                            await handleUpdateStep(newCurrentStep);
                            setBodyData(data);
                        }
                    } else {
                        message.info('请输入 JSON 格式');
                        setBodyData(currentStep?.stepInfo.params.params.body);
                    }
                }}
            />
        </>
    );
};

export default ParamsBodyDefined;