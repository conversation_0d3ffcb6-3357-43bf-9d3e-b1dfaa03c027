import {useState, useEffect, useCallback} from 'react';
import {isEmpty} from 'lodash';
import {Input, Divider, message} from 'antd';
import {formatRequest, isJSON} from 'COMMON/components/TreeComponents/Step/utils';
import JsonEditor from './JsonEditor';
import styles from './Params.module.less';

const {TextArea} = Input;

function ParamsVerifyResponse(props) {
    const {currentStep, editType, handleUpdateStep} = props;

    const [resData, setResData] = useState('');

    useEffect(() => {
        if (!isEmpty(currentStep?.stepInfo.params.params.mockRequest.verifyDetail.response)) {
            setResData(JSON.stringify(currentStep?.stepInfo.params.params.mockRequest.verifyDetail.response));
        } else {
            setResData('');
        }
    }, [currentStep]);

    const RenderJsonEditor = useCallback(() => {
        return (
            <JsonEditor
                currentStep={currentStep}
                resData={resData}
                handleUpdateStep={handleUpdateStep}
                setResData={setResData}
            />
        );
    }, [resData]);


    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                Response 校验
                &nbsp;
                {!['readonly', 'debug', 'execute'].includes(editType) ?
                    <RenderJsonEditor /> : null}
            </Divider>
            <TextArea
                rows={'' === resData ? 2 : 20}
                className="inputEditor"
                value={formatRequest(resData)}
                onChange={(e) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    let data = JSON.parse(JSON.stringify(e.target.value).replace(/\r|\n/g, ''));
                    setResData(data);
                }}
                onBlur={async (e) => {
                    try {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        let data = JSON.parse(JSON.stringify(e.target.value).replace(/\r|\n/g, ''));
                        if ('' === data.trim() || isJSON(data.trim())) {
                            if (currentStep.stepInfo.params.params.mockRequest.responseDetail.body !== data.trim()) {
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.mockRequest.responseDetail.body = data;
                                if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                    '' !== newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.hostname) {
                                    newCurrentStep.stepInfo.params.params.mockRequest.verifyDetail.response =
                                        '' !== data.trim() ?
                                            JSON.parse(e.target.value) : {};
                                }
                                await handleUpdateStep(newCurrentStep);
                            }
                        } else {
                            message.info('请输入 JSON 格式');
                            if (!isEmpty(currentStep?.stepInfo.params.params.mockRequest.verifyDetail.response)) {
                                setResData(JSON.stringify(
                                    currentStep?.stepInfo.params.params.mockRequest.verifyDetail.response));
                            } else {
                                setResData('');
                            }
                        }
                    } catch (err) {
                    }
                }}
            />
        </>
    );
};


export default ParamsVerifyResponse;