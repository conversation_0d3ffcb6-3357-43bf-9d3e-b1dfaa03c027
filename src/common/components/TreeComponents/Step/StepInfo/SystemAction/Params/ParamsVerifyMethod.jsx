import {useState, useEffect} from 'react';
import {Divider, Tooltip, Button, message} from 'antd';
import styles from './Params.module.less';

function ParamsVerifyMethod(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [verifyMethod, setVerifyMethod] = useState(1);

    useEffect(() => {
        setVerifyMethod(currentStep?.stepInfo.params.params.mockRequest.verifyDetail.verifyType);
    }, [currentStep]);

    const selectVerifyMethod = async (currentStep, value) => {
        try {
            if (['readonly', 'debug', 'execute'].includes(editType)) {
                return false;
            }
            let newCurrentStep = {...currentStep};
            newCurrentStep.stepInfo.params.params.mockRequest.verifyDetail.verifyType = value;
            await handleUpdateStep(newCurrentStep);
            setVerifyMethod(value);
        } catch (err) {
        }
    };
    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                校验方式
            </Divider>
            <Tooltip title='校验单个请求'>
                <Button
                    size='small'
                    type={1 === verifyMethod ? 'primary' : 'default'}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    onClick={() => selectVerifyMethod(currentStep, 1)}
                >
                    常规校验
                </Button>
            </Tooltip>
            <Tooltip title='校验用例执行过程中所有范围请求'>
                <Button
                    size='small'
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    type={2 === verifyMethod ? 'primary' : 'default'
                    }
                    onClick={() => selectVerifyMethod(currentStep, 2)}
                >
                    全量校验
                </Button>
            </Tooltip>
        </>
    );
};

export default ParamsVerifyMethod;
