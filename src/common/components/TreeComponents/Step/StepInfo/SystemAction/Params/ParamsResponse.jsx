import {useState, useEffect, useCallback} from 'react';
import {Tooltip, Input, Divider, Button, message, Select} from 'antd';
import {getQueryParams} from 'COMMON/utils/utils';
import {formatRequest, isJSON} from 'COMMON/components/TreeComponents/Step/utils';
import ParamsHeader from './ParamsHeader';
import ParamsCookie from './ParamsCookie';
import JsonEditor from './JsonEditor';
import styles from './Params.module.less';

const {TextArea} = Input;

function ParamsResponse(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [resData, setResData] = useState('{}');
    const [bodyMockType, setBodyMockType] = useState(1);

    useEffect(() => {
        const responseDetail = currentStep?.stepInfo?.params.params?.mockRequest?.responseDetail;
        // 未定义 或 bodyMockType 为 1 或 2 时，为response模拟
        // bodyMockType 为 3 时，为请求体模拟
        if (!responseDetail?.bodyMockType || [1, 2]?.includes(responseDetail?.bodyMockType)) {
            setResData(responseDetail.body);
        }
        setBodyMockType(responseDetail?.bodyMockType ?? 1);
    }, [currentStep]);

    const RenderJsonEditor = useCallback(() => {
        return (
            <JsonEditor
                currentStep={currentStep}
                resData={resData}
                handleUpdateStep={handleUpdateStep}
                setResData={setResData}
            />
        );
    }, [resData]);

    return (
        <>
            <Divider orientation="left" plain className={styles.paramsName}>
                {'数据模拟'}
            </Divider>
            <div>
                <Button
                    size='small'
                    type={3 !== bodyMockType ? 'primary' : 'default'}
                    onClick={async () => {
                        try {
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return false;
                            }

                            let newCurrentStep = {...currentStep};
                            newCurrentStep.stepInfo.params.params.
                                mockRequest.responseDetail.bodyMockType = 1;
                            await handleUpdateStep(newCurrentStep);
                            setBodyMockType(1);
                        } catch (err) {
                        }
                    }}
                >
                    返回数据模拟
                </Button>
                <Button
                    size='small'
                    type={3 === bodyMockType &&
                        !('Cookie' in (currentStep?.stepInfo?.params.params?.mockRequest?.requestDetail?.headers || {}))
                        ? 'primary' : 'default'}
                    onClick={() => {
                        try {
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return false;
                            }
                            let newCurrentStep = {...currentStep};
                            newCurrentStep.stepInfo.params.params.
                                mockRequest.responseDetail.bodyMockType = 3;
                            newCurrentStep.stepInfo.params.params
                                .mockRequest.requestDetail.headers = {};
                            handleUpdateStep(newCurrentStep);
                            setBodyMockType(3);
                        } catch (err) {
                        }
                    }}
                >
                    请求体模拟
                </Button>
                <Button
                    size='small'
                    type={3 === bodyMockType &&
                        ('Cookie' in (currentStep?.stepInfo?.params.params?.mockRequest?.requestDetail?.headers
                            || {})) ? 'primary' : 'default'}
                    onClick={async () => {
                        try {
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return false;
                            }
                            let newCurrentStep = {...currentStep};
                            newCurrentStep.stepInfo.params.params
                                .mockRequest.requestDetail.headers = {
                                Cookie: ''
                            };
                            newCurrentStep.stepInfo.params.params.
                                mockRequest.responseDetail.bodyMockType = 3;
                            await handleUpdateStep(newCurrentStep);
                            setBodyMockType(3);
                        } catch (err) {
                        }
                    }}
                >
                    Cookie 模拟
                </Button>
            </div>
            {
                [1, 2].includes(bodyMockType) &&
                <>
                    <Divider orientation="left" plain className={styles.paramsName}>
                        {
                            'mock' === currentStep?.stepInfo.params.type ? '返回数据' : 'Response 校验'}
                        &nbsp;
                        {!['readonly', 'debug', 'execute'].includes(editType) ?
                            <RenderJsonEditor /> : null}
                    </Divider>
                    {
                        'mock' === currentStep?.stepInfo.params.type ?
                            <div className={styles.paramsBtn}>
                                <Tooltip title='请求被拦截，数据全量变更（不经过服务器）'>
                                    <Button
                                        size='small'
                                        type={1 === bodyMockType ? 'primary' : 'default'}
                                        onClick={async () => {
                                            try {
                                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                                    return false;
                                                }
                                                let newCurrentStep = {...currentStep};
                                                newCurrentStep.stepInfo.params.params.
                                                    mockRequest.responseDetail.bodyMockType = 1;
                                                await handleUpdateStep(newCurrentStep);
                                                setBodyMockType(1);
                                            } catch (err) {
                                            }
                                        }}
                                    >
                                        全量模拟
                                    </Button>
                                </Tooltip>
                                <Tooltip title='请求正常，仅部分数据变更（经过服务器）'>
                                    <Button
                                        size='small'
                                        type={2 === bodyMockType ? 'primary' : 'default'}
                                        onClick={async () => {
                                            try {
                                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                                    return false;
                                                }
                                                let newCurrentStep = {...currentStep};
                                                newCurrentStep.stepInfo.params.params.
                                                    mockRequest.responseDetail.bodyMockType = 2;
                                                await handleUpdateStep(newCurrentStep);
                                                setBodyMockType(2);
                                            } catch (err) {
                                            }
                                        }}
                                    >
                                        部分模拟
                                    </Button>
                                </Tooltip>
                            </div> : null
                    }
                    <TextArea
                        rows={'' === resData ? 2 : 20}
                        className="inputEditor"
                        value={formatRequest(resData)}
                        onChange={(e) => {
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return false;
                            }
                            let data = JSON.parse(JSON.stringify(e.target.value).replace(/\r|\n/g, ''));
                            setResData(data);
                        }}
                        onBlur={async (e) => {
                            try {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let data = JSON.parse(JSON.stringify(e.target.value).replace(/\r|\n/g, ''));
                                if ('' === data.trim() || isJSON(data.trim())) {
                                    let newCurrentStep = {...currentStep};
                                    newCurrentStep.stepInfo.params.params.
                                        mockRequest.responseDetail.body = data;
                                    if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                                        '' !== newCurrentStep.stepInfo.params.params.
                                            mockRequest.matchRequest.hostname) {
                                        newCurrentStep.stepInfo.params.params.
                                            mockRequest.verifyDetail.response =
                                            '' !== data.trim() ?
                                                JSON.parse(e.target.value) : {};
                                    }
                                    await handleUpdateStep(newCurrentStep);
                                } else {
                                    message.info('请输入 JSON 格式');
                                    setResData(currentStep?.stepInfo.params.params.
                                        mockRequest.responseDetail.body);
                                }
                            } catch (err) {
                            }
                        }}
                    />
                </>
            }
            {
                [3].includes(bodyMockType) &&
                !('Cookie' in (currentStep?.stepInfo?.params.params?.mockRequest?.requestDetail?.headers || {})) &&
                <ParamsHeader {...props} />
            }
            {
                [3].includes(bodyMockType) &&
                ('Cookie' in (currentStep?.stepInfo?.params.params?.mockRequest?.requestDetail?.headers || {})) &&
                <ParamsCookie {...props} />
            }
        </>
    );
};




export default ParamsResponse;