@import "RESOURCES/css/common.less";

.paramsName {
    font-size: 12px !important;
    color: var(--color2) !important;
    font-weight: bold !important;
}

.paramsTitle,
.paramsInput {
    width: calc((100% - 40px) / 2);
}

.paramsIcon {
    width: 20px;
}

.paramsTitle {
    display: inline-block;
    margin: 10px 0 5px 0;
    text-align: center;
    background-color: var(--background-color1);
    border: 1px solid var(--border-color);
}

.paramsDelete {
    color: red;
}

.paramsBtn {
    margin-bottom: 10px;
}

.paramsSelectedDelete {
    margin-left: 5px;
    cursor: pointer;
    font-weight: normal;
}

.selectInfo {
    display: inline-block;
    margin: 10px 0 5px 5px;
    font-size: 12px;
    color: #777;
    font-weight: bold;
    height: 24px;
    line-height: 24px !important;
}

.urlPath {
    width: 100%;
    padding-bottom: 40px;

    .addBeforePath {
        float: left;
        height: 32px;
        line-height: 32px;
        width: 40%;
        padding: 0 8px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        background-color: var(--background-color1);
        border: 1px solid var(--border-color);
        cursor: pointer;
    }

    .pathInput {
        float: left;
        width: 60%;
    }
}


.jsonEditor :global(.jsoneditor-tree) {
    max-height: 500px;
}

.jsonEditor :global(.jsoneditor-transform),
.jsonEditor :global(.jsoneditor-sort),
.jsonEditor :global(.jsoneditor-sort-asc),
.jsonEditor :global(.jsoneditor-extract) {
    display: none;
}