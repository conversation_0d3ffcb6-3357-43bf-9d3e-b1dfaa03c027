import { forwardRef, useRef, useState, useEffect, useCallback, useImperativeHandle } from 'react';
import { message, InputNumber, Select, Button, Tooltip, Spin, Input } from 'antd';
import { EditOutlined, RedoOutlined } from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { checkDevice } from 'COMMON/config/device';
import { createDomStepInitConfig } from 'COMMON/components/TreeComponents/Step/StepInit/utils';
import { getStepList } from 'COMMON/api/front_qe_tools/step';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import { CardTitle } from 'COMMON/components/common/Card';
import SelectWithCategory from 'COMMON/components/Select/SelectWithCategory';
import CommonConfig from 'COMMON/components/TreeComponents/Step/StepInfo/components/CommonConfig';
import VenusDomAction from 'COMMON/components/TreeComponents/Step/StepInfo/DomAction/VenusDomAction';
import StepListOverview from 'COMMON/components/TreeComponents/Step/StepItem/Params/StepListOverview';
import MarsDomAction from 'COMMON/components/TreeComponents/Step/StepInfo/DomAction/MarsDomAction';

import styles from '../SystemAction.module.less';

function RunTemplate(props) {
    const {
        currentStep,
        snippetList,
        curOsType,
        nodeId,
        setShowModal,
        editType,
        stepList,
        handleUpdateStep,
        handleUpdateStepList,
        pageSourceSwitch,
        defaultConfig,
        deviceList,
        currentDevice
    } = props;
    const [id, setId] = useState(null);
    const [loading, setLoading] = useState(false);
    const [loopTimes, setLoopTimes] = useState(1);
    const [triggerCondition, setTriggerCondition] = useState(1);
    const [content, setContent] = useState('');
    const [selectedCategory, setSelectedCategory] = useState(null);
    const templateModalRef = useRef();
    const startRef = useRef();
    const [messageApi, contextHolder] = message.useMessage();
    const startRecordRef = useRef(false);
    let commonParams = {
        commonAlertClear: defaultConfig?.mobileConfig?.stepConfig?.commonAlertClear ?? false,
        stepInterval: +defaultConfig?.mobileConfig?.stepConfig?.stepInterval ?? 2
    };
    let widgetParams = {
        beforeReplayWait: +defaultConfig?.mobileConfig?.widgetConfig?.beforeReplayWait ?? 0,
        beforeActionWait: +defaultConfig?.mobileConfig?.widgetConfig?.beforeActionWait ?? 0,
        screenCount: +defaultConfig?.mobileConfig?.widgetConfig?.screenCount ?? 1,
        retryTimes: +defaultConfig?.mobileConfig?.widgetConfig?.retryTimes ?? 0
    };
    useEffect(() => {
        setId(currentStep?.stepInfo?.params?.params?.id);
        setLoopTimes(currentStep?.stepInfo?.params?.params?.loopTimes ?? 1);
        const condition = currentStep?.stepInfo?.params?.params?.condition;
        if (condition?.params?.type === 'expression') {
            setTriggerCondition(3);
            setContent(condition?.params?.params?.content ?? '');
        } else if ([9, 10]?.includes(condition?.type)) {
            setTriggerCondition(2);
        } else {
            setTriggerCondition(1);
        }
    }, [currentStep?.stepId]);

    const showTemplateModal = (templateId) => {
        let template = (snippetList?.[curOsType] ?? [])?.find(
            (item) => item.templateId === templateId
        );
        templateModalRef?.current.show({
            key: 'step-template',
            data: template?.templateId,
            tabName: template?.tabName
        });
        setShowModal(true);
    };

    const getDetail = (step) => {
        let templateCaseNodeId = (snippetList?.[curOsType] ?? [])?.find(
            (item) => item.templateId === step?.stepInfo.params.params.id
        )?.caseNodeId;
        if (templateCaseNodeId) {
            setLoading(true);
            getStepList({
                caseNodeId: +templateCaseNodeId,
                osType: curOsType
            })
                .then((res) => {
                    step.stepInfo.params.params.step = res.stepList;
                    handleUpdateStepList(
                        stepList?.map((item) => {
                            if (
                                item.stepInfo.params.params.id === step?.stepInfo.params.params.id
                            ) {
                                return step;
                            }
                            return item;
                        }),
                        step
                    );
                })
                .catch((err) => {})
                .finally(() => {
                    setLoading(false);
                });
        }
    };

    useEffect(() => {
        // 等待模板片段加载完成之后再开始录制
        if (startRef.current && startRecordRef.current) {
            startRef.current?.start();
            startRecordRef.current = false;
        }
    }, [startRef.current, startRecordRef.current]);
    return (
        <>
            {contextHolder}
            <div>
                <CardTitle text="操作参数" />
                <div className={styles.paramsSelect}>
                    <span className={styles.paramsSelectTitle}>片段选择</span>
                    <SelectWithCategory
                        placeholder={'选择模版'}
                        className={styles.paramsSelectData}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        value={id}
                        showSearch
                        editType={editType}
                        showSearchIcon={false}
                        filterOption={(input, option) => option.label.includes(input)}
                        dataSource={snippetList?.[curOsType] ?? []}
                        categoryKey="tabName"
                        labelKey="templateName"
                        valueKey="templateId"
                        selectedCategory={selectedCategory}
                        onCategorySelect={setSelectedCategory}
                        text={'测试片段选择'}
                        onSelect={async (_, option) => {
                            try {
                                let newCurrentStep = { ...currentStep };
                                newCurrentStep.stepDesc = '执行【' + option.label + '】测试片段';
                                newCurrentStep.stepInfo.desc = newCurrentStep.stepDesc;
                                newCurrentStep.stepInfo.params.params.id = option.value;
                                setId(option.value);
                                await handleUpdateStep(newCurrentStep);
                                await getDetail(newCurrentStep);
                            } catch (err) {
                                console.log(err?.message ?? err);
                                message.error(err?.message ?? err);
                            }
                        }}
                        addChange={() => showTemplateModal()}
                        settingChange={() => showTemplateModal()}
                        editChange={(id) => showTemplateModal(id)}
                    />
                </div>
                <CardTitle
                    text={
                        <span>
                            片段详情&nbsp;
                            {!['debug']?.includes(editType) && (
                                <EditOutlined onClick={(e) => showTemplateModal(id)} />
                            )}
                        </span>
                    }
                />
                <Spin spinning={loading} tip="加载中...">
                    <StepListOverview {...props} step={currentStep} />
                </Spin>
            </div>
            <CommonConfig {...props} />
            {/* server端的beforeall配置不展示 */}
            {curOsType !== 4 && (
                <>
                    <CardTitle text="执行次数" />
                    <InputNumber
                        className={styles.separateLine}
                        value={loopTimes}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        onChange={(e) => {
                            setLoopTimes(e);
                            let newCurrentStep = { ...currentStep };
                            newCurrentStep.stepInfo.params.params.loopTimes = e;
                            handleUpdateStep(newCurrentStep);
                        }}
                        placeholder="请输入执行次数"
                        allowClear
                    />
                    <CardTitle text="触发条件" />
                    <div className={styles.separateLine}>
                        <Select
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            className={triggerCondition === 2 ? styles.selectIf : styles.select}
                            placeholder="请选择触发条件"
                            value={triggerCondition}
                            options={[
                                { label: '无条件', value: 1 },
                                { label: '当找到如下控件时触发', value: 2 },
                                { label: '表达式判断（C语法）', value: 3 }
                            ]}
                            onChange={(e) => {
                                let newCurrentStep = { ...currentStep };
                                if (e === 1) {
                                    newCurrentStep.stepInfo.params.params.condition = {};
                                    handleUpdateStep(newCurrentStep);
                                }
                                if (e === 2) {
                                    if (['readonly']?.includes(editType)) {
                                        messageApi.warning('当前模式下，仅只读');
                                        return false;
                                    }
                                    if (!isElectron()) {
                                        messageApi.warning('Web 不支持步骤创建');
                                        return false;
                                    }
                                    if (!checkDevice(deviceList, currentDevice, curOsType)) {
                                        return;
                                    }
                                    let step = createDomStepInitConfig({
                                        type: 'nope',
                                        widgetParams: widgetParams,
                                        commonParams: commonParams,
                                        pageSourceSwitch: pageSourceSwitch
                                    });
                                    newCurrentStep.stepInfo.params.params.condition = {
                                        ...step,
                                        params: {
                                            ...step.params,
                                            actionInfo: {
                                                ...step.params.actionInfo,
                                                type: 'nope'
                                            }
                                        },
                                        desc: '执行条件 If'
                                    };
                                    handleUpdateStep(newCurrentStep);
                                    startRecordRef.current = true;
                                }
                                if (e === 3) {
                                    newCurrentStep.stepInfo.params.params.condition = {
                                        type: 1,
                                        params: {
                                            type: 'expression',
                                            params: {
                                                content: ''
                                            }
                                        }
                                    };
                                    handleUpdateStep(newCurrentStep);
                                }
                                setTriggerCondition(e);
                            }}
                        />
                    </div>
                    {triggerCondition === 2 && (
                        <div className={styles.separateLine} style={{ overflow: 'scroll' }}>
                            {currentStep?.stepInfo?.params?.params?.condition?.type === 9 ? (
                                <VenusDomAction
                                    key={'step_edit_stepInfo'}
                                    currentStep={{
                                        stepInfo: {
                                            ...currentStep?.stepInfo?.params?.params?.condition
                                        },
                                        type: 'nope',
                                        stepId: currentStep.stepId,
                                        stepDesc: '执行条件 If'
                                    }}
                                    setCurrentStep={(data) => {
                                        let newCurrentStep = { ...currentStep };
                                        newCurrentStep.stepInfo.params.params.condition =
                                            data?.stepInfo;
                                        handleUpdateStep(newCurrentStep);
                                    }}
                                    handleUpdateStep={(e) => {
                                        let newCurrentStep = { ...currentStep };
                                        newCurrentStep.stepInfo.params.params.condition =
                                            e?.stepInfo;
                                        handleUpdateStep(newCurrentStep);
                                    }}
                                    osType={curOsType}
                                    curOsType={curOsType}
                                    hasProxy={false}
                                    nodeId={nodeId}
                                    props={props}
                                    stepId={currentStep.stepId}
                                    operationType={'ifThen'}
                                    ref={startRef}
                                />
                            ) : null}
                            {currentStep?.stepInfo?.params?.params?.condition?.type === 10 ? (
                                <MarsDomAction
                                    key={'step_edit_stepInfo'}
                                    currentStep={{
                                        stepInfo: {
                                            ...currentStep?.stepInfo?.params?.params?.condition
                                        },
                                        type: 'nope',
                                        stepId: currentStep.stepId,
                                        stepDesc: '执行条件 If'
                                    }}
                                    setCurrentStep={(data) => {
                                        let newCurrentStep = { ...currentStep };
                                        newCurrentStep.stepInfo.params.params.condition =
                                            data?.stepInfo;
                                        handleUpdateStep(newCurrentStep);
                                    }}
                                    handleUpdateStep={(e) => {
                                        let newCurrentStep = { ...currentStep };
                                        newCurrentStep.stepInfo.params.params.condition =
                                            e?.stepInfo;
                                        handleUpdateStep(newCurrentStep);
                                    }}
                                    osType={curOsType}
                                    curOsType={curOsType}
                                    hasProxy={false}
                                    nodeId={nodeId}
                                    props={props}
                                    stepId={currentStep.stepId}
                                    operationType={'ifThen'}
                                    ref={startRef}
                                />
                            ) : null}
                        </div>
                    )}
                    {triggerCondition === 3 && (
                        <div
                            className={styles.separateLine}
                            style={{ overflow: 'scroll', marginTop: 5 }}
                        >
                            <Input
                                placeholder="请输入表达式"
                                value={content}
                                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                onChange={(e) => {
                                    e.stopPropagation();
                                    setContent(e.target.value);
                                }}
                                onBlur={(e) => {
                                    let newCurrentStep = { ...currentStep };
                                    newCurrentStep.stepInfo.params.params.condition.params.params.content =
                                        e.target.value;
                                    handleUpdateStep(newCurrentStep);
                                }}
                            />
                        </div>
                    )}
                </>
            )}

            <SettingModal ref={templateModalRef} />
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    defaultConfig: state.common.base.defaultConfig,
    snippetList: state.common.case.snippetList,
    showModal: state.common.base.showModal,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    pageSourceSwitch: state.common.case.pageSourceSwitch
}))(forwardRef(RunTemplate));
