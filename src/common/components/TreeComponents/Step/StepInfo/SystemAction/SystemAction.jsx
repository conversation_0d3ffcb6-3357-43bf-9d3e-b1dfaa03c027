import {useState} from 'react';
import Loading from 'COMMON/components/common/Loading';
import LaunchApp from './LaunchApp';
import ClearApp from './ClearApp';
import AuthApp from './AuthApp';
import Scheme from './Scheme';
import Swipe from './Swipe';
import Wait from './Wait';
import Home from './Home';
import Back from './Back';
import RunTemplate from './RunTemplate';
import ClearPop from './ClearPop';
import LogCheck from './LogCheck';
import ClearRequest from './ClearRequest';
import ClearMock from './ClearMock';
import Connect from './Connect';
import DisConnect from './DisConnect';
import KvVerify from './KyVerify';
import Mock from './Mock';
import RequestApi from './RequestApi';
import RequestRedirect from './RequestRedirect';
import WhiteScreen from './WhiteScreen';
import PushFile from './PushFile';
import Shell from './Shell';
import StepOperator from './StepOperator';
import InstallApp from './InstallApp';
import Login from './Login';
import styles from './SystemAction.module.less';

function SystemAction(props) {
    const {currentStep, editType, descExtra} = props;
    const [loading, setLoading] = useState(false);

    if (loading) {
        return <Loading />;
    }

    return (
        <div className={styles.systemAction}>
            {
                -1 === ['debug', 'execute'].indexOf(editType) &&
                <StepOperator {...props} />
            }
            {descExtra}
            {currentStep?.stepInfo.params.type === 'launchApp' ?
                <LaunchApp {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'clearApp' ?
                <ClearApp {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'authApp' ?
                <AuthApp {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'scheme' ?
                <Scheme {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'swipe' ?
                <Swipe {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'wait' ?
                <Wait {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'home' ?
                <Home {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'back' ?
                <Back {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'runTemplate' ?
                <RunTemplate {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'clearPop' ?
                <ClearPop {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'logCheck' ?
                <LogCheck {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'clearRequest' ?
                <ClearRequest {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'clearMock' ?
                <ClearMock {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'networkConnect' &&
                currentStep?.stepInfo.params.params.type === 1 ? <Connect {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'networkConnect' &&
                currentStep?.stepInfo.params.params.type === 0 ? <DisConnect {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'requestVerify' ?
                <KvVerify {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'mock' ?
                <Mock {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'request' ?
                <RequestApi {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'requestRedirect' ?
                <RequestRedirect {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'whiteScreen' ?
                <WhiteScreen {...props} /> : null}
            {currentStep?.stepInfo.params.type === 'pushFile' ?
                <PushFile {...props} setLoading={setLoading} /> : null}
            {currentStep?.stepInfo.params.type === 'shell' ?
                <Shell {...props} setLoading={setLoading} /> : null}
            {currentStep?.stepInfo.params.type === 'installApp' ?
                <InstallApp {...props} setLoading={setLoading} /> : null}
            {currentStep?.stepInfo.params.type === 'login' ?
                <Login {...props} setLoading={setLoading} /> : null}
        </div>
    );
};

export default SystemAction;
