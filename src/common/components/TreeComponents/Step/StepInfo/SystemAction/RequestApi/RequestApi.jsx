import {Tabs} from 'antd';
import {useState, useEffect} from 'react';
import {CardTitle} from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import ParamsBodyDefined from '../Params/ParamsBodyDefined';
import ParamsRequestMethod from '../Params/ParamsRequestMethod';
import ParamsTimeout from '../Params/ParamsTimeout';
import ParamsUrl from '../Params/ParamsUrl';
import ParamsCurl from '../Params/ParamsCurl';
import VariableExtraction from '../Params/VariableExtraction';

function ResponseApi(props) {
    const {currentStep, editType} = props;
    const [activedKey, setActivedKey] = useState('1');

    useEffect(() => {
        setActivedKey(currentStep?.stepInfo.params.params?.curl ? '2' : '1');
    }, [currentStep?.stepId]);

    return (
        <div>
            <CardTitle text='操作参数' />
            <Tabs
                activeKey={activedKey}
                type="card"
                items={[
                    {
                        label: '通用调用',
                        key: '1',
                        children: (
                            <>
                                <ParamsUrl {...props} />
                                <ParamsBodyDefined {...props} />
                                <ParamsRequestMethod {...props} />
                                <ParamsTimeout {...props} />
                            </>
                        )
                    },
                    {
                        label: 'curl',
                        key: '2',
                        children: (
                            <ParamsCurl {...props} />
                        )
                    }
                ]}
                onChange={(key) => {
                    setActivedKey(key);
                }}
            />
            <VariableExtraction {...props} />
            <CommonConfig {...props} />
        </div>
    );
};

export default ResponseApi;