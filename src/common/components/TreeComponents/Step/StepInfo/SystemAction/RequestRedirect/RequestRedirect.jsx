import {isEmpty} from 'lodash';
import {Empty, Divider, Row, message} from 'antd';
import NoContent from 'COMMON/components/common/NoContent';
import {CardTitle} from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import ParamsUrl from '../Params/ParamsUrl';
import ParamsQuery from '../Params/ParamsQuery';
import ParamsHeader from '../Params/ParamsHeader';
import ParamsRedirect from '../Params/ParamsRedirect';
import styles from '../SystemAction.module.less';

function RequestRedirect(props) {
    const {currentStep} = props;
    const hasUrl = isEmpty(currentStep?.stepInfo.params.params.mockRequest?.matchRequest?.hostname);
    return (
        <div>
            <CardTitle text='拦截请求' />
            {hasUrl ? <NoContent
                text="暂未选择请求"
                className={styles.noContent}
            /> : <>
                <ParamsQuery {...props} />
                <CardTitle text='转发请求' />
                <ParamsUrl {...props} />
                <ParamsHeader {...props} />
                <ParamsRedirect {...props} />
            </>}
            <CommonConfig {...props} />
        </div>
    )
        ;
};

export default RequestRedirect;