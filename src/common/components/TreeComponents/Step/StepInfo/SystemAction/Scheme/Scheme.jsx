import {useState, useEffect, useRef, useMemo} from 'react';
import classnames from 'classnames';
import {Input, InputNumber, message, Radio, Spin} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import {CardTitle} from 'COMMON/components/common/Card';
import SelectWithDropdownRender from 'COMMON/components/Select/SelectWithDropdownRender';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import SchemeModal from 'COMMON/components/TreeComponents/components/SchemeModal/SchemeModal';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

const {TextArea} = Input;

function Scheme(props) {
    const {currentStep, currentSpace, editType, handleUpdateStep, curOsType,
        setShowModal, schemeList, setSchemeList} = props;
    const [loading, setLoading] = useState(false);
    const [schemeId, setSchemeId] = useState(null);
    const [scheme, setScheme] = useState('');
    const [wait, setWait] = useState(0);
    const [source, setSource] = useState(1);
    const schemeModalRef = useRef();
    const schemeAddModalRef = useRef();

    useEffect(() => {
        if (!currentStep?.stepInfo.params.params.id) {
            setSource(2);
            setScheme(
                currentStep?.stepInfo.params.params.scheme ??
                null
            );
        } else {   // 如果当前步骤有id，则选择展示模版信息
            setSource(1);
            let scheme = schemeList?.[curOsType]?.find(
                (item) => item.schemeId === currentStep?.stepInfo.params.params.id
            );
            setSchemeId(scheme?.schemeId ?? null);
            setScheme(scheme?.schemeContent ?? null);
        }
        setWait(currentStep?.stepInfo.params.params.wait);
    }, [currentStep?.stepId]);

    let descOptions = useMemo(() => {
        return (schemeList?.[curOsType] ?? [])?.map(item => (
            {
                label: item.schemeName,
                value: item.schemeId
            })
        );
    }, [schemeList?.[curOsType]]);

    const showSchemeModal = () => {
        setShowModal(true);
        schemeModalRef?.current.show({key: 'scheme-manage'});
    };

    const showSchemeAddModal = () => {
        setShowModal(true);
        schemeAddModalRef?.current?.show(curOsType);
    };
    const sourceChange = ({target: {value}}) => {
        setSource(value);
        let newCurrentStep = {...currentStep};
        newCurrentStep.stepInfo.params.params.id = null;
        newCurrentStep.stepInfo.params.params.desc = '';
        newCurrentStep.stepInfo.params.params.scheme = '';
        newCurrentStep.stepInfo.desc = newCurrentStep.stepDesc;
        newCurrentStep.stepDesc = value === 1 ? '' : '跳转【自定义 Scheme 链接】';
        newCurrentStep.stepInfo.desc = newCurrentStep.stepDesc;
        setSchemeId(null);
        setScheme('');
        handleUpdateStep(newCurrentStep);
    };

    return (
        <Spin spinning={loading}>
            <CardTitle text="操作参数" />
            <div className={styles.paramsSelect}>
                <span className={styles.title}>来源</span>
                <Radio.Group
                    options={[
                        {label: 'Scheme 模版', value: 1},
                        {label: '自定义', value: 2}
                    ]}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    onChange={sourceChange}
                    value={source}
                />
            </div>
            <div>
                {source === 1 && (
                    <div className={styles.paramsSelect}>
                        <span className={styles.paramsSelectTitle}>Scheme 类型</span>
                        <SelectWithDropdownRender
                            className={styles.paramsSelectData}
                            editType={editType}
                            placeholder="请选择 Scheme"
                            text="Scheme 选择"
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            showSearch
                            showSearchIcon={false}
                            filterOption={(input, option) => option.label.includes(input)}
                            value={schemeId}
                            options={descOptions}
                            onChange={(value) => {
                                const selectScheme = (schemeList?.[curOsType] ?? [])?.find(
                                    (item) => item.schemeId === value
                                );
                                let newId = selectScheme?.schemeId;
                                let newSchemeName = selectScheme?.schemeName;
                                let newSchemeContent = selectScheme?.schemeContent;
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.params.id = newId;
                                newCurrentStep.stepInfo.params.params.desc = newSchemeName;
                                newCurrentStep.stepDesc = '跳转【' + newSchemeName + '】';
                                newCurrentStep.stepInfo.params.params.scheme = newSchemeContent;
                                newCurrentStep.stepInfo.desc = newCurrentStep.stepDesc;
                                setSchemeId(newId);
                                setScheme(newSchemeContent);
                                handleUpdateStep(newCurrentStep);
                            }}
                            addChange={showSchemeAddModal}
                            settingChange={showSchemeModal}
                        />
                    </div>
                )}
                <div className={styles.paramsSelect}>
                    <span className={styles.paramsSelectTitle}>Scheme 链接</span>
                    <TextArea
                        className={classnames(styles.paramsSelectData, 'inputEditor')}
                        placeholder="请输入 Scheme"
                        disabled={
                            source === 1 || ['readonly', 'debug', 'execute'].includes(editType)
                        }
                        value={scheme}
                        autoSize={{minRows: source === 1 ? 2 : 8, maxRows: source === 1 ? 2 : 12}}
                        onChange={async (value) => {
                            if (source === 1) {
                                message.info('Scheme 不支持编辑, 仅支持从 Scheme 库中引用');
                                return;
                            } else {
                                setScheme(value.target.value);
                            }
                        }}
                        onBlur={(e) => {
                            let newCurrentStep = {...currentStep};
                            delete newCurrentStep.stepInfo.params.params.id;
                            newCurrentStep.stepInfo.params.params.desc = '';
                            newCurrentStep.stepDesc = '跳转【自定义 Scheme 链接】';
                            newCurrentStep.stepInfo.params.params.scheme =
                                e.target.value;
                            newCurrentStep.stepInfo.desc = newCurrentStep.stepDesc;
                            setSchemeId(null);
                            setScheme(e.target.value);
                            handleUpdateStep(newCurrentStep);
                        }}
                    />
                </div>
            </div>
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>跳转后等待</span>
                <InputNumber
                    className={classnames(styles.paramsSelectData, 'inputEditor')}
                    min={0}
                    addonAfter="秒"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    value={wait}
                    onChange={(value) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        setWait(value);
                    }}
                    onBlur={(e) => {
                        let value = 0;
                        if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                            value = parseInt(e.target.value.trim(), 10);
                        }
                        let newCurrentStep = {...currentStep};
                        newCurrentStep.stepInfo.params.params.wait = value;
                        setWait(value);
                        handleUpdateStep(newCurrentStep);
                    }}
                />
            </div>
            <CommonConfig {...props} />
            <SettingModal ref={schemeModalRef} />
            <SchemeModal
                ref={schemeAddModalRef}
                curOsType={curOsType}
                onChange={(schemeId, schemeName, schemeContent) => {
                    let newCurrentStep = {...currentStep};
                    newCurrentStep.stepInfo.params.params.id = schemeId;
                    newCurrentStep.stepInfo.params.params.desc = schemeName;
                    newCurrentStep.stepDesc = '跳转【' + schemeName + '】';
                    newCurrentStep.stepInfo.params.params.scheme = schemeContent;
                    setSchemeId(schemeId);
                    setScheme(schemeContent);
                    handleUpdateStep(newCurrentStep);
                }}
            />
        </Spin>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    showModal: state.common.base.showModal,
    schemeList: state.common.case.schemeList
}))(Scheme);
