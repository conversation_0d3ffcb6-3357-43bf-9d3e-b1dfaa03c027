@import "RESOURCES/css/common.less";

.systemAction {
    width: calc(100% -30px);
    margin: 15px 25px 15px 15px;
}

.paramsEdit {
    width: 100%;

    :global {

        .ant-select-selection-overflow,
        .custom-default-select-selection-overflow,
        .custom-dark-select-selection-overflow {
            flex-wrap: wrap;
        }

    }
}


.paramsName {
    color: #959292 !important;
    font-weight: bold !important;
}

.flexCenter {
    display: flex;
    align-items: center;
}

.radio {
    width: 100%;
}

.radioGroup {
    margin-top: 5px;
}

// Select
.paramsSelect {
    position: relative;
    margin-bottom: 5px;
}

.paramsSelectTitle {
    display: block;
    float: left;
    width: 90px;
    margin-left: 10px;
    height: 32px;
    line-height: 32px;
}

.paramsSelectData {
    width: calc(100% - 100px);
}

// modal icon
.operatorDetailIcon,
.paramsSelectTag {
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
}

.paramsSelectTag {
    right: -50px;
}

.addIcon {
    font-size: 12px;
    color: #777;
}

.separateLine {
    width: calc(100% - 10px);
    margin-left: 10px;

    // margin-right: 20px;
    .select{
        width: 100%;
    }
    .selectIf {
        width: calc(100% - 30px);
    }

    .icon {
        width: 20px;
        cursor: pointer;
        margin-left: 10px;
        color: var(--primary-color);
    }
}

.title {
    width: 190px;
    margin-left: 10px;
    margin-right: 60px;
    height: 32px;
    line-height: 32px;
}

// 禁止修改
.banModify {
    font-size: 12px;
    color: red;
    cursor: pointer;
}

// 快速导入
.import {
    font-size: 12px;
    color: #777;
    cursor: pointer;

    &:hover {
        text-decoration: underline;
        color: var(--primary-color);
    }
}

