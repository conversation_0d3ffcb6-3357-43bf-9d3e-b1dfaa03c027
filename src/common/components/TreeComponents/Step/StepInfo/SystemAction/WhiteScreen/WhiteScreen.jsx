import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {InfoCircleOutlined} from '@ant-design/icons';
import {InputNumber, Tooltip, message} from 'antd';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import {CardTitle} from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function WhiteScreen(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [ratio, setRatio] = useState(80);

    useEffect(() => {
        setRatio(currentStep?.stepInfo.params.params.ratio);
    }, [currentStep]);

    return (
        <div>
            <CardTitle text='操作参数' />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>
                    阈值设置
                    &nbsp;
                    <Tooltip title="纯色屏指数，100 为纯白">
                        <InfoCircleOutlined />
                    </Tooltip>
                </span>
                <InputNumber
                    className={classnames(styles.paramsSelectData, 'inputEditor')}
                    min={0}
                    max={100}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    value={ratio}
                    addonAfter='%'
                    onChange={(value) => {
                        if (!isElectron() ||
                            -1 !== ['readonly', 'debug', 'execute'].indexOf(editType)) {
                            return false;
                        }
                        setRatio(value);
                    }}
                    onBlur={async (e) => {
                        let value = 0;
                        if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                            value = parseInt(e.target.value.trim(), 10);
                        }
                        if (value > 100) {
                            value = 100;
                        }
                        if (value < 0) {
                            value = 0;
                        }
                        let newCurrentStep = {...currentStep};
                        newCurrentStep.stepInfo.params.params.ratio = value;
                        await handleUpdateStep(newCurrentStep);
                    }}
                />
            </div>
            <CommonConfig {...props} />
        </div>
    );
};

export default WhiteScreen;
