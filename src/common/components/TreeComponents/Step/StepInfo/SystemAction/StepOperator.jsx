import {useState, useEffect, useRef} from 'react';
import {Tooltip, message, Popconfirm} from 'antd';
import {
    ApiOutlined,
    ClearOutlined
} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import electron from 'COMMON/utils/electron';
import {getName} from 'COMMON/components/TreeComponents/Step/utils';
import RequestProxyModal from 'COMMON/components/TreeComponents/Step/RequestProxyModal';
import styles from '../StepInfo.module.less';
import StepTest from '../../StepItem/Operation/StepTest';

function StepOperator(props) {
    const {currentStep, currentDevice, setShowModal,
        curOsType, editType, handleUpdateStep, setMockList,
        currentNode,
    } = props;
    const [messageApi, contextHolder] = message.useMessage();
    const [newDevice, setNewDevice] = useState(null);
    const RequestProxyModalRef = useRef();

    useEffect(() => {
        setNewDevice(currentDevice);
    }, [currentDevice]);

    let StepTestRender = (
        <StepTest
            className={styles.operatorDetailIcon}
            step={currentStep}
            currentStep={currentStep}
            curOsType={curOsType}
            currentNode={currentNode}
            type='domAction'
        />
    );
    return (
        <div className={styles.stepOperator}>
            {contextHolder}
            <span className={styles.stepIndex}>
                {getName(currentStep.stepInfo.type, currentStep.stepInfo.params)}
            </span>
            {
                (1 !== currentStep.stepInfo.type ||
                    (1 === currentStep.stepInfo.type &&
                        -1 === ['mock', 'requestRedirect', 'verifyRequest'].indexOf(
                            currentStep.stepInfo.params.type)) || (
                        1 === currentStep.stepInfo.type &&
                        -1 !== ['mock', 'requestRedirect', 'verifyRequest'].indexOf(
                            currentStep.stepInfo.params.type) &&
                        '' !== currentStep.stepInfo.params.params
                            .mockRequest?.matchRequest?.hostname)) && isElectron()
                    ? StepTestRender : null
            }
            {isElectron() &&
                !['readonly', 'debug', 'execute'].includes(editType) ?
                <span>
                    {currentStep.stepInfo.type === 1 &&
                        -1 !== ['mock', 'requestVerify', 'requestRedirect'
                        ].indexOf(currentStep.stepInfo.params.type)
                        && !currentStep.stepInfo.params.params?.digitalNodeId ?
                        <Tooltip title="选择请求">
                            <ApiOutlined
                                className={styles.operatorDetailIcon}
                                onClick={async () => {
                                    if (!newDevice) {
                                        messageApi.error('暂无设备');
                                        return false;
                                    }
                                    try {
                                        if (![2, 3].includes(newDevice.status)) {
                                            messageApi.error('设备状态异常');
                                            return false;
                                        }
                                        await electron.send('proxy.start',
                                            {deviceType: curOsType, deviceId: newDevice?.deviceId});
                                        setShowModal(true);
                                        RequestProxyModalRef?.current?.show();
                                    } catch (err) {
                                    }
                                }}
                            />
                        </Tooltip>
                        : null}
                </span> : null}
            {isElectron() &&
                !['readonly', 'debug', 'execute'].includes(editType) && currentStep.stepInfo.type === 1 &&
                -1 !== ['mock'].indexOf(currentStep.stepInfo.params.type) ?
                <span>
                    <Tooltip title='清空'>
                        <ClearOutlined
                            className={styles.operatorDetailIcon}
                            onClick={async () => {
                                try {
                                    if (null === currentDevice) {
                                        messageApi.error('暂无设备');
                                        return false;
                                    }
                                    if (2 !== currentDevice.status) {
                                        messageApi.error('设备非空闲状态');
                                        return false;
                                    }
                                    await electron.send('proxy.mock.clear',
                                        {deviceType: curOsType, deviceId: currentDevice.deviceId});
                                    messageApi.success('清空成功');
                                    setMockList([]);
                                } catch (err) {
                                }
                            }}
                        />
                    </Tooltip>
                </span> : null
            }
            <RequestProxyModal
                ref={RequestProxyModalRef}
                editType={editType}
                curOsType={curOsType}
                currentStep={currentStep}
                handleUpdateStep={handleUpdateStep}
                currentNode={currentNode}
            />
        </div>
    );
};

export default connectModel([commonModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    recording: state.common.base.recording,
    showModal: state.common.base.showModal,
    mockList: state.common.case.mockList,
}))(StepOperator);
