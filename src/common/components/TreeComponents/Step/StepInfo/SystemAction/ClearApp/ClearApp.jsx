import { useState, useEffect, useRef } from 'react';
import { isEmpty } from 'lodash';
import { Select, Tooltip, message, Switch, Spin } from 'antd';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { CardTitle } from 'COMMON/components/common/Card';
import SelectWithDropdownRender from 'COMMON/components/Select/SelectWithDropdownRender';
import RunCaseSettingModal from 'FEATURES/front_qe_tools/case/edit/EditPage/Modal/RunCaseSettingModal/RunCaseSettingModal';
import AppModal from 'COMMON/components/TreeComponents/components/AppModal/AppModal';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function ClearApp(props) {
    const {
        currentStep,
        currentSpace,
        appList,
        setAppList,
        editType,
        setShowModal,
        curOsType,
        handleUpdateStep
    } = props;
    const [loading, setLoading] = useState(true);
    const [appId, setAppId] = useState(null);
    const [isAutoAuthApp, setIsAutoAuthApp] = useState(true);
    const [appName, setAppName] = useState('');
    const appModalRef = useRef();
    const appAddModalRef = useRef();

    // 初始化清理 App 步骤
    const initClearAppStep = (step, app) => {
        let newCurrentStep = { ...step };
        newCurrentStep.stepInfo.params.params.id = app?.appId;
        newCurrentStep.stepInfo.params.params.appName = app?.appName;
        newCurrentStep.stepInfo.params.params.packageName = app?.packageName;
        newCurrentStep.stepInfo.desc =
            '1' === newCurrentStep.stepInfo.params.params.isColdStart
                ? '冷启动【' + app?.appName + '】'
                : '热启动【' + app?.appName + '】';
        setAppId(app?.appId);
        setAppName(app?.appName);
        setIsAutoAuthApp(newCurrentStep?.stepInfo.params.params?.autoAuthApp ?? true);
        handleUpdateStep(newCurrentStep);
    };

    // 监听当前步骤 stepId 变化
    useEffect(() => {
        if (currentStep?.stepInfo.params.params.id) {
            setAppId(currentStep?.stepInfo?.params?.params?.id);
            setAppName(currentStep?.stepInfo?.params?.params?.appName);
            setIsAutoAuthApp(currentStep?.stepInfo?.params?.params?.autoAuthApp ?? true);
        } else {
            if (['template', 'edit'].includes(editType)) {
                let _app = appList?.[curOsType]?.[0];
                if (!currentStep?.stepInfo.params.params.id && _app) {
                    initClearAppStep(currentStep, _app);
                }
            }
        }
        setLoading(false);
    }, [currentStep?.stepId, curOsType]);

    let appNameOptions = [];
    appNameOptions = appList?.[curOsType]?.map((item) => {
        return {
            label: item.appName,
            value: item.appName
        };
    });

    const showAppModal = () => {
        setShowModal(true);
        appModalRef?.current.show({ key: 'app-config' });
    };

    const showAppAddModal = () => {
        setShowModal(true);
        appAddModalRef?.current?.show(curOsType);
    };
    return (
        <Spin spinning={loading}>
            <CardTitle text="操作参数" />
            <div className={styles.paramsSelect}>
                <Tooltip title={`ID: ${appId}`} placement="left">
                    <span className={styles.paramsSelectTitle}>APP 名称</span>
                </Tooltip>
                <SelectWithDropdownRender
                    className={styles.paramsSelectData}
                    editType={editType}
                    placeholder="请选择 APP 名称"
                    text="APP 选择"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    showSearch
                    showSearchIcon={false}
                    filterOption={(input, option) => option.label.includes(input)}
                    value={appName}
                    options={appNameOptions}
                    onChange={(value) => {
                        const selectApp = appList?.[curOsType]?.find(
                            (item) => item.appName === value
                        );
                        let newId = selectApp?.appId;
                        let newCurrentStep = { ...currentStep };
                        newCurrentStep.stepInfo.params.params.id = newId;
                        newCurrentStep.stepInfo.params.params.appName = value;
                        newCurrentStep.stepDesc = '清理【' + value + '】';
                        newCurrentStep.stepInfo.desc = newCurrentStep.stepDesc;
                        setAppName(value);
                        setAppId(newId);
                        handleUpdateStep(newCurrentStep);
                    }}
                    addChange={() => showAppAddModal()}
                    settingChange={() => showAppModal()}
                />
            </div>
            <div className={styles.flexCenter}>
                <span className={styles.paramsSelectTitle}>
                    <Switch
                        size="small"
                        checked={isAutoAuthApp}
                        disabled={
                            !isElectron() ||
                            -1 !== ['readonly', 'debug', 'execute'].indexOf(editType)
                        }
                        onChange={(value) => {
                            setIsAutoAuthApp(!isAutoAuthApp);
                            let newCurrentStep = { ...currentStep };
                            if (!newCurrentStep?.stepInfo?.params) {
                                newCurrentStep.stepInfo.params = {};
                            }
                            newCurrentStep.stepInfo.params.params.autoAuthApp = !isAutoAuthApp;
                            handleUpdateStep(newCurrentStep);
                        }}
                    />
                </span>
                <span className={styles.paramsSelectData}>是否自动完成权限授予</span>
            </div>
            <CommonConfig {...props} />
            <RunCaseSettingModal ref={appModalRef} />
            <AppModal
                ref={appAddModalRef}
                curOsType={curOsType}
                onChange={(appId, appName, packageName) => {
                    let newCurrentStep = { ...currentStep };
                    newCurrentStep.stepInfo.params.params.id = appId;
                    newCurrentStep.stepInfo.params.params.appName = appName;
                    newCurrentStep.stepInfo.params.params.packageName = packageName;
                    newCurrentStep.stepInfo.desc = '清理【' + appName + '】';
                    setAppName(appName);
                    setAppId(appId);
                    handleUpdateStep(newCurrentStep);
                }}
            />
        </Spin>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    showModal: state.common.base.showModal,
    appList: state.common.case.appList
}))(ClearApp);
