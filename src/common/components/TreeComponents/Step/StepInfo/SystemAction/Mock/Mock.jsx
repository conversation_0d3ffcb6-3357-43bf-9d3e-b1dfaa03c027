import { useState, useRef, useEffect, useMemo } from 'react';
import { isEmpty } from 'lodash';
import { Divider, Dropdown, message, Popconfirm, Radio, Spin, Tag, Tooltip } from 'antd';
import { FolderOutlined, PullRequestOutlined } from '@ant-design/icons';

import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { getRequestDataDetail } from 'COMMON/api/front_qe_tools/config/request-data';
import NoContent from 'COMMON/components/common/NoContent';
import { CardTitle } from 'COMMON/components/common/Card';
import TreeSelectWithDropdownRender from 'COMMON/components/Select/TreeSelectWithDropdownRender';
import AddModal from 'FEATURES/setting/CommonSetting/RequestManage/LayoutSider/AddModal';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import ImportTemplateModal from './ImportTemplateModal';
import CommonConfig from '../../components/CommonConfig';
import ParamsResponse from '../Params/ParamsResponse';
import ParamsQuery from '../Params/ParamsQuery';
import styles from '../SystemAction.module.less';

function Mock(props) {
    const {
        currentSpace,
        currentStep,
        editType,
        setShowModal,
        handleUpdateStep,
        requestDataList,
        setRequestDataList
    } = props;
    const [loading, setLoading] = useState(false);
    const [source, setSource] = useState(1);
    const [digitalNodeId, setDigitalNodeId] = useState();
    const requestMangeModalRef = useRef();
    const addRequestMangeRef = useRef();
    const importTemplateModalRef = useRef();

    const updateMockStepDetail = (id) => {
        setDigitalNodeId(id);
        getRequestDataDetail({
            digitalNodeId: id
        }).then((dataInfo) => {
            let newCurrentStep = { ...currentStep };
            newCurrentStep.stepInfo.params.params = {
                mockRequest: {
                    matchRequest: {
                        headers: {},
                        protocol: '',
                        hostname: '',
                        port: '',
                        method: '',
                        path: ''
                    },
                    responseDetail: {},
                    verifyDetail: {
                        verifyType: 1,
                        query: {},
                        body: {},
                        response: {}
                    }
                },
                ...(typeof dataInfo?.digitalInfo === 'string'
                    ? JSON.parse(dataInfo?.digitalInfo || '{}')
                    : dataInfo?.digitalInfo),
                digitalNodeId: id
            };
            handleUpdateStep(newCurrentStep);
            setSource(1);
        });
    };

    // 监听当前步骤 stepId 变化
    useEffect(() => {
        let _id = currentStep?.stepInfo.params.params.digitalNodeId;
        if (_id) {
            updateMockStepDetail(_id);
        } else {
            setSource(2);
        }
    }, [currentStep?.stepId, requestDataList]);

    const sourceChange = async (value) => {
        setSource(value);
        let newCurrentStep = { ...currentStep };
        if (value === 1) {
            newCurrentStep.stepInfo.params.params = {
                timestamp: 0,
                mockRequest: {
                    matchRequest: {
                        headers: {},
                        protocol: '',
                        hostname: '',
                        port: '',
                        method: '',
                        path: ''
                    },
                    responseDetail: {}
                }
            };
        }
        if (value === 2) {
            setDigitalNodeId(null);
            delete newCurrentStep.stepInfo.params.params.digitalNodeId;
        }
        handleUpdateStep(newCurrentStep);
    };

    const showRequestMangeModal = (data) => {
        requestMangeModalRef.current?.show({
            key: 'proxy-manage',
            digitalNodeId: data?.digitalNodeId,
            nodeType: data?.nodeType
        });
        setShowModal(true);
    };

    const getIcon = (item) => {
        if (item?.nodeType === 2) {
            return <PullRequestOutlined className={styles.arrowIcon} />;
        }
        // 为空的目录展示 文件夹ICON
        if (item?.nodeType === 1) {
            return <FolderOutlined className={styles.arrowIcon} />;
        }
    };

    let descOptions = useMemo(() => {
        const getDataTree = (data) => {
            if (data?.length === 0) {
                return;
            }
            return (data ?? [])?.map((item) => {
                return {
                    ...item,
                    key: item.digitalNodeId + '-' + item?.nodeType,
                    title: item.nodeName,
                    label: (
                        <>
                            {item?.nodeType === 2 && <Tag>ID: {item?.digitalNodeId}</Tag>}
                            {getIcon(item)}&nbsp;
                            {item.nodeName}
                        </>
                    ),
                    selectable: item?.nodeType === 2,
                    value: item.digitalNodeId,
                    children: getDataTree(item.children)
                };
            });
        };
        return getDataTree(requestDataList);
    }, [requestDataList]);

    return (
        <div>
            <Spin spinning={loading}>
                <CardTitle text="操作参数">
                    &nbsp;&nbsp;
                    {source === 1 && !['execute'].includes(editType) && (
                        <span className={styles.banModify}>注意：模版数据不支持在当前页修改</span>
                    )}
                    {source === 2 && !['readonly', 'debug', 'execute'].includes(editType) && (
                        <>
                            <Tooltip
                                title={
                                    !descOptions || descOptions?.length === 0
                                        ? '暂无数据'
                                        : '注意：快速导入会覆盖当前数据'
                                }
                            >
                                <span
                                    className={styles.import}
                                    onClick={() => {
                                        if (!descOptions) {
                                            message.warning('暂无数据');
                                            return;
                                        }
                                        importTemplateModalRef?.current?.show();
                                    }}
                                >
                                    快速导入
                                </span>
                            </Tooltip>
                            <Divider type="vertical" />
                            <span
                                className={styles.import}
                                onClick={() => {
                                    addRequestMangeRef.current?.show({
                                        digitalInfo: currentStep?.stepInfo?.params?.params
                                    });
                                }}
                            >
                                保存为模版
                            </span>
                        </>
                    )}
                </CardTitle>
                <div className={styles.paramsSelect}>
                    <span className={styles.title}>来源</span>
                    <Radio.Group
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        onChange={() => {
                            if (source === 1) {
                                sourceChange(2);
                            }
                        }}
                        value={source}
                    >
                        <Popconfirm
                            placement="right"
                            title={
                                <>
                                    <span style={{ fontSize: 14, fontWeight: 'bold' }}>
                                        确定要切换吗?
                                    </span>
                                    <br />
                                    「自定义」下数据会清空。如有需要可以先保存为模版。
                                </>
                            }
                            okText="确定"
                            cancelText="取消"
                            destroyTooltipOnHide
                            onConfirm={() => {
                                sourceChange(1);
                            }}
                        >
                            <Radio value={1}>代理请求模版</Radio>
                        </Popconfirm>
                        <Radio value={2}>自定义</Radio>
                    </Radio.Group>
                </div>
                {source === 1 && (
                    <div className={styles.paramsSelect}>
                        <span className={styles.paramsSelectTitle}>请求模版</span>
                        <TreeSelectWithDropdownRender
                            allowClear
                            className={styles.paramsSelectData}
                            editType={editType}
                            placeholder="请选择模版"
                            text="模版选择"
                            disabled={['readonly', 'execute'].includes(editType)}
                            value={digitalNodeId}
                            treeData={descOptions ?? []}
                            onChange={async (value) => {
                                let res = await getRequestDataDetail({
                                    digitalNodeId: value
                                });
                                let newCurrentStep = { ...currentStep };
                                newCurrentStep.stepInfo.params.params = {
                                    mockRequest: {
                                        matchRequest: {
                                            headers: {},
                                            protocol: '',
                                            hostname: '',
                                            port: '',
                                            method: '',
                                            path: ''
                                        },
                                        responseDetail: {},
                                        verifyDetail: {
                                            verifyType: 1,
                                            query: {},
                                            body: {},
                                            response: {}
                                        }
                                    },
                                    ...(typeof res?.digitalInfo === 'string'
                                        ? JSON.parse(res?.digitalInfo || '{}')
                                        : res?.digitalInfo),
                                    digitalNodeId: value
                                };
                                await handleUpdateStep(newCurrentStep);
                            }}
                            settingChange={showRequestMangeModal}
                            editChange={(value) => showRequestMangeModal(value)}
                        />
                    </div>
                )}
                {currentStep?.stepInfo?.params?.params.mockRequest?.matchRequest?.hostname ? (
                    <>
                        <ParamsQuery {...props} editType={source === 1 ? 'readonly' : editType} />
                        <ParamsResponse
                            {...props}
                            editType={source === 1 ? 'readonly' : editType}
                        />
                    </>
                ) : (
                    <NoContent text="暂无有效请求" />
                )}
            </Spin>
            <CommonConfig {...props} />
            <SettingModal ref={requestMangeModalRef} />
            <AddModal ref={addRequestMangeRef} type="step" />
            <ImportTemplateModal
                ref={importTemplateModalRef}
                treeData={descOptions}
                currentStep={currentStep}
                handleUpdateStep={handleUpdateStep}
            />
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    requestDataList: state.common.case.requestDataList,
    showModal: state.common.base.showModal
}))(Mock);
