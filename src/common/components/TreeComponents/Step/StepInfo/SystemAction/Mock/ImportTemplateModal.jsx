import { forwardRef, useImperativeHandle, useState } from 'react';
import { isEmpty } from 'lodash';
import { message, Modal, TreeSelect } from 'antd';
import { getRequestDataDetail } from 'COMMON/api/front_qe_tools/config/request-data';

const ImportTemplateModal = (props, ref) => {
    const { treeData, currentStep, handleUpdateStep } = props;
    const [visible, setVisible] = useState(false);
    const [selectedTemplateId, setSelectedTemplateId] = useState(null);

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(ref, () => ({
        show: () => {
            setVisible(true);
        },
        hide: () => {
            setVisible(false);
        }
    }));

    // 处理快速导入模板选择
    const handleImportTemplateChange = (value) => {
        setSelectedTemplateId(value);
    };

    // 确认导入模板
    const handleImportTemplate = async () => {
        if (!selectedTemplateId) {
            message.warning('请选择模板');
            return;
        }

        let res = await getRequestDataDetail({
            digitalNodeId: selectedTemplateId
        });
        let { digitalInfo } = res;
        digitalInfo = res?.digitalInfo;
        if (isEmpty(digitalInfo)) {
            message.warning('该模版数据为空');
            return;
        }

        let newCurrentStep = { ...currentStep };
        newCurrentStep.stepInfo.params.params = {
            mockRequest: {
                matchRequest: {
                    headers: {},
                    protocol: '',
                    hostname: '',
                    port: '',
                    method: '',
                    path: ''
                },
                responseDetail: {},
                verifyDetail: {
                    verifyType: 1,
                    query: {},
                    body: {},
                    response: {}
                }
            },
            ...digitalInfo
        };
        await handleUpdateStep(newCurrentStep);

        setVisible(false);
    };

    return (
        <Modal
            title="选择导入模板"
            open={visible}
            onOk={handleImportTemplate}
            onCancel={() => setVisible(false)}
            destroyOnClose
            okText="确定"
            cancelText="取消"
        >
            <TreeSelect
                showSearch
                style={{ width: '100%' }}
                value={selectedTemplateId}
                styles={{
                    popup: { root: { maxHeight: 400, overflow: 'auto' } }
                }}
                placeholder="请选择模板"
                allowClear
                treeDefaultExpandAll
                onChange={handleImportTemplateChange}
                treeData={treeData}
                filterTreeNode={(inputValue, treeNode) => {
                    return (
                        treeNode.title.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0 ||
                        (treeNode.value && treeNode.value.toString().indexOf(inputValue) >= 0)
                    );
                }}
                showArrow
                searchPlaceholder="搜索模板..."
            />
        </Modal>
    );
};

export default forwardRef(ImportTemplateModal);
