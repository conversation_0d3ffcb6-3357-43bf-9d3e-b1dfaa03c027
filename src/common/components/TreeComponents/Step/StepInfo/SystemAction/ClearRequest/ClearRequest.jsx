import {Input} from 'antd';
import {CardTitle} from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function ClearRequest(props) {
    return (
        <div>
            <CardTitle text='操作参数' />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>具体操作</span>
                <Input
                    value={'清空请求'}
                    disabled
                    className={styles.paramsSelectData}
                />
            </div>
            <CommonConfig {...props} />
        </div>
    );
};

export default ClearRequest;