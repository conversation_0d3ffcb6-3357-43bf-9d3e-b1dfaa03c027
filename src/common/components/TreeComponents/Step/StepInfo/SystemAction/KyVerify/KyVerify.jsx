import {isEmpty} from 'lodash';
import NoContent from 'COMMON/components/common/NoContent';
import {CardTitle} from 'COMMON/components/common/Card';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import CommonConfig from '../../components/CommonConfig';
import ParamsVerifyResponse from '../Params/ParamsVerifyResponse';
import ParamsBody from '../Params/ParamsBody';
import ParamsQuery from '../Params/ParamsQuery';
import ParamsVerifyQuery from '../Params/ParamsVerifyQuery';
import ParamsVerifyMethod from '../Params/ParamsVerifyMethod';
import styles from '../SystemAction.module.less';

function KvVerify(props) {
    const {currentStep} = props;
    const hasUrl = isEmpty(currentStep?.stepInfo.params.params?.mockRequest?.matchRequest?.hostname);

    return (
        <div>
            <CardTitle text='请求匹配' />
            {
                hasUrl ? <NoContent
                    text="暂未选择请求"
                    className={styles.noContent}
                /> : <>
                    <ParamsQuery {...props} />
                    <CardTitle text='请求校验' />
                    {
                        currentStep?.stepInfo.params.params.mockRequest.verifyDetail.verifyType ?
                            <ParamsVerifyMethod {...props} /> : null
                    }
                    <ParamsVerifyQuery {...props} />
                    <ParamsBody {...props} />
                    <ParamsVerifyResponse {...props} />
                </>
            }
            <CommonConfig {...props} />
        </div>
    );
};


export default connectModel([baseModel], (state) => ({
    currentDevice: state.common.base.currentDevice,
    showModal: state.common.base.showModal,
}))(KvVerify);