import {Input} from 'antd';
import {CardTitle} from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function Connect(props) {
    return (
        <div>
            <CardTitle text='操作参数' />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>网络状态</span>
                <Input
                    value={'连网'}
                    disabled
                    className={styles.paramsSelectData}
                />
            </div>
            <CommonConfig {...props} />
        </div>
    );
};

export default Connect;
