import { useState, useEffect } from 'react';
import classnames from 'classnames';
import { InputNumber, message, Select } from 'antd';
import { CardTitle } from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import { isNumber } from 'COMMON/components/TreeComponents/Step/utils';
import styles from '../SystemAction.module.less';

function Swipe(props) {
    const { currentStep, editType, handleUpdateStep } = props;
    const [direction, setDirection] = useState(1);
    const [times, setTimes] = useState(1);
    const [interval, setInterval] = useState(1);
    const [extendPic, setExtendPic] = useState(false);

    useEffect(() => {
        setDirection(currentStep?.stepInfo.params.params.direction);
        setTimes(currentStep?.stepInfo.params.params.times);
        setInterval(currentStep?.stepInfo.params.params.interval);
        setExtendPic(currentStep?.stepInfo.params.params.extendPic ?? false);
    }, [currentStep]);

    const options = [
        {
            value: 1,
            label: '从下往上'
        },
        {
            value: 2,
            label: '从上往下'
        },
        {
            value: 3,
            label: '从左往右'
        },
        {
            value: 4,
            label: '从右往左'
        }
    ];

    const extendPicOptions = [
        {
            value: false,
            label: '关闭'
        },
        {
            value: true,
            label: '开启'
        }
    ];
    return (
        <div>
            <CardTitle text="操作参数" />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>划动方向</span>
                <Select
                    className={classnames(styles.paramsSelectData, 'inputEditor')}
                    value={direction}
                    placeholder="请选择划动方向"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    options={options}
                    onChange={async (value) => {
                        try {
                            let newCurrentStep = { ...currentStep };
                            newCurrentStep.stepInfo.params.params.direction = value;
                            await handleUpdateStep(newCurrentStep);
                        } catch (err) {}
                    }}
                />
            </div>

            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>划动次数</span>
                <InputNumber
                    className={classnames(styles.paramsSelectData, 'inputEditor')}
                    min={1}
                    addonAfter="次"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    value={times}
                    onChange={(value) => {
                        if (
                            !isElectron() ||
                            -1 !== ['readonly', 'debug', 'execute'].indexOf(editType)
                        ) {
                            return false;
                        }
                        setTimes(value);
                    }}
                    onBlur={async (e) => {
                        try {
                            let value = 1;
                            if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                                value = parseInt(e.target.value.trim(), 10);
                            }
                            let newCurrentStep = { ...currentStep };
                            newCurrentStep.stepInfo.params.params.times = value;
                            await handleUpdateStep(newCurrentStep);
                        } catch (err) {}
                    }}
                />
            </div>
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>划动间隔</span>
                <InputNumber
                    className={classnames(styles.paramsSelectData, 'inputEditor')}
                    min={1}
                    addonAfter="秒"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    value={interval}
                    onChange={(value) => {
                        if (
                            !isElectron() ||
                            -1 !== ['readonly', 'debug', 'execute'].indexOf(editType)
                        ) {
                            return false;
                        }
                        setInterval(value);
                    }}
                    onBlur={async (e) => {
                        try {
                            let value = 0;
                            if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                                value = parseInt(e.target.value.trim(), 10);
                            }
                            let newCurrentStep = { ...currentStep };
                            newCurrentStep.stepInfo.params.params.interval = value;
                            await handleUpdateStep(newCurrentStep);
                        } catch (err) {}
                    }}
                />
            </div>
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>拼接截图</span>
                <Select
                    className={classnames(styles.paramsSelectData, 'inputEditor')}
                    value={extendPic}
                    placeholder="请选择是否开启拼接截图"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    options={extendPicOptions}
                    onChange={async (value) => {
                        try {
                            let newCurrentStep = { ...currentStep };
                            newCurrentStep.stepInfo.params.params.extendPic = value;
                            await handleUpdateStep(newCurrentStep);
                        } catch (err) {
                            console.log(err);
                        }
                    }}
                />
            </div>
            <CommonConfig {...props} />
        </div>
    );
}

export default Swipe;
