import { useState, useEffect, useRef } from 'react';
import { Tooltip, Select, message, Input, Spin } from 'antd';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { CardTitle } from 'COMMON/components/common/Card';
import SelectWithDropdownRender from 'COMMON/components/Select/SelectWithDropdownRender';
import RunCaseSettingModal from 'FEATURES/front_qe_tools/case/edit/EditPage/Modal/RunCaseSettingModal/RunCaseSettingModal';
import AppModal from 'COMMON/components/TreeComponents/components/AppModal/AppModal';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function LaunchApp(props) {
    const { currentStep, appList, curOsType, editType, setShowModal, handleUpdateStep } = props;
    const [appId, setAppId] = useState(undefined);
    const [appName, setAppName] = useState(undefined);
    const [activity, setActivity] = useState(undefined);
    const [isColdStart, setIsColdStart] = useState(true);
    const appModalRef = useRef();
    const appAddModalRef = useRef();

    // 初始化启动 App 步骤
    const initLaunchAppStep = (step, app) => {
        let newCurrentStep = { ...step };
        newCurrentStep.stepInfo.params.params.id = app?.appId;
        newCurrentStep.stepInfo.params.params.appName = app?.appName;
        newCurrentStep.stepInfo.params.params.packageName = app?.packageName;
        let desc = newCurrentStep.stepInfo.params.params.isColdStart
            ? '冷启动【' + app?.appName + '】'
            : '热启动【' + app?.appName + '】';
        newCurrentStep.stepInfo.desc = desc;
        newCurrentStep.stepDesc = desc;
        setAppId(app?.appId);
        setAppName(app?.appName);
        setIsColdStart(newCurrentStep?.stepInfo.params.params?.isColdStart);
        setActivity(null);
        handleUpdateStep(newCurrentStep);
    };

    // 监听当前步骤 stepId 变化
    useEffect(() => {
        if (currentStep?.stepInfo.params.params.id) {
            setAppId(currentStep?.stepInfo?.params?.params?.id);
            setAppName(currentStep?.stepInfo?.params?.params?.appName);
            setIsColdStart(currentStep?.stepInfo?.params?.params?.isColdStart);
            setActivity(
                curOsType === 1 ? currentStep?.stepInfo?.params?.params?.activity : undefined
            );
        } else {
            let _app = appList?.[curOsType]?.[0];
            if (['template', 'edit'].includes(editType)) {
                if (!currentStep?.stepInfo.params.params.id && _app) {
                    initLaunchAppStep(currentStep, _app);
                }
            }
        }
    }, [currentStep?.stepId, curOsType, appList]);

    let appNameOptions = [];
    appNameOptions = appList?.[curOsType]?.map((item) => {
        return {
            label: item.appName,
            value: item.appName
        };
    });

    const showAppModal = () => {
        setShowModal(true);
        appModalRef?.current.show({ key: 'app-config' });
    };

    const showAppAddModal = () => {
        setShowModal(true);
        appAddModalRef?.current?.show(curOsType);
    };

    const coldStartOptions = [
        {
            label: '冷启动',
            value: '1'
        },
        {
            label: '热启动',
            value: '2'
        }
    ];
    return (
        <>
            <CardTitle text="操作参数" />
            <div className={styles.paramsSelect}>
                <Tooltip title={`ID: ${appId}`} placement="left">
                    <span className={styles.paramsSelectTitle}>APP 名称</span>
                </Tooltip>
                <SelectWithDropdownRender
                    className={styles.paramsSelectData}
                    placeholder="请选择 APP 名称"
                    editType={editType}
                    text="APP 选择"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    showSearch
                    showSearchIcon={false}
                    filterOption={(input, option) => option.label.includes(input)}
                    value={appName}
                    options={appNameOptions}
                    onChange={(value) => {
                        const selectApp = appList?.[curOsType]?.find(
                            (item) => item.appName === value
                        );
                        let newId = selectApp?.appId;
                        let newCurrentStep = { ...currentStep };
                        newCurrentStep.stepInfo.params.params.id = newId;
                        newCurrentStep.stepInfo.params.params.appName = value;
                        newCurrentStep.stepDesc = newCurrentStep.stepInfo.params.params.isColdStart
                            ? '冷启动【' + value + '】'
                            : '热启动【' + value + '】';
                        newCurrentStep.stepInfo.desc = newCurrentStep.stepDesc;
                        handleUpdateStep(newCurrentStep);
                        setAppName(value);
                        setAppId(newId);
                    }}
                    addChange={() => showAppAddModal()}
                    settingChange={() => showAppModal()}
                />
            </div>
            {curOsType === 1 && (
                <div className={styles.paramsSelect}>
                    <span className={styles.paramsSelectTitle}>Activity</span>
                    <Input
                        className={styles.paramsSelectData}
                        placeholder="非必填"
                        value={activity}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        onChange={(e) => {
                            let value = e.target.value;
                            setActivity(value);
                        }}
                        onBlur={(e) => {
                            let newCurrentStep = { ...currentStep };
                            newCurrentStep.stepInfo.params.params.activity = activity;
                            handleUpdateStep(newCurrentStep);
                        }}
                    />
                </div>
            )}
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>启动方式</span>
                <Select
                    className={styles.paramsSelectData}
                    value={isColdStart ? '1' : '2'}
                    placeholder="请选择启动方式"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    options={coldStartOptions}
                    onChange={(value) => {
                        let newCurrentStep = { ...currentStep };
                        newCurrentStep.stepInfo.desc =
                            '1' === value
                                ? '冷启动【' + newCurrentStep.stepInfo.params.params.appName + '】'
                                : '热启动【' + newCurrentStep.stepInfo.params.params.appName + '】';
                        newCurrentStep.stepInfo.params.params.isColdStart = '1' === value;
                        newCurrentStep.stepDesc = newCurrentStep.stepInfo.desc;
                        setIsColdStart('1' === value);
                        handleUpdateStep(newCurrentStep);
                    }}
                />
            </div>
            <CommonConfig {...props} />
            <RunCaseSettingModal ref={appModalRef} />
            <AppModal
                ref={appAddModalRef}
                curOsType={curOsType}
                onChange={(appId, appName, packageName) => {
                    let newCurrentStep = { ...currentStep };
                    newCurrentStep.stepInfo.params.params.id = appId;
                    newCurrentStep.stepInfo.params.params.appName = appName;
                    newCurrentStep.stepInfo.params.params.packageName = packageName;
                    newCurrentStep.stepInfo.desc =
                        '1' === newCurrentStep.stepInfo.params.params.isColdStart
                            ? '冷启动【' + appName + '】'
                            : '热启动【' + appName + '】';
                    setAppId(appId);
                    setAppName(appName);
                    handleUpdateStep(newCurrentStep);
                }}
            />
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    showModal: state.common.base.showModal,
    appList: state.common.case.appList
}))(LaunchApp);
