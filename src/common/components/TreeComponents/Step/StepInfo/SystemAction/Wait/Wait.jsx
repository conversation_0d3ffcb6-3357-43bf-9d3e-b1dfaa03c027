import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {InputNumber, message} from 'antd';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import {CardTitle} from 'COMMON/components/common/Card';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function Wait(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [seconds, setSeconds] = useState(1);

    useEffect(() => {
        setSeconds(currentStep?.stepInfo.params.params.seconds);
    }, [currentStep]);

    return (
        <div>
            <CardTitle text='操作参数' />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>设置时长</span>
                <InputNumber
                    className={classnames(styles.paramsSelectData, 'inputEditor')}
                    min={1}
                    addonAfter='秒'
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    value={seconds}
                    onChange={(value) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        setSeconds(value);
                    }}
                    onBlur={async (e) => {
                        let value = 0;
                        if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                            value = parseInt(e.target.value.trim(), 10);
                        }
                        let newCurrentStep = {...currentStep};
                        newCurrentStep.stepInfo.params.params.seconds = value;
                        await handleUpdateStep(newCurrentStep);
                    }}
                />
            </div>
            <CommonConfig {...props} />
        </div>
    );
};

export default Wait;
