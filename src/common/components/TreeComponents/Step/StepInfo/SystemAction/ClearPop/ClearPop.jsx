import { useState, useEffect, useRef, useMemo } from 'react';
import { Spin } from 'antd';
import classnames from 'classnames';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { getSnippetList } from 'COMMON/api/front_qe_tools/config';
import { CardTitle } from 'COMMON/components/common/Card';
import SelectWithCategory from 'COMMON/components/Select/SelectWithCategory';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import CommonConfig from '../../components/CommonConfig';
import styles from '../SystemAction.module.less';

function ClearPop(props) {
    const {
        currentStep,
        currentSpace,
        snippetList,
        setSnippetList,
        setShowModal,
        curOsType,
        editType,
        handleUpdateStep
    } = props;
    const [loading, setLoading] = useState(false);
    const [popList, setPopList] = useState(null);
    const templateModalRef = useRef();

    useEffect(() => {
        setPopList(currentStep?.stepInfo.params.params.popList);
    }, [currentStep?.stepId]);

    const [selectedCategory, setSelectedCategory] = useState(null);

    const showTemplateModal = () => {
        setShowModal(true);
        templateModalRef?.current.show({ key: 'step-template' });
    };

    return (
        <div>
            <Spin spinning={loading}>
                <CardTitle text="操作参数" />
                <div className={styles.paramsSelect}>
                    <span className={styles.paramsSelectTitle}>点除模版</span>
                    <SelectWithCategory
                        mode="multiple"
                        placeholder={'选择模版'}
                        editType={editType}
                        text={'弹窗点除模版选择'}
                        showSearch
                        showSearchIcon={false}
                        className={classnames('inputEditor', styles.paramsSelectData)}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        value={popList}
                        filterOption={(input, option) => option.label.includes(input)}
                        dataSource={snippetList?.[curOsType] ?? []}
                        categoryKey="tabName"
                        labelKey="templateName"
                        valueKey="templateId"
                        selectedCategory={selectedCategory}
                        onCategorySelect={setSelectedCategory}
                        onChange={(value) => {
                            let newCurrentStep = { ...currentStep };
                            newCurrentStep.stepInfo.params.params.popList = value;
                            setPopList(value);
                            handleUpdateStep(newCurrentStep);
                        }}
                        addChange={() => showTemplateModal()}
                        settingChange={() => showTemplateModal()}
                    />
                </div>
            </Spin>
            <CommonConfig {...props} />
            <SettingModal ref={templateModalRef} />
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    snippetList: state.common.case.snippetList,
    showModal: state.common.base.showModal
}))(ClearPop);
