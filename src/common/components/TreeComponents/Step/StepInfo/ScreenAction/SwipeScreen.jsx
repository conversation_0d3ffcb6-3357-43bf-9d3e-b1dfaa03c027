import {useState, useEffect, useRef} from 'react';
import {message, Spin} from 'antd';
import {connectModel} from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import {getCanvasPos, getRealXPos, getRealYPos} from './utils';
import styles from './ScreenAction.module.less';

function SwipeScreen(props) {
    const {currentStep, editType, curOsType, setCurrentStep,
        setLoading, imgSrc, isCanvas, handleUpdateStep} = props;
    const [innerHeight, setInnerHeight] = useState(window.innerHeight);
    const [innerWidth, setInnerWidth] = useState(window.innerWidth);
    const [canvasPos, setCanvasPos] = useState({x: 0, y: 0, targetX: 0, targetY: 0});
    const canvas = useRef();

    // 页面大小变动后，图片resize
    useEffect(() => {
        window.onresize = () => {
            setInnerHeight(window.innerHeight);
            setInnerWidth(window.innerWidth);
        };
    }, []);

    useEffect(() => {
        setLoading(true);
        drawImage();
    }, [imgSrc]);

    useEffect(() => {
        drawImage();
    }, [canvasPos, innerHeight, innerWidth]);

    const drawImage = () => {
        const context = canvas.current.getContext('2d');
        const img = new Image();
        img.src = imgSrc;
        img.onload = () => {
            context.drawImage(img, 0, 0, imgWidth * ratio, imgHeight * ratio);
            draw(context);
            setLoading(false);
        };
    };

    // 单击事件监听
    useEffect(() => {
        document.addEventListener('mousedown', downAction);
        return () => document.removeEventListener('mousedown', downAction);
    }, [currentStep, isCanvas, canvasPos]);

    // 单击事件监听
    useEffect(() => {
        document.addEventListener('mouseup', upAction);
        return () => document.removeEventListener('mouseup', upAction);
    }, [currentStep, isCanvas, canvasPos]);

    const downAction = async (e) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            return false;
        }
        if (e.target.className === 'swipeImage') {
            if (!isCanvas) {
                return false;
            }
            let {rotation} = currentStep.stepInfo.params.deviceInfo.screenSize;
            let ratio = window.devicePixelRatio || 1;
            setCanvasPos({
                x: (e.offsetX * ratio) / e.target.width,
                y: (e.offsetY * ratio) / e.target.height,
                targetX: 0,
                targetY: 0
            });
            currentStep.stepInfo.params.actionInfo.params.startX =
                getRealXPos(rotation, (e.offsetX * ratio) / e.target.width, (e.offsetY * ratio) / e.target.height);
            currentStep.stepInfo.params.actionInfo.params.startY =
                getRealYPos(rotation, (e.offsetX * ratio) / e.target.width, (e.offsetY * ratio) / e.target.height);
            currentStep.stepInfo.params.actionInfo.params.targetX = getRealXPos(rotation, 0, 0);
            currentStep.stepInfo.params.actionInfo.params.targetY = getRealYPos(rotation, 0, 0);

            await handleUpdateStep({...currentStep});
        }
    };
    const upAction = async (e) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            return false;
        }
        if (e.target.className === 'swipeImage') {
            if (!isCanvas) {
                return false;
            }
            let {x, y} = canvasPos;
            let {rotation} = currentStep.stepInfo.params.deviceInfo.screenSize;
            let ratio = window.devicePixelRatio || 1;
            setCanvasPos({
                ...canvasPos,
                targetX: (e.offsetX * ratio) / e.target.width,
                targetY: (e.offsetY * ratio) / e.target.height,
            });
            currentStep.stepInfo.params.actionInfo.params.startX = getRealXPos(rotation, x, y, curOsType);
            currentStep.stepInfo.params.actionInfo.params.startY = getRealYPos(rotation, x, y, curOsType);
            currentStep.stepInfo.params.actionInfo.params.targetX =
                getRealXPos(
                    rotation,
                    (e.offsetX * ratio) / e.target.width,
                    (e.offsetY * ratio) / e.target.height,
                    curOsType)
                ;
            currentStep.stepInfo.params.actionInfo.params.targetY =
                getRealYPos(
                    rotation, (e.offsetX * ratio) / e.target.width,
                    (e.offsetY * ratio) / e.target.height,
                    curOsType
                );
            await handleUpdateStep({...currentStep});
        }
    };

    let ratio = window.devicePixelRatio || 1;
    let screenWidth = currentStep.stepInfo.params.deviceInfo.screenSize.width;
    let screenHeight = currentStep.stepInfo.params.deviceInfo.screenSize.height;
    let imageScale = (innerHeight - 400) / screenHeight;
    let imgHeight = innerHeight - 400;
    let imgWidth = imageScale * screenWidth;

    let width_rotation = currentStep.stepInfo.params.deviceInfo.screenSize.width;
    let height_rotation = currentStep.stepInfo.params.deviceInfo.screenSize.height;
    if (-1 !== Object.keys(currentStep.stepInfo.params.deviceInfo.screenSize).indexOf('rotation')) {
        // iOS 端需要旋转 宽/高
        if (curOsType === 2 && -1 !== [90, 270]
            .indexOf(currentStep.stepInfo.params.deviceInfo.screenSize.rotation)) {
            let _width = width_rotation;
            width_rotation = height_rotation;
            height_rotation = _width;
            screenWidth = width_rotation;
            screenHeight = height_rotation;
        }
        if (-1 !== [90, 270]
            .indexOf(currentStep.stepInfo.params.deviceInfo.screenSize.rotation)) {
            imageScale = (((innerWidth - 20) * 0.6 - 50) * 10) / 24 / screenWidth;
            imgWidth = (((innerWidth - 20) * 0.6 - 50) * 10) / 24;
            imgHeight = imageScale * screenHeight;
        }
    }
    const drawArrow = (ctx, fromX, fromY, toX, toY, theta, headlen, width, color) => {
        let angle = Math.atan2(fromY - toY, fromX - toX) * 180 / Math.PI;
        let angle1 = (angle + theta) * Math.PI / 180;
        let angle2 = (angle - theta) * Math.PI / 180;
        let topX = headlen * Math.cos(angle1);
        let topY = headlen * Math.sin(angle1);
        let botX = headlen * Math.cos(angle2);
        let botY = headlen * Math.sin(angle2);

        ctx.save();
        ctx.beginPath();

        let arrowX = toX + topX;
        let arrowY = toY + topY;
        ctx.moveTo(arrowX, arrowY);
        ctx.lineTo(toX, toY);
        arrowX = toX + botX;
        arrowY = toY + botY;
        ctx.lineTo(arrowX, arrowY);
        ctx.strokeStyle = color;
        ctx.lineWidth = width;
        ctx.stroke();
        ctx.restore();
    };

    const draw = (context) => {
        let {x, y, targetX, targetY} = getCanvasPos(currentStep, curOsType, 'swipe');
        // 绘制滑动线段
        if (-1 !== Object.keys(currentStep.stepInfo.params.deviceInfo.screenSize).indexOf('rotation')) {
            const {rotation, width, height} = currentStep.stepInfo.params.deviceInfo.screenSize;
            if ([90, 270].includes(rotation)) {
                // iOS 端需要旋转 宽/高
                if (curOsType === 2 && width > height) {
                    let _width = imgWidth;
                    imgWidth = imgHeight;
                    imgHeight = _width;
                }
                // 双端旋转270度，镜面处理
                x = curOsType === 2 ? 1 - x : x;
                y = curOsType === 2 ? 1 - y : y;
                targetX = curOsType === 2 ? 1 - targetX : targetX;
                targetY = curOsType === 2 ? 1 - targetY : targetY;
            }
        }
        if (0 !== x && 0 !== y && 0 !== targetX && 0 !== targetY) {
            x *= imgWidth * ratio;
            y *= imgHeight * ratio;
            targetX *= imgWidth * ratio;
            targetY *= imgHeight * ratio;
            context.lineWidth = 4;
            context.strokeStyle = 'red';
            context.beginPath();
            context.moveTo(x, y);
            context.lineTo(targetX, targetY);
            context.stroke();
            // 画箭头
            drawArrow(context, x, y, targetX, targetY, 30, 15, 4, 'red');
            context.closePath();
        }
    };

    return (
        <div>
            <canvas
                ref={canvas}
                width={imgWidth * ratio}
                height={imgHeight * ratio}
                style={{
                    width: imgWidth,
                    height: imgHeight,
                    border: '1px solid #eee'
                }}
                className={!['readonly', 'debug', 'execute'].includes(editType) ? 'swipeImage' : ''}
            />
        </div>
    );
};

export default SwipeScreen;
