export function getRealXPos(rotation, x, y, osType = 2, type = 'tap') {
    if (undefined === rotation || -1 === [90, 270].indexOf(rotation)) {
        return x;
    }
    if ([270].includes(rotation)) {
        return osType === 2 ? 1 - y : x;
    }
    if ([90].includes(rotation)) {
        return osType === 2 ? y : x;
    }
};

export function getRealYPos(rotation, x, y, osType = 2, type = 'tap') {
    if (undefined === rotation || -1 === [90, 270].indexOf(rotation)) {
        return y;
    }
    if ([270].includes(rotation)) {
        return osType === 2 ? x : y;
    }
    if ([90].includes(rotation)) {
        return osType === 2 ? 1 - x : y;
    }
};

export function getCanvasPos(step, osType, type = 'tap') {
    let canvasPos = {
        x: 0,
        y: 0,
        targetX: 0,
        targetY: 0,
    };
    if (4 === step.stepInfo.type) {
        let {rotation} = step.stepInfo.params.deviceInfo.screenSize;
        let {x, y, startX, startY, targetX, targetY} = step.stepInfo.params.actionInfo.params;
        if (x) {
            canvasPos.x = getRealXPos(rotation, x, y, osType, type);
        }
        if (y) {
            canvasPos.y = getRealYPos(rotation, x, y, osType, type);
        }
        if (startX) {
            canvasPos.x = getRealXPos(rotation, startX, startY, osType, type);
        }
        if (startY) {
            canvasPos.y = getRealYPos(rotation, startX, startY, osType, type);
        }
        if (targetX) {
            canvasPos.targetX = getRealXPos(rotation, targetX, targetY, osType, type);
        }
        if (targetY) {
            canvasPos.targetY = getRealYPos(rotation, targetX, targetY, osType, type);
        }
    }
    return canvasPos;
};