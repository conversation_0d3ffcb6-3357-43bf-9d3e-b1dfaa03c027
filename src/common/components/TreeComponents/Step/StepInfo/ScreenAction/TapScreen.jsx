import {useState, useEffect, useRef} from 'react';
import {message} from 'antd';
import {getCanvasPos, getRealXPos, getRealYPos} from './utils';

function TapScreen(props) {
    const {imgSrc, currentStep, editType, handleUpdateStep, curOsType, setLoading,
        currentNode, templateId, isCanvas, handleUpdateNode} = props;
    const [innerHeight, setInnerHeight] = useState(window.innerHeight);
    const [innerWidth, setInnerWidth] = useState(window.innerWidth);
    const [canvasPos, setCanvasPos] = useState({x: 0, y: 0, targetX: 0, targetY: 0});
    const canvas = useRef();

    // 页面大小变动后，图片resize
    useEffect(() => {
        window.onresize = () => {
            setInnerHeight(window.innerHeight);
            setInnerWidth(window.innerWidth);
        };
    }, []);

    useEffect(() => {
        setLoading(true);
        drawImage();
    }, [imgSrc]);

    useEffect(() => {
        drawImage();
    }, [canvasPos, innerHeight, innerWidth]);

    const drawImage = () => {
        const context = canvas.current.getContext('2d');
        const img = new Image();
        img.src = imgSrc;
        img.onload = () => {
            context.drawImage(img, 0, 0, imgWidth * ratio, imgHeight * ratio);
            draw(context);
            setLoading(false);
        };
    };

    // 单击事件监听
    useEffect(() => {
        document.addEventListener('click', clickAction);
        return () => document.removeEventListener('click', clickAction);
    }, [currentStep, isCanvas, canvasPos]);

    const clickAction = async (e) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            return false;
        }
        if (e.target.className === 'tapImage') {
            if (!isCanvas) {
                return false;
            }
            let {rotation} = currentStep.stepInfo.params.deviceInfo.screenSize;
            let ratio = window.devicePixelRatio || 1;
            setCanvasPos({
                ...canvasPos,
                x: (e.offsetX * ratio) / e.target.width,
                y: (e.offsetY * ratio) / e.target.height,
            });
            currentStep.stepInfo.params.actionInfo.params.x =
                getRealXPos(rotation, (e.offsetX * ratio) / e.target.width,
                    (e.offsetY * ratio) / e.target.height, curOsType);
            currentStep.stepInfo.params.actionInfo.params.y =
                getRealYPos(rotation, (e.offsetX * ratio) / e.target.width,
                    (e.offsetY * ratio) / e.target.height, curOsType);
            await handleUpdateStep({...currentStep});
        }
    };

    let ratio = window.devicePixelRatio || 1;
    let screenWidth = currentStep.stepInfo.params.deviceInfo.screenSize.width;
    let screenHeight = currentStep.stepInfo.params.deviceInfo.screenSize.height;
    let imageScale = (innerHeight - 400) / screenHeight;
    let imgHeight = innerHeight - 400;
    let imgWidth = imageScale * screenWidth;

    let width_rotation = currentStep.stepInfo.params.deviceInfo.screenSize.width;
    let height_rotation = currentStep.stepInfo.params.deviceInfo.screenSize.height;
    if (-1 !== Object.keys(currentStep.stepInfo.params.deviceInfo.screenSize).indexOf('rotation')) {
        // iOS 端需要旋转 宽/高
        if (curOsType === 2 &&
            -1 !== [90, 270]
                .indexOf(currentStep.stepInfo.params.deviceInfo.screenSize.rotation)) {
            let _width = width_rotation;
            width_rotation = height_rotation;
            height_rotation = _width;
            screenWidth = width_rotation;
            screenHeight = height_rotation;
        }
        if (-1 !== [90, 270]
            .indexOf(currentStep.stepInfo.params.deviceInfo.screenSize.rotation)) {
            imageScale = (((innerWidth - 20) * 0.6 - 50) * 10) / 24 / screenWidth;
            imgWidth = (((innerWidth - 20) * 0.6 - 50) * 10) / 24;
            imgHeight = imageScale * screenHeight;
        }
    }
    const draw = (context) => {
        // 绘制点击位置的十字
        let {x, y} = getCanvasPos(currentStep, curOsType);
        if (-1 !== Object.keys(currentStep.stepInfo.params.deviceInfo.screenSize).indexOf('rotation')) {
            const {rotation, width, height} = currentStep.stepInfo.params.deviceInfo.screenSize;
            if ([90, 270].includes(rotation)) {
                // iOS 端需要旋转 宽/高
                if (curOsType === 2 && width > height) {
                    let _width = imgWidth;
                    imgWidth = imgHeight;
                    imgHeight = _width;
                }
                // 双端旋转90 270度，镜面处理
                x = curOsType === 2 ? 1 - x : x;
                y = curOsType === 2 ? 1 - y : y;
            }
        }
        if (x !== 0 || y !== 0) {
            x *= imgWidth * ratio;
            y *= imgHeight * ratio;
            let ratioWidth = imgWidth * ratio;
            let ratioHeight = imgHeight * ratio;
            context.lineWidth = 2;
            context.strokeStyle = 'red';
            context.beginPath();
            context.moveTo(x, y);
            context.lineTo(ratioWidth, y);
            context.stroke();
            context.moveTo(x, y);
            context.lineTo(0, y);
            context.stroke();
            context.moveTo(x, y);
            context.lineTo(x, ratioHeight);
            context.stroke();
            context.moveTo(x, y);
            context.lineTo(x, 0);
            context.stroke();
            context.closePath();
        }
    };
    return (
        <div>
            <canvas
                ref={canvas}
                width={imgWidth * ratio}
                height={imgHeight * ratio}
                style={{
                    width: imgWidth,
                    height: imgHeight,
                    border: '1px solid #eee'
                }}
                className={!['readonly', 'debug', 'execute'].includes(editType) ? 'tapImage' : ''}
            />
        </div>
    );
};

export default TapScreen;
