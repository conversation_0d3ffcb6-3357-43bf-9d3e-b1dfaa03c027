import {Tooltip, message} from 'antd';
import {
    EditOutlined, CameraOutlined
} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import electron from 'COMMON/utils/electron';
import commonModel from 'COMMON/models/commonModel';
import {base64ImageUpload} from 'COMMON/utils/utils';
import {getName} from 'COMMON/components/TreeComponents/Step/utils';
import {createScreenStepInitConfig} from 'COMMON/components/TreeComponents/Step/StepInit/utils';
import styles from '../StepInfo.module.less';

function StepOperator(props) {
    const {currentStep, setRecording,
        curOsType, handleUpdateStep, editType,
        isCanvas, setIsCanvas,
        deviceList, currentDevice
    } = props;
    const [messageApi, contextHolder] = message.useMessage();

    return (
        <div className={styles.stepOperator}>
            {contextHolder}
            <span className={styles.stepIndex}>
                {getName(currentStep.stepInfo.type, currentStep.stepInfo.params)}
            </span>
            {!['readonly', 'debug', 'execute'].includes(editType) ?
                <>
                    <Tooltip title={isCanvas ? '结束绘制' : '开启绘制'} placement='left'>
                        <EditOutlined
                            className={styles.operatorDetailIcon}
                            style={{
                                color: isCanvas ? '#3376cd' : '#959292'
                            }}
                            onClick={(e) => {
                                setIsCanvas(!isCanvas);
                            }}
                        />
                    </Tooltip>
                    {
                        isElectron() && !['readonly', 'debug', 'execute'].includes(editType) &&
                        <Tooltip title="重新截屏">
                            <CameraOutlined
                                className={styles.operatorDetailIcon}
                                onClick={async (e) => {
                                    try {
                                        // 确定有设备连接
                                        let gotDevice = false;
                                        for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                                            if (device.deviceId === currentDevice?.deviceId) {
                                                gotDevice = true;
                                                if (![2].includes(device?.status)) {
                                                    message.error('请确保设备状态正常');
                                                    return false;
                                                }
                                                break;
                                            }
                                        }
                                        if (!gotDevice) {
                                            message.error('请确保有设备连接');
                                            return false;
                                        }
                                        setRecording(true);
                                        let {
                                            screenshot, screenSize
                                        } = await electron.send('device.record.screenshot', {
                                            deviceType: curOsType, deviceId: currentDevice?.deviceId
                                        });
                                        base64ImageUpload(screenshot).then(async res => {
                                            let bosUrl = res?.url;
                                            let {step} = createScreenStepInitConfig({
                                                type: currentStep?.stepInfo?.params?.actionInfo?.type
                                            });
                                            step.common = currentStep?.stepInfo?.common;
                                            for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                                                if (device.deviceId === currentDevice?.deviceId) {
                                                    step.params.deviceInfo = {
                                                        type: 2 === curOsType ? 'ios' : 'android',
                                                        screenSize: {
                                                            width: screenSize.width,
                                                            height: screenSize.height,
                                                            scale: screenSize.scale,
                                                            rotation: screenSize?.rotation || 0,
                                                        },
                                                        screenshot: bosUrl,
                                                    };
                                                    break;
                                                }
                                            }
                                            await handleUpdateStep({
                                                ...currentStep,
                                                stepInfo: step
                                            });
                                        });
                                    } catch (err) {
                                        message.error(err.message ? err.message : err);
                                    } finally {
                                        setRecording(false);
                                    }
                                }}
                            />
                        </Tooltip>
                    }
                </> : null}
        </div>
    );
};

export default connectModel([commonModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
}))(StepOperator);
