import { useState, useEffect } from 'react';
import classnames from 'classnames';
import { Spin, message } from 'antd';
import { getBosToken } from 'COMMON/api/base/common';
import TapScreen from './TapScreen';
import SwipeScreen from './SwipeScreen';
import StepOperator from './StepOperator';
import styles from './ScreenAction.module.less';

function ScreenAction(props) {
    const { currentStep, editType, descExtra } = props;
    const [isCanvas, setIsCanvas] = useState(false);
    const [loading, setLoading] = useState(true);
    const [imgSrc, setImgSrc] = useState('');

    const handelIsCanvas = (flag) => {
        setIsCanvas(flag);
    };

    useEffect(() => {
        let newImg = currentStep?.stepInfo.params.deviceInfo?.screenshot;
        if (newImg?.startsWith('http') && -1 !== newImg.indexOf('https://hydra.bj.bcebos.com')) {
            getBosToken({ bosLink: newImg }).then((res) => {
                if (!res?.token) {
                    message.error('身份校验失败，获取图片失败');
                }
                newImg += '?authorization=' + res.token;
                setImgSrc(newImg);
            });
        } else {
            setImgSrc(newImg);
        }
    }, [currentStep?.stepInfo.params.deviceInfo?.screenshot]);

    return (
        <div className={styles.action}>
            <StepOperator {...props} isCanvas={isCanvas} setIsCanvas={handelIsCanvas} />
            {descExtra}
            <div className={classnames(styles.screenAction)}>
                <Spin spinning={loading}>
                    {'tap' === currentStep.stepInfo.params.actionInfo.type ? (
                        <TapScreen
                            {...props}
                            imgSrc={imgSrc}
                            isCanvas={isCanvas}
                            setLoading={setLoading}
                        />
                    ) : (
                        <SwipeScreen
                            {...props}
                            imgSrc={imgSrc}
                            isCanvas={isCanvas}
                            setLoading={setLoading}
                        />
                    )}
                </Spin>
            </div>
        </div>
    );
}

export default ScreenAction;
