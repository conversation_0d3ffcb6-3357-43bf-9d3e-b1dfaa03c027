@import "RESOURCES/css/common.less";

.action {
    width: calc(100% -30px);
    margin: 15px 25px 15px 15px;
}

.noContent {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.desc {
    padding: 5px 0 10px 0;

    .descContent {
        float: left;
    }

    :global {
        .ant-input {
            padding: 0 !important;
            color: #777 !important;
            font-size: 12px !important;
        }
    }
}

.stepDetail {
    position: relative;
    height: calc(100vh - 100px);
    min-width: 350px;
    overflow: scroll;
}

.startCanvas {
    color: #3376cd;
}

.stepDetailInfo {
    height: 100%;
    // overflow: scroll;
}

.stepIndex {
    margin-right: 3px;
    font-size: 15px;
    color: #959292;
    font-weight: bold;
}

.operatorDetail {
    float: right;
    font-size: 16px;
    color: #959292;

    &:hover {
        color: #3376cd;
    }
}

.operatorDetailIcon {
    font-size: 16px;
    margin: 3px 6px;
    color: #959292;
}

.operatorIcon:hover {
    color: #3376cd;
}

.operatorCut {
    color: #eee;
    margin: 0 2px;
}

.operatorItem {
    cursor: pointer;
}

.delIcon {
    color: red;
}

.paramsSelect {
    :global {
        .ant-select-selection-item {
            font-size: 14px !important;
        }
    }
}

.paramsSmallSelect {
    :global {
        .ant-select-selection-item {
            font-size: 12px !important;
        }

    }
}


.stepOperator {
    border-bottom: none;
    z-index: 99;

    :global {

        .ant-select-selection-item,
        .ant-select,
        .custom-default-select,
        .custom-dark-select {
            color: #959292 !important;
            font-size: 13px !important;
        }

        .ant-select-selector,
        .custom-default-select-selector,
        .custom-dark-select-selector {
            padding: 0 5px 0 0 !important;
        }

        .ant-select-single,
        .custom-default-select-single,
        .custom-dark-select-single {
            padding: 0 !important;
        }
    }
}

.switchBtn {
    display: block;
    height: 30px;
    line-height: 30px;
    padding: 0 15px;
    color: rgb(149, 146, 146);
    text-align: center;
    cursor: pointer;
    font-size: 12px;
    background-color: var(--background-color);
    z-index: 999;
    box-shadow: 0 0 3px 2px rgba(157, 157, 157, 0.12);
    border-radius: 8px;
}