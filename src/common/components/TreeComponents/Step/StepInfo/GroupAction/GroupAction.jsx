import {useState, useRef, useEffect} from 'react';
import {Input, Tabs, Checkbox} from 'antd';
import {ExclamationCircleOutlined} from '@ant-design/icons';
import NoContent from 'COMMON/components/common/NoContent';
import StepListWithCommon from 'COMMON/components/TreeComponents/StepDetail/StepList/StepListWithCommon';
import AddStepGroupAttr from '../../StepItem/Operation/AddStepGroupAttr';
import StepListOverview from '../../StepItem/Params/StepListOverview';
import styles from './GroupAction.module.less';

const {TextArea} = Input;

function GroupAction(props) {
    const {currentStep, editType, stepResult,
        handleUpdateStep = () => { }} = props;
    const hasAssert = currentStep?.stepInfo?.params?.params?.assert?.type === 1;
    const inputRef = useRef(null);
    const [activeKey, setActiveKey] = useState(hasAssert ? 'ai-assert' : 'step-list');
    const [inputAssert, setInputAssert] = useState('');

    useEffect(() => {
        if (currentStep?.stepInfo?.params?.params?.assert?.type === 0) {
            setActiveKey('step-list');
        } else {
            setActiveKey('ai-assert');
        }
    }, [currentStep?.stepInfo?.params?.params?.assert?.type]);

    useEffect(() => {
        setInputAssert(currentStep?.stepInfo?.params?.params?.assert?.params?.inputAssert);
    }, [currentStep?.stepInfo?.params?.params?.assert?.params?.inputAssert]);

    const handleScreenRecord = (e) => {
        currentStep.stepInfo.params.params.assert.params.withScreenRecord = e.target.checked;
        handleUpdateStep(currentStep);
    };

    const items = [
        {
            key: 'ai-assert',
            closable: !['readonly', 'debug', 'execute'].includes(editType),
            disabled: !hasAssert,
            label: '智能校验',
            children: (
                <div className={styles.AIAssertContentTextArea}>
                    <div>
                        <span className={styles.AIAssertTitle}>
                            校验内容
                        </span>
                    </div>
                    <div className={styles.AIAssertDescTextArea}>
                        <TextArea
                            ref={inputRef}
                            value={inputAssert}
                            className='input_editor'
                            size='small'
                            showCount
                            maxLength={50}
                            style={{height: 70, resize: 'none'}}
                            placeholder='请填写校验内容'
                            variant='borderless'
                            onChange={(e) => {
                                e.stopPropagation();
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return;
                                }
                                setInputAssert(e.target.value);
                            }}
                            onBlur={() => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return;
                                }
                                if (inputAssert !==
                                    currentStep?.stepInfo?.params?.params?.assert?.params?.inputAssert) {
                                    currentStep.stepInfo.params.params.assert.params.inputAssert = inputAssert;
                                    handleUpdateStep(currentStep);
                                }
                            }}
                        />
                        <div className={styles.aiAssertConfig}>
                            <p className={styles.aiAssertConfigTitle}>校验配置</p>
                            <Checkbox
                                checked={
                                    !!currentStep?.stepInfo?.params?.params?.assert?.params
                                        ?.withScreenRecord
                                }
                                onChange={handleScreenRecord}
                                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            >
                                视频校验
                            </Checkbox>
                            {currentStep?.stepInfo?.params?.params?.assert?.params
                                        ?.withScreenRecord &&
                                currentStep?.stepInfo?.params?.params?.stepIdList?.length > 2 && (
                                    <span className={styles.stepWarning}>
                                        <ExclamationCircleOutlined style={{ marginRight: 4 }} />
                                        步骤组超过两步，有失败的风险！
                                    </span>
                                )}
                        </div>
                    </div>
                </div>
            )
        }, {
            key: 'step-list',
            label: '步骤列表',
            closable: false,
            children: (
                <div className={styles.stepList}>
                    {currentStep?.stepChildren?.length > 0 ?
                        <StepListWithCommon
                            {...props}
                            editType='execute'
                            selectable={false}
                            showAddGroup={false}
                            stepList={currentStep?.stepChildren}
                            moduleStepList={currentStep?.stepChildren}
                        />
                        : <NoContent className={styles.noContent} text='暂无步骤' />}
                </div>
            )
        }
    ];

    const onEdit = (targetKey, action) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            return;
        }
        if (action === 'remove') {
            switch (targetKey) {
                case 'ai-assert':
                    let newCurrentStep = {...currentStep};
                    newCurrentStep.stepInfo.params.params.assert = {type: 0};
                    handleUpdateStep(newCurrentStep);
                    setActiveKey('step-list');
                    break;
                default:
                    break;
            }
        }
    };

    return (
        <div className={styles.systemAction}>
            <Tabs
                hideAdd
                size='small'
                type='editable-card'
                activeKey={activeKey}
                onChange={(key) => {
                    setActiveKey(key);
                }}
                items={items?.filter((item) => !item.disabled)}
                onEdit={onEdit}
                tabBarExtraContent={{
                    right: (
                        <>
                            {!['readonly', 'debug', 'execute'].includes(editType) &&
                                <AddStepGroupAttr
                                    disabled={{
                                        aiAssert: hasAssert
                                    }}
                                    title='添加属性'
                                    step={currentStep}
                                    handleUpdateStep={handleUpdateStep}
                                    renderTabBar={(tabBarExtraContent) => (
                                        <div className={styles.addStepGroupAttr}>
                                            {tabBarExtraContent}
                                        </div>
                                    )}
                                />}
                        </>
                    )
                }}
            />
        </div>
    );
};

export default GroupAction;
