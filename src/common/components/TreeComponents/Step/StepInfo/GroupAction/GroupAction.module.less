@import "RESOURCES/css/common.less";
@import '../AIAction//AIAssert/AIAssert.module.less';

.noContent {
    margin-top: 200px;
}

.systemAction {
    width: calc(100% -30px);
    margin: 15px 25px 15px 15px;
}

.delete {
    margin-left: 10px;
    color: red;
}

.AIAssertContentTextArea {
    position: absolute;
    width: 100%;
    background-color: #fafbff;
    border: 1px solid rgba(110, 103, 238, .2);
    border-radius: 3px;
    z-index: 9;
}

.AIAssertTitle {
    position: absolute;
    top: 10px;
    left: 15px;
    height: 20px;
    line-height: 20px;
    width: 64px;
    color: #fff;
    background-color: #6c65f0;
    font-size: 11px;
    text-align: center;
    border-radius: 3px;
}

.aiAssertConfig {
  margin-top: 40px;
  margin-left: -10px;
}

.aiAssertConfigTitle {
  margin-bottom: 4px;
  color: var(--color2);
  font-weight: bold;
}

.stepWarning {
    margin-left: 8;
    color: #faad14;
}