import { useState, useEffect, useRef } from 'react';
import classnames from 'classnames';
import { Spin, message, Tabs, Select, Button, Tooltip, Input, Space, Divider } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { TAB_SQL_OPTIONS } from '../const';
import { getDataLength } from '../utils';
import { SettingOutlined } from '@ant-design/icons';
import RunCaseSettingModal from 'FEATURES/front_qe_tools/case/edit/EditPage/Modal/RunCaseSettingModal';

import OptionLabel from '../components/common/OptionLabel';
import Editor from '../components/common/Editor';
import Assertion from '../components/Assertion';
import PostOperation from '../components/PostOperation';
import VariableExtraction from '../components/VariableExtraction';

import styles from './SQL.module.less';

function SqlAction(props) {
    const { currentStep, editType, handleUpdateStep = () => {}, dbList } = props;
    const [sqlBody, setSqlBody] = useState('');
    const [isEditorInit, setIsEditorInit] = useState(true);
    const [tabItem, setTabItem] = useState('sql');
    const runCaseSettingModalRef = useRef();

    useEffect(() => {
        setSqlBody(currentStep?.stepInfo?.sqlStatement);
        setIsEditorInit(true);
    }, [currentStep]);

    const updateCurStep = (data, module, type) => {
        let newCurrentStep = { ...currentStep };
        if (!type) {
            newCurrentStep.stepInfo[module] = data;
        } else {
            newCurrentStep.stepInfo[module][type] = data;
        }
        handleUpdateStep(newCurrentStep);
    };

    const onBlur = async (value) => {
        setSqlBody(value);
        setIsEditorInit(false);
    };

    useEffect(() => {
        if (!isEditorInit && sqlBody !== currentStep?.stepInfo?.sqlStatement) {
            updateCurStep(sqlBody, 'sqlStatement');
        }
    }, [isEditorInit, sqlBody, currentStep]);

    const transServerManagement = () => {
        runCaseSettingModalRef?.current.show({ key: 'db-config' });
    };

    return (
        <>
            <div className={styles.dbHeader}>
                <Space.Compact style={{ width: '100%' }}>
                    <span className={styles.title}>数据库服务</span>
                    <Select
                        className={styles.right}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        placeholder="请选择数据库服务"
                        value={currentStep?.stepInfo?.dbId}
                        options={dbList?.dbList?.map((item) => ({
                            label: item.dbName,
                            value: item.dbId
                        }))}
                        onChange={(value) => {
                            updateCurStep(value, 'dbId');
                        }}
                        dropdownRender={(menu) => (
                            <div>
                                {menu}
                                <Divider style={{ margin: '5px 0' }} />
                                <Button
                                    style={{
                                        width: '96%',
                                        margin: '0 2%',
                                        borderRadius: 6
                                    }}
                                    type="text"
                                    onClick={transServerManagement}
                                    icon={<SettingOutlined />}
                                >
                                    数据库服务管理
                                </Button>
                            </div>
                        )}
                    />
                </Space.Compact>
                <div className={styles.tabs}>
                    {TAB_SQL_OPTIONS?.map((item) => {
                        return (
                            <span
                                key={'tab-' + item.value}
                                className={classnames(styles.tabItem, {
                                    [styles.activedTabItem]: tabItem === item.value
                                })}
                                onClick={() => {
                                    setTabItem(item.value);
                                }}
                            >
                                <OptionLabel
                                    name={item.label}
                                    data={getDataLength(currentStep, item.value)}
                                />
                            </span>
                        );
                    })}
                </div>
                {['sql']?.includes(tabItem) && (
                    <div className={styles.tabContent}>
                        <Editor
                            language="sql"
                            height={window.innerHeight - 200}
                            editType={editType}
                            viewValue={currentStep?.stepInfo?.sqlStatement || ''}
                            onBlur={onBlur}
                        />
                    </div>
                )}
                {['variable_extraction']?.includes(tabItem) && (
                    <div className={styles.tabContent}>
                        <VariableExtraction
                            editType={editType}
                            data={currentStep?.stepInfo?.variableExtract}
                            onChange={(value) => {
                                updateCurStep(value, 'variableExtract');
                            }}
                        />
                    </div>
                )}
                {['response_assert']?.includes(tabItem) && (
                    <div className={styles.tabContent}>
                        <Assertion
                            editType={editType}
                            data={currentStep?.stepInfo?.responseAssert}
                            onChange={(value) => {
                                updateCurStep(value, 'responseAssert');
                            }}
                        />
                    </div>
                )}
                {['post_operation']?.includes(tabItem) && (
                    <div className={styles.tabContent}>
                        <PostOperation
                            editType={editType}
                            data={currentStep?.stepInfo?.postOperation}
                            onChange={(value) => {
                                updateCurStep(value, 'postOperation');
                            }}
                        />
                    </div>
                )}
            </div>
            <RunCaseSettingModal ref={runCaseSettingModalRef} />
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    dbList: state.common.case.dbList
}))(SqlAction);
