import { Input, Radio, Select, message } from 'antd';
import { useEffect, useState } from 'react';
import CollapsePanel from
    'COMMON/components/TreeComponents/Step/StepInfo/ApiAction/components/common/CollapsePanel/CollapsePanel.jsx';
import CollapsePanelList from
    'COMMON/components/TreeComponents/Step/StepInfo/ApiAction/components/common/CollapsePanel/CollapsePanelList.jsx';
import VariableItem from './VariableItem';

import styles from './VariableExtraction.module.less';
import { isEmpty } from 'lodash';

const { Option } = Select;

// 变量提取列表组件（主组件）
const VariableExtraction = ({ editType, formData, handelUpdateFormData }) => {
    const [variables, setVariables] = useState([]);

    // 初始化变量提取数据
    useEffect(() => {
        if (isEmpty(formData?.variableExtract)) {
            const newVariables = JSON.parse(JSON.stringify(formData?.variableExtract));
            setVariables(newVariables);
        }
    }, [formData?.variableExtract]);

    // 添加新变量
    const addVariable = () => {
        const newVariable = {
            id: Date.now().toString(),
            extractName: '',
            extractTarget: 2, // 默认为 Response JSON
            extractScope: 2, // 默认为提取部分
            jsonPath: '',
            checked: true // 默认选中
        };

        const newVariables = [...variables, newVariable];
        setVariables(newVariables);

        // 更新父组件的数据，确保调用接口
        updateParentData(newVariables, true);
    };

    // 移除变量
    const removeVariable = (id) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            message.error('当前状态无法删除变量');
            return false;
        }
        if (variables.length === 1) {
            setVariables([]);
            updateParentData([], true);
        } else {
            const newVariables = variables.filter((item) => item.id !== id);
            setVariables(newVariables);

            // 更新父组件的数据，确保调用接口
            updateParentData(newVariables, true);
        }
    };

    // 更新变量内容
    const updateVariable = (id, updatedVariable, callApi = false) => {
        // 创建新数组，避免引用问题
        const newVariables = variables.map((item) => {
            if (item.id === id) {
                // 返回全新的对象，而不是合并现有对象
                return { ...updatedVariable };
            }
            return { ...item }; // 克隆其他项
        });

        setVariables(newVariables);

        // 更新父组件的数据，根据 callApi 参数决定是否调用接口
        updateParentData(newVariables, callApi);
    };

    // 更新父组件数据
    const updateParentData = (newVariables, isFetch = false) => {
        // 防止意外修改，深拷贝数组
        const variablesCopy = JSON.parse(JSON.stringify(newVariables));

        // 更新父组件数据，传入 isFetch 参数
        handelUpdateFormData(
            {
                ...formData,
                variableExtract: variablesCopy
            },
            isFetch
        );
    };

    // 添加复制变量功能
    const copyVariable = (id) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            message.error('当前状态无法复制变量');
            return false;
        }

        // 找到要复制的变量
        const variableToCopy = variables.find((item) => item.id === id);
        if (!variableToCopy) {
            return;
        }

        // 创建副本，使用新的ID
        const newVariable = {
            ...JSON.parse(JSON.stringify(variableToCopy)),
            id: Date.now().toString(), // 使用当前时间戳作为新ID
            extractName: `${variableToCopy.extractName || ''}`
        };

        // 添加到数组中
        const newVariables = [...variables, newVariable];
        setVariables(newVariables);

        // 更新父组件的数据
        updateParentData(newVariables, true);

        message.success('变量已复制');
    };

    // 渲染每个变量项
    const renderVariableItem = (item) => {
        return (
            <VariableItem
                key={item.id}
                variable={item}
                onRemove={removeVariable}
                onCopy={copyVariable}
                onChange={(id, updatedVariable, callApi) =>
                    updateVariable(id, updatedVariable, callApi)
                }
                editType={editType}
            />
        );
    };

    // 处理列表变更
    const handleVariablesChange = (newVariables, callApi) => {
        setVariables(newVariables);
        updateParentData(newVariables, callApi);
    };

    return (
        <div className={styles.variableContainer}>
            <CollapsePanelList
                items={variables}
                onItemsChange={handleVariablesChange}
                renderItem={renderVariableItem}
                disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                onAddItem={addVariable}
                addButtonText="添加变量"
            />
        </div>
    );
};

export default VariableExtraction;
