// AssertionItem.js
import React, { useState, useEffect, useRef } from 'react';
import { Select, Input, Button, message } from 'antd';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import CollapsePanel from
    'COMMON/components/TreeComponents/Step/StepInfo/ApiAction/components/common/CollapsePanel/CollapsePanel.jsx';
import CollapsePanelList from
    'COMMON/components/TreeComponents/Step/StepInfo/ApiAction/components/common/CollapsePanel/CollapsePanelList.jsx';
import styles from '../../SQL/SQL.module.less';
import AssertionContentItem from './AssertionContentItem';

const { Option } = Select;

// 单个断言项组件
const AssertionItem = ({ assertion, onRemove, onChange, editType, onCopy, stepType = '' }) => {
    const [localAssertion, setLocalAssertion] = useState(() =>
        JSON.parse(JSON.stringify(assertion))
    );
    const [hasError, setHasError] = useState(false);

    // 组件挂载或 assertion 变化时更新本地状态
    useEffect(() => {
        const newAssertion = JSON.parse(JSON.stringify(assertion));
        setLocalAssertion(newAssertion);
        validateAssertion(newAssertion);
    }, [assertion.id]);

    // 验证断言是否有空值
    const validateAssertion = (assertionData) => {
        // 检查JSONPath是否为空（当断言类型为Response JSON时）
        const needsJsonPath = assertionData.assertType === 1;
        const jsonPathEmpty = needsJsonPath && !assertionData.assertContent?.jsonPath?.trim();

        // 检查断言内容是否为空（除了"存在jsonPath"操作符外都需要断言内容）
        const needsData = assertionData.assertContent?.type !== 9;
        const dataEmpty = needsData && !assertionData.assertContent?.data?.trim();

        setHasError(jsonPathEmpty || dataEmpty);
    };

    // 处理断言类型变更
    const handleAssertTypeChange = (value) => {
        setLocalAssertion((prev) => {
            const newAssertion = {
                ...JSON.parse(JSON.stringify(prev)),
                assertType: value,
                assertContent: {
                    ...JSON.parse(JSON.stringify(prev.assertContent)),
                    jsonPath: value === 1 ? prev.assertContent?.jsonPath || '' : ''
                }
            };
            validateAssertion(newAssertion);
            onChange(assertion.id, newAssertion, true);
            return newAssertion;
        });
    };

    // 处理JSONPath变更
    const handleJsonPathChange = (e) => {
        const value = e.target.value;
        setLocalAssertion((prev) => {
            const newAssertion = {
                ...JSON.parse(JSON.stringify(prev)),
                assertContent: {
                    ...JSON.parse(JSON.stringify(prev.assertContent)),
                    jsonPath: value
                }
            };
            validateAssertion(newAssertion);
            onChange(assertion.id, newAssertion, false);
            return newAssertion;
        });
    };

    // 处理JSONPath失焦
    const handleJsonPathBlur = () => {
        onChange(assertion.id, localAssertion, true);
    };

    // 处理操作符变更
    const handleTypeChange = (value) => {
        setLocalAssertion((prev) => {
            const newAssertion = {
                ...JSON.parse(JSON.stringify(prev)),
                assertContent: {
                    ...JSON.parse(JSON.stringify(prev.assertContent)),
                    type: value
                }
            };
            validateAssertion(newAssertion);
            onChange(assertion.id, newAssertion, true);
            return newAssertion;
        });
    };

    // 处理断言内容变更
    const handleDataChange = (e) => {
        const value = e.target.value;
        setLocalAssertion((prev) => {
            const newAssertion = {
                ...JSON.parse(JSON.stringify(prev)),
                assertContent: {
                    ...JSON.parse(JSON.stringify(prev.assertContent)),
                    data: value
                }
            };
            validateAssertion(newAssertion);
            onChange(assertion.id, newAssertion, false);
            return newAssertion;
        });
    };

    // 处理断言内容失焦
    const handleDataBlur = () => {
        onChange(assertion.id, localAssertion, true);
    };

    // 处理选中状态变更
    const handleCheckChange = (id, checked) => {
        setLocalAssertion((prev) => {
            const newAssertion = {
                ...JSON.parse(JSON.stringify(prev)),
                checked: checked
            };
            onChange(assertion.id, newAssertion, true);
            return newAssertion;
        });
    };

    // 处理断言目标变更
    const handleAssertTargetChange = (value) => {
        setLocalAssertion((prev) => {
            const newAssertion = {
                ...JSON.parse(JSON.stringify(prev)),
                assertTarget: value
            };
            onChange(assertion.id, newAssertion, true);
            return newAssertion;
        });
    };

    // 获取断言类型文本
    const getAssertionTypeText = (assertType) => {
        switch (assertType) {
            case 1:
                return 'Response JSON';
            case 2:
                return 'Response Text';
            case 3:
                return 'Response Schema';
            default:
                return '未知断言类型';
        }
    };

    // 获取操作符文本
    const getOperatorText = (type) => {
        switch (type) {
            case 1:
                return '包含';
            case 2:
                return '正则';
            case 3:
                return '等于';
            case 4:
                return '大于';
            case 5:
                return '小于';
            case 6:
                return '大于等于';
            case 7:
                return '小于等于';
            case 8:
                return '不等于';
            case 9:
                return '存在jsonPath';
            default:
                return '未知操作';
        }
    };

    // 获取断言标题
    const getAssertionTitle = () => {
        const assertTypeText = getAssertionTypeText(localAssertion.assertType);
        const operatorText = getOperatorText(localAssertion.assertContent?.type);
        const dataText = localAssertion.assertContent?.data || '';

        // 获取断言对象文本
        const getAssertTargetText = () => {
            switch (localAssertion.assertTarget) {
                case 1:
                    return 'Response Headers';
                case 2:
                    return 'Response Body';
                default:
                    return 'Response Body';
            }
        };

        const targetText = getAssertTargetText();

        if (localAssertion.assertType === 1) {
            const jsonPath = localAssertion.assertContent?.jsonPath || '';
            return (
                <>
                    {assertTypeText} &nbsp;&nbsp;
                    <span style={{ fontSize: '12px', color: '#888', marginLeft: '8px' }}>
                        ({targetText}) &nbsp;&nbsp;
                        {jsonPath} {operatorText} {dataText}
                    </span>
                </>
            );
        }

        return (
            <>
                {assertTypeText}&nbsp;&nbsp;
                <span style={{ fontSize: '12px', color: '#888', marginLeft: '8px' }}>
                    ({targetText}) &nbsp;&nbsp; {operatorText} {dataText}
                </span>
            </>
        );
    };

    return (
        <CollapsePanel
            id={assertion.id}
            title={getAssertionTitle()}
            checked={localAssertion.checked || false}
            error={hasError}
            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
            onCheckedChange={handleCheckChange}
            onCopy={onCopy}
            onRemove={onRemove}
            showCopy
            showDelete={!['readonly', 'debug', 'execute']?.includes(editType)}
        >
            <AssertionContentItem
                localAssertion={localAssertion}
                editType={editType}
                handleAssertTypeChange={handleAssertTypeChange}
                handleAssertTargetChange={handleAssertTargetChange}
                handleJsonPathChange={handleJsonPathChange}
                handleJsonPathBlur={handleJsonPathBlur}
                handleTypeChange={handleTypeChange}
                handleDataChange={handleDataChange}
                handleDataBlur={handleDataBlur}
                stepType={stepType}
            />
        </CollapsePanel>
    );
};

// 断言列表组件（主组件）
const Assertion = ({ editType, data, onChange, stepType = '' }) => {
    const prevAssertionsRef = useRef(null);
    const [assertions, setAssertions] = useState([]);
    // 初始化断言数据
    useEffect(() => {
        if (data && JSON.stringify(prevAssertionsRef.current) !== JSON.stringify(data)) {
            const assertionsWithIds = (data || []).map((item) => ({
                ...item,
                id: item.id || `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
            }));

            setAssertions(JSON.parse(JSON.stringify(assertionsWithIds)));
            prevAssertionsRef.current = JSON.parse(JSON.stringify(data));
        }
    }, [data]);

    // 添加新断言
    const addAssertion = () => {
        const newId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const newAssertion = {
            id: newId,
            assertType: 1,
            assertTarget: 2, // SQL只有body
            assertContent: {
                jsonPath: '',
                type: 3, // 默认等于
                data: ''
            },
            checked: true
        };

        const newAssertions = [...assertions, newAssertion];
        setAssertions(newAssertions);
        updateParentData(newAssertions, true);
    };

    // 移除断言
    const removeAssertion = (id) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            message.error('当前状态无法删除断言');
            return false;
        }

        const newAssertions = assertions.filter((item) => item.id !== id);
        setAssertions(newAssertions);
        updateParentData(newAssertions, true);
    };

    // 更新断言内容
    const updateAssertion = (id, updatedAssertion, callApi = false) => {
        const newAssertions = assertions.map((item) => {
            if (item.id === id) {
                return JSON.parse(JSON.stringify(updatedAssertion));
            }
            return JSON.parse(JSON.stringify(item));
        });

        setAssertions(newAssertions);
        if (callApi) {
            updateParentData(newAssertions, true);
        }
    };

    // 更新父组件数据
    const updateParentData = (newAssertions, isFetch = false) => {
        const assertionsCopy = JSON.parse(JSON.stringify(newAssertions));
        const formattedAssertions = assertionsCopy.map((assertion) => {
            let assertContent = {};

            switch (assertion.assertType) {
                case 1:
                    assertContent = {
                        jsonPath: assertion.assertContent?.jsonPath || '',
                        type: assertion.assertContent?.type || 3,
                        data: assertion.assertContent?.data || ''
                    };
                    break;
                case 2:
                    assertContent = {
                        type: assertion.assertContent?.type || 3,
                        data: assertion.assertContent?.data || ''
                    };
                    break;
                case 3:
                    assertContent = {
                        data: assertion.assertContent?.data || ''
                    };
                    break;
                default:
                    assertContent = {
                        jsonPath: '',
                        type: 3,
                        data: ''
                    };
            }

            return {
                id: assertion.id,
                assertType: assertion.assertType,
                assertTarget: assertion.assertTarget || 2,
                assertContent,
                checked: assertion.checked
            };
        });
        onChange && onChange(formattedAssertions, 'responseAssert');
        prevAssertionsRef.current = JSON.parse(JSON.stringify(formattedAssertions));
    };

    // 复制断言
    const copyAssertion = (id) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            message.error('当前状态无法复制断言');
            return false;
        }

        const assertionToCopy = assertions.find((item) => item.id === id);
        if (!assertionToCopy) {
            return;
        }

        const newAssertion = {
            ...JSON.parse(JSON.stringify(assertionToCopy)),
            id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        };

        const newAssertions = [...assertions, newAssertion];
        setAssertions(newAssertions);
        updateParentData(newAssertions, true);

        message.success('断言已复制');
    };

    // 渲染断言项
    const renderAssertionItem = (item) => {
        return (
            <AssertionItem
                key={item.id}
                assertion={item}
                onRemove={removeAssertion}
                onChange={updateAssertion}
                onCopy={copyAssertion}
                editType={editType}
                stepType={stepType}
            />
        );
    };

    // 处理列表变更
    const handleAssertionsChange = (newAssertions, callApi) => {
        setAssertions(newAssertions);
        updateParentData(newAssertions, callApi);
    };

    return (
        <div className={styles.assertionContainer}>
            <CollapsePanelList
                items={assertions}
                onItemsChange={handleAssertionsChange}
                renderItem={renderAssertionItem}
                disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                onAddItem={addAssertion}
                addButtonText="添加断言"
            />
        </div>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace
}))(Assertion);
