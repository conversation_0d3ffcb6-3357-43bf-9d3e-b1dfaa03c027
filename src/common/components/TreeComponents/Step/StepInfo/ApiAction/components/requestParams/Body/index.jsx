import {useEffect, useState, useRef, useCallback} from 'react';
import {
    Row
} from 'antd';
import classnames from 'classnames';
import NoContent from 'COMMON/components/common/NoContent';
import FormDataTable from '../../common/Table/FormDataTable';
import EditableTable from '../../common/Table/EditableTable';
import Editor from '../../common/Editor';
import {BODY_TYPE_MAP} from '../../../const';
import styles from './index.module.less';

const Body = ({currentStep, editType, onChange}) => {
    const [isJsonBodyEditorInit, setIsJsonBodyEditorInit] = useState(true);
    const [isRawBodyEditorInit, setIsRawBodyEditorInit] = useState(true);
    const [bodyType, setBodyType] = useState(1);
    const [jsonBody, setJsonBody] = useState('');
    const [rawBody, setRawBody] = useState('');

    useEffect(() => {
        let data = currentStep?.stepInfo?.requestParams?.body;
        setBodyType(data?.type || 'none');
        if (data.type === 4) {
            setJsonBody(data?.json || '');
            setIsJsonBodyEditorInit(true);
        }
        if (data.type === 5) {
            setRawBody(data?.raw || '');
            setIsRawBodyEditorInit(true);
        }
    }, [currentStep]);

    const onBlur = async (value) => {
        if (bodyType === 4) {
            setJsonBody(value);
            setIsJsonBodyEditorInit(false);
        }
        if (bodyType === 5) {
            setRawBody(value);
            setIsRawBodyEditorInit(false);
        }
    };

    useEffect(() => {
        if (!isJsonBodyEditorInit && bodyType === 4 &&
            jsonBody !== currentStep?.stepInfo?.requestParams?.body.json) {
            onChange && onChange([{data: jsonBody, type: 'json'}]);
        };
    }, [isJsonBodyEditorInit, jsonBody, currentStep]);

    useEffect(() => {
        if (!isRawBodyEditorInit && bodyType === 5 &&
            rawBody !== currentStep?.stepInfo?.requestParams?.body.raw) {
            onChange && onChange([{data: rawBody, type: 'raw'}]);
        };
    }, [isRawBodyEditorInit, rawBody, currentStep]);

    // 根据 Body 类型生成不同的显示内容
    const getBody = bd => {
        // none
        if (bd === 1) {
            return (
                <NoContent text='该请求体为空' className={styles.noContent} />
            );
        }
        // form-data
        if (bd === 2) {
            return (
                <FormDataTable
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    data={currentStep?.stepInfo?.requestParams?.body?.formData ?? []}
                    onChange={(value) => {
                        onChange && onChange([{
                            data: value,
                            type: 'formData'
                        }]);
                    }}
                />
            );
        }
        // x-www-form-urlencoded
        if (bd === 3) {
            return (
                <EditableTable
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    data={currentStep?.stepInfo?.requestParams?.body?.formUrlencoded ?? []}
                    onChange={(value) => {
                        onChange && onChange([{
                            data: value,
                            type: 'formUrlencoded'
                        }]);
                    }}
                />
            );
        }
        // json 和 raw
        if ([4, 5]?.includes(bd)) {
            return (
                <RenderEditor />
            );
        }
    };

    // 编辑器重新渲染
    const RenderEditor = useCallback(() => {
        let data = currentStep?.stepInfo?.requestParams?.body;
        return (
            <Editor
                editType={editType}
                height={window.innerHeight - 250}
                language={data?.type === 4 ? 'json' : 'text'}
                type={data?.type}
                viewValue={data?.type === 4 ?
                    data?.json : data?.raw}
                onBlur={onBlur}
            />
        );
    }, [currentStep, bodyType, jsonBody, rawBody]);

    return (
        <div className={styles.body}>
            <Row className={styles.bodyTypeList}>
                {
                    Object.keys(BODY_TYPE_MAP).map(item => (
                        <div
                            key={'item_' + item}
                            className={classnames(styles.bodyType,
                                {[styles.activedBodyType]: bodyType === +item}
                            )}
                            onClick={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return;
                                }
                                e.stopPropagation();
                                setBodyType(+item);
                                let params = [{
                                    data: +item,
                                    type: 'type'
                                }];
                                // 如果当前类型没有 type 字段，则添加一个 type 字段
                                if (!currentStep?.stepInfo?.requestParams?.body?.[BODY_TYPE_MAP[+item].type]) {
                                    params.push({
                                        data: BODY_TYPE_MAP[+item].init,
                                        type: BODY_TYPE_MAP[+item].type
                                    });
                                }
                                onChange && onChange(params);
                            }}
                        >
                            <div className={styles.circle} />
                            <div className={styles.bodyTypeText}>{BODY_TYPE_MAP[+item].label}</div>
                        </div>
                    ))
                }
            </Row>
            {getBody(bodyType)}
        </div>
    );
};

export default Body;