// PostOperation.jsx
import React, { useState, useEffect } from 'react';
import { Select, Input, Checkbox, message, Radio } from 'antd';
import { isEmpty } from 'lodash';
import { getDBList } from 'COMMON/api/front_qe_tools/config';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import CollapsePanel from 'COMMON/components/TreeComponents/Step/StepInfo/ApiAction/components/common/CollapsePanel/CollapsePanel.jsx';
import CollapsePanelList from 'COMMON/components/TreeComponents/Step/StepInfo/ApiAction/components/common/CollapsePanel/CollapsePanelList.jsx';
import OperationContent from './OperationContentItem';
import styles from '../../SQL/SQL.module.less';

const { Option } = Select;
// 单个后置操作组件
const OperationItem = ({
    operation,
    onRemove,
    onChange,
    editType,
    onCopy,
    currentSpace,
    dbList
}) => {
    const [localOperation, setLocalOperation] = useState({});
    const [editor, setEditor] = useState(null);
    const [enableVerify, setEnableVerify] = useState(false);
    const [hasError, setHasError] = useState(false);

    useEffect(() => {
        const op = JSON.parse(JSON.stringify(operation));
        setLocalOperation(op);
        setEnableVerify(op.stepInfo?.verifyResult !== 0);
        // 检查是否有错误
        validateOperation(op);
    }, [operation]);

    // 验证操作是否完整
    const validateOperation = (op) => {
        const { stepInfo } = op;
        const hasEmptyFields = !stepInfo?.dbId || !stepInfo?.sqlStatement?.trim();
        setHasError(hasEmptyFields);
    };
    // 处理数据库 ID 变更 - 保持实时调用
    const handleDbIdChange = (value) => {
        const newOperation = {
            ...localOperation,
            stepInfo: {
                ...localOperation.stepInfo,
                dbId: value
            }
        };
        setLocalOperation(newOperation);
        validateOperation(newOperation);
        onChange(operation.id, newOperation, true);
    };
    // 处理 SQL 语句失焦 - 调用接口
    const handleSqlStatementBlur = (value) => {
        if (value !== undefined) {
            const newOperation = {
                ...localOperation,
                stepInfo: {
                    ...localOperation.stepInfo,
                    sqlStatement: value
                }
            };
            setLocalOperation(newOperation);
            validateOperation(newOperation);
            onChange(operation.id, newOperation, true);
        }
    };

    // 处理校验结果集变更 - 保持实时调用
    const handleVerifyResultChange = (e) => {
        const newOperation = {
            ...localOperation,
            stepInfo: {
                ...localOperation.stepInfo,
                verifyResult: e.target.value
            }
        };
        setLocalOperation(newOperation);
        onChange(operation.id, newOperation, true);
    };

    // 处理校验启用状态变更 - 保持实时调用
    const handleEnableVerifyChange = (e) => {
        const isChecked = e.target.checked;
        setEnableVerify(isChecked);
        const newOperation = {
            ...localOperation,
            stepInfo: {
                ...localOperation.stepInfo,
                // 如果不启用，设为0；如果启用，使用当前Radio的值或默认为1
                verifyResult: isChecked ? localOperation.stepInfo?.verifyResult || 1 : 0
            }
        };
        setLocalOperation(newOperation);
        onChange(operation.id, newOperation, true);
    };

    // 处理步骤描述变更 - 只更新本地状态
    const handleStepDescChange = (e) => {
        const newOperation = {
            ...localOperation,
            stepDesc: e.target.value
        };
        setLocalOperation(newOperation);
    };

    // 新增：处理步骤描述失焦 - 调用接口
    const handleStepDescBlur = (e) => {
        const newOperation = {
            ...localOperation,
            stepDesc: e.target.value
        };
        setLocalOperation(newOperation);
        onChange(operation.id, newOperation, true);
    };

    // 获取操作标题
    const getOperationTitle = () => {
        return 'SQL 操作';
    };

    return (
        <CollapsePanel
            id={operation.id}
            title={getOperationTitle()}
            checked={localOperation.checked || false}
            error={hasError}
            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
            onCheckedChange={(id, checked) => {
                const newOperation = {
                    ...localOperation,
                    checked: checked
                };
                setLocalOperation(newOperation);
                onChange(id, newOperation, true);
            }}
            onCopy={onCopy}
            onRemove={onRemove}
            showCopy
            showDelete={!['readonly', 'debug', 'execute']?.includes(editType)}
        >
            <OperationContent
                localOperation={localOperation}
                enableVerify={enableVerify}
                dbList={dbList?.dbList}
                editType={editType}
                editor={editor}
                setEditor={setEditor}
                handleStepDescChange={handleStepDescChange}
                handleStepDescBlur={handleStepDescBlur}
                handleDbIdChange={handleDbIdChange}
                handleSqlStatementBlur={handleSqlStatementBlur}
                handleEnableVerifyChange={handleEnableVerifyChange}
                handleVerifyResultChange={handleVerifyResultChange}
            />
        </CollapsePanel>
    );
};

// 后置操作列表组件（主组件）
const PostOperation = ({ editType, data, onChange, currentSpace, dbList }) => {
    const [operations, setOperations] = useState([]);
    // 初始化后置操作数据
    useEffect(() => {
        if (data) {
            const newOperations = JSON.parse(JSON.stringify(data || []));
            setOperations(newOperations);
        }
    }, [data]);

    // 添加新后置操作
    const addOperation = () => {
        // 获取默认数据库ID（如果有）
        const defaultDbId =
            !isEmpty(dbList?.dbList) && Array.isArray(dbList?.dbList)
                ? dbList?.dbList[0]?.dbId
                : undefined;
        const newOperation = {
            id: Date.now().toString(),
            stepType: 1101, // SQL执行步骤类型
            stepDesc: '',
            checked: true,
            stepInfo: {
                dbId: defaultDbId, // 设置默认数据库ID
                sqlStatement: '',
                verifyResult: 0 // 默认
            }
        };
        const currentOperations = Array.isArray(operations) ? operations : [];
        const newOperations = [...currentOperations, newOperation];
        setOperations(newOperations);
        updateParentData(newOperations, true);
    };

    // 移除后置操作
    const removeOperation = (id) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            message.error('当前状态无法删除操作');
            return false;
        }
        const newOperations = operations.filter((item) => item.id !== id);
        setOperations(newOperations);
        updateParentData(newOperations, true);
    };

    // 更新后置操作内容
    const updateOperation = (id, updatedOperation, callApi = false) => {
        // 确保 operations 是数组
        if (!Array.isArray(operations)) {
            setOperations([updatedOperation]);
            updateParentData([updatedOperation], callApi);
            return;
        }
        const newOperations = operations.map((item) => {
            if (item.id === id) {
                return { ...updatedOperation };
            }
            return { ...item };
        });
        setOperations(newOperations);
        // 更新父组件的数据，根据 callApi 参数决定是否调用接口
        updateParentData(newOperations, callApi);
    };

    // 更新父组件数据
    const updateParentData = (newOperations, isFetch = false) => {
        const operationsCopy = JSON.parse(JSON.stringify(newOperations));
        onChange && onChange(operationsCopy, 'postOperation');
    };

    // 处理列表变更
    const handleOperationsChange = (newOperations, callApi) => {
        if (!Array.isArray(newOperations)) {
            newOperations = [];
        }
        setOperations(newOperations);
        updateParentData(newOperations, callApi);
    };

    // 添加复制操作功能
    const copyOperation = (id) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            message.error('当前状态无法复制操作');
            return false;
        }
        // 确保 operations 是数组
        if (!Array.isArray(operations)) {
            return false;
        }
        // 找到要复制的操作
        const operationToCopy = operations.find((item) => item.id === id);
        if (!operationToCopy) {
            return;
        }

        // 创建副本，使用新的ID
        const newOperation = {
            ...JSON.parse(JSON.stringify(operationToCopy)),
            id: Date.now().toString(),
            stepDesc: `${operationToCopy.stepDesc || '未命名操作'} (复制)`
        };

        // 添加到数组中
        const newOperations = [...operations, newOperation];
        setOperations(newOperations);

        // 更新父组件的数据
        updateParentData(newOperations, true);

        message.success('操作已复制');
    };

    return (
        <div className={styles.operationContainer}>
            <CollapsePanelList
                items={operations}
                onItemsChange={handleOperationsChange}
                renderItem={(item) => (
                    <>
                        <OperationItem
                            key={item.id}
                            operation={item}
                            onRemove={removeOperation}
                            onCopy={copyOperation}
                            onChange={(id, updatedOperation, callApi) =>
                                updateOperation(id, updatedOperation, callApi)
                            }
                            currentSpace={currentSpace}
                            editType={editType}
                            dbList={dbList}
                        />
                    </>
                )}
                disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                onAddItem={addOperation}
                addButtonText="添加后置操作"
            />
        </div>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    dbList: state.common.case.dbList
}))(PostOperation);
