.collapsePanelContainer {
  margin-bottom: 10px;

  .customCollapse {
    border-radius: 4px;

    :global {
      .ant-collapse-header {
        padding: 8px 16px !important;
        border-radius: 4px !important;
      }

      .ant-collapse-content-box {
        padding: 0 !important;
      }
    }
  }

  .panelContent {
    display: flex;
    flex-direction: column;
    gap: 16px;
    background-color: #fff;
    padding: 16px;
  }
}

.customPanel {
  border-radius: 4px;
  margin-bottom: 8px;

  :global {
    .ant-collapse-header {
      background-color: rgba(0, 0, 0, 0.02) !important;
      border-radius: 4px !important;
    }

    // border: 1px solid #ec605c;
  }
}

.errorPanelClose {

  // border: 1px solid #ff4d4f;
  // border-left: 1px solid #ff4d4f;
  // border-right: 1px solid #ff4d4f;
  :global {
    .ant-collapse-header {
      background-color: #fdf2f0 !important;
      // border: 1px solid #ff4d4f !important;
      // border: 1px solid #ec9495;
      // border: 1px solid #ec605c;

    }

    // ant-collapse-header ant-collapse-header-collapsible-only
  }
}

.errorPanelOpen {
  // border: 1px solid #ff4d4f;
  border: 1px solid #ec605c;

  :global {
    .ant-collapse-header {
      background-color: #fdf2f0 !important;
      // border: 1px solid #ff4d4f !important;
      // border: 1px solid #ff4d4f;
    }

    // ant-collapse-header ant-collapse-header-collapsible-only
  }
}

.PanelOpen {
  border: 1px solid #eeeeee;

  :global {


  }
}