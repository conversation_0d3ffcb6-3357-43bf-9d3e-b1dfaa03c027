import {useState, useEffect, useRef} from 'react';
import * as monaco from 'monaco-editor';
import * as jsonc from 'jsonc-parser';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {CONFIG} from './config';
import styles from './Editor.module.less';

const Editor = (props) => {
    const {
        height,
        options = {},
        viewValue,
        onBlur,
        editType,
        language,
        paramList
    } = props;
    const [editor, setEditor] = useState(null);
    const editorRef = useRef(null);
    const monacoProviderRef = useRef(null);

    // 格式校验
    const validateJsonWithVariables = (content) => {
        const model = editorRef?.current?.getModel();
        const markers = []; // 错误集合

        // 检测未闭合的 {{
        const openMatches = (content.match(/{{/g) || []).length;
        const closeMatches = (content.match(/}}/g) || []).length;
        if (openMatches !== closeMatches) {
            markers.push({
                severity: monaco.MarkerSeverity.Error,
                message: "'{{' 和 '}}' 数量不匹配，变量未闭合！",
                startLineNumber: 1,
                startColumn: 1,
                endLineNumber: 1,
                endColumn: 1
            });
            monaco.editor.setModelMarkers(model, 'owner', markers);
            return;
        }

        // 用 jsonc-parser 解析 JSON 校验
        // 替换所有 {{}} 成 "placeholder" 以避免变量解析错误
        const cleanContent = content.replace(/{{\s*[\w.]+\s*}}/g, '"placeholder"');
        const errors = [];
        if (cleanContent !== '') {
            jsonc.parse(cleanContent, errors, {allowTrailingComma: false});
            for (const error of errors) {
                const position = model.getPositionAt(error.offset);
                markers.push({
                    severity: monaco.MarkerSeverity.Error,
                    message: `JSON错误: ${jsonc.printParseErrorCode(error.error)}`,
                    startLineNumber: position.lineNumber,
                    startColumn: position.column,
                    endLineNumber: position.lineNumber,
                    endColumn: position.column
                });
            }
        }

        // 更新编辑器里的错误
        monaco.editor.setModelMarkers(model, 'owner', markers);
    }

    // 初始化编辑器
    useEffect(() => {
        // JSON 关闭注释和常规格式校验
        monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
            validate: false,
            allowComments: false
        });
        // 全局变量补全提示
        monacoProviderRef.current = monaco.languages.registerCompletionItemProvider('json', {
            triggerCharacters: ['{'],
            provideCompletionItems: function (model, position) {
                const textUntilPosition = model.getValueInRange({
                    startLineNumber: position.lineNumber,
                    startColumn: 1,
                    endLineNumber: position.lineNumber,
                    endColumn: position.column
                });

                if (!textUntilPosition.endsWith('{{')) {
                    return {suggestions: []};
                }

                return {
                    suggestions: paramList.map(item => ({
                        label: item.paramKey + ' (Global Variable)',
                        kind: monaco.languages.CompletionItemKind.Variable,
                        documentation: item.paramValue,
                        insertText: item.paramKey + '}}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        range: {
                            startLineNumber: position.lineNumber,
                            startColumn: position.column,
                            endLineNumber: position.lineNumber,
                            endColumn: position.column
                        }
                    }))
                };
            }
        });
        // 创建编辑器实例
        const editor = monaco.editor.create(document.getElementById('container'), {
            value: viewValue ?? '',
            language: language ?? 'plaintext',
            readOnly: ['readonly', 'debug', 'execute'].includes(editType) || false,
            ...CONFIG,
            editorClassName: 'monaco-editor',
            ...options,
        });
        editorRef.current = editor;
        setEditor(editor);

        return () => {
            console.log('销毁编辑器');
            setEditor(null);
            monacoProviderRef.current?.dispose();
            editorRef.current?.dispose();
        };
    }, []);

    // 编辑器内容展示更新
    useEffect(() => {
        if (editor && editorRef?.current) {
            editorRef?.current?.setValue(viewValue ?? '');
        }
    }, [editor, editorRef?.current, viewValue]);

    // 编辑器事件
    useEffect(() => {
        // 实时验证处理：去掉 {{}} 后再 JSON.parse
        editorRef?.current?.onDidChangeModelContent(() => {
            const value = editorRef?.current?.getValue();
            validateJsonWithVariables(value);
        });
        // 监听失焦保存
        editorRef.current?.onDidBlurEditorText(() => {
            let val = editorRef.current?.getValue();
            if (val !== viewValue) {
                onBlur && onBlur(val);
            }
        });
    }, [editorRef?.current]);

    return (
        <div
            id='container'
            style={{
                border: '1px solid #eee',
                height: height || 300,
                overflow: 'hidden',
            }}
        />
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    paramList: state.common.case.paramList,
}))(Editor);