// 判断对象字段是否存在不为空
export const isObjectItemsHasNotNull = (item, extraKeys = []) => {
    const default_keys = ['name', 'value', 'description'];
    let keys = [...default_keys, ...extraKeys];
    let _item = {};
    for (let key of Object.keys(item ?? {})) {
        if (keys.includes(key)) {
            _item[key] = item[key] ?? null;
        }
    }
    return Object.values(_item ?? {}).some(val => val != null);
};

// 判断对象字段是否都为空
export const isObjectItemsAllNull = (item, extraKeys = []) => {
    const default_keys = ['name', 'value', 'description'];
    let keys = [...default_keys, ...extraKeys];
    let _item = {};
    for (let key of Object.keys(item ?? {})) {
        if (keys.includes(key)) {
            _item[key] = item[key] ?? null;
        }
    }
    return Object.values(_item ?? {}).every(val => val === null);
};