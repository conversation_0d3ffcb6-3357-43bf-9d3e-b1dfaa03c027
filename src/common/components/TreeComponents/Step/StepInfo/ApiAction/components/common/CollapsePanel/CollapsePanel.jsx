import {
    CopyOutlined,
    DeleteOutlined,
    DownOutlined,
    EllipsisOutlined,
    HolderOutlined,
    RightOutlined
} from '@ant-design/icons';
import {Checkbox, Collapse, Dropdown, Menu, Space} from 'antd';
import React, {useState} from 'react';
import styles from './CollapsePanel.module.less';
import classNames from 'classnames';

const {Panel} = Collapse;

/**
 * 可折叠面板组件
 * @param {Object} props 组件属性
 * @param {string} props.id 唯一标识
 * @param {string} props.title 面板标题
 * @param {boolean} props.checked 是否选中
 * @param {boolean} props.disabled 是否禁用
 * @param {function} props.onCheckedChange 选中状态变更回调
 * @param {function} props.onCopy 复制回调
 * @param {function} props.onRemove 删除回调
 * @param {React.ReactNode} props.children 面板内容
 * @param {boolean} props.showCopy 是否显示复制按钮
 * @param {boolean} props.showDelete 是否显示删除按钮
 * @param {boolean} props.error 是否显示错误状态
 * @param {string} props.className 自定义类名
 */
const CollapsePanel = ({
    id,
    title,
    checked = false,
    disabled = false,
    onCheckedChange,
    onCopy,
    onRemove,
    children,
    showCheckbox = true,
    showCopy = true,
    showDelete = true,
    error = false,
    className = ''
}) => {
    const [isActive, setIsActive] = useState(true);

    // 使用 Dropdown 菜单
    const dropdownMenu = (
        <Menu>
            {showCopy && (
                <Menu.Item
                    key="copy"
                    icon={<CopyOutlined />}
                    onClick={(e) => {
                        e.domEvent.stopPropagation();
                        onCopy && onCopy(id);
                    }}
                >
                    复制
                </Menu.Item>
            )}
            {showDelete && (
                <Menu.Item
                    key="delete"
                    icon={<DeleteOutlined />}
                    danger
                    disabled={disabled}
                    onClick={(e) => {
                        e.domEvent.stopPropagation();
                        onRemove && onRemove(id);
                    }}
                >
                    删除
                </Menu.Item>
            )}
        </Menu>
    );

    return (
        <div className={`${styles.collapsePanelContainer} ${className}`}>
            <Collapse
                style={{borderRadius: '4px', padding: '0px'}}
                bordered={false}
                collapsible="header"
                size="small"
                className={classNames(styles.customCollapse, {
                    [styles.errorPanelClose]: error && !isActive,
                    [styles.errorPanelOpen]: error && isActive,
                    [styles.PanelOpen]: !error && isActive
                })}
                ghost
                expandIcon={({isActive}) => {
                    setIsActive(isActive);
                    return <HolderOutlined />;
                }}
                defaultActiveKey={[id]}
            >
                <Panel
                    header={
                        <div style={{display: 'flex', alignItems: 'center'}}>
                            {
                                showCheckbox ?
                                    <Checkbox
                                        checked={checked}
                                        onChange={(e) => {
                                            onCheckedChange && onCheckedChange(id, e.target.checked);
                                        }}
                                        onClick={(e) => e.stopPropagation()}
                                        disabled={disabled}
                                        style={{marginRight: '8px'}}
                                    /> : null
                            }
                            <span>{title}</span>
                        </div>
                    }
                    className={styles.customPanel}
                    key={id}
                    extra={
                        <Space>
                            {(showCopy || showDelete) && (
                                <Dropdown
                                    overlay={dropdownMenu}
                                    trigger={['hover']}
                                    placement="bottomRight"
                                >
                                    <EllipsisOutlined
                                        style={{cursor: 'pointer'}}
                                        onClick={(e) => e.stopPropagation()}
                                    />
                                </Dropdown>
                            )}
                            {isActive ? <DownOutlined /> : <RightOutlined />}
                        </Space>
                    }
                >
                    <div className={styles.panelContent}>{children}</div>
                </Panel>
            </Collapse>
        </div>
    );
};

export default CollapsePanel;
