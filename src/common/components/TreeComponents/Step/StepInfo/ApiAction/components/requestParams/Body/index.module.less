.noContent {
    margin-top: 50px;
}

.body {
    .bodyTypeList {
        margin-bottom: 10px;
    }
    
    .bodyType {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-right: 15px;

        &:hover {
            .circle {
                border: 1px solid var(--primary-color);
            }
        }

        .circle {
            display: inline-block;
            width: 10px;
            height: 10px;
            margin-right: 3px;
            border-radius: 50%;
            border: 1px solid #777;
        }

        .bodyTypeText {
            display: inline-block;
            font-size: 12px;
            color: #232323;
        }
    }

    .activedBodyType {
        .circle {
            border: 3px solid var(--primary-color);
        }

        &:hover {
            .circle {
                border: 3px solid var(--primary-color);
            }
        }

    }
}