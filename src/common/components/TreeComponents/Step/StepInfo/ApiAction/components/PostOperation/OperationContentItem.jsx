import React from 'react';
import { Select, Input, Checkbox, Radio } from 'antd';
import JSONMonacoEditor from '../../../InterfaceAction/Postman/JSONMonacoEditor';
import styles from '../../SQL/SQL.module.less';
import Editor from '../common/Editor';

const OperationContentItem = ({
    localOperation,
    enableVerify,
    dbList,
    editType,
    editor,
    setEditor,
    handleStepDescChange,
    handleStepDescBlur,
    handleDbIdChange,
    handleSqlStatementChange,
    handleSqlStatementBlur,
    handleEnableVerifyChange,
    handleVerifyResultChange
}) => {
    return (
        <>
            <div style={{ display: 'flex', width: '100%', alignItems: 'center' }}>
                <div className={styles.formItemLeftSmall}>步骤描述：</div>
                <Input
                    placeholder="请输入步骤描述"
                    value={localOperation.stepDesc || ''}
                    onChange={handleStepDescChange}
                    onBlur={handleStepDescBlur}
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    style={{ width: '100%', flex: 1 }}
                />
            </div>

            <div style={{ display: 'flex', width: '100%', alignItems: 'center' }}>
                <div className={styles.formItemLeftSmall}>数据库服务 &nbsp;&nbsp;</div>
                <Select
                    className={styles.formItemRight}
                    value={localOperation.stepInfo?.dbId}
                    onChange={handleDbIdChange}
                    placeholder="请选择数据库服务"
                    options={dbList?.map((item) => ({
                        label: item.dbName,
                        value: item.dbId
                    }))}
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                />
            </div>

            <div style={{ display: 'flex', width: '100%' }}>
                <div className={styles.formItemLeftSmall}>SQL 语句 &nbsp;&nbsp;</div>

                <div style={{ flex: 1 }}>
                    <Editor
                        language="sql"
                        height="10vh"
                        editType={editType}
                        viewValue={localOperation.stepInfo?.sqlStatement || ''}
                        onBlur={handleSqlStatementBlur}
                    />
                </div>
            </div>

            {/*
            <JSONMonacoEditor
                language="sql"
                value={localOperation.stepInfo?.sqlStatement || ''}
                disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                setEditor={setEditor}
                onChange={handleSqlStatementChange}
                onBlur={handleSqlStatementBlur}
                readOnly={['readonly', 'debug', 'execute']?.includes(editType)}
            /> */}

            <div style={{ display: 'flex', width: '100%', alignItems: 'center' }}>
                <div className={styles.formItemLeftSmall}>校验结果集：</div>
                <Checkbox
                    checked={enableVerify}
                    onChange={handleEnableVerifyChange}
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    style={{ marginRight: '12px' }}
                >
                    启用校验
                </Checkbox>
                {enableVerify && (
                    <Radio.Group
                        value={localOperation.stepInfo?.verifyResult || 1}
                        onChange={handleVerifyResultChange}
                        disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    >
                        <Radio value={1}>空</Radio>
                        <Radio value={2}>非空</Radio>
                    </Radio.Group>
                )}
            </div>
        </>
    );
};

export default OperationContentItem;
