.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    text-align: left;

    :global {
        .ant-checkbox-checked .ant-checkbox-inner {
            border-color: #000 !important;
            background-color: #000 !important;
        }
        
        .ant-select-selector {
            font-size: 12px !important;
            color: #777 !important;
        }

        .ant-input {
            font-size: 12px !important;
        }
        .ant-checkbox-inner:after{
            border-color: #fff !important;
        }
    }

    thead {
        height: 32px;
        color: #6b6b6b;
        cursor: default;
    }

    th, td:nth-child(1) {
        padding-left: 11px;
    }

    th:nth-child(1) {
        width: 40px;
    }

    th,
    td {
        position: relative;
        border: 1px solid #ddd;
    }

    .delIcon {
        display: none;
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translate(0, -50%);
        color: red;
        background-color: #fff;
        cursor: pointer;
    }

    tbody {
        tr:hover {
            .delIcon {
                display: block;
            }
        }
    }
}

.batchDelIcon {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translate(0, -50%);
    font-size: 12px;
    font-weight: normal;
    cursor: pointer;

    &:hover {
        color: var(--error-color);
        text-decoration: underline;
    }
}