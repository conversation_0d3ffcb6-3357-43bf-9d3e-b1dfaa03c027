import { PlusOutlined } from '@ant-design/icons';
import { Button, message } from 'antd';
import { useEffect, useState } from 'react';
import styles from './CollapsePanelList.module.less';

/**
 * 可折叠面板列表组件
 * @param {Object} props 组件属性
 * @param {Array} props.items 面板项列表
 * @param {function} props.onItemsChange 列表变更回调
 * @param {function} props.renderItem 渲染每个面板的函数
 * @param {boolean} props.disabled 是否禁用
 * @param {function} props.onAddItem 添加项回调
 * @param {string} props.addButtonText 添加按钮文本
 */
const CollapsePanelList = ({
    items = [],
    onItemsChange,
    renderItem,
    disabled = false,
    onAddItem,
    addButtonText = '添加项'
}) => {
    const [panelItems, setPanelItems] = useState([]);
    const [tempItems, setTempItems] = useState([]); // 拖拽过程中的临时状态
    const [draggedItem, setDraggedItem] = useState(null);

    useEffect(() => {
        // 处理空数组的情况
        if (!items || items.length === 0) {
            setPanelItems([]);
            setTempItems([]);
            return;
        }

        // 深拷贝以避免引用问题
        const newItems = JSON.parse(JSON.stringify(items));
        setPanelItems(newItems);
        setTempItems(newItems);
    }, [items]);

    // 处理拖拽开始
    const handleDragStart = (e, index) => {
        if (disabled) {
            message.error('当前状态无法拖拽操作');
            return false;
        }
        if (Array.isArray(tempItems) && tempItems[index]) {
            setDraggedItem(tempItems[index]);
        }
    };

    // 处理拖拽结束
    const handleDragEnd = (e) => {
        if (disabled) {
            message.error('当前状态无法拖拽操作');
            return false;
        }

        if (draggedItem) {
            // 只在拖拽结束时更新父组件数据和实际items
            setPanelItems(tempItems);
            onItemsChange && onItemsChange(tempItems, true);
        }

        setDraggedItem(null);
    };

    // 处理拖拽经过目标区域
    const handleDragEnter = (e, targetIndex) => {
        e.preventDefault();

        if (!draggedItem || disabled) {
            return;
        }

        if (draggedItem && draggedItem.id !== tempItems[targetIndex].id) {
            const draggedItemIndex = tempItems.findIndex((item) => item.id === draggedItem.id);
            if (draggedItemIndex === -1) {
                return;
            }

            // 只更新临时状态，不调用API
            const newItems = [...tempItems];
            const [movedItem] = newItems.splice(draggedItemIndex, 1);
            newItems.splice(targetIndex, 0, movedItem);

            setTempItems(newItems);
        }
    };

    // 允许放置
    const handleDragOver = (e) => {
        e.preventDefault();
    };

    // 添加新项
    const handleAddItem = () => {
        if (onAddItem) {
            onAddItem();
        }
    };

    return (
        <div className={styles.collapsePanelListContainer}>
            {Array.isArray(tempItems) &&
                tempItems?.map((item, index) => (
                    <div
                        key={item.id}
                        draggable={!disabled}
                        onDragStart={(e) => handleDragStart(e, index)}
                        onDragEnd={(e) => handleDragEnd(e)}
                        onDragOver={(e) => handleDragOver(e)}
                        onDragEnter={(e) => handleDragEnter(e, index)}
                        onDrop={(e) => handleDragEnd(e)}
                        style={{
                            marginBottom: '10px',
                            cursor: disabled ? 'default' : 'grab'
                        }}
                    >
                        {renderItem(item, index)}
                    </div>
                ))}

            <Button
                type="dashed"
                onClick={handleAddItem}
                icon={<PlusOutlined />}
                style={{ width: '100%' }}
                disabled={disabled}
            >
                {addButtonText}
            </Button>
        </div>
    );
};

export default CollapsePanelList;
