import {useState, useEffect} from 'react';
import {
    Input,
    Row,
    Col
} from 'antd';
import styles from './Timeout.module.less';

const Timeout = (props) => {
    const {onChange, data, disabled} = props;
    const [timeout, setTimeout] = useState(60);

    useEffect(() => {
        setTimeout(+data ?? 60);
    }, [data]);

    return (
        <Row className={styles.paramsItem}>
            <Col flex='110px'>
                <span className={styles.paramsItemTitle}>超时时间</span>
            </Col>
            <Col flex='auto'>
                <Input
                    disabled={disabled}
                    value={timeout}
                    onChange={(e) => {
                        let value = e.target.value;
                        setTimeout(value);
                    }}
                    onBlur={() => {
                        onChange && onChange(timeout);
                    }}
                />
            </Col>
        </Row>
    );
};

export default Timeout;
