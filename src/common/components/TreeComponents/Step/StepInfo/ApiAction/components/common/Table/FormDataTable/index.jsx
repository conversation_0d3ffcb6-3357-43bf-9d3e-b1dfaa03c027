/*
可编辑表格组件
1、空数据行不可删除
2、空数据行添加数据后，自动添加一行新空数据行，并勾选上当前输入行
 */
import {useState, useEffect, useRef} from 'react';
import {isEmpty} from 'lodash';
import {Checkbox, Input, message, Select} from 'antd';
import {CloseOutlined, DeleteOutlined} from '@ant-design/icons';
import {upload} from 'COMMON/utils/requestUtils';
import {isObjectItemsHasNotNull, isObjectItemsAllNull} from '../utils';
import {DEAFULT_FORMDATATABLE_TR, KEY_TYPE} from '../const';
import styles from './index.module.less';

function FormDataTable(props) {
    const {data, onChange, disabled} = props;
    const [dataSource, setDataSource] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [checkedKeys, setCheckedKeys] = useState([]);
    const uploadRef = useRef();

    const isFilter = (item) => {
        return isObjectItemsHasNotNull(item) || item.checked || item.type === 2;
    };

    useEffect(() => {
        setDataSource(data.map((item, index) => ({...item, index: index})));
    }, [data]);

    useEffect(() => {
        const checkedItems = dataSource.filter(item => item.checked);
        setCheckedKeys(checkedItems.map(item => item.index));
        setTableData([...dataSource, {...DEAFULT_FORMDATATABLE_TR, index: dataSource.length}]);
    }, [dataSource]);

    // 更新表格数据
    const updateTableData = (updateItems, index, flag = false) => {
        let isChangeData = flag;
        let newTableData = [];
        tableData.forEach((item, i) => {
            let data = {};
            if (i === index) {
                // 更新当前行数据
                data = {...item};
                for (let updateItem of updateItems) {
                    data[updateItem.type] = updateItem.value;
                    // 如果类型切换为 Text，则清空 value
                    if (updateItem.type === 'type' && updateItem.value === 1) {
                        data.value = '';
                        data.link = '';
                    }
                    // 如果当前行是空数据行，则默认勾选
                    if ((isObjectItemsAllNull(item) || updateItem.type === 'type') && updateItem.value !== null) {
                        isChangeData = true;
                        data.checked = true;
                    }
                }
            } else {
                data = {...item};
            }
            newTableData.push(data);
        });
        let data = newTableData
            .filter(item => isFilter(item));
        setDataSource(data);
        if (isChangeData) {
            onChangeData(data);
        }
    };

    // 表格行全选
    const handleSelectAll = (e) => {
        let newCheckedKeys = [];
        if (e.target.checked) {
            newCheckedKeys = tableData
                .filter(item => isFilter(item))
                .map((item, index) => {
                    return index;
                });
        } else {
            newCheckedKeys = [];
        }
        setCheckedKeys(newCheckedKeys);
        let data = tableData
            .filter(item => isFilter(item))
            .map((item, index) => ({
                ...item,
                checked: newCheckedKeys.includes(index)
            }));
        onChangeData(data);
    };

    // 表格行单选
    const handleSelectOption = (e, index) => {
        let newDataSource = [...dataSource];
        newDataSource[index].checked = e.target.checked;
        setDataSource(newDataSource);
        onChangeData(newDataSource);
    };

    // 删除选中项
    const deleteTableData = (key) => {
        let newTableData = [];
        let _index = 0;
        tableData.forEach((item, index) => {
            if (key !== index) {
                newTableData.push({...item, index: _index});
                _index++;
            }
        });
        let data = newTableData
            .filter(item => isFilter(item));
        setDataSource(data);
        onChangeData(data);
    };

    // 更新数据
    const onChangeData = (data = dataSource) => {
        const updateData = data
            .filter(item => isFilter(item))
            .map(item => ({
                name: item.name,
                value: item.value,
                description: item.description,
                type: item.type,
                link: item.link,
                checked: item.checked
            }));
        onChange && onChange(updateData);
    };

    // 上传文件按钮的点击事件
    const openImport = () => {
        uploadRef.current.click();
    };

    // 导入文件的功能
    const handleImport = (event, index) => {
        const file = event.target.files[0];
        const maxSize = 1048576 * 50; // 最大文件大小，单位为字节（1MB）

        if (!file) {
            return;
        }

        if (file.size > maxSize) {
            message.warning('文件大小超过了 50MB 的限制，请重新选择');
            return;
        }

        let formData = new FormData();
        formData.set('file', file);
        upload('/core/bos/file/create', formData).then(res => {
            if (!res?.bosUrl) {
                message.error('文件上传失败，请重试');
                return;
            }
            updateTableData([
                {type: 'value', value: file?.name},
                {type: 'link', value: res?.bosUrl}
            ], index, true);
        })
    };

    return (
        <table className={styles.table}>
            <thead>
                <tr>
                    <th>
                        {
                            (dataSource.length > 0) &&
                            <Checkbox
                                disabled={disabled}
                                checked={dataSource.length === checkedKeys.length && dataSource.length > 0}
                                onChange={(e) => {
                                    handleSelectAll(e);
                                }}
                            />
                        }
                    </th>
                    <th>KEY</th>
                    <th>VALUE</th>
                    <th>DESCRIPTION</th>
                </tr>
            </thead>
            <tbody>
                {tableData.map((item, index) => (
                    <tr key={`tabel_${String(index)}`}>
                        <td>
                            {
                                isFilter(item) &&
                                <Checkbox
                                    disabled={disabled}
                                    checked={checkedKeys.includes(index)}
                                    onChange={(e) => {
                                        handleSelectOption(e, index);
                                    }}
                                />
                            }
                        </td>
                        <td>
                            <Input
                                variant='borderless'
                                value={item?.name}
                                disabled={disabled}
                                placeholder='Key'
                                onChange={(e) => updateTableData([
                                    {type: 'name', value: e.target.value}], index)}
                                onBlur={(e) => {
                                    onChangeData(dataSource);
                                }}
                                addonAfter={
                                    <Select
                                        variant='borderless'
                                        value={item?.type}
                                        disabled={disabled}
                                        popupMatchSelectWidth={false}
                                        width={50}
                                        options={KEY_TYPE}
                                        onChange={(e) => {
                                            updateTableData([
                                                {type: 'type', value: e}], index, true);
                                        }}
                                    />
                                }
                            />
                        </td>
                        <td>
                            {/* File 类型 */}
                            {/* 未选择文件 */}
                            {
                                (item.type === 2 && isEmpty(item.link)) &&
                                <>
                                    <span
                                        className={styles.selectFile}
                                        onClick={openImport}
                                    >
                                        选择文件
                                    </span>
                                    <input
                                        ref={uploadRef}
                                        disabled={disabled}
                                        type="file"
                                        onChange={(e) => handleImport(e, index)}
                                        style={{display: 'none'}}
                                    />
                                </>
                            }
                            {/* 已选择文件 */}
                            {
                                (!disabled && item.type === 2 && !isEmpty(item.link)) &&
                                <>
                                    <span
                                        className={styles.selectFile}
                                    >
                                        {item.value}
                                        <CloseOutlined
                                            className={styles.selectFileDelIcon}
                                            onClick={() => {
                                                updateTableData([
                                                    {type: 'value', value: ''},
                                                    {type: 'link', value: ''}], index, true);
                                            }}
                                        />
                                    </span>
                                </>
                            }
                            {/* Text 类型 */}
                            {
                                item.type === 1 &&
                                <Input
                                    disabled={disabled}
                                    style={{width: 'calc(100% - 20px)'}}
                                    variant='borderless'
                                    value={item?.value}
                                    placeholder='Value'
                                    onChange={(e) => updateTableData([
                                        {type: 'value', value: e.target.value}], index)}
                                    onBlur={(e) => {
                                        onChangeData(dataSource);
                                    }}
                                />
                            }
                        </td>
                        <td>
                            <Input
                                disabled={disabled}
                                style={{width: 'calc(100% - 20px)'}}
                                variant='borderless'
                                value={item?.description}
                                placeholder='Description'
                                onChange={(e) => updateTableData([
                                    {type: 'description', value: e.target.value}], index)}
                                onBlur={(e) => {
                                    onChangeData(dataSource);
                                }}
                            />
                            {
                                (!disabled && isFilter(item)) &&
                                <DeleteOutlined
                                    className={styles.delIcon}
                                    onClick={() => {
                                        deleteTableData(index);
                                    }}
                                />
                            }
                        </td>
                    </tr>
                ))}
            </tbody>
        </table>
    );
}

export default FormDataTable;