import {useState, useEffect} from 'react';
import {
    Switch,
    Row,
    Col,
    Tooltip
} from 'antd';
import styles from './Redirect.module.less';
import {InfoCircleOutlined} from '@ant-design/icons';

const Redirect = (props) => {
    const {onChange, data, disabled} = props;
    const [isRedirect, setIsRedirect] = useState(true);

    useEffect(() => {
        setIsRedirect(data ?? true);
    }, [data]);

    return (
        <Row className={styles.paramsItem}>
            <Col flex='110px'>
                 <span className={styles.paramsItemTitle}>自动重定向</span>
                <Tooltip title="开启后，访问 301 / 302 时，将自动重定向到目标链接">
                    <InfoCircleOutlined className={styles.info} />
                </Tooltip>
            </Col>
            <Col flex='auto'>
                <Switch
                    disabled={disabled}
                    checked={isRedirect}
                    onChange={(value) => {
                        setIsRedirect(value);
                        onChange && onChange(value);
                    }}
                />
            </Col>
        </Row>
    );
};

export default Redirect;
