import {connectModel} from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import EditableTable from '../../common/Table/EditableTable';

function Headers(props) {
    const {data, onChange, disabled} = props;

    return (
        <EditableTable
            disabled={disabled}
            data={data}
            onChange={onChange}
        />
    );
}


export default connectModel([baseModel, commonModel], (state) => ({
}))(Headers);
