import {useState, useEffect} from 'react';
import {
    Select,
} from 'antd';
import {METHID_OPTIONS} from '../../../const';
import styles from './SelectMethod.module.less';

const SelectMethod = (props) => {
    const {onChange, data, disabled} = props;
    const [method, setMethod] = useState(1); // 请求方法

    useEffect(() => {
        setMethod(data ?? 1);
    }, [data]);

    return (
        <Select
            disabled={disabled}
            className={styles.methodSelect}
            popupMatchSelectWidth={false}
            options={METHID_OPTIONS}
            placeholder="请选择方法"
            value={method}
            onChange={(data) => {
                setMethod(data);
                onChange && onChange({method: data});
            }}
            labelRender={(option) => {
                let selectOption = METHID_OPTIONS?.find((item) => item.value === option?.value);
                return (
                    <span
                        style={{color: selectOption?.color}}
                        className={styles.methodSelectOption}
                    >
                        {selectOption?.label}
                    </span>
                );
            }}
            optionRender={(option) => {
                return (
                    <span
                        style={{color: option?.data?.color}}
                        className={styles.methodSelectOption}
                    >
                        {option.label}
                    </span>
                );
            }}
        />
    );
};

export default SelectMethod;
