import {Tooltip} from 'antd';
import classnames from 'classnames';
import styles from './index.module.less';

const OptionLabel = ({name, data}) => {
    return (
        <Tooltip title={data?.error?.status === 1 ? data?.error?.msg : ''}>
            <span
                className={classnames(
                    styles.optionLabel,
                )}
            >
                {name}

                {/* 数量 > 1 展示 */}
                {/* params / header / body (formdata / x-www-form-urlencoded) / 变量 / 断言 / 后置操作 */}
                {
                    (data?.status === 1 && data?.length > 0) &&
                    <span
                        className={classnames(
                            styles.count,
                            {[styles.errorOptionLabel]: data?.error?.status === 1}
                        )}
                    >
                        ({data?.length})
                    </span>
                }
                {/* 有值展示点 */}
                {/* body (json / raw) */}
                {
                    (data?.status === 1 && data?.value) &&
                    <span className={styles.point} />
                }
            </span>
        </Tooltip>

    );
};

export default OptionLabel;