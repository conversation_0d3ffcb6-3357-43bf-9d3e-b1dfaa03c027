import pares from 'json-to-ast';

const getJsonPath = (tree, selection, path = [], jsonPath = {}) => {
  tree.forEach(node => {
    path.push(node.key?.value);
    // 行匹配
    if (node.loc.start.line === selection.positionLineNumber) {
      jsonPath.path = '$.' + path.join('.');
      jsonPath.data = node.value?.value ?? '';
    }
    if (node?.value?.children) {
      jsonPath = getJsonPath(node.value.children, selection, path, jsonPath);
    }
    path.pop();
  });
  return jsonPath;
};

export const jsonToJsonPath = (value, selection) => {
  let astTree = pares(value);
  return getJsonPath(astTree.children, selection);
};