/*
可编辑表格组件
1、空数据行不可删除
2、空数据行添加数据后，自动添加一行新空数据行，并勾选上当前输入行
 */
import {useState, useEffect} from 'react';
import {Checkbox, Input} from 'antd';
import {DeleteOutlined} from '@ant-design/icons';
import {isObjectItemsHasNotNull, isObjectItemsAllNull} from '../utils';
import {DEAFULT_EDITTABLE_TR} from '../const';
import styles from './index.module.less';

function EditableTable(props) {
    const {data, onChange, disabled} = props;
    const [dataSource, setDataSource] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [checkedKeys, setCheckedKeys] = useState([]);

    useEffect(() => {
        setDataSource(data.map((item, index) => ({...item, index: index})));
    }, [data]);

    useEffect(() => {
        const checkedItems = dataSource.filter(item => item.checked);
        setCheckedKeys(checkedItems.map(item => item.index));
        setTableData([...dataSource, {...DEAFULT_EDITTABLE_TR, index: dataSource.length}]);
    }, [dataSource]);

    // 更新表格数据
    const updateTableData = (type, value, index) => {
        let newTableData = [];
        tableData.forEach((item, i) => {
            let data = {};
            if (i === index) {
                // 更新当前行数据
                data = {...item, [type]: value};
                // 如果当前行是空数据行，则默认勾选
                if (isObjectItemsAllNull(item) && value !== null) {
                    data.checked = true;
                }
            } else {
                data = {...item};
            }
            newTableData.push(data);
        });
        let data = newTableData
            .filter(item => isObjectItemsHasNotNull(item));
        setDataSource(data);
    };

    // 表格行全选
    const handleSelectAll = (e) => {
        let newCheckedKeys = [];
        if (e.target.checked) {
            newCheckedKeys = tableData
                .filter(item => isObjectItemsHasNotNull(item))
                .map((item, index) => {
                    return index;
                });
        } else {
            newCheckedKeys = [];
        }
        setCheckedKeys(newCheckedKeys);
        let data = tableData
            .filter(item => isObjectItemsHasNotNull(item))
            .map((item, index) => ({
                ...item,
                checked: newCheckedKeys.includes(index)
            }));
        onChangeData(data);
    };

    // 表格行单选
    const handleSelectOption = (e, index) => {
        let newDataSource = [...dataSource];
        newDataSource[index].checked = e.target.checked;
        setDataSource(newDataSource);
        onChangeData(newDataSource);
    };

    // 删除选中项
    const deleteTableData = (key) => {
        let newTableData = [];
        let _index = 0;
        tableData.forEach((item, index) => {
            if (key !== index) {
                newTableData.push({...item, index: _index});
                _index++;
            }
        });
        let data = newTableData
            .filter(item => isObjectItemsHasNotNull(item));
        setDataSource(data);
        onChangeData(data);
    };

    // 更新数据
    const onChangeData = (data = tableData) => {
        const updateData = data
            .filter(item => isObjectItemsHasNotNull(item))
            .map(item => ({
                name: item.name,
                value: item.value,
                description: item.description,
                checked: item.checked
            }));
        onChange && onChange(updateData);
    };

    return (
        <table className={styles.table}>
            <thead>
                <tr>
                    <th>
                        {
                            (dataSource.length > 0) &&
                            <Checkbox
                                disabled={disabled}
                                checked={dataSource.length === checkedKeys.length && dataSource.length > 0}
                                onChange={(e) => {
                                    handleSelectAll(e);
                                }}
                            />
                        }
                    </th>
                    <th>KEY</th>
                    <th>VALUE</th>
                    <th>DESCRIPTION</th>
                </tr>
            </thead>
            <tbody>
                {tableData.map((item, index) => (
                    <tr key={`tabel_${String(index)}`}>
                        <td>
                            {
                                isObjectItemsHasNotNull(item) &&
                                <Checkbox
                                    disabled={disabled}
                                    checked={checkedKeys.includes(index)}
                                    onChange={(e) => {
                                        handleSelectOption(e, index);
                                    }}
                                />
                            }
                        </td>
                        <td>
                            <Input
                                disabled={disabled}
                                variant='borderless'
                                value={item?.name}
                                placeholder='Key'
                                onChange={(e) => updateTableData('name', e.target.value, index)}
                                onBlur={(e) => {
                                    onChangeData(dataSource);
                                }}
                            />
                        </td>
                        <td>
                            <Input
                                disabled={disabled}
                                style={{width: 'calc(100% - 20px)'}}
                                variant='borderless'
                                value={item?.value}
                                placeholder='Value'
                                onChange={(e) => updateTableData('value', e.target.value, index)}
                                onBlur={(e) => {
                                    onChangeData(dataSource);
                                }}
                            />
                        </td>
                        <td>
                            <Input
                                disabled={disabled}
                                style={{width: 'calc(100% - 20px)'}}
                                variant='borderless'
                                value={item?.description}
                                placeholder='Description'
                                onChange={(e) => updateTableData('description', e.target.value, index)}
                                onBlur={(e) => {
                                    onChangeData(dataSource);
                                }}
                            />
                            {
                                (!disabled && isObjectItemsHasNotNull(item)) &&
                                <DeleteOutlined
                                    className={styles.delIcon}
                                    onClick={() => {
                                        deleteTableData(index);
                                    }}
                                />
                            }
                        </td>
                    </tr>
                ))}
            </tbody>
        </table>
    );
}

export default EditableTable;