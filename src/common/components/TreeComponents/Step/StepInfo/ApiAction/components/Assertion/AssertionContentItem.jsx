import React from 'react';
import {Select, Input, Space} from 'antd';
import {ASSERT_OPTIONS} from './const';

const {Option} = Select;

const AssertionContentItem = ({
    localAssertion,
    editType,
    handleAssertTypeChange,
    handleAssertTargetChange,
    handleJsonPathChange,
    handleJsonPathBlur,
    handleTypeChange,
    handleDataChange,
    handleDataBlur,
    stepType = ''
}) => {
    return (
        <div
            style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
                backgroundColor: '#fff'
            }}
        >
            <div style={{display: 'flex', width: '100%', alignItems: 'center'}}>
                <div style={{marginLeft: '50px'}}>断言方式 &nbsp;&nbsp;</div>
                <Select
                    style={{width: '100%', flex: 1}}
                    value={localAssertion.assertType}
                    onChange={handleAssertTypeChange}
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                >
                    <Option value={1}>Response JSON</Option>
                    {stepType === 'http' && (
                        <>
                            <Option value={2}>Response Text</Option>
                            <Option value={3}>Response JSON Schema</Option>
                        </>
                    )}
                </Select>
            </div>
            <div style={{display: 'flex', width: '100%', alignItems: 'center'}}>
                <div style={{marginLeft: '50px'}}>断言对象 &nbsp;&nbsp;</div>
                <Select
                    style={{width: '100%', flex: 1}}
                    value={localAssertion.assertTarget}
                    onChange={handleAssertTargetChange}
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                >
                    {stepType === 'http' && <Option value={1}>Response Header</Option>}

                    <Option value={2}>Response Body</Option>
                </Select>
            </div>

            {localAssertion.assertType === 1 && (
                <div style={{display: 'flex', width: '100%', alignItems: 'center'}}>
                    <div>JSONPath 表达式 &nbsp;&nbsp;</div>
                    <Input
                        placeholder="JSON Path 表达式，如: $.store.book[0].title"
                        value={localAssertion.assertContent?.jsonPath || ''}
                        onChange={handleJsonPathChange}
                        onBlur={handleJsonPathBlur}
                        disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                        style={{width: '100%', flex: 1}}
                    />
                </div>
            )}

            {localAssertion.assertType === 3 ? (
                <div style={{display: 'flex', width: '100%', justifyContent: 'center'}}>
                    <div style={{marginLeft: '50px'}}>断言内容 &nbsp;&nbsp;</div>
                    <Input.TextArea
                        placeholder="请输入JSON Schema"
                        value={localAssertion.assertContent?.data || ''}
                        onChange={handleDataChange}
                        onBlur={handleDataBlur}
                        disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                        style={{width: '100%', flex: 1}}
                        rows={4}
                    />
                </div>
            ) : (
                <div style={{display: 'flex', width: '100%', alignItems: 'center'}}>
                    <div style={{marginLeft: '78px'}}>断言 &nbsp;&nbsp;</div>
                    <div
                        style={{
                            width: '100%',
                            flex: 1
                        }}
                    >
                        <Space.Compact style={{width: '100%'}}>
                            <Select
                                style={{width: 210}}
                                placeholder="请选择操作符"
                                value={localAssertion.assertContent?.type || 3}
                                onChange={handleTypeChange}
                                disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                            >
                                {
                                    ASSERT_OPTIONS.map((item) => (
                                        <Select.Option key={item.value} value={item.value}>
                                            {item.label}
                                        </Select.Option>
                                    ))
                                }
                            </Select>
                            <Input
                                placeholder="请输入断言条件"
                                value={localAssertion.assertContent?.data || ''}
                                onChange={handleDataChange}
                                onBlur={handleDataBlur}
                                disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                                style={{width: '100%'}}
                            />
                        </Space.Compact>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AssertionContentItem;
