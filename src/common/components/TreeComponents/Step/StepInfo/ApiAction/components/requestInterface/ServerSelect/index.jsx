import { But<PERSON>, Divider, Select } from 'antd';
import { useState, useEffect, useMemo, useRef } from 'react';
import { SettingOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import RunCaseSettingModal from 'FEATURES/front_qe_tools/case/edit/EditPage/Modal/RunCaseSettingModal/RunCaseSettingModal';
import styles from './ServerSelect.module.less';

function ServerSelect(props) {
    const { onChange, disabled, data, serverList, setShowModal } = props;
    const [serverId, setServerId] = useState(null);
    const runCaseSettingModalRef = useRef();

    useEffect(() => {
        setServerId(data);
    }, [data]);

    const transServerManagement = () => {
        setShowModal(true);
        runCaseSettingModalRef?.current.show({ key: 'server-config' });
    };

    const SERVER_OPTIONS = useMemo(() => {
        return serverList?.map((item) => ({
            label: item.serverName,
            value: item.serverId
        }));
    }, [serverList]);

    return (
        <>
            <Select
                disabled={disabled}
                className={styles.methodSelect}
                placeholder="请选择服务"
                popupMatchSelectWidth={false}
                value={serverId}
                options={SERVER_OPTIONS}
                onChange={(value) => {
                    setServerId(value);
                    onChange && onChange(value);
                }}
                dropdownRender={(menu) => (
                    <div>
                        {menu}
                        <Divider style={{ margin: '5px 0' }} />
                        <Button
                            style={{
                                width: '96%',
                                margin: '0 2%',
                                borderRadius: 6
                            }}
                            type="text"
                            onClick={transServerManagement}
                            icon={<SettingOutlined />}
                        >
                            Server 管理
                        </Button>
                    </div>
                )}
            />
            <RunCaseSettingModal ref={runCaseSettingModalRef} />
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    serverList: state.common.case.serverList,
    showModal: state.common.base.showModal
}))(ServerSelect);
