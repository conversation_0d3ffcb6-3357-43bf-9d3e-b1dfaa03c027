import { Input, Radio, Select } from 'antd';
import { useEffect, useState } from 'react';
import CollapsePanel from '../common/CollapsePanel/CollapsePanel';
import styles from './VariableExtraction.module.less';

const { Option } = Select;

// 单个变量提取项组件
const VariableItem = ({ variable, onRemove, onChange, editType, onCopy, stepType = '' }) => {
    // 本地状态用于控制组件内的值
    const [localVariable, setLocalVariable] = useState({});
    const [hasError, setHasError] = useState(false);
    // 组件挂载或 variable 变化时更新本地状态
    useEffect(() => {
        const varData = JSON.parse(JSON.stringify(variable));
        setLocalVariable({
            ...varData,
            extractTarget: varData.extractTarget || 2
        });
        validateVariable(varData);
    }, [variable.id]);

    // 验证变量是否有空值
    const validateVariable = (varData) => {
        const isEmpty = !varData.extractScope;
        const needsJsonPath = varData.extractScope === 2 && varData.extractTarget === 2;
        const jsonPathEmpty = needsJsonPath && !varData.jsonPath?.trim();
        setHasError(isEmpty || jsonPathEmpty);
    };

    // 处理变量名称变更
    const handleVariableNameChange = (e) => {
        const newVariable = {
            ...localVariable,
            extractName: e.target.value
        };
        setLocalVariable(newVariable);
        validateVariable(newVariable);
        onChange(variable.id, newVariable, false);
    };

    // 处理变量名称失焦
    const handleVariableNameBlur = () => {
        onChange(variable.id, localVariable, true);
    };

    // 处理提取来源变更
    const handleSourceChange = (value) => {
        const newVariable = {
            ...localVariable,
            extractTarget: value
        };
        setLocalVariable(newVariable);
        validateVariable(newVariable);
        onChange(variable.id, newVariable, true);
    };

    // 处理提取范围变更
    const handleScopeChange = (value) => {
        const newVariable = {
            ...localVariable,
            extractScope: value
        };
        setLocalVariable(newVariable);
        validateVariable(newVariable);
        onChange(variable.id, newVariable, true);
    };

    // 处理JSONPath表达式变更
    const handleJsonPathChange = (e) => {
        const newVariable = {
            ...localVariable,
            jsonPath: e.target.value
        };
        setLocalVariable(newVariable);
        validateVariable(newVariable);
        onChange(variable.id, newVariable, false);
    };

    // 处理JSONPath表达式失焦
    const handleJsonPathBlur = () => {
        onChange(variable.id, localVariable, true);
    };

    // 处理选中状态变更
    const handleCheckedChange = (id, checked) => {
        const newVariable = {
            ...localVariable,
            checked: checked
        };
        setLocalVariable(newVariable);
        onChange(id, newVariable, true);
    };

    // 获取变量标题
    const getVariableTitle = () => {
        // 获取提取来源文本
        const getSourceText = () => {
            if (localVariable.extractTarget === 1) {
                return 'Response Headers';
            }
            if (localVariable.extractTarget === 2) {
                return 'Response JSON';
            }
            return '';
        };

        // 获取JSONPath或范围文本
        const getScopeText = () => {
            if (localVariable.extractTarget === 2) {
                if (localVariable.extractScope === 1) {
                    return '(整个返回数据)';
                }
                if (localVariable.extractScope === 2 && localVariable.jsonPath) {
                    return `(${localVariable.jsonPath})`;
                }
            }
            return '';
        };

        return (
            <>
                提取变量
                <span style={{ fontSize: '12px', marginLeft: '8px' }}>
                    {localVariable.extractName || ''}{' '}
                </span>
                <span style={{ fontSize: '12px', color: '#888', marginLeft: '8px' }}>
                    {getSourceText()} {getScopeText()}
                </span>
            </>
        );
    };

    // 面板内容
    const panelContent = (
        <>
            <div
                style={{
                    display: 'flex',
                    width: '100%',
                    alignItems: 'center'
                    // marginBottom: '16px'
                }}
            >
                <div className={styles.formItemLeft}>变量名称 &nbsp;&nbsp;</div>
                <Input
                    placeholder="请输入变量名称"
                    value={localVariable.extractName || ''}
                    onChange={handleVariableNameChange}
                    onBlur={handleVariableNameBlur}
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    className={styles.formItemRight}
                />
            </div>

            <div
                style={{
                    display: 'flex',
                    width: '100%',
                    alignItems: 'center'
                    // marginBottom: '16px'
                }}
            >
                <div className={styles.formItemLeft}>提取来源 &nbsp;&nbsp;</div>
                <Select
                    className={styles.formItemRight}
                    value={localVariable.extractTarget}
                    onChange={handleSourceChange}
                    placeholder="请选择提取来源"
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                >
                    {stepType === 'http' && <Option value={1}>Response Headers</Option>}
                    <Option value={2}>Response JSON</Option>
                </Select>
            </div>
            {localVariable.extractTarget === 2 && (
                <div
                    style={{
                        display: 'flex',
                        width: '100%',
                        alignItems: 'center'
                        // marginBottom: '16px'
                    }}
                >
                    <div className={styles.formItemLeft}>提取范围 &nbsp;&nbsp;</div>
                    <Radio.Group
                        className={styles.formItemRight}
                        value={localVariable.extractScope}
                        onChange={(e) => handleScopeChange(e.target.value)}
                        disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    >
                        <Radio value={1}>整个返回数据</Radio>
                        <Radio value={2}>提取部分</Radio>
                    </Radio.Group>
                </div>
            )}
            {localVariable.extractScope === 2 && localVariable.extractTarget === 2 && (
                <div style={{ display: 'flex', width: '100%', alignItems: 'center' }}>
                    <div className={styles.formItemLeft}>JSONPath 表达式 &nbsp;&nbsp;</div>
                    <Input
                        placeholder="例: $.store.book[0].title"
                        value={localVariable.jsonPath || ''}
                        onChange={handleJsonPathChange}
                        onBlur={handleJsonPathBlur}
                        disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                        className={styles.formItemRight}
                    />
                </div>
            )}
        </>
    );

    return (
        <CollapsePanel
            id={variable.id}
            title={getVariableTitle()}
            checked={localVariable.checked || false}
            error={hasError}
            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
            onCheckedChange={handleCheckedChange}
            onCopy={onCopy}
            onRemove={onRemove}
            showCopy
            showDelete={!['readonly', 'debug', 'execute']?.includes(editType)}
        >
            {panelContent}
        </CollapsePanel>
    );
};

export default VariableItem;
