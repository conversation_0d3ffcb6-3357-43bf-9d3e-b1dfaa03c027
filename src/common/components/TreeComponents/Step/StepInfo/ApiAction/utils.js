// 数据量展示

const calcData = (data, type) => {
    if (type === 'length') {
        if (data?.length === 0) {
            return {status: 0};
        }
        if (data?.length > 0) {
            return {status: 1, length: data?.length};
        }
    }

    if (type === 'string') {
        if (data === '') {
            return {status: 0, value: false};
        }
        if (data !== '') {
            return {status: 1, value: true};
        }
    }
};
export const getDataLength = (step, type) => {
    let requestParams = step?.stepInfo?.requestParams;
    let variableExtract = step?.stepInfo?.variableExtract;
    let responseAssert = step?.stepInfo?.responseAssert;
    let postOperation = step?.stepInfo?.postOperation;
    switch (type) {
        // 请求参数 params
        case 'params':
            return calcData(requestParams?.params, 'length');
        // 请求参数 headers
        case 'headers':
            return calcData(requestParams?.headers, 'length');
        // 请求参数 body
        case 'body':
            // none
            if (requestParams?.body?.type === 1) {
                return {status: 0, value: false};
            }
            // form-data
            if (requestParams?.body?.type === 2) {
                return calcData(requestParams?.body?.formData, 'length');
            }
            // x-www-form-urlencoded
            if (requestParams?.body?.type === 3) {
                return calcData(requestParams?.body?.formUrlencoded, 'length');
            }
            // json
            if (requestParams?.body?.type === 4) {
                return calcData(requestParams?.body?.json, 'string');
            }
            // raw
            if (requestParams?.body?.type === 5) {
                return calcData(requestParams?.body?.raw, 'string');
            }
        // 变量提取
        case 'variable_extraction':
            let variable_extraction = calcData(variableExtract, 'length');
            // 若 json path 为空，则报错
            if (variableExtract && variable_extraction?.status === 1) {
            for (let item of variableExtract) {
                if (item?.jsonPath === '') {
                    variable_extraction.error = {status: 1, msg: '存在 JSONPath 未填写'};
                    break;
                }
            }
        }
            return variable_extraction;
        // 断言
        case 'response_assert':
            let response_assert = calcData(responseAssert, 'length');
            // 若 json / text / scheme 为空，则报错
            if (responseAssert && response_assert?.status === 1) {
            for (let item of responseAssert) {
                if ((item?.assertContent?.jsonPath === '' || item?.assertContent?.data === '') && item?.assertType === 1) {
                    response_assert.error = {status: 1, msg: '存在未填写项'};
                    break;
                }
            }
        }
            return response_assert;
        // 后置操作
        case 'post_operation':
            let post_operation = calcData(postOperation, 'length');
             // 若 json / text / scheme 为空，则报错
             if (postOperation && post_operation?.status === 1) {
             for (let item of postOperation) {
                if (!item?.stepInfo?.dbId) {
                    post_operation.error = {status: 1, msg: '存在数据库服务缺失'};
                    break;
                }
                if (item?.stepInfo?.sqlStatement === 1) {
                    post_operation.error = {status: 1, msg: '存在 SQL 语句缺失'};
                    break;
                }
            }
        }
            return post_operation;
        default:
            break;
    }
};
