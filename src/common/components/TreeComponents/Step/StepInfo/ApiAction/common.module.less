// module
.url,
.tabs {
    margin-bottom: 15px;
}

// title 
.rpcHeader,
.dbHeader {
    padding: 10px 20px;

    :global {

        .ant-select-selector,
        .ant-input {
            border-radius: 2px !important;
        }
    }
}

.dbHeader {
    .title {
        padding-left: 3px;
        height: 32px;
        line-height: 32px;
        width: 100px;
        cursor: default;
    }

    .right {
        width: calc(100% - 100px);
    }
}

// options tabs
.tabs {
    margin-top: 10px;

    .tabItem {
        padding: 8px 3px;
        margin-right: 10px;
        color: #777;
        font-size: 12px;
        cursor: pointer;

        &:hover {
            color: #000;
        }
    }

    .activedTabItem {
        color: #000;
        border-bottom: 2px solid #1677ff;
    }

    .count {
        padding: 0 2px;
        color: #1677ff;
    }
}

.tabContent {
    padding: 5px 2px;
    height: calc(100vh - 200px);
    overflow: scroll;
}

.largeContent {
    padding: 15px 2px;
    height: calc(100vh - 200px);
    overflow: scroll;
}

.formItemLeft {
    width: 130px;
    text-align: right;
}

.formItemRight {
    flex: 1;
    width: 100%;
}

.formItemLeftSmall {
    width: 100px;
    text-align: right;
}

.formItemRightSmall {
    flex: 1;
    width: 100%;
}