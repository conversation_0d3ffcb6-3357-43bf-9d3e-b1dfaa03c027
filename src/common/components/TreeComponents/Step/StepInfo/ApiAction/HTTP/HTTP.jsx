import {Input, Space} from 'antd';
import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {connectModel} from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import {TAB_HTTP_OPTIONS} from '../const';
import {getDataLength} from '../utils';
// common
import OptionLabel from '../components/common/OptionLabel';
// request interface
import SelectMethod from '../components/requestInterface/SelectMethod';
import ServerSelect from '../components/requestInterface/ServerSelect';
// request params
import Params from '../components/requestParams/Params';
import Headers from '../components/requestParams/Headers';
import Body from '../components/requestParams/Body';

import Assertion from '../components/Assertion';
import PostOperation from '../components/PostOperation';
import VariableExtraction from '../components/VariableExtraction';

// more-config
import Redirect from '../components/moreConfig/Redirect';
import Timeout from '../components/moreConfig/Timeout';

import styles from './HTTP.module.less';


function HTTP(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [path, setPath] = useState('');
    const [tabItem, setTabItem] = useState('params');

    const updateCurStep = (params) => {
        let newCurrentStep = {...currentStep};
        for (let item of params) {
            const {data, module, type, obj} = item;
            if (obj) {
                newCurrentStep.stepInfo[module][type][obj] = data;
            } else if (type) {
                newCurrentStep.stepInfo[module][type] = data;
            } else if (module) {
                newCurrentStep.stepInfo[module] = data;
            }
        }
        handleUpdateStep(newCurrentStep);
    };

    const onMethodChange = ({method}) => {
        currentStep.stepInfo.method = method;
        handleUpdateStep(currentStep);
    };

    const onServerChange = (serverId) => {
        currentStep.stepInfo.requestInterface.serverId = serverId;
        handleUpdateStep(currentStep);
    };

    const onPathChange = (path) => {
        currentStep.stepInfo.requestInterface.path = path;
        handleUpdateStep(currentStep);
    };

    useEffect(() => {
        setPath(currentStep?.stepInfo?.requestInterface?.path || '');
    }, [currentStep?.stepId]);

    return (
        <div className={styles.rpcHeader}>
            {/* 请求 url */}
            <Space.Compact style={{width: '100%'}}>
                <SelectMethod
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    data={currentStep?.stepInfo?.method}
                    onChange={onMethodChange}
                />
                <ServerSelect
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    data={currentStep?.stepInfo?.requestInterface?.serverId}
                    onChange={onServerChange}
                />
                <Input
                    value={path}
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    placeholder="请输入要请求的path"
                    onChange={(e) => {
                        setPath(e.target.value);
                    }}
                    onBlur={() => {
                        onPathChange(path);
                    }}
                />
            </Space.Compact>
            {/* 具体配置 */}
            <div className={styles.tabs}>
                {TAB_HTTP_OPTIONS?.map(item => {
                    return (
                        <span
                            key={'tab-' + item.value}
                            className={classnames(styles.tabItem, {
                                [styles.activedTabItem]: tabItem === item.value,
                            })}
                            onClick={() => {
                                setTabItem(item.value);
                            }}
                        >
                            <OptionLabel
                                name={item.label}
                                data={getDataLength(currentStep, item.value)}
                            />
                        </span>
                    );
                })}
            </div>
            {
                ['params']?.includes(tabItem) && (
                    <div className={styles.tabContent}>
                        <Params
                            editType={editType}
                            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                            data={currentStep?.stepInfo?.requestParams?.params ?? []}
                            onChange={(data) => {
                                updateCurStep([{
                                    data: data,
                                    module: 'requestParams',
                                    type: 'params'
                                }]);
                            }}
                        />
                    </div>
                )
            }
            {
                ['headers']?.includes(tabItem) && (
                    <div className={styles.tabContent}>
                        <Headers
                            editType={editType}
                            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                            data={currentStep?.stepInfo?.requestParams?.headers ?? []}
                            onChange={(data) => {
                                updateCurStep([{
                                    data: data,
                                    module: 'requestParams',
                                    type: 'headers'
                                }]);
                            }}
                        />
                    </div>
                )
            }
            {
                ['body']?.includes(tabItem) && (
                    <div className={styles.tabContent}>
                        <Body
                            editType={editType}
                            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                            currentStep={currentStep}
                            data={currentStep?.stepInfo?.requestParams?.body}
                            onChange={(params) => {
                                let datas = [];
                                for (let item of params) {
                                    const {data, type} = item;
                                    datas.push({
                                        data: data,
                                        module: 'requestParams',
                                        type: 'body',
                                        obj: type
                                    });
                                }
                                updateCurStep(datas);
                            }}
                        />
                    </div>
                )
            }
            {['variable_extraction']?.includes(tabItem) && (
                <div className={styles.tabContent}>
                    <VariableExtraction
                        editType={editType}
                        data={currentStep?.stepInfo?.variableExtract}
                        onChange={(value) => {
                            updateCurStep([{
                                data: value,
                                module: 'variableExtract'
                            }]);
                        }}
                        stepType={'http'}
                    />
                </div>
            )}
            {['response_assert']?.includes(tabItem) && (
                <div className={styles.tabContent}>
                    <Assertion
                        editType={editType}
                        data={currentStep?.stepInfo?.responseAssert}
                        onChange={(value) => {
                            updateCurStep([{
                                data: value,
                                module: 'responseAssert'
                            }]);
                        }}
                        stepType={'http'}
                    />
                </div>
            )}
            {['post_operation']?.includes(tabItem) && (
                <div className={styles.tabContent}>
                    <PostOperation
                        editType={editType}
                        data={currentStep?.stepInfo?.postOperation}
                        onChange={(value) => {
                            updateCurStep([{
                                data: value,
                                module: 'postOperation'
                            }]);
                        }}
                    />
                </div>
            )}
            {
                ['more_config']?.includes(tabItem) && (
                    <div className={styles.tabContent}>
                        <Timeout
                            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                            data={currentStep?.stepInfo?.moreConfig?.timeout}
                            onChange={(data) => {
                                updateCurStep([{
                                    data: data,
                                    module: 'moreConfig',
                                    type: 'timeout'
                                }]);
                            }}
                        />
                        <Redirect
                            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                            data={currentStep?.stepInfo?.moreConfig?.isRedirect}
                            onChange={(data) => {
                                updateCurStep([{
                                    data: data,
                                    module: 'moreConfig',
                                    type: 'isRedirect'
                                }]);
                            }}
                        />
                    </div>
                )
            }
        </div>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    serverList: state.common.case.serverList
}))(HTTP);
