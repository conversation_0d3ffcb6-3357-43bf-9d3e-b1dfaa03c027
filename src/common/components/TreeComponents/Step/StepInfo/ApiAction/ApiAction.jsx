import HTTP from './HTTP';
import SQL from './SQL';
import Redis from './Redis';
import styles from './ApiAction.module.less';

function ApiAction(props) {
    const { currentStep, descExtra } = props;

    return (
        <div className={styles.action}>
            {descExtra}
            {currentStep?.stepType === 1001 && <HTTP {...props} />}
            {currentStep?.stepType === 1101 && <SQL {...props} />}
            {currentStep?.stepType === 1201 && <Redis {...props} />}
        </div>
    );
}

export default ApiAction;
