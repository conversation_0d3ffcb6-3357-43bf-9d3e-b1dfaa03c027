// 具体配置项
export const TAB_HTTP_OPTIONS = [
    {value: 'params', label: 'Params'},
    {value: 'headers', label: 'Headers'},
    {value: 'body', label: 'Body'},
    {value: 'variable_extraction', label: '变量提取'},
    {value: 'response_assert', label: '返回断言'},
    {value: 'post_operation', label: '后置操作'},
    {value: 'more_config', label: '更多设置'}
];
export const TAB_SQL_OPTIONS = [
    {value: 'sql', label: 'SQL 语句'},
    {value: 'variable_extraction', label: '变量提取'},
    {value: 'response_assert', label: '返回断言'},
    {value: 'post_operation', label: '后置操作'},
];
export const TAB_REDIS_OPTIONS = [
    {value: 'redis', label: 'Redis 语句'},
    {value: 'variable_extraction', label: '变量提取'},
    {value: 'response_assert', label: '返回断言'},
    {value: 'post_operation', label: '后置操作'},
];

// 请求方法
export const METHID_OPTIONS = [
    {value: 1, label: 'GET', color: '#67ad5b'},
    {value: 2, label: 'POST', color: '#eb903a'},
    {value: 3, label: 'PUT', color: '#448ef7'},
    {value: 4, label: 'PATCH', color: '#d84292'},
    {value: 5, label: 'DELETE', color: '#e76033'},
    {value: 6, label: 'HEAD', color: '#377d3b'},
    {value: 7, label: 'OPTIONS', color: '#415be3'}
];

// body 类型
export const BODY_TYPE_MAP = {
    1: {
        label: 'none',
        type: 'none',
        init: ''
    },
    2: {
        label: 'form-data',
        type: 'formData',
        init: []
    },
    3: {
        label: 'x-www-form-urlencoded',
        type: 'formUrlencoded',
        init: []
    },
    4: {
        label: 'json',
        type: 'json',
        init: ''
    },
    5: {
        label: 'raw',
        type: 'raw',
        init: ''
    }
};