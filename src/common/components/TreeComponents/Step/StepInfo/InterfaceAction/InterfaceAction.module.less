@import "RESOURCES/css/common.less";

.action {
    padding: 0 20px;
    width: 100%;
    height: 100%;
    overflow: scroll;
    // width: calc(100% -30px);
    // margin: 15px;
}
.header {
    display: flex;
    align-items: center;
    .right {
        flex: 1;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        justify-content: space-between;
    }
}
.noContent {
    margin-top: 100px;
}

.stepOperator {
    padding: 0 5px 5px 5px;
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    z-index: 99;
}

.defaultManualAction {
    margin-top: 5px;
}

.manualAction {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.manualActionWithExecute {
    text-align: center;
}

.uploadBtnList {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.uploadBtn {
    position: relative;
    display: inline-block;
    width: 100px;
    height: 100px;
    line-height: 100px;
    border: 1px solid var(--border-color);
    border-radius: 100%;
    background-color: var(--background-color);
}

:global {

    .ant-upload-list,
    .custom-default-upload-list,
    .custom-dark-upload-list {
        display: none;
    }
}

.uploadBtnIcon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 50px;
    color: #bfbfbf;
}

.uploadBtn:hover .uploadBtnIcon {
    color: #709cf9;
}