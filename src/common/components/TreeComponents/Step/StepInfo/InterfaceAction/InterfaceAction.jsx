import {useState, useEffect, useRef, useMemo} from 'react';
import {Spin, message, Tabs, Select, Button, Tooltip} from 'antd'; // 引入Tabs和Button
import {methodOptions} from 'COMMON/components/TreeComponents/Step/utils.js';
import Postman from './Postman';
import styles from './InterfaceAction.module.less';

const safeJsonParse = (value) => {
    if (value && typeof value === 'object') {
        return value;
    }
    if (typeof value === 'string') {
        return JSON.parse(value);
    }
};

function InterFacePostman(props) {
    const {currentStep, editType,
        handleUpdateStep = () => { }, stepId} = props;
    const [messageApi, contextHolder] = message.useMessage();
    const [httpMethod, setHttpMethod] = useState(null); // 默认选择POST
    const [postmanData, setPostmanData] = useState({});
    const [blurTriggered, setBlurTriggered] = useState(false);
    const debounceRequest = useRef();
    const handleMethodChange = (value) => {
        setHttpMethod(value);
        handleChangePostman({
            method: value
        });
    };

    useEffect(() => {
        setHttpMethod(currentStep?.stepInfo?.method || 2);
    }, [currentStep?.stepInfo]);

    const expandRoot = useRef();
    const onBlurUpdateStep = (isDebounce = true) => {
        if (debounceRequest.current) {
            clearTimeout(debounceRequest.current);
        }
        if (isDebounce) {
            debounceRequest.current = setTimeout(async () => {
                setBlurTriggered(true);
            }, 500);
        } else {
            handleChangePostman();
        }
    };
    useEffect(() => {
        if (blurTriggered) {
            // 监听值变化并在 blur 时发送请求
            handleChangePostman().then(() => setBlurTriggered(false));
        }
    }, [blurTriggered, postmanData]);

    const selectedOption = useMemo(
        () => methodOptions.find((option) => option.value === httpMethod),
        [httpMethod]
    );
    const handleChangePostman = async (stepInfo = {}) => {
        // console.log('postmanData', postmanData, stepInfo, httpMethod);
        let parsedJson = postmanData?.requestParams?.body?.json || '';
        let parsedRaw = postmanData?.requestParams?.body?.raw || '';
        try {
            if (
                postmanData?.requestParams?.body?.json &&
                postmanData?.requestParams?.body?.json !== ''
            ) {
                // console.log('postmanData?.requestParams?.body?.json', postmanData?.requestParams?.body?.json);
                parsedJson = safeJsonParse(postmanData.requestParams.body.json);
            }
        } catch (error) {
            // messageApi.error('解析JSON失败，请检查JSON格式');
            return;
        }
        try {
            await handleUpdateStep({
                ...currentStep,
                stepIdList: [stepId],
                stepInfo: {
                    type: 301,
                    ...postmanData,
                    method: httpMethod,
                    ...stepInfo,
                    // 使用转换后的数据
                    requestParams: {
                        ...postmanData?.requestParams,
                        body: {
                            ...postmanData?.requestParams?.body,
                            json: parsedJson,
                            raw: parsedRaw
                        }
                    },
                    responseAssert: {
                        ...postmanData?.responseAssert,
                        assertFullDiff: postmanData?.responseAssert?.assertFullDiff ?? false
                    }
                }
                // stepDesc:'新建步骤'
            });
            // messageApi.success('保存成功');
        } catch (error) {
            console.log(error);
        }
    };
    return (
        <>
            {contextHolder}
            <div
                className={styles.action}
                ref={expandRoot}
                style={{paddingLeft: 0}}
                key={currentStep.stepId}
            >
                <div className={styles.header}>
                    {/* HTTP 选择 */}
                    {/* <Select
                        // bordered={true}
                        size="mini"
                        value={httpMethod}
                        onChange={handleMethodChange}
                        style={{ marginLeft: 12, width: 100, color: selectedOption?.color }}
                        labelRender={() => (
                            <span style={{ color: selectedOption?.color, fontWeight: 'bold' }}>
                                {selectedOption?.label}
                            </span>
                        )}
                        disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    >
                        {methodOptions.map((option) => (
                            <option
                                style={{ color: option.color, fontWeight: 'bold' }}
                                key={option.value}
                                value={option.value}
                            >
                                {option.label}
                            </option>
                        ))}
                    </Select> */}

                    {/* 动作名称 */}
                    <div className={styles.right}>
                        {/* <div className={stepStyles.stepIndex}>
                            {getName(currentStep.stepType, currentStep.stepInfo)}
                        </div> */}
                        {/* 执行按钮 */}
                        {/* 不支持单步调试 */}
                        {/* <Tooltip placement="left" title="发送">
                        <PlayCircleOutlined
                            className={stepStyles.stepIndex}
                            style={{ cursor: 'pointer' }}
                            onClick={run}
                        />
                    </Tooltip> */}
                    </div>
                    {/* <div>{blurTriggered ? '保存中' : '已保存'}</div> */}
                </div>
                <div style={{paddingLeft: 20}}>
                    <Postman
                        postmanData={postmanData}
                        stepId={currentStep.stepId}
                        setPostmanData={(e) => {
                            // console.log(e, postmanData?.requestInterface?.path);
                            setPostmanData(e);
                        }}
                        onBlurUpdateStep={(e) => {
                            // console.log('blur',postmanData?.requestInterface?.path);
                            onBlurUpdateStep(e);
                        }}
                        httpMethod={httpMethod}
                        onMethodChange={handleMethodChange}
                        currentStep={currentStep}
                        editType={editType}
                    />
                </div>
            </div>
        </>
    );
}

function InterfaceAction(props) {
    const {currentStep} = props;
    return <InterFacePostman key={currentStep?.stepId} stepId={currentStep?.stepId} {...props} />;
}
export default InterfaceAction;
