import { useState, useEffect, useMemo } from 'react';
import { Button, Form, InputNumber, Select, Switch } from 'antd';
import { connectModel } from 'COMMON/middleware';
import classnames from 'classnames';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import {CardTitle} from 'COMMON/components/common/Card';
import styles from './Postman.module.less';

const { Option } = Select;

const ParameterExtraction = (props) => {
    const { fieldValue, setFieldValue, onBlurUpdateStep, currentStep, editType } = props;
    const [autoRedirect, setAutoRedirect] = useState(false);
    const [stepForward, setStepForward] = useState(0);
    const [stepInterval, setStepInterval] = useState(0);
    const [timeout, setTimeoutValue] = useState(0);
    const setCommonFieldValue = (field, value) => {
        setFieldValue('common', {
            ...fieldValue.common,
            [field]: value
        });
    };
    const setApiFieldValue = (field, value) => {
        setFieldValue('apiConfig', {
            ...fieldValue.apiConfig,
            [field]: value
        });
    };
    useEffect(() => {
        // 初始化状态变量的值
        setStepForward(currentStep?.stepInfo?.common?.stepForward || 0);
        setStepInterval(currentStep?.stepInfo?.common?.stepInterval || 0);
        setTimeoutValue(currentStep?.stepInfo?.common?.timeout || 0);
        setAutoRedirect(currentStep?.stepInfo?.apiConfig?.isRedirect ?? true);
    }, [currentStep?.stepId]);

    const disabled = useMemo(() => {
        return ['readonly', 'debug', 'execute']?.includes(editType);
    }, [editType]);
    return (
        <div>
            {/* <h tstyle={{ padding: '0 10px',margin: '0px 10px', marginBottom: '10px' }} /> */}
            <CardTitle text="步骤参数" style={{ margin: '0px 10px', marginBottom: '10px' }} />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>操作前等待</span>
                <InputNumber
                    className={classnames('input_editor', styles.paramsSelectData)}
                    defaultValue="5"
                    addonAfter="秒"
                    max={300}
                    onChange={(value) => {
                        setStepForward(value);
                        setCommonFieldValue('stepForward', value);
                    }}
                    disabled={disabled}
                    value={stepForward || 0}
                    min={0}
                    onBlur={onBlurUpdateStep}
                />
            </div>
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>操作后等待</span>
                <InputNumber
                    className={classnames('input_editor', styles.paramsSelectData)}
                    defaultValue="5"
                    addonAfter="秒"
                    disabled={disabled}
                    min={0}
                    max={300}
                    value={stepInterval || 0}
                    onChange={(value) => {
                        setStepInterval(value);
                        setCommonFieldValue('stepInterval', value);
                    }}
                    onBlur={onBlurUpdateStep}
                />
            </div>
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>超时时间</span>
                <InputNumber
                    className={classnames('input_editor', styles.paramsSelectData)}
                    defaultValue="60"
                    addonAfter="秒"
                    disabled={disabled}
                    min={0}
                    max={600}
                    value={timeout || 60}
                    onChange={(value) => {
                        setTimeoutValue(value);
                        setCommonFieldValue('timeout', value);
                    }}
                    onBlur={onBlurUpdateStep}
                />
            </div>
            <CardTitle text="步骤配置" style={{ margin: '0px 10px', marginBottom: '10px' }} />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>自动重定向</span>
                <Switch
                    // value={autoRedirect}
                    disabled={disabled}
                    checked={autoRedirect || false}
                    onChange={(value) => {
                        setAutoRedirect(value);
                        setApiFieldValue('isRedirect', value);
                        onBlurUpdateStep();
                    }}
                />
            </div>
        </div>
    );
};

export default connectModel([commonModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice
}))(ParameterExtraction);
