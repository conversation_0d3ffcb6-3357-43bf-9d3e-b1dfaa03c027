.tabs {
    position: absolute;
    top: 8px;
    left: 100px;
    height: 50px;
    color: #747474;
    cursor: pointer;

    span {
        padding: 0 10px;
    }

    span:hover {
        color: var(--primary-color);
    }
}

.tabActive {
    color: var(--primary-color);
    font-weight: bold;
}

.cardLayout {
    padding: 0 20px;
}

.desc {
    padding: 10px 0;

    .descContent {
        font-size: 12px;
        color: #777;
    }
}

// content
.content {
    ::-webkit-scrollbar {
        width: 4px;
        /* 滚动条宽度 */
        display: block !important;
    }

    ::-webkit-scrollbar-thumb {
        background-color: rgba(154, 154, 154, 0.943);
        /* 滚动条颜色 */
    }
}

.addOpera {
    // border: red solid;
    padding: 10px;
    padding-right: 20px;
    :global {
        .ant-form-item {
            margin-bottom: 8px !important;
        }
    }

    .submitBtn {
        float: right;
    }


}

.addIcon {
    display: inline-block;
    margin-top: 10px;
    font-size: 12px;
    color: var(--primary-color);
    cursor: pointer;
}

.deleteIcon {
    color: red;
}

.dynamicDeleteBtn {
    position: absolute;
    right: -17px;
    top: 8px;
    color: #777;
}

.dynamicDeleteBtn:hover {
    color: var(--primary-color);
}

.optionOpera {
    float: right;
    display: none;

    .optionOperaIcon,
    .optionOperaDelIcon {
        margin-left: 5px;
        color: #777;
    }

    .optionOperaDelIcon {
        color: red;
    }
}

.optionTitle {
    float: left;
    width: calc(100% - 5px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;


}

.optionWrapper:hover {
    .optionTitle {
        width: calc(100% - 40px);
    }

    .optionOpera {
        display: block;
    }
}

.defaultValue {
    color: #777;
    font-size: 12px;

}

.optionEditIcon {
    margin-left: 10px;
    color: #777;
    font-size: 12px;
}

.optionEditIcon:hover {
    color: var(--primary-color);
}