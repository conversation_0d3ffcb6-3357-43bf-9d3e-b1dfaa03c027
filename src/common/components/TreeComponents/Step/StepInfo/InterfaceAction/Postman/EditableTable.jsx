import React, { useEffect, useState, useMemo } from 'react';
import { EditableProTable } from '@ant-design/pro-table';
import { DeleteOutlined } from '@ant-design/icons';
import styles from './EditableTable.module.less';
import { v4 } from 'uuid';
export default ({
    columns,
    dataSource,
    title,
    setDataSource,
    editableKeys = [],
    setEditableRowKeys,
    extra,
    selectRows,
    setSelectRows,
    currentStep,
    onBlurUpdateStep,
    editType,
    disabled,
    defaultItemData
}) => {
    const { stepInfo } = currentStep;
    useEffect(() => {
        setEditableRowKeys(dataSource.map((v) => v.id));
    }, [dataSource]);
    const othersProps = useMemo(() => {
        if (disabled) {
            return {
                tableAlertRender: false
            };
        }
        return {};
    }, [disabled]);
    return (
        <EditableProTable
            // disabled={['readonly', 'debug', 'execute']?.includes(editType)}
            headerTitle={title}
            columns={columns}
            rowKey="id"
            value={dataSource}
            className={styles['table-editable']}
            onChange={setDataSource}
            recordCreatorProps={
                disabled
                    ? false
                    : {
                          newRecordType: 'dataSource',
                          record: () => {
                              if (defaultItemData) {
                                  return {
                                      ...defaultItemData,
                                      id: v4()
                                  };
                              }
                              return {
                                  id: v4()
                              };
                          }
                      }
            }
            {...othersProps}
            rowSelection={{
                type: 'checkbox',
                selectedRowKeys: selectRows,
                onChange: (e) => {
                    setSelectRows(e);
                    onBlurUpdateStep();
                },
                hideSelectAll: true,
                selections: false,
                getCheckboxProps: (record) => ({ disabled: disabled })
            }}
            select={{
                selectedRowKeys: []
            }}
            editable={{
                type: 'multiple',
                editableKeys: disabled ? [] : editableKeys,
                actionRender: (row, config, defaultDoms) => {
                    return [defaultDoms.delete];
                },
                onValuesChange: (record, recordList) => {
                    if (extra) {
                        extra(recordList);
                    }
                    setDataSource(recordList);
                    onBlurUpdateStep();
                },
                deleteText: (
                    <DeleteOutlined
                        style={{
                            color: 'red'
                        }}
                    />
                ),
                // onChange: setEditableRowKeys
                onChange: (e, originData) => {
                    // 新增功能：默认选中新增的那一项
                    //  originData 之前的编辑id 列表
                    const originDataIds = originData?.map((item) => item.id);
                    // console.log(e,originDataIds.includes(e[e.length - 1]),originDataIds, 'originDataIds');
                    // 如果最后一个在里面，是新增的
                    if (originDataIds.includes(e[e.length - 1])) {
                        // 将最后一项添加到选中的值当中
                        setSelectRows([...selectRows, e[e.length - 1]]);
                    }
                    setEditableRowKeys(e);
                }
            }}
        />
    );
};
