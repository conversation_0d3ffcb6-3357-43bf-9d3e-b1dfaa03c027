import React, { useEffect, useRef } from 'react';
// import MonacoEditor from '@monaco-editor/react';
import MonacoEditor, { loader } from '@monaco-editor/react';
import * as monaco from 'monaco-editor';
loader.config({ monaco });
loader.init().then(/* ... */);
const JSONMonacoEditor = ({
    value,
    onChange,
    height,
    readOnly,
    theme,
    setEditor,
    bodyType,
    options = {},
    tables = [],
    viewValue,
    onBlur,
    editType,
    language,
    ...props
}) => {
    const editorRef = useRef(null);
    const handleEditorDidMount = (editor, monaco) => {
        editorRef.current = editor;
        if (typeof setEditor === 'function') {
            setEditor(editorRef.current);
        }
        monaco.languages.registerCompletionItemProvider('json', {
            provideCompletionItems: () => {
                const suggestions = tables.map((v) => ({
                    label: v,
                    kind: monaco.languages.CompletionItemKind.Text,
                    insertText: v
                }));
                return { suggestions };
            }
        });
        editorRef.current?.onDidBlurEditorText(() => {
            let val = editorRef.current?.getValue();
            typeof onBlur === 'function' && onBlur(val);
        });
    };

    useEffect(() => {
        if (viewValue) {
            setTimeout(() => {
                editorRef.current?.setValue(viewValue);
            }, 100);
        }
    }, [viewValue, editorRef?.current]);
    return (
        <div
            style={{
                border: '1px dashed #eee',
                borderRadius: '4px',
                height: height || 300,
                width: '100%',
                padding: '2px'
            }}
        >
            {/* {height} */}
            <MonacoEditor
                height={height || 300}
                width={'100%'}
                language={language ? language : props?.rawType ? props?.rawType : 'json'}
                value={value}
                theme={'vitesse-light'}
                options={{
                    readOnly: readOnly || false,
                    automaticLayout: true,
                    wordWrap: 'on',
                    tabSize: 4,
                    // 隐藏行号
                    lineNumbers: 'off',
                    minimap: { enabled: false },
                    folding: true, // 折叠
                    ...options
                }}
                onChange={(value) => {
                    if (typeof onChange === 'function') {
                        onChange(value);
                    }
                    typeof onBlur === 'function' && onBlur();
                }}
                onMount={handleEditorDidMount}
                // disabled={['readonly', 'debug', 'execute']?.includes(editType)}
            />
        </div>
    );
};

export default JSONMonacoEditor;
