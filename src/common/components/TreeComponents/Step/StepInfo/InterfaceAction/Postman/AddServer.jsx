import {
    Table,
    Select,
    Popconfirm,
    message,
    Popover,
    Input,
    Button,
    Form,
    Divider,
    Modal
} from 'antd';
import classnames from 'classnames';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { useImperativeHandle, forwardRef, useState, useEffect } from 'react';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { createServer, deleteServer, updateServer } from 'COMMON/api/front_qe_tools/config';
import styles from './AddServer.module.less';

const formItemLayout = {
    labelCol: {
        span: 4
    },
    wrapperCol: {
        span: 24
    }
};
const formItemLayoutWithOutLabel = {
    wrapperCol: {
        offset: 4
    }
};
function AddServer(props, ref) {
    const { serverList, getServerList, currentSpace } = props;
    const [createOpen, setCreateOpen] = useState(false);
    const [updateOpen, setUpdateOpen] = useState(false);
    const [editServerId, setEditServerId] = useState(null);
    const [messageApi, contextHolder] = message.useMessage();
    const [form] = Form.useForm();
    const [updateForm] = Form.useForm();
    // 使用 useImperativeHandle 来将方法暴露给父组件
    useImperativeHandle(ref, () => ({
        openCreateModal: () => setCreateOpen(true),
        openUpdateModal: (serverId) => {
            const currentServer = serverList.find((server) => server.serverId === serverId);
            if (currentServer) {
                updateForm.setFieldsValue({
                    serverName: currentServer.serverName,
                    addressList: currentServer.addressList
                });
                setEditServerId(serverId);
                setUpdateOpen(true);
            }
        }
    }));

    const handleSubmit = () => {
        form.validateFields()
            .then((values) => {
                let _server = serverList.find((item) => item.serverName === values.serverName);
                if (_server) {
                    messageApi.warning('该服务已存在');
                    return;
                }
                if (_server) {
                    updateServer({
                        serverId: _server?.serverId,
                        serverName: _server.serverName,
                        addressList: [..._server?.addressList, ...values.addressList]
                    })
                        .then(() => {
                            messageApi.success('新增成功');
                            form.resetFields();
                            getServerList({ moduleId: currentSpace?.id });
                            setCreateOpen(false);
                        })
                        .catch((err) => {
                            console.log(err?.message ?? err);
                        });
                } else {
                    createServer({
                        moduleId: currentSpace?.id,
                        serverName: values.serverName,
                        addressList: values.addressList
                    })
                        .then(() => {
                            messageApi.success('新增成功');
                            form.resetFields();
                            getServerList({ moduleId: currentSpace?.id });
                            setCreateOpen(false);
                        })
                        .catch((err) => {
                            console.log(err?.message ?? err);
                        });
                }
            })
            .catch((errorInfo) => {
                messageApi.error('表单填写不完整');
            });
    };
    return (
        <>
            {contextHolder}
            <Modal
                title="新增服务"
                visible={createOpen}
                onCancel={() => setCreateOpen(false)}
                footer={null}
            >
                <div className={styles.addOpera}>
                    <Form form={form} colon={false} requiredMark={false}>
                        <Form.Item
                            label="服务名称"
                            name="serverName"
                            rules={[
                                {
                                    required: true,
                                    message: '请输入服务名称'
                                }
                            ]}
                            labelCol={{ span: 4 }}
                            wrapperCol={{ span: 24 }}
                        >
                            <Input placeholder="请输入服务名称" />
                        </Form.Item>
                        <Form.List name="addressList" initialValue={['']}>
                            {(fields, { add, remove }, { errors }) => (
                                <>
                                    {fields.map((field, index) => (
                                        <Form.Item
                                            key="address"
                                            {...(index === 0
                                                ? formItemLayout
                                                : formItemLayoutWithOutLabel)}
                                            label={index === 0 ? '服务地址' : ''}
                                        >
                                            <Form.Item
                                                {...field}
                                                validateTrigger={['onBlur']}
                                                rules={[
                                                    {
                                                        validator: async (_, value) => {
                                                            if (!value) {
                                                                // 如果值为空，不进行 URL 检查
                                                                return Promise.reject(
                                                                    '服务地址不能为空'
                                                                );
                                                            }
                                                            try {
                                                                new URL(value);
                                                            } catch (err) {
                                                                return Promise.reject(
                                                                    '有效地址（http://xxx 或 https://xxx）'
                                                                );
                                                            }
                                                        }
                                                    }
                                                ]}
                                                // noStyle
                                            >
                                                <Input
                                                    placeholder="请输入服务地址"
                                                    style={{
                                                        width: '100%'
                                                    }}
                                                    allowClear
                                                />
                                            </Form.Item>
                                            {fields.length > 1 ? (
                                                <MinusCircleOutlined
                                                    className={styles.dynamicDeleteBtn}
                                                    onClick={() => remove(field.name)}
                                                />
                                            ) : (
                                                <MinusCircleOutlined
                                                    className={styles.dynamicDeleteBtn}
                                                    style={{ color: '#ccc' }} // 灰色图标表示不可点击
                                                />
                                            )}
                                        </Form.Item>
                                    ))}
                                    <Form.Item {...formItemLayoutWithOutLabel}>
                                        <Button
                                            type="dashed"
                                            onClick={() => add()}
                                            block
                                            icon={<PlusOutlined />}
                                        >
                                            新增
                                        </Button>
                                    </Form.Item>
                                </>
                            )}
                        </Form.List>
                        <Form.Item>
                            <Button
                                style={{ float: 'right' }}
                                onClick={handleSubmit}
                                type="primary"
                            >
                                确认
                            </Button>
                        </Form.Item>
                    </Form>
                </div>
            </Modal>
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    serverList: state.common.case.serverList
}))(forwardRef(AddServer));
