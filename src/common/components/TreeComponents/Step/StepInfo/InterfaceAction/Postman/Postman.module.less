.items {
    width: 100%;
    display: flex;
    align-items: center;
    margin: 10px;
}
.flexX {
    display: flex;
    align-items: center;
}
// Select
.paramsSelect {
    position: relative;
    width: 100%;
    margin-bottom: 5px;
}

.paramsSelectTitle {
    display: inline-block;
    width: 90px;
    margin-left: 10px;
    height: 32px;
    line-height: 32px;
}

.paramsSelectData {
    width: calc(100% - 100px);
}
.label {
    display: inline-block;
    width: 112px; // 这里可以根据需要调整宽度
    text-align: right; // 右对齐
    // padding-right: 10px; // 添加一些内边距
}