import React, {useEffect, useState, useCallback, useMemo} from 'react';
import {
    Button,
    Card,
    Col,
    Dropdown,
    Input,
    Menu,
    Radio,
    Row,
    Select,
    Tabs,
    Badge,
    Segmented,
    Tooltip
} from 'antd';
import {EllipsisOutlined, FileAddOutlined, DownOutlined} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import EditableTable from './EditableTable';
import JSONMonacoEditor from './JSONMonacoEditor';
import {v4} from 'uuid';
const {Option} = Select;

const FormDataFileInput = (props) => {
    const {type, value, ...others} = props;
    switch (type) {
        case 'file':
            return (
                <>
                    <Button
                        size="small"
                        style={{
                            width: '100%'
                        }}
                    >
                        {value ? (
                            <Tooltip title={value}>{(value || '')?.slice(0, 16) + '...'}</Tooltip>
                        ) : (
                            <FileAddOutlined />
                        )}
                        <input
                            style={{
                                opacity: 0,
                                position: 'absolute',
                                width: '100%',
                                height: 32
                            }}
                            onChange={async (e) => {
                                const file = e.target.files[0];
                                // 上传文件
                                fileImageUpload(file).then(async res => {
                                    props.onChange(result.url);
                                }).catch(err => {
                                    console.log(err?.message ?? err);
                                });
                            }}
                            type="file"
                        />
                    </Button>
                </>
            );
        default:
            return <Input size="small" value={value} {...others} placeholder="请输入" />;
    }
};

// const safeJsonParseString = (value) => {
//     console.log('safeJsonParseString', JSON.stringify(JSON.parse(value), null, 2));
//     try {
//         // 格式化JSON，添加缩进
//         return JSON.stringify(JSON.parse(value), null, 2);
//     } catch (error) {
//         // 转化失败，返回原值
//         return value;
//     }
// };

const safeJsonStringify = (value) => {
    if (!value) {
        return JSON.stringify({}, null, 2);
    }
    try {
        return JSON.stringify(value, null, 2);
    } catch (error) {
        return JSON.stringify({}, null, 2);
    }
};

const RequestParams = (props) => {
    const {fieldValue, setFieldValue, currentStep, onBlurUpdateStep, editType} = props;
    const [value, setValue] = useState('Params');
    const [bodyType, setBodyType] = useState(1);
    const [rawType, setRawType] = useState('Text');
    const [method, setMethod] = useState('GET');
    const [paramsData, setParamsData] = useState([]);
    const [headers, setHeaders] = useState([]);
    const [editableKeys, setEditableRowKeys] = useState(() => paramsData.map((item) => item.id));
    const [headersKeys, setHeadersKeys] = useState(() => headers.map((item) => item.id));
    const [body, setBody] = useState(null);
    const [loading, setLoading] = useState(false);
    const [response, setResponse] = useState({});
    const [formData, setFormData] = useState([]);
    const [fromUrlencode, setFromUrlencode] = useState([]);
    const [selectFromUrlencode, setSelectFromUrlencode] = useState([]);
    const [selectFormData, setSelectFormData] = useState([]);
    const [editor, setEditor] = useState(null);
    const [rawEditor, setRawEditor] = useState(null);
    const [selectParams, setSelectParams] = useState([]);
    const [selectHeaders, setSelectHeaders] = useState([]);
    const {TextArea} = Input;
    const {stepInfo} = currentStep;

    const disabled = useMemo(() => {
        return ['readonly', 'debug', 'execute']?.includes(editType);
    }, [editType]);
    // 设置请求参数, request 、headers、body
    const setRequestParams = (field, value) => {
        setFieldValue({
            ...fieldValue,
            [field]: value
        });
    };
    useEffect(() => {
        if (currentStep?.stepInfo?.requestParams) {
            // console.log(
            //     'currentStep?.stepInfo',
            //     currentStep?.stepInfo,
            //     stepInfo?.requestParams?.body?.type
            // );
            setMethod(stepInfo?.requestParams?.method);
            setBodyType(stepInfo?.requestParams?.body?.type);
        }
    }, [currentStep?.stepId]);

    useEffect(() => {
        rawEditor && rawEditor?.setValue(stepInfo?.requestParams?.body?.raw || '');
        // 改了监听，不会一直更新值
    }, [rawEditor, stepInfo?.stepId]);

    useEffect(() => {
        editor && editor?.setValue(safeJsonStringify(stepInfo?.requestParams?.body?.json));
        // 改了监听，不会一直更新值
    }, [editor, stepInfo?.stepId]);
    // 设置body 中字段数据
    const setBodyFieldData = (field, value) => {
        const bodyData = fieldValue?.body;
        setRequestParams('body', {
            ...bodyData,
            [field]: value
        });
    };

    const formatSeletedKeyValue = (values = [], selected = []) => {
        const result = values.map((item) => {
            // 是否被选中
            const name = item.name;
            const value = item.value;
            return {
                name,
                value,
                checked: selected.includes(item.id)
            };
        });
        return result;
    };

    const getTableListAndSelects = (table) => {
        const result = table.map((item, index) => ({
            ...item,
            id: v4()
        }));
        return {
            table: result,
            selects: result.filter((item) => item.checked).map((item) => item.id)
        };
    };
    useEffect(() => {
        if (currentStep?.stepInfo?.requestParams?.query) {
            const {table: paramsData, selects: selectedKeys} = getTableListAndSelects(
                stepInfo?.requestParams?.query || []
            );
            setParamsData(paramsData);
            setSelectParams(selectedKeys);
        }

        if (currentStep?.stepInfo?.requestParams?.headers) {
            const {table: headersData, selects: selectedHeaders} = getTableListAndSelects(
                stepInfo?.requestParams?.headers || []
            );
            setSelectHeaders(selectedHeaders);
            setHeaders(headersData);
        }
        if (stepInfo?.requestParams?.body?.formData) {
            const {table: formData, selects: selectedFormData} = getTableListAndSelects(
                stepInfo?.requestParams?.body?.formData || []
            );
            setFormData(
                formData.map((item) => {
                    return {
                        ...item,
                        value: item.type === 'file' ? item.link : item.value
                    };
                })
            );
            setSelectFormData(selectedFormData);
        }
        if (stepInfo?.requestParams?.body?.fromUrlencode) {
            const {table: fromUrlencodeData, selects: selectedFromUrlencode} =
                getTableListAndSelects(stepInfo?.requestParams?.body?.fromUrlencode || []);
            setFromUrlencode(fromUrlencodeData);
            setSelectFromUrlencode(selectedFromUrlencode);
        }
    }, [currentStep?.stepId]);
    useEffect(() => {
        if (paramsData.length > 0) {
            setRequestParams('query', formatSeletedKeyValue(paramsData, selectParams));
        }
    }, [paramsData, selectParams]);

    useEffect(() => {
        if (headers.length > 0) {
            setRequestParams('headers', formatSeletedKeyValue(headers, selectHeaders));
        }
    }, [headers, selectHeaders]);

    useEffect(() => {
        if (fromUrlencode.length > 0) {
            setBodyFieldData(
                'fromUrlencode',
                formatSeletedKeyValue(fromUrlencode, selectFromUrlencode)
            );
        }
    }, [fromUrlencode, selectFromUrlencode]);

    useEffect(() => {
        if (formData.length > 0) {
            const result = formData.map((item) => {
                // 是否被选中
                const result = {
                    name: item?.name,
                    type: item?.type,
                    checked: selectFormData.includes(item.id)
                };
                if (item?.type === 'file') {
                    result.link = item.value;
                } else {
                    result.value = item.value;
                }

                return result;
            });
            setBodyFieldData('formData', result);
        }
    }, [selectFormData, formData]);

    // 请求url+params
    const [url, setUrl] = useState('');
    const columns = (columnType) => {
        if (columnType === 'form-data') {
            return [
                {
                    title: 'Key',
                    key: 'name',
                    dataIndex: 'name',
                    renderFormItem() {
                        return <Input size="small" placeholder="请输入" />;
                    }
                },
                {
                    title: 'Type',
                    key: 'type',
                    dataIndex: 'type',
                    width: 80,
                    renderFormItem(props) {
                        return (
                            <Select size="small" {...props} placeholder="请选择">
                                <Option value="1">Text</Option>
                                <Option value="2">File</Option>
                            </Select>
                        );
                    }
                },
                {
                    title: 'Value',
                    key: 'value',
                    dataIndex: 'value',
                    renderFormItem(props) {
                        // 根据type 切换是选择文件还是输入框
                        return <FormDataFileInput type={props?.entry?.type} />;
                    },
                    render: (_, row) => {
                        return (
                            <span>
                                {typeof row.value === 'object' ? row.value.name || '' : row.value}{' '}
                            </span>
                        );
                    }
                },
                // {
                //     title: 'Description',
                //     key: 'description',
                //     dataIndex: 'description',
                //     renderFormItem() {
                //         return <Input size="small" placeholder="请输入" />;
                //     }
                // },
                {
                    title: <EllipsisOutlined />,
                    valueType: 'option',
                    width: 50
                }
            ];
        }
        return [
            {
                title: 'Key',
                key: 'name',
                dataIndex: 'name',
                // width: '20%',
                renderFormItem() {
                    return <Input size="small" placeholder="请输入" />;
                    // return <InputComponent/>;
                }
            },
            {
                title: 'Value',
                key: 'value',
                valueType: 'file',
                dataIndex: 'value',
                // width: '20%',
                renderFormItem() {
                    return <Input size="small" placeholder="请输入" />;
                    // return <InputComponent/>;
                }
            },
            // {
            //     title: 'Description',
            //     key: 'description',
            //     dataIndex: 'description',
            //     renderFormItem() {
            //         return <Input size="small" placeholder="请输入" />;
            //     }
            // },
            {
                title: <EllipsisOutlined />,
                valueType: 'option',
                width: 50
            }
        ];
    };
    // 这期暂不支持 先注释~~
    // const menuItems = ['Text', 'JavaScript', 'JSON', 'HTML', 'XML'];
    // const menu = (
    //     <Menu>
    //         {menuItems.map((item) => (
    //             <Menu.Item key={item}>
    //                 <a onClick={() => onClickMenu(item)}>{item}</a>
    //             </Menu.Item>
    //         ))}
    //     </Menu>
    // );
    // const onClickMenu = (key) => {
    //     setRawType(key);
    // };
    // 根据paramsData拼接url
    const joinUrl = (data) => {
        let tempUrl = url.split('?')[0];
        data.forEach((item, idx) => {
            if (item.key) {
                // 如果item.key有效
                if (idx === 0) {
                    tempUrl = `${tempUrl}?${item.key}=${item.value || ''}`;
                } else {
                    tempUrl = `${tempUrl}&${item.key}=${item.value || ''}`;
                }
            }
        });
        setUrl(tempUrl);
    };
    const getBody = (bd = 1) => {
        if (bd === 1) {
            return (
                <div style={{height: '20vh', lineHeight: '20vh', textAlign: 'center'}}>
                    This request does not have a body
                </div>
            );
        }
        if (bd === 2) {
            return (
                <EditableTable
                    key="form-data"
                    columns={columns('form-data')}
                    dataSource={formData}
                    setDataSource={setFormData}
                    setSelectRows={setSelectFormData}
                    selectRows={selectFormData}
                    editableKeys={formData.map((item) => item.id)}
                    setEditableRowKeys={setHeadersKeys}
                    currentStep={currentStep}
                    onBlurUpdateStep={onBlurUpdateStep}
                    editType={editType}
                    disabled={disabled}
                    defaultItemData={{
                        type: '1',
                        value: ''
                    }}
                />
            );
        }
        if (bd === 3) {
            return (
                <EditableTable
                    key="x-www-form-urlencoded"
                    columns={columns('x-www-form-urlencoded')}
                    dataSource={fromUrlencode}
                    setDataSource={setFromUrlencode}
                    setSelectRows={setSelectFromUrlencode}
                    selectRows={selectFromUrlencode}
                    editableKeys={fromUrlencode.map((item) => item.id)}
                    setEditableRowKeys={setHeadersKeys}
                    currentStep={currentStep}
                    onBlurUpdateStep={onBlurUpdateStep}
                    editType={editType}
                    disabled={disabled}
                />
            );
        }
        if (bd === 4) {
            // JSON
            return (
                <div style={{flex: 1, width: '100%'}}>
                    <JSONMonacoEditor
                        // value={safeJsonStringify(stepInfo.requestParams?.body?.json)}
                        onChange={(e) => {
                            setBodyFieldData('json', e);
                        }}
                        key="json"
                        height="100%"
                        setEditor={setEditor}
                        bodyType={bd}
                        onBlur={onBlurUpdateStep}
                        editType={editType}
                    />
                </div>
            );
        }
        return (
            <div style={{flex: 1, width: '100%'}}>
                <JSONMonacoEditor
                    key={'raw'}
                    // value={fieldValue?.body?.raw || ''}
                    onChange={(e) => {
                        setBodyFieldData('raw', e);
                    }}
                    height="100%"
                    setEditor={setRawEditor}
                    // rawType={cloneDeep(rawType.toLowerCase())}
                    rawType={'text'}
                    bodyType={bd}
                    onBlur={onBlurUpdateStep}
                    currentStep={currentStep}
                    editType={editType}
                />
            </div>
            // <Row style={{ marginTop: 12 }}>
            // <Col span={24}>
            // <Card bodyStyle={{ padding: 0 }}>

            // </Card>
            // </Col>
            // </Row>
        );
    };
    /**
     * 创建一个选项对象
     *
     * @param label 选项的显示文本
     * @param value 选项的值
     * @param count 徽标的数量（用于显示选项旁边的徽标）
     * @returns 包含label、value和key的对象，其中label是一个React元素，包含显示文本和徽标
     */
    function createOption(value, count) {
        return {
            label: (
                <div style={{display: 'flex', alignItems: 'center'}}>
                    <div>{value}</div>
                    {count > 0 && (
                        <Badge
                            size="small"
                            count={+count}
                            showZero={false}
                            color={'#448ef7'}
                            style={{marginLeft: 4}}
                        />
                    )}
                </div>
            ),
            value: value,
            key: value
        };
    }

    const getBodyCount = () => {
        const jsonExists = fieldValue?.body?.json != null && fieldValue?.body?.json !== undefined; // 检查是否存在且不是 null 或 undefined
        const rawExists =
            fieldValue?.body?.raw != null &&
            fieldValue?.body?.raw !== undefined &&
            fieldValue?.body?.raw !== '';
        if (bodyType === 2) {
            return fieldValue?.body?.formData?.filter((item) => item?.checked)?.length;
        }
        if (bodyType === 3) {
            return fieldValue?.body?.fromUrlencode?.filter((item) => item?.checked)?.length;
        }
        if (bodyType === 4) {
            return jsonExists ? 1 : 0;
        }
        if (bodyType === 5) {
            return rawExists ? 1 : 0;
        }
        return 0;
    };
    const options = [
        createOption('Params', selectParams ? selectParams.length : 0),
        createOption('Headers', selectHeaders ? selectHeaders.length : 0),
        createOption('Body', getBodyCount())
    ];
    return (
        <div>
            <Segmented
                options={options}
                value={value}
                onChange={setValue}
                style={{marginBottom: 10}}
            />
            {value && value === 'Params' && (
                <EditableTable
                    disabled={disabled}
                    columns={columns('Params')}
                    // title="Query Params"
                    dataSource={paramsData}
                    setDataSource={setParamsData}
                    setSelectRows={(e) => {
                        setSelectParams(e);
                    }}
                    selectRows={selectParams}
                    extra={joinUrl}
                    editableKeys={editableKeys}
                    setEditableRowKeys={setEditableRowKeys}
                    currentStep={currentStep}
                    onBlurUpdateStep={onBlurUpdateStep}
                />
            )}
            {value && value === 'Headers' && (
                <EditableTable
                    disabled={disabled}
                    columns={columns('headers')}
                    // title="Headers
                    dataSource={headers}
                    setDataSource={setHeaders}
                    setSelectRows={setSelectHeaders}
                    selectRows={selectHeaders}
                    editableKeys={headersKeys}
                    setEditableRowKeys={setHeadersKeys}
                    currentStep={currentStep}
                    onBlurUpdateStep={onBlurUpdateStep}
                />
            )}
            {value && value === 'Body' && (
                <div style={{display: 'flex', height: '65vh', flexDirection: 'column'}}>
                    <Row>
                        <Radio.Group
                            defaultValue={1}
                            value={bodyType || 1}
                            onChange={(e) => {
                                setBodyType(e.target.value);
                                setBodyFieldData('type', e.target.value);
                                onBlurUpdateStep();
                            }}
                            disabled={disabled}
                        >
                            <Radio value={1}>none</Radio>
                            <Radio value={2}>form-data</Radio>
                            <Radio value={3}>x-www-form-urlencoded</Radio>
                            <Radio value={4}>json</Radio>
                            <Radio value={5}>raw</Radio>
                            {/* <Radio value={5}>GraphQL</Radio> */}
                        </Radio.Group>
                        {/* 这期不做 暂不支持 先注释 */}
                        {/* {bodyType === 5 ? (
                            <Dropdown style={{ marginLeft: 8 }} overlay={menu} trigger={['click']}>
                                <a onClick={(e) => e.preventDefault()}>
                                    {rawType} <DownOutlined />
                                </a>
                            </Dropdown>
                        ) : null} */}
                    </Row>
                    {getBody(bodyType)}
                </div>
            )}
        </div>
    );
};

export default connectModel([commonModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice
}))(RequestParams);
