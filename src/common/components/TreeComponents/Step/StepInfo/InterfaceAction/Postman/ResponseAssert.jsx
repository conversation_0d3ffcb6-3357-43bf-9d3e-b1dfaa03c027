import React, { useEffect, useState } from 'react';
import { Button, Input, Select, Form, Space, Checkbox, Segmented, Switch, Tooltip } from 'antd';
import { PageContainer } from '@ant-design/pro-components';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import {CardTitle} from 'COMMON/components/common/Card';
import aiIcon from 'RESOURCES/img/ai.png';
import JSONMonacoEditor from './JSONMonacoEditor';
import { operationList } from './const';
import styles from './Postman.module.less';
import { isEmpty } from 'lodash';

const ResponseAssertItem = (props) => {
    const { onBlurUpdateStep, disabled } = props;
    const [value, setValue] = useState({});
    useEffect(() => {
        // console.log('props.value', props.value, props.defaultValue);
        if (!isEmpty(props.defaultValue)) {
            setValue(props.defaultValue);
        } else if (!isEmpty(props.value)) {
            setValue(props.value);
        } else {
            setValue({});
        }
    }, [props.defaultValue, props.value]);

    const onBlur = (field, fieldValue) => {
        props.onChange({
            ...value,
            [field]: fieldValue
        });
    };

    return (
        <>
            <div
                style={{
                    display: 'flex',
                    marginTop: 8,
                    alignItems: 'center',
                    width: '100%'
                }}
            >
                <div
                    style={{
                        width: 140,
                        marginRight: 6,
                        flexShrink: 0,
                        textAlign: 'center',
                        display: 'inline-block',
                        background: '#f5f7fa',
                        padding: '4px 8px',
                        borderRadius: 2
                    }}
                >
                    JSONPath
                </div>
                <Input
                    style={{ flex: 1 }}
                    defaultValue={props.value?.jsonPath}
                    placeholder="请输入JSONPath"
                    disabled={disabled}
                    // value={value?.jsonPath || props.defaultValue?.jsonPath}
                    onChange={(e) => {
                        onBlur('jsonPath', e.target.value);
                        // setValue({
                        //     ...value,
                        //     jsonPath: e.target.value
                        // });
                        onBlurUpdateStep();
                    }}
                    allowClear
                />
            </div>
            <div style={{ display: 'flex', marginTop: 8 }}>
                <Select
                    style={{
                        marginRight: 6,
                        width: 140,
                        flexShrink: 0
                    }}
                    disabled={disabled}
                    // value={value?.type || props.defaultValue?.type}
                    placeholder="请选择操作符"
                    options={operationList}
                    defaultValue={props.value?.type || props.defaultValue?.type}
                    onChange={(selectvalue) => {
                        onBlur('type', selectvalue);
                        // setValue({
                        //     ...value,
                        //     type: selectvalue
                        // });
                        onBlurUpdateStep();
                    }}
                    allowClear
                />
                <Input
                    style={{ flex: 1 }}
                    defaultValue={props.value?.data}
                    // value={value?.data || props.defaultValue?.data}
                    placeholder="请输入断言值"
                    onChange={(e) => {
                        onBlur('data', e.target.value);
                        onBlurUpdateStep();
                    }}
                    disabled={disabled}
                    // disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    allowClear
                />
            </div>
        </>
    );
};
const DiffItem = (props) => {
    const { onBlurUpdateStep, disabled } = props;
    const [value, setValue] = useState({});
    useEffect(() => {
        // console.log('props.value', props.value, props.defaultValue);
        if (!isEmpty(props.defaultValue)) {
            setValue(props.defaultValue);
        } else if (!isEmpty(props.value)) {
            setValue(props.value);
        } else {
            setValue({});
        }
    }, [props.defaultValue, props.value]);

    const onBlur = (field, fieldValue) => {
        props.onChange({
            ...value,
            [field]: fieldValue
        });
    };
    return (
        <>
            <div
                style={{
                    display: 'flex',
                    marginTop: 8,
                    alignItems: 'center',
                    width: '100%'
                }}
            >
                <div
                    style={{
                        width: 140,
                        marginRight: 6,
                        flexShrink: 0,
                        textAlign: 'center',
                        display: 'inline-block',
                        background: '#f5f7fa',
                        padding: '4px 8px',
                        borderRadius: 2
                    }}
                >
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'center',
                            textAlign: 'center'
                        }}
                    >
                        {}
                        {/* {JSON.stringify(value)} */}
                        {value.isGen ? (
                            <div style={{ marginRight: '5px', paddingBottom: '3px' }}>
                                <Tooltip title="该断言项为智能去噪分析得到" arrow>
                                    <img
                                        src={aiIcon}
                                        alt="AI Icon"
                                        style={{ width: '17px', height: '17px', cursor: 'pointer' }}
                                    />
                                </Tooltip>
                            </div>
                        ) : null}

                        <div>JSONPath</div>
                    </div>
                </div>
                <Input
                    style={{ flex: 1 }}
                    defaultValue={props.value?.jsonPath}
                    placeholder="请输入JSONPath"
                    disabled={disabled}
                    // value={value?.jsonPath || props.defaultValue?.jsonPath}
                    onChange={(e) => {
                        onBlur('jsonPath', e.target.value);
                        onBlurUpdateStep();
                    }}
                    allowClear
                />
            </div>
        </>
    );
};
export default function ResponseAssert(props) {
    const { fieldValue, setFieldValue, onBlurUpdateStep, currentStep, editType } = props;
    const [responseJsonAssertForm] = Form.useForm();
    const [assertCheckKeyDiffForm] = Form.useForm();
    const [assertIgnoreKeyDiffForm] = Form.useForm();
    const [responseTextAssertForm] = Form.useForm();
    const [editor, setEditor] = useState(null);
    const [mode, setMode] = useState('default');
    const [assertFullDiff, setAssertFullDiff] = useState(false);
    const setAssertFieldValue = (field, value) => {
        setFieldValue({
            ...fieldValue,
            [field]: value
        });
    };
    useEffect(() => {
        if (editor) {
            const jsonSchema = currentStep?.stepInfo?.responseAssert?.assertJsonSchema?.data || '';
            editor.setValue(jsonSchema);
        }
    }, [editor, currentStep?.stepId]);
    // 当组件加载或currentStep变化时，初始化表单值
    useEffect(() => {
        // 设置Response Text断言值
        if (currentStep?.stepInfo?.responseAssert?.assertText) {
            responseTextAssertForm.setFieldsValue({
                assertText: currentStep.stepInfo.responseAssert.assertText
            });
        }

        // 设置Response JSON断言值
        if (currentStep?.stepInfo?.responseAssert?.assertJson) {
            const formattedAssertJson = currentStep.stepInfo.responseAssert.assertJson.map(
                (item) => ({
                    checked: item.checked,
                    field: {
                        type: item.type,
                        jsonPath: item.jsonPath,
                        data: item.data
                    }
                })
            );
            responseJsonAssertForm.setFieldsValue({
                assertJson: formattedAssertJson
            });
        }
        // 设置assertCheckKeyDiff断言值
        if (currentStep?.stepInfo?.responseAssert?.assertCheckKeyDiff) {
            const formattedCheckKeyDiff =
                currentStep.stepInfo.responseAssert.assertCheckKeyDiff.map((item) => ({
                    checked: item.checked,
                    field: {
                        type: item.type,
                        jsonPath: item.jsonPath,
                        data: item.data
                    }
                }));
            assertCheckKeyDiffForm.setFieldsValue({
                assertCheckKeyDiff: formattedCheckKeyDiff
            });
        }
        if (currentStep?.stepInfo?.responseAssert?.assertIgnoreKeyDiff) {
            const formattedIgnoreKeyDiff =
                currentStep.stepInfo.responseAssert.assertIgnoreKeyDiff.map((item) => ({
                    checked: item.checked,
                    field: {
                        type: item.type,
                        jsonPath: item.jsonPath,
                        data: item.data,
                        isGen: item.isGen || false
                    }
                }));
            assertIgnoreKeyDiffForm.setFieldsValue({
                assertIgnoreKeyDiff: formattedIgnoreKeyDiff
            });
        }
        setAssertFullDiff(currentStep?.stepInfo?.responseAssert?.assertFullDiff || false);
        setFieldValue(currentStep?.stepInfo?.responseAssert);

        // 对于JSON Schema断言，由于它不是通过Form表单管理，所以这里不需要设置
    }, [currentStep?.stepId]);

    return (
        <div>
            {mode === 'default' ? (
                <div>
                    <CardTitle text={'Response Text 断言'} />
                    <>
                        <Form
                            name="assertText"
                            style={{
                                width: '100%'
                            }}
                            form={responseTextAssertForm}
                            onValuesChange={() => {
                                // console.log('assertText', responseTextAssertForm.getFieldsValue());
                                const assertText =
                                    responseTextAssertForm.getFieldsValue()?.assertText || [];
                                setAssertFieldValue(
                                    'assertText',
                                    assertText?.map((item) => {
                                        return {
                                            type: item?.type || '',
                                            data: item?.data || '',
                                            checked: item?.checked || false
                                        };
                                    })
                                );
                            }}
                            autoComplete="off"
                        >
                            <Form.List name="assertText">
                                {(fields, { add, remove }) => (
                                    <>
                                        {fields.map(({ key, name, ...restField }) => (
                                            <div
                                                key={key}
                                                style={{
                                                    display: 'flex',
                                                    alignItems: 'flex-start'
                                                }}
                                                align="baseline"
                                            >
                                                <Form.Item
                                                    name={[name, 'checked']}
                                                    valuePropName="checked"
                                                >
                                                    <Checkbox
                                                        style={{
                                                            marginRight: 4
                                                        }}
                                                        disabled={[
                                                            'readonly',
                                                            'debug',
                                                            'execute'
                                                        ]?.includes(editType)}
                                                        onChange={() => onBlurUpdateStep(false)}
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    {...restField}
                                                    name={[name, 'type']}
                                                    style={{
                                                        display: 'flex'
                                                    }}
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请选择操作符'
                                                        }
                                                    ]}
                                                >
                                                    <Select
                                                        style={{
                                                            marginRight: 6,
                                                            width: 120,
                                                            flexShrink: 0
                                                        }}
                                                        placeholder="请选择操作符"
                                                        disabled={[
                                                            'readonly',
                                                            'debug',
                                                            'execute'
                                                        ]?.includes(editType)}
                                                        options={[
                                                            {
                                                                value: 1,
                                                                label: '包含'
                                                            },
                                                            {
                                                                value: 2,
                                                                label: '正则'
                                                            }
                                                        ]}
                                                        onChange={() => onBlurUpdateStep(false)}
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    {...restField}
                                                    name={[name, 'data']}
                                                    style={{
                                                        flex: 1
                                                    }}
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请输入断言条件'
                                                        }
                                                    ]}
                                                >
                                                    <Input
                                                        placeholder="请输入断言条件"
                                                        onBlur={() => onBlurUpdateStep(false)}
                                                        disabled={[
                                                            'readonly',
                                                            'debug',
                                                            'execute'
                                                        ]?.includes(editType)}
                                                    />
                                                </Form.Item>
                                                <MinusCircleOutlined
                                                    style={{
                                                        marginTop: 8,
                                                        marginLeft: 4
                                                    }}
                                                    onClick={() => {
                                                        if (
                                                            [
                                                                'readonly',
                                                                'debug',
                                                                'execute'
                                                            ]?.includes(editType)
                                                        ) {
                                                            return;
                                                        }
                                                        remove(name);
                                                        onBlurUpdateStep();
                                                    }}
                                                />
                                            </div>
                                        ))}
                                        <Form.Item>
                                            <Button
                                                type="dashed"
                                                onClick={() => {
                                                    add({ checked: true, type: 1, data: null });
                                                    onBlurUpdateStep();
                                                }}
                                                block
                                                icon={<PlusOutlined />}
                                                disabled={[
                                                    'readonly',
                                                    'debug',
                                                    'execute'
                                                ]?.includes(editType)}
                                            >
                                                添加一行数据
                                            </Button>
                                        </Form.Item>
                                    </>
                                )}
                            </Form.List>
                            {/* <Form.Item>
                    <Button type="primary" htmlType="submit">
                        Submit
                    </Button>
                </Form.Item> */}
                        </Form>
                    </>
                    <CardTitle text={'Response JSON 断言'} />
                    <Form
                        name="assertJson"
                        style={
                            {
                                // maxWidth: 600
                            }
                        }
                        form={responseJsonAssertForm}
                        autoComplete="off"
                        onValuesChange={(value) => {
                            onBlurUpdateStep();
                            const assertJson =
                                responseJsonAssertForm.getFieldsValue()?.assertJson || [];
                            setAssertFieldValue(
                                'assertJson',
                                assertJson?.map((item) => {
                                    return {
                                        type: item?.field?.type || '',
                                        jsonPath: item?.field?.jsonPath || '',
                                        data: item?.field?.data || '',
                                        checked: item?.checked || false
                                    };
                                })
                            );
                        }}
                    >
                        <Form.List name="assertJson">
                            {(fields, { add, remove }) => (
                                <>
                                    {fields.map(({ key, name, ...restField }) => (
                                        <div
                                            key={key}
                                            style={{
                                                display: 'flex',
                                                width: '100%'
                                            }}
                                            align="baseline"
                                        >
                                            <Form.Item
                                                {...restField}
                                                name={[name, 'checked']}
                                                valuePropName="checked"
                                            >
                                                <Checkbox
                                                    style={{
                                                        marginRight: 4,
                                                        marginTop: 12
                                                    }}
                                                    onChange={onBlurUpdateStep}
                                                    disabled={[
                                                        'readonly',
                                                        'debug',
                                                        'execute'
                                                    ]?.includes(editType)}
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                {...restField}
                                                name={[name, 'field']}
                                                style={{
                                                    width: '100%'
                                                }}
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '断言字段不能为空'
                                                    }
                                                ]}
                                            >
                                                <ResponseAssertItem
                                                    onBlurUpdateStep={onBlurUpdateStep}
                                                    // editType={editType}
                                                    disabled={[
                                                        'readonly',
                                                        'debug',
                                                        'execute'
                                                    ]?.includes(editType)}
                                                />
                                            </Form.Item>
                                            <MinusCircleOutlined
                                                style={{
                                                    marginBottom: 55,
                                                    marginLeft: 4
                                                }}
                                                onClick={() => {
                                                    if (
                                                        ['readonly', 'debug', 'execute']?.includes(
                                                            editType
                                                        )
                                                    ) {
                                                        return;
                                                    }
                                                    remove(name);
                                                    onBlurUpdateStep();
                                                }}
                                            />
                                        </div>
                                    ))}
                                    <Form.Item>
                                        <Button
                                            type="dashed"
                                            onClick={() => {
                                                add({
                                                    checked: true,
                                                    field: { type: 3 },
                                                    data: null
                                                });
                                                onBlurUpdateStep();
                                            }}
                                            block
                                            icon={<PlusOutlined />}
                                            disabled={['readonly', 'debug', 'execute']?.includes(
                                                editType
                                            )}
                                        >
                                            添加一行数据
                                        </Button>
                                    </Form.Item>
                                </>
                            )}
                        </Form.List>
                    </Form>
                    <CardTitle text={'Response JSON Schema 断言'} />
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'flex-start',
                            alignContent: 'flex-start'
                        }}
                    >
                        <div>
                            <Checkbox
                                style={{ marginRight: 12 }}
                                checked={fieldValue?.assertJsonSchema?.checked}
                                onChange={(e) => {
                                    setAssertFieldValue('assertJsonSchema', {
                                        ...fieldValue?.assertJsonSchema,
                                        checked: e.target.checked
                                    });
                                    onBlurUpdateStep();
                                }}
                                disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                            />
                        </div>

                        <JSONMonacoEditor
                            height="50vh"
                            language="json"
                            value={fieldValue?.assertJsonSchema?.data || ''}
                            setEditor={setEditor}
                            onChange={(e) => {
                                setAssertFieldValue('assertJsonSchema', {
                                    ...fieldValue?.assertJsonSchema,
                                    data: e
                                });
                                onBlurUpdateStep();
                            }}
                            style={{ border: '1px solid #eee' }}
                            // onBlur={()=>onBlurUpdateStep(false)}
                            readOnly={['readonly', 'debug', 'execute']?.includes(editType)}
                        />
                    </div>
                </div>
            ) : (
                <div>
                    <div style={{ display: 'flex', alignItems: 'center', margin: '20px 0px' }}>
                        <div> 全量断言： </div>
                        <Switch
                            checked={assertFullDiff} 
                            onChange={(e) => {
                                setAssertFullDiff(e);
                                setAssertFieldValue('assertFullDiff', e);
                                onBlurUpdateStep();
                            }}
                            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                        />
                    </div>
                    {!assertFullDiff ? (
                        <div>
                            <CardTitle text={'指定 Key Diff 断言'} />
                            <Form
                                form={assertCheckKeyDiffForm}
                                autoComplete="off"
                                onValuesChange={(value) => {
                                    onBlurUpdateStep();
                                    const assertCheckKeyDiff =
                                        assertCheckKeyDiffForm.getFieldsValue()
                                            ?.assertCheckKeyDiff || [];
                                    setAssertFieldValue(
                                        'assertCheckKeyDiff',
                                        assertCheckKeyDiff?.map((item) => {
                                            return {
                                                jsonPath: item?.field?.jsonPath || '',
                                                checked: item?.checked || false
                                            };
                                        })
                                    );
                                }}
                            >
                                <Form.List name="assertCheckKeyDiff">
                                    {(fields, { add, remove }) => (
                                        <>
                                            {fields.map(({ key, name, ...restField }) => (
                                                <div
                                                    key={key}
                                                    style={{
                                                        display: 'flex',
                                                        width: '100%'
                                                    }}
                                                    align="baseline"
                                                >
                                                    <Form.Item
                                                        {...restField}
                                                        name={[name, 'checked']}
                                                        valuePropName="checked"
                                                    >
                                                        <Checkbox
                                                            style={{
                                                                marginRight: 4,
                                                                marginTop: 12
                                                            }}
                                                            onChange={onBlurUpdateStep}
                                                            disabled={[
                                                                'readonly',
                                                                'debug',
                                                                'execute'
                                                            ]?.includes(editType)}
                                                        />
                                                    </Form.Item>
                                                    <Form.Item
                                                        {...restField}
                                                        name={[name, 'field']}
                                                        style={{
                                                            width: '100%'
                                                        }}
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message: '断言字段不能为空'
                                                            }
                                                        ]}
                                                    >
                                                        <DiffItem
                                                            onBlurUpdateStep={onBlurUpdateStep}
                                                            // editType={editType}
                                                            key={'assertCheckKeyDiff'}
                                                            disabled={[
                                                                'readonly',
                                                                'debug',
                                                                'execute'
                                                            ]?.includes(editType)}
                                                        />
                                                    </Form.Item>
                                                    <MinusCircleOutlined
                                                        style={{
                                                            marginBottom: 20,
                                                            marginLeft: 4
                                                        }}
                                                        onClick={() => {
                                                            if (
                                                                [
                                                                    'readonly',
                                                                    'debug',
                                                                    'execute'
                                                                ]?.includes(editType)
                                                            ) {
                                                                return;
                                                            }
                                                            remove(name);
                                                            onBlurUpdateStep();
                                                        }}
                                                    />
                                                </div>
                                            ))}
                                            <Form.Item>
                                                <Button
                                                    type="dashed"
                                                    onClick={() => {
                                                        add({
                                                            checked: true,
                                                            field: { type: 3 },
                                                            data: null
                                                        });
                                                        onBlurUpdateStep();
                                                    }}
                                                    block
                                                    icon={<PlusOutlined />}
                                                    disabled={[
                                                        'readonly',
                                                        'debug',
                                                        'execute'
                                                    ]?.includes(editType)}
                                                >
                                                    添加一行数据
                                                </Button>
                                            </Form.Item>
                                        </>
                                    )}
                                </Form.List>
                            </Form>
                            <CardTitle text={'忽略 Key Diff 断言'} />
                            <Form
                                form={assertIgnoreKeyDiffForm}
                                autoComplete="off"
                                onValuesChange={(value) => {
                                    onBlurUpdateStep();
                                    const assertIgnoreKeyDiff =
                                        assertIgnoreKeyDiffForm.getFieldsValue()
                                            ?.assertIgnoreKeyDiff || [];
                                    setAssertFieldValue(
                                        'assertIgnoreKeyDiff',
                                        assertIgnoreKeyDiff?.map((item) => {
                                            return {
                                                jsonPath: item?.field?.jsonPath || '',
                                                checked: item?.checked || false
                                            };
                                        })
                                    );
                                }}
                            >
                                <Form.List name="assertIgnoreKeyDiff">
                                    {(fields, { add, remove }) => (
                                        <>
                                            {fields.map(({ key, name, ...restField }) => (
                                                <div
                                                    key={key}
                                                    style={{
                                                        display: 'flex',
                                                        width: '100%'
                                                    }}
                                                    align="baseline"
                                                >
                                                    <Form.Item
                                                        {...restField}
                                                        name={[name, 'checked']}
                                                        valuePropName="checked"
                                                    >
                                                        <Checkbox
                                                            style={{
                                                                marginRight: 4,
                                                                marginTop: 12
                                                            }}
                                                            onChange={onBlurUpdateStep}
                                                            disabled={[
                                                                'readonly',
                                                                'debug',
                                                                'execute'
                                                            ]?.includes(editType)}
                                                        />
                                                    </Form.Item>
                                                    <Form.Item
                                                        {...restField}
                                                        name={[name, 'field']}
                                                        style={{
                                                            width: '100%'
                                                        }}
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message: '断言字段不能为空'
                                                            }
                                                        ]}
                                                    >
                                                        <DiffItem
                                                            onBlurUpdateStep={onBlurUpdateStep}
                                                            // editType={editType}
                                                            disabled={[
                                                                'readonly',
                                                                'debug',
                                                                'execute'
                                                            ]?.includes(editType)}
                                                        />
                                                    </Form.Item>
                                                    <MinusCircleOutlined
                                                        style={{
                                                            marginBottom: 20,
                                                            marginLeft: 4
                                                        }}
                                                        onClick={() => {
                                                            if (
                                                                [
                                                                    'readonly',
                                                                    'debug',
                                                                    'execute'
                                                                ]?.includes(editType)
                                                            ) {
                                                                return;
                                                            }
                                                            remove(name);
                                                            onBlurUpdateStep();
                                                        }}
                                                    />
                                                </div>
                                            ))}
                                            <Form.Item>
                                                <Button
                                                    type="dashed"
                                                    onClick={() => {
                                                        add({
                                                            checked: true,
                                                            field: { type: 3 },
                                                            data: null
                                                        });
                                                        onBlurUpdateStep();
                                                    }}
                                                    block
                                                    icon={<PlusOutlined />}
                                                    disabled={[
                                                        'readonly',
                                                        'debug',
                                                        'execute'
                                                    ]?.includes(editType)}
                                                >
                                                    添加一行数据
                                                </Button>
                                            </Form.Item>
                                        </>
                                    )}
                                </Form.List>
                            </Form>
                        </div>
                    ) : null}
                </div>
            )}
        </div>
    );
}
