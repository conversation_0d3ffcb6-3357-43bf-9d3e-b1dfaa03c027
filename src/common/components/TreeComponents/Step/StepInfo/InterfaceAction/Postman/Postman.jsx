import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import {
    Button,
    Card,
    Col,
    Dropdown,
    Input,
    Menu,
    notification,
    Radio,
    Row,
    Select,
    Table,
    Tabs,
    Badge,
    Form,
    message,
    Space,
    Checkbox
} from 'antd';
import { isEmpty } from 'lodash';
import SelectWithDropdownRender from 'COMMON/components/Select/SelectWithDropdownRender';
import RunCaseSettingModal from 'FEATURES/front_qe_tools/case/edit/EditPage/Modal/RunCaseSettingModal/RunCaseSettingModal';
import AddServer from './AddServer';
// import ServerConfig from 'antd/lib/form';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { methodOptions } from 'COMMON/components/TreeComponents/Step/utils.js';
import { getServerList } from 'COMMON/api/front_qe_tools/config';
import OtherConfigurations from './OtherConfigurations';
import RequestParams from './RequestParams';
import ParameterExtraction from './ParameterExtraction';
import styles from './Postman.module.less';
import ResponseAssert from './ResponseAssert';
const { Option } = Select;
const { TabPane } = Tabs;
const Postman = (props) => {
    const {
        setPostmanData,
        postmanData,
        serverList,
        setServerList,
        currentSpace,
        getServerList,
        onBlurUpdateStep,
        currentStep,
        editType,
        onMethodChange,
        httpMethod
    } = props;
    const [options, setOptions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [path, setPath] = useState(null);
    const [urlError, setUrlError] = useState(null);

    const [messageApi, contextHolder] = message.useMessage();
    const { TextArea } = Input;

    useEffect(() => {
        getServerList({ moduleId: currentSpace?.id }).then((res) => {
            setServerList(res?.serverList);
        });
    }, [currentStep?.stepId]);

    // 请求url+params
    const appModalRef = useRef();
    const addServerRef = useRef();
    const validateUrl = (url) => {
        if (!url) {
            return;
        }
        // 匹配完整的 URL (http/https 协议)
        const fullUrlPattern = new RegExp(
            '^(https?:\\/\\/)' + // 协议 http:// 或者 https://
                '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z]{2,}|' + // 域名
                '((\\d{1,3}\\.){3}\\d{1,3}))' + // 或者IPv4地址
                '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // 端口和路径
                '(\\?[;&a-z\\d%_.~+=-]*)?$',
            'i'
        ); // 查询字符串

        // 匹配仅路径，以斜杠开头
        // 允许更多字符，例如子目录和多个路径部分 /gdoc/aigc
        // 匹配仅路径，以斜杠开头，允许以斜杠结尾 /v2/
        const pathPattern = new RegExp('^\\/([^\\/]+(\\/[^\\/]+)*)?\\/??$', 'i');
        return !!(pathPattern.test(url) || fullUrlPattern.test(url));
    };

    const selectedOption = useMemo(
        () => methodOptions.find((option) => option.value === httpMethod),
        [httpMethod]
    );
    // 校验初始化的 URL
    useEffect(() => {
        if (!postmanData?.requestInterface?.path) {
            return;
        }
        setUrlError(
            validateUrl(postmanData?.requestInterface?.path) ? null : '请输入有效的请求地址'
        );
    }, [postmanData?.requestInterface?.path]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const options = serverList.map((server) => ({
                    value: server.serverId,
                    label: server.serverName
                }));

                setOptions(options);
            } catch (error) {
                messageApi.error('获取服务器列表失败');
                console.error('Error fetching server list:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    useEffect(() => {
        setFieldValue('requestInterface', currentStep?.stepInfo?.requestInterface);
        setPostmanData(currentStep?.stepInfo);
    }, [currentStep?.stepId]);

    const setFieldValue = (field, value) => {
        setPostmanData({
            ...postmanData,
            [field]: value
        });
    };

    const setRequestInterfaceFieldValue = (field, value) => {
        setFieldValue('requestInterface', {
            ...postmanData.requestInterface,
            [field]: value
        });
    };

    const getCountOptionsLabel = (name, count) => {
        return (
            <div>
                {name}
                {count > 0 && (
                    <Badge
                        size="small"
                        count={count}
                        showZero={false}
                        color={'#448ef7'}
                        style={{ marginLeft: 4 }}
                    />
                )}
            </div>
        );
    };
    const getRequestLabel = () => {
        const show = !isEmpty(postmanData?.requestParams);
        return (
            <div>
                请求参数
                {show && <Badge size="small" dot color={'#448ef7'} style={{ marginLeft: 4 }} />}
            </div>
        );
    };
    const items = [
        {
            key: '1',
            label: getRequestLabel(),
            children: (
                <div>
                    <RequestParams
                        fieldValue={postmanData?.requestParams}
                        setFieldValue={(value) => {
                            setFieldValue('requestParams', value);
                        }}
                        currentStep={currentStep}
                        onBlurUpdateStep={onBlurUpdateStep}
                        editType={editType}
                    />
                </div>
            )
        },
        {
            key: '2',
            label: getCountOptionsLabel(
                '返回断言',
                (postmanData?.responseAssert?.assertText?.filter((v) => v.checked)?.length || 0) +
                    (postmanData?.responseAssert?.assertJson?.filter((v) => v.checked)?.length ||
                        0) +
                    (postmanData?.responseAssert?.assertJsonSchema?.checked ? 1 : 0)
            ),
            children: (
                <ResponseAssert
                    fieldValue={postmanData?.responseAssert}
                    setFieldValue={(value) => {
                        setFieldValue('responseAssert', value);
                    }}
                    currentStep={currentStep}
                    onBlurUpdateStep={onBlurUpdateStep}
                    editType={editType}
                />
            )
        },
        {
            key: '3',
            label: getCountOptionsLabel(
                '参数提取',
                postmanData?.variableExtract?.filter((v) => v.checked)?.length || 0
            ),
            children: (
                <ParameterExtraction
                    fieldValue={postmanData?.variableExtract}
                    setFieldValue={(value) => {
                        setFieldValue('variableExtract', value);
                    }}
                    currentStep={currentStep}
                    onBlurUpdateStep={onBlurUpdateStep}
                    editType={editType}
                />
            )
        },
        {
            key: '4',
            label: '其他配置',
            children: (
                <OtherConfigurations
                    fieldValue={postmanData}
                    setFieldValue={setFieldValue}
                    currentStep={currentStep}
                    onBlurUpdateStep={onBlurUpdateStep}
                    editType={editType}
                />
            )
        }
    ];
    const showAppModal = () => {
        appModalRef?.current.show({ key: 'server-config' });
    };

    const openCreateModal = () => {
        if (addServerRef.current) {
            addServerRef.current.openCreateModal();
        }
    };
    const items1 = [
        {
            key: '1',
            label: '请求接口',
            children: (
                <>
                    <div className={styles.items}>
                        <div>服务选择</div>
                        <SelectWithDropdownRender
                            style={{
                                flex: 1,
                                marginLeft: 12
                            }}
                            placeholder="请选择服务" // 占位符文本
                            text="服务选择" // 用于显示的文本
                            showSearchIcon={false} // 显示搜索图标
                            options={serverList.map((item) => ({
                                // 转换服务列表为组件需要的格式
                                value: item.serverId,
                                label: item.serverName
                            }))}
                            filterOption={(input, option) => option.label.includes(input)}
                            showSearch
                            value={postmanData?.requestInterface?.serverId} // 当前选中的服务ID
                            onChange={(value) => {
                                // 当选中的服务变化时，执行的操作
                                setRequestInterfaceFieldValue('serverId', value);
                                onBlurUpdateStep();
                            }}
                            onClick={() => {
                                getServerList({
                                    moduleId: currentSpace?.id
                                });
                            }}
                            addChange={() => {
                                openCreateModal();
                            }}
                            settingChange={() => showAppModal()}
                            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                        />
                    </div>

                    <div className={styles.items}>
                        <div>方法路径</div>
                        <Space.Compact style={{ flex: 1 }}>
                            <Select
                                // bordered={true}
                                size="mini"
                                value={httpMethod}
                                onChange={onMethodChange}
                                style={{ marginLeft: 12, width: 100, color: selectedOption?.color }}
                                labelRender={() => (
                                    <span
                                        style={{ color: selectedOption?.color, fontWeight: 'bold' }}
                                    >
                                        {selectedOption?.label}
                                    </span>
                                )}
                                disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                            >
                                {methodOptions.map((option) => (
                                    <option
                                        style={{ color: option.color, fontWeight: 'bold' }}
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </option>
                                ))}
                            </Select>
                            <Input
                                style={{
                                    flex: 1,
                                    width: '100%'
                                    // marginLeft: 12
                                }}
                                value={postmanData.requestInterface?.path}
                                placeholder="接口路径，以 / 起始"
                                onChange={(e) => {
                                    // setUrl(e.target.value);
                                    setPath(e.target.value);
                                    setRequestInterfaceFieldValue('path', e.target.value);
                                    setUrlError(
                                        validateUrl(e.target.value) ? null : '请输入有效的请求地址'
                                    );
                                    // splitUrl(e.target.value);
                                }}
                                onBlur={() => {
                                    if (urlError) {
                                        messageApi.error({
                                            content: '保存失败，请输入有效的请求地址'
                                        });
                                        return;
                                    }
                                    onBlurUpdateStep(false);
                                }}
                                disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                            />
                        </Space.Compact>
                    </div>
                    {urlError && (
                        <div style={{ color: 'red', marginLeft: '80px', fontSize: '12px' }}>
                            {urlError}
                        </div>
                    )}
                </>
            )
        }
    ];

    return (
        <div>
            {contextHolder}
            <Tabs defaultActiveKey="1" items={items1} />
            <Tabs defaultActiveKey="1" items={items} />
            <RunCaseSettingModal ref={appModalRef} />
            <AddServer ref={addServerRef} />
        </div>
    );
};

export default connectModel([commonModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    serverList: state.common.case.serverList,
    currentSpace: state.common.base.currentSpace
    // 其他需要的状态
}))(Postman);
