import React, { useEffect, useState } from 'react';
import { connectModel } from 'COMMON/middleware';
import { Button, Input, Select, Form, Space, Checkbox, Switch } from 'antd';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { isEmpty } from 'lodash';
import styles from './Postman.module.less';

const { Option } = Select;

const ParameterFormItem = (props) => {
    const { onBlurUpdateStep, currentStep, editType } = props;
    const [value, setValue] = useState({});
    const [disabled, setDisabled] = useState(false);
    useEffect(() => {
        // console.log('props.value', props.value, props.defaultValue);
        if (!isEmpty(props.defaultValue)) {
            setValue(props.defaultValue);
        } else if (!isEmpty(props.value)) {
            setValue(props.value);
        } else {
            setValue({});
        }
    }, [props.defaultValue, props.value]);
    useEffect(() => {
        if (props.value?.scope === 1) {
            setDisabled(true);
        } else {
            setDisabled(false);
        }
    }, [props.value?.scope]);
    const onBlur = (field, fieldValue) => {
        props.onChange({
            ...value,
            [field]: fieldValue
        });
    };

    return (
        <>
            <div
                style={{
                    display: 'flex',
                    alignItems: 'center'
                }}
            >
                <Input
                    style={{
                        marginRight: 6,
                        width: 190,
                        flexShrink: 0
                    }}
                    defaultValue={props.value?.name}
                    onChange={(e) => {
                        onBlur('name', e.target.value);
                        onBlurUpdateStep();
                    }}
                    placeholder="变量名称"
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    allowClear
                />
                <Select
                    placeholder="请选择提取来源"
                    options={[
                        { value: 1, label: '响应 JSON' },
                        { value: 2, label: '响应 Header' }
                    ]}
                    defaultValue={props.value?.source || 1}
                    onChange={(selectvalue) => {
                        onBlur('source', selectvalue);
                        onBlurUpdateStep();
                    }}
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    allowClear
                />
            </div>
            <div style={{ display: 'flex', marginTop: 8 }}>
                <Select
                    style={{
                        marginRight: 6,
                        width: 190,
                        flexShrink: 0
                    }}
                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                    // value={value?.type || props.defaultValue?.type}
                    placeholder="请选择提取内容"
                    options={[
                        { value: 1, label: '整个返回数据' },
                        { value: 2, label: '提取部分' }
                    ]}
                    defaultValue={props.value?.scope || 2}
                    onChange={(selectvalue) => {
                        if (selectvalue === 1) {
                            props.onChange({
                                ...value,
                                jsonPath: null,
                                scope: selectvalue
                            });
                        } else {
                            onBlur('scope', selectvalue);
                        }
                        onBlurUpdateStep();
                    }}
                    allowClear
                />
                {!disabled ? (
                    <Input
                        defaultValue={disabled ? null : props.value?.jsonPath}
                        placeholder="如：$.store.book[0].title"
                        disabled={disabled ||['readonly', 'debug', 'execute']?.includes(editType)}
                        // value={value?.jsonPath || props.defaultValue?.jsonPath}
                        onChange={(e) => {
                            onBlur('jsonPath', e.target.value);
                            // setValue({
                            //     ...value,
                            //     jsonPath: e.target.value
                            // });
                            onBlurUpdateStep();
                        }}
                        allowClear
                    />
                ) : null}
            </div>
        </>
    );
};

const ParameterExtraction = ({
    fieldValue,
    setFieldValue,
    onBlurUpdateStep,
    currentStep,
    editType
}) => {
    const [form] = Form.useForm();
    useEffect(() => {
        const list = (currentStep?.stepInfo?.variableExtract ?? []).map((formData) => {
            return {
                checked: formData.checked,
                field: {
                    name: formData.name ?? null,
                    source: formData.source ?? null,
                    jsonPath: formData.jsonPath ?? null,
                    scope: formData.scope ?? null
                }
            };
        });

        form.setFieldsValue({
            list: list
        });
        setFieldValue(currentStep?.stepInfo?.variableExtract);
    }, [currentStep?.stepId]);
    return (
        <div>
            <Form
                name="assertJson"
                style={{
                    // maxWidth: 600
                    width: '100%',
                }}
                form={form}
                autoComplete="off"
                onValuesChange={(value) => {
                    onBlurUpdateStep();
                    const list = form.getFieldsValue()?.list || [];
                    setFieldValue(
                        list?.map((item) => {
                            return {
                                name: item?.field?.name || '',
                                jsonPath: item?.field?.jsonPath || '',
                                source: item?.field?.source || '',
                                scope: item?.field?.scope || '',
                                checked: item?.checked || false
                            };
                        })
                    );
                }}
            >
                <Form.List name="list">
                    {(fields, { add, remove }) => (
                        <>
                            {fields.map(({ key, name, ...restField }) => (
                                <div
                                    key={key}
                                    style={{
                                        display: 'flex',
                                        alignItems: 'flex-start'
                                    }}
                                    align="baseline"
                                >
                                    <Form.Item
                                        {...restField}
                                        name={[name, 'checked']}
                                        valuePropName="checked"
                                    >
                                        <Checkbox
                                            style={{
                                                marginRight: 4
                                            }}
                                            onChange={onBlurUpdateStep}
                                        />
                                    </Form.Item>
                                    <Form.Item
                                        {...restField}
                                        style={{
                                            flex: 1
                                        }}
                                        name={[name, 'field']}
                                        rules={[
                                            {
                                                required: true,
                                                message: '断言字段不能为空'
                                            }
                                        ]}
                                    >
                                        <ParameterFormItem
                                            onBlurUpdateStep={onBlurUpdateStep}
                                            currentStep={currentStep}
                                            editType={editType}
                                            disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                                        />
                                    </Form.Item>
                                    <MinusCircleOutlined
                                        style={{
                                            marginTop: 8,
                                            marginLeft: 4
                                        }}
                                        onClick={() => {
                                            // if (
                                            //     ['readonly', 'debug', 'execute']?.includes(editType)
                                            // ) {
                                            //     return;
                                            // }
                                            remove(name);
                                            onBlurUpdateStep();
                                        }}
                                    />
                                </div>
                            ))}
                            <Form.Item>
                                <Button
                                    type="dashed"
                                    onClick={() => {
                                        add({ checked: true, field: { scope: 2, source: 1 } });
                                        onBlurUpdateStep();
                                    }}
                                    block
                                    icon={<PlusOutlined />}
                                    disabled={['readonly', 'debug', 'execute']?.includes(editType)}
                                >
                                    添加一行数据
                                </Button>
                            </Form.Item>
                        </>
                    )}
                </Form.List>
            </Form>
        </div>
    );
};

export default connectModel([commonModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice
}))(ParameterExtraction);
