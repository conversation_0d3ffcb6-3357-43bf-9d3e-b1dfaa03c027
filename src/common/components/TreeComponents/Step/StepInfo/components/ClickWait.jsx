import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {InputNumber} from 'antd';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './common.module.less';

export default ({editType, currentStep, handleUpdateStep}) => {
    const [times, setTimes] = useState(0);

    useEffect(() => {
        setTimes(currentStep.stepInfo.params.actionInfo?.before?.sleep ?
            currentStep.stepInfo.params.actionInfo.before.sleep : 3);
    }, [currentStep]);

    return (
        <div className={styles.paramsBox}>
            <div className={styles.paramsLeft}>定位后等待</div>
            <InputNumber
                className={classnames(styles.paramsCenter, 'input_editor')}
                min={0}
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                value={parseInt(times / 1000, 10)}
                onChange={(value) => {
                    setTimes(value * 1000);
                }}
                onBlur={(e) => {
                    let value = 0;
                    if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                        value = parseInt(e.target.value.trim(), 10);
                    }
                    if (currentStep.stepInfo.params.actionInfo?.before?.sleep !== value) {
                        currentStep.stepInfo.params.actionInfo.before = {
                            ...currentStep.stepInfo.params.actionInfo.before,
                            sleep: value * 1000
                        };
                        handleUpdateStep(currentStep);
                    }
                }}
            />
            <div className={styles.paramsRight}>秒</div>
        </div>
    );
};
