import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {InputNumber} from 'antd';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './common.module.less';

export default ({editType, currentStep, handleUpdateStep}) => {
    const [times, setTimes] = useState(0);

    useEffect(() => {
        const stepInterval = currentStep?.stepInfo?.common?.stepInterval;
        setTimes(isNumber(stepInterval) ? stepInterval : 2);
    }, [currentStep]);

    return (
        <div className={styles.paramsBox}>
            <div className={styles.paramsLeft}>步骤后等待</div>
            <InputNumber
                className={classnames(styles.paramsCenter, 'input_editor')}
                min={0}
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                value={times}
                onChange={(value) => {
                    setTimes(value);
                }}
                onBlur={(e) => {
                    let value = 0;
                    if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                        value = parseInt(e.target.value.trim(), 10);
                    }
                    if (!currentStep?.stepInfo?.common) {
                        currentStep.stepInfo.common = {};
                    }
                    if (currentStep.stepInfo.common?.stepInterval !== value) {
                        currentStep.stepInfo.common.stepInterval = value;
                        handleUpdateStep(currentStep);
                    }
                }}
            />
            <div className={styles.paramsRight}>秒</div>
        </div>
    );
};
