import {InputNumber} from 'antd';
import classnames from 'classnames';
import {useState, useEffect} from 'react';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './common.module.less';

export default ({editType, currentStep, handleUpdateStep}) => {
    const [times, setTimes] = useState(0);

    useEffect(() => {
        setTimes(currentStep.stepInfo.params.findParams?.before?.wait ?
            currentStep.stepInfo.params.findParams.before.wait : 0);
    }, [currentStep]);

    return (
        <div className={styles.paramsBox}>
            <div className={styles.paramsLeft}>定位前等待</div>
            <InputNumber
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                className={classnames(styles.paramsCenter, 'input_editor')}
                min={0}
                max={60}
                value={parseInt(times / 1000, 10)}
                onChange={(value) => {
                    setTimes(value * 1000);
                }}
                onBlur={(e) => {
                    let value = 0;
                    if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                        value = parseInt(e.target.value.trim(), 10);
                    }
                    if (currentStep.stepInfo.params.findParams?.before?.wait !== value) {
                        currentStep.stepInfo.params.findParams = {
                            ...currentStep.stepInfo.params.findParams,
                            before: {wait: value * 1000}
                        };
                        handleUpdateStep(currentStep);
                    }
                }}
            />
            <div className={styles.paramsRight}>秒</div>
        </div>
    );
};
