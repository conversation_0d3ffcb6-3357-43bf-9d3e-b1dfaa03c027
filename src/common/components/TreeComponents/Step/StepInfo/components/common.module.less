@import "RESOURCES/css/common.less";

.paramsItem,
.paramsSmallItem {
    margin-bottom: 10px;
    overflow: hidden;
}

.paramsSwitch {
    position: absolute;
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
}

.paramsLeft {
    width: 100px;
    // float: left;
    font-size: 14px;
}

.paramsCenter {
    width: calc(100% - 150px);
    float: left;
}

.paramsRight {
    float: left;
    width: 30px;
    margin-left: 10px;
}
.paramsBox {
    display: flex;
    width: 100%;
    margin: 8px 0;
    align-items: center;
}
.inputBox {
    width: calc(100% - 50px);
    margin-right: 10px;
    // flex: 1
}