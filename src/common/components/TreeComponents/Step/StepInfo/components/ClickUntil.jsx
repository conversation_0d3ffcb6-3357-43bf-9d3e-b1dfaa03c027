import classnames from 'classnames';
import {InputNumber, Switch} from 'antd';
import {useState, useEffect} from 'react';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './common.module.less';

export default ({editType, currentStep, handleUpdateStep}) => {
    const [interval, setInterval] = useState(0);
    const [times, setTimes] = useState(0);
    const [open, setOpen] = useState(false);


    useEffect(() => {
        if (-1 !== ['tap'].indexOf(currentStep.stepInfo.params.actionInfo.type) &&
            currentStep.stepInfo.params.findParams?.until) {
            setInterval(currentStep.stepInfo.params.findParams.until.interval);
            setTimes(currentStep.stepInfo.params.findParams.until.times);
            setOpen(true);
        }
    }, [currentStep]);

    const onDel = () => {
        delete currentStep.stepInfo.params.findParams.until;
        handleUpdateStep(currentStep);
    };


    return (
        <div className={styles.paramsItem}>
            <div className={styles.paramsLeft}>
                Click Until
                <Switch
                    checked={open}
                    style={{
                        marginLeft: 5
                    }}
                    size='small'
                    disabled={!isElectron() ||
                        ['readonly', 'debug', 'execute'].includes(editType)}
                    onChange={(checked) => {
                        setOpen(checked);
                        if (!checked) {
                            onDel();
                        } else {
                            currentStep.stepInfo.params.findParams = {
                                ...currentStep.stepInfo.params.findParams,
                                until: {
                                    enable: true,
                                    times: 1,
                                    interval: 2000
                                }
                            };
                            setInterval(2000);
                            setTimes(1);
                            handleUpdateStep({...currentStep});
                        }
                    }}
                />
            </div>
            {
                open ? <br /> : null
            }
            {
                open ?
                    <div className={styles.paramsItem}>
                        <div className={styles.paramsLeft}>点击直到不存在次数</div>
                        <InputNumber
                            size='small'
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            className={classnames(styles.paramsCenter, 'input_editor')}
                            min={0}
                            value={times}
                            onChange={(value) => {
                                setTimes(value);
                            }}
                            onBlur={(e) => {
                                let value = 0;
                                if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                                    value = parseInt(e.target.value.trim(), 10);
                                }
                                if (currentStep.stepInfo.params.findParams.until.times !== value) {
                                    currentStep.stepInfo.params.findParams.until.times = value;
                                    handleUpdateStep(currentStep);
                                }
                            }}
                        />
                        <div className={styles.paramsRight}>次</div>
                    </div> : null
            }
            {
                open ?
                    <div className={styles.paramsItem}>
                        <div className={styles.paramsLeft}>控件点击间隔</div>
                        <InputNumber
                            className={classnames(styles.paramsCenter, 'input_editor')}
                            size='small'
                            min={0}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            value={interval}
                            onChange={(value) => {
                                setInterval(value);
                            }}
                            onBlur={(e) => {
                                let value = 0;
                                if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                                    value = parseInt(e.target.value.trim(), 10);
                                }
                                if (currentStep.stepInfo.params.findParams.until.interval !== value) {
                                    currentStep.stepInfo.params.findParams.until.interval = value;
                                    handleUpdateStep(currentStep);
                                }
                            }}
                        />
                        <div className={styles.paramsRight}>毫秒</div>
                    </div> : null
            }
        </div>

    );
};
