import {Switch} from 'antd';
import {useState, useEffect} from 'react';
import styles from './common.module.less';

export default ({editType, currentStep, handleUpdateStep}) => {
    const [allowFail, setAllowFail] = useState(false);
    useEffect(() => {
        if (currentStep.stepInfo.params.findParams?.allowFail) {
            setAllowFail(currentStep.stepInfo.params.findParams.allowFail);
        } else {
            setAllowFail(false);
        }
    }, [currentStep?.stepId]);

    return (
        <div className={styles.paramsBox}>
            <div className={styles.paramsLeft}>
                <Switch
                    checked={allowFail}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    onChange={(checked) => {
                        setAllowFail(checked);
                        currentStep.stepInfo.params.findParams.allowFail = checked;
                        handleUpdateStep({...currentStep});
                    }}
                />
            </div>
            <div>
                执行失败后忽略错误，继续执行
            </div>
        </div>

    );
};
