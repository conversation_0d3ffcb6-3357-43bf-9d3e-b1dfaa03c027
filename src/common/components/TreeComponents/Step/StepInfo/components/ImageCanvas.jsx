import { useState, useEffect, useRef } from 'react';
import { message } from 'antd';

function ImageCanvas(props) {
    const { rect, stepWidth, stepHeight, currentStep, editType, onFinish, imgSrc, isCanvas } =
        props;
    const [innerHeight, setInnerHeight] = useState(window.innerHeight);
    const [innerWidth, setInnerWidth] = useState(window.innerWidth);
    const [screenPos, setScreenPos] = useState({ x: 0, y: 0, targetX: 0, targetY: 0 });
    const canvas = useRef();
    let imageScale = 1;
    const ratio = window.devicePixelRatio || 1;

    // 页面大小变动后，图片resize
    useEffect(() => {
        window.onresize = () => {
            setInnerHeight(window.innerHeight);
            setInnerWidth(window.innerWidth);
        };
    }, []);

    useEffect(() => {
        if (!rect) {
            setScreenPos({ x: 0, y: 0, targetX: 0, targetY: 0 });
            return;
        }
        // 101、201 步骤
        if ([101, 201]?.includes(currentStep.stepType)) {
            let { x, y, width, height } = rect;
            if (0 !== x && 0 !== y && 0 !== width && 0 !== height) {
                screenPos.x = x > 1 ? x / stepWidth : x;
                screenPos.y = y > 1 ? y / stepHeight : y;
                screenPos.targetX = x > 1 ? (x + width) / stepWidth : x + width;
                screenPos.targetY = y > 1 ? (y + height) / stepHeight : y + height;
                setScreenPos(screenPos);
                return;
            }
        }
        if ([601]?.includes(currentStep?.stepType)) {
            let { x, y, w, h } = rect;
            screenPos.x = x / stepWidth;
            screenPos.y = y / stepHeight;
            screenPos.targetX = (x + w) / stepWidth;
            screenPos.targetY = (y + h) / stepHeight;
            setScreenPos(screenPos);
            return;
        }
        setScreenPos({ x: 0, y: 0, targetX: 0, targetY: 0 });
    }, [rect, imgSrc]);

    useEffect(() => {
        const context = canvas.current.getContext('2d');
        const img = new Image();
        let url = imgSrc;
        if (url?.startsWith('http://')) {
            let res = url.split(':');
            url = 'https:' + res[1];
        }
        img.src = url;
        img.onload = () => {
            context.drawImage(img, 0, 0, imgWidth * ratio, imgHeight * ratio);
            draw(context);
        };
    }, [currentStep?.stepId, screenPos, imgSrc, innerHeight, innerWidth]);

    // 单击事件监听
    useEffect(() => {
        document.addEventListener('mousedown', downAction);
        return () => document.removeEventListener('mousedown', downAction);
    }, [currentStep, isCanvas, screenPos]);

    // 单击事件监听
    useEffect(() => {
        document.addEventListener('mouseup', upAction);
        return () => document.removeEventListener('mouseup', upAction);
    }, [currentStep, isCanvas, screenPos]);

    let standardHeight = innerHeight - 350;
    let standardWidth = ((innerWidth - 20) * 0.6 - 60) * 0.48 - 54;
    let imgWidth = !stepWidth || 0 === stepWidth ? standardWidth : stepWidth;
    let imgHeight = !stepHeight || 0 === stepHeight ? standardHeight : stepHeight;
    if (imgWidth >= standardWidth) {
        imgHeight = imgHeight * (standardWidth / imgWidth);
        imageScale = standardWidth / imgWidth;
        imgWidth = standardWidth;
    }
    if (imgHeight >= standardHeight) {
        imgWidth = imgWidth * (standardHeight / imgHeight);
        imageScale = imageScale * (standardHeight / imgHeight);
        imgHeight = standardHeight;
    }

    const downAction = (e) => {
        try {
            if (['readonly', 'debug', 'execute'].includes(editType)) {
                return false;
            }
            if (e.target.className === 'screenImage') {
                if (!isCanvas) {
                    return false;
                }
                let ratio = window.devicePixelRatio || 1;
                setScreenPos({
                    x: (e.offsetX * ratio) / e.target.width,
                    y: (e.offsetY * ratio) / e.target.height,
                    targetX: 0,
                    targetY: 0
                });
            }
        } catch (err) {
            message.error(err.message ? err.message : err);
        }
    };

    const upAction = async (e) => {
        try {
            if (['readonly', 'debug', 'execute'].includes(editType)) {
                return false;
            }
            if (e.target.className === 'screenImage') {
                if (!isCanvas) {
                    return false;
                }
                let { x, y } = screenPos;
                let width = 0;
                let height = 0;
                let ratio = window.devicePixelRatio || 1;
                setScreenPos({
                    ...screenPos,
                    targetX: (e.offsetX * ratio) / e.target.width,
                    targetY: (e.offsetY * ratio) / e.target.height
                });
                x =
                    x > (e.offsetX * ratio) / e.target.width
                        ? (e.offsetX * ratio) / e.target.width
                        : x;
                y =
                    y > (e.offsetY * ratio) / e.target.height
                        ? (e.offsetY * ratio) / e.target.height
                        : y;
                width = Math.abs((e.offsetX * ratio) / e.target.width - x);
                height = Math.abs((e.offsetY * ratio) / e.target.height - y);
                onFinish &&
                    onFinish({
                        x: Math.round(x * stepWidth),
                        y: Math.round(y * stepHeight),
                        width: Math.round(width * stepWidth),
                        height: Math.round(height * stepHeight)
                    });
            }
        } catch (err) {
            console.log(err);
            message.error(err.message ? err.message : err);
        }
    };

    const draw = (context) => {
        // 绘制滑动线段
        let { x, y, targetX, targetY } = screenPos;
        if (0 !== x && 0 !== y && 0 !== targetX && 0 !== targetY) {
            x *= stepWidth * imageScale * ratio;
            y *= stepHeight * imageScale * ratio;
            targetX *= stepWidth * imageScale * ratio;
            targetY *= stepHeight * imageScale * ratio;
            context.lineWidth = 4;
            context.strokeStyle = 'red';
            context.beginPath();
            context.moveTo(x, y);
            context.lineTo(targetX, y);
            context.stroke();
            context.moveTo(x, y);
            context.lineTo(x, targetY);
            context.stroke();
            context.moveTo(targetX, y);
            context.lineTo(targetX, targetY);
            context.stroke();
            context.moveTo(x, targetY);
            context.lineTo(targetX, targetY);
            context.stroke();
            // 画箭头
            context.closePath();
        }
    };

    return (
        <canvas
            ref={canvas}
            width={imgWidth * ratio}
            height={imgHeight * ratio}
            style={{
                width: imgWidth,
                height: imgHeight,
                border: '1px solid #eee'
            }}
            className={-1 === ['execute', 'debug'].indexOf(editType) ? 'screenImage' : ''}
        />
    );
}

export default ImageCanvas;
