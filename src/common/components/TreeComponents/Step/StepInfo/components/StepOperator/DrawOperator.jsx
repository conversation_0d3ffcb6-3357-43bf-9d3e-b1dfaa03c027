import classnames from 'classnames';
import { DeleteOutlined, HighlightOutlined } from '@ant-design/icons';
import { Popover } from 'antd';
import styles from './common.module.less';

function DrawOperator(props) {
    const { disabled = false, onClick, onClear, isCanvas } = props;

    if (disabled) {
        return;
    }

    return (
        <Popover
            content={
                <DeleteOutlined
                    className={styles.deleteIcon}
                    onClick={() => onClear && onClear()}
                />
            }
        >
            <span
                className={classnames(styles.operatorItem, {
                    [styles.activedOperatorItem]: isCanvas
                })}
                onClick={onClick}
            >
                <HighlightOutlined />
                &nbsp;绘制
            </span>
        </Popover>
    );
}

export default DrawOperator;
