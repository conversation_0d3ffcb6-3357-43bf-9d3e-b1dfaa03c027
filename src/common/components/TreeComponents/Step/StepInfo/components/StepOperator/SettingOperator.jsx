import { SettingOutlined } from '@ant-design/icons';
import styles from './common.module.less';

function SettingOperator(props) {
    const { disabled = false, onClick } = props;
    if (disabled) {
        return;
    }

    return (
        <span className={styles.operatorItem} onClick={onClick}>
            <SettingOutlined />
            &nbsp;设置
        </span>
    );
}

export default SettingOperator;
