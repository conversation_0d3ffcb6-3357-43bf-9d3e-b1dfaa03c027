import { useRef } from 'react';
import { Upload, message } from 'antd';
import { CloudUploadOutlined } from '@ant-design/icons';
import { fileImageUpload } from 'COMMON/utils/utils';
import styles from './common.module.less';

function UploadOperator(props) {
    const { disabled = false, editType, setLoading, onChange } = props;

    const uploadRef = useRef();
    if (disabled) {
        return;
    }
    const beforeUpload = (file) => {
        const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
        if (!isJpgOrPng) {
            message.error('请上传 JPG/PNG 格式图片!');
        }
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isLt2M) {
            message.error('图片必须小于 2MB!');
        }
        return isJpgOrPng && isLt2M;
    };

    const customRequest = async (file) => {
        return new Promise(function (resolve, reject) {
            const fileReader = new FileReader();
            fileReader.onload = (e) => {
                const src = e.target.result;
                console.log(src);

                const image = new Image();
                image.onload = async function () {
                    uploadImageAction({
                        file: file,
                        url: e.target.result,
                        screenSize: { width: this.width, height: this.height }
                    });
                };
                image.onerror = reject;
                image.src = src;
            };
            fileReader.readAsDataURL(file);
        });
    };

    const uploadImageAction = async ({ file, url, screenSize }) => {
        setLoading && setLoading(true);
        let data = file?.name?.split('.');
        if (data?.length < 1) {
            return message.error('图片上传失败');
        }
        fileImageUpload(file)
            .then(async (res) => {
                onChange && onChange(res?.url, screenSize);
                setLoading && setLoading(false);
            })
            .catch((err) => {
                console.log(err?.message ?? err);
                setLoading && setLoading(false);
            });
    };

    // 上传文件按钮的点击事件
    const openImport = () => {
        uploadRef.current.click();
    };

    // 导入文件的功能
    const handleImport = (event) => {
        const file = event.target.files[0];
        if (!file) {
            return;
        }
        if (!beforeUpload(file)) {
            return;
        }
        customRequest(file);
    };

    return (
        <>
            <span className={styles.operatorItem} onClick={openImport}>
                <CloudUploadOutlined />
                &nbsp;上传
            </span>
            <input
                ref={uploadRef}
                type="file"
                onChange={(e) => handleImport(e)}
                style={{ display: 'none' }}
            />
        </>
    );
}

export default UploadOperator;
