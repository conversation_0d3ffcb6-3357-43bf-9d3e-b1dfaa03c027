import { useState, useEffect } from 'react';
import { Tooltip, Select } from 'antd';
import classnames from 'classnames';
import { isEmpty } from 'lodash';
import commonStyles from 'COMMON/components/TreeComponents/Step/StepInfo/StepInfo.module.less';

function SelectOperator(props) {
    const {
        currentStep,
        styles,
        border = true,
        findInfoType,
        editType,
        handleUpdateStep,
        sizeType = 'normal',
        // 空表示所有
        includeOptions = []
    } = props;
    const [actionType, setActionType] = useState(null);

    useEffect(() => {
        if (currentStep?.stepId) {
            setActionType(currentStep?.stepInfo?.params?.actionInfo?.type);
        }
    }, [currentStep?.stepId, handleUpdateStep]);

    const options = [
        {
            value: 'tap',
            label: '点击'
        },
        {
            value: 'input',
            label: '输入'
        },
        {
            value: 'swipe',
            disabled: currentStep?.stepType === 601,
            label: '滑动'
        },
        {
            value: 'addTap',
            disabled: currentStep?.stepType === 601,
            label: '长按'
        },
        {
            value: 'nope',
            disabled: currentStep?.stepType === 601,
            label: '存在'
        },
        {
            value: 'absence',
            disabled: currentStep?.stepType === 601,
            label: '不存在'
        },
        {
            value: 'drag',
            disabled: currentStep?.stepType === 601 || ![10]?.includes(currentStep?.stepInfo?.type),
            label: '拖拽'
        },
        // 仅单控件
        {
            value: 'expression',
            disabled:
                currentStep?.stepType === 601 ||
                ![10]?.includes(currentStep?.stepInfo?.type) ||
                !currentStep?.stepInfo?.params?.findInfo?.baseInfo?.chosenTag?.includes('single'),
            label: '表达式校验'
        }
    ];
    return (
        <Tooltip title="切换操作" placement="left">
            <Select
                style={{ ...styles }}
                size="small"
                bordered={border}
                popupMatchSelectWidth={false}
                popupClassName={false}
                className={classnames(commonStyles.paramsSelect, {
                    [commonStyles.paramsSmallSelect]: sizeType === 'small'
                })}
                value={
                    actionType === 'tap' &&
                    currentStep?.stepInfo.params?.actionInfo?.params?.duration
                        ? 'addTap'
                        : actionType
                }
                options={(!isEmpty(includeOptions)
                    ? options?.filter((item) => includeOptions.includes(item.value))
                    : options
                ).filter((item) => !item.disabled)}
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                onChange={async (type) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    try {
                        if (
                            -1 === ['tap'].indexOf(type) &&
                            undefined !== currentStep.stepInfo.params.findParams &&
                            undefined !== currentStep.stepInfo.params.findParams.until
                        ) {
                            delete currentStep.stepInfo.params.findParams.until;
                        }
                        if ('tap' === type) {
                            currentStep.actionType = 'tap';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'tap',
                                params: {}
                            };
                        } else if ('addTap' === type) {
                            currentStep.actionType = 'tap';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'tap',
                                params: {
                                    duration: 1000
                                }
                            };
                        } else if ('input' === type) {
                            currentStep.actionType = 'input';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'input',
                                params: {
                                    text: ''
                                }
                            };
                        } else if ('nope' === type) {
                            currentStep.actionType = 'nope';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'nope',
                                params: {}
                            };
                        } else if ('absence' === type) {
                            currentStep.actionType = 'absence';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'absence',
                                params: {}
                            };
                        } else if ('drag' === type) {
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'drag',
                                params: {
                                    direction: 1
                                }
                            };
                        } else if ('expression' === type) {
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'expression',
                                params: {
                                    expression: '${ONE_WIDGET_CONTENT}'
                                }
                            };
                        } else if ('swipe' === type) {
                            currentStep.actionType = 'swipe';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'swipe',
                                params: {
                                    direction: 1,
                                    duration: 500,
                                    times: 1
                                }
                            };
                            // 额外处理
                            if (findInfoType) {
                                if (
                                    undefined !==
                                    currentStep.stepInfo.params.findInfo?.[findInfoType].findType
                                ) {
                                    currentStep.stepInfo.params.findInfo[findInfoType].findType = 0;
                                }
                                if (
                                    undefined !==
                                    currentStep.stepInfo.params.findInfo?.[findInfoType]
                                        .scrollDirection
                                ) {
                                    delete currentStep.stepInfo.params.findInfo[findInfoType]
                                        .scrollDirection;
                                }
                                if (
                                    undefined !==
                                    currentStep.stepInfo.params.findInfo?.[findInfoType].screenCount
                                ) {
                                    currentStep.stepInfo.params.findInfo[
                                        findInfoType
                                    ].screenCount = 1;
                                }
                            }
                        }
                        setActionType(currentStep.stepInfo.params.actionInfo.type);
                        let newActionInfo = JSON.parse(JSON.stringify(currentStep.stepInfo));
                        if (
                            [9]?.includes(newActionInfo?.type) &&
                            newActionInfo?.params?.deviceInfo?.screenshot &&
                            !newActionInfo?.params?.deviceInfo?.screenshot?.startsWith('http')
                        ) {
                            newActionInfo.params.deviceInfo.screenshot = '';
                        }
                        if (
                            [10]?.includes(newActionInfo?.type) &&
                            newActionInfo?.params?.recordInfo?.deviceInfo?.screenshot &&
                            !newActionInfo?.params?.recordInfo?.deviceInfo?.screenshot?.startsWith(
                                'http'
                            )
                        ) {
                            newActionInfo.params.recordInfo.deviceInfo.screenshot = '';
                        }
                        await handleUpdateStep({
                            ...currentStep,
                            stepInfo: newActionInfo
                        });
                    } catch (err) {}
                }}
            />
        </Tooltip>
    );
}

export default SelectOperator;
