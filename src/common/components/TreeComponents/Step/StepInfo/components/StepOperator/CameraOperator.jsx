import { message } from 'antd';
import { CameraOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import electron from 'COMMON/utils/electron';
import { base64ImageUpload } from 'COMMON/utils/utils';
import styles from './common.module.less';

function CameraOperator(props) {
    const {
        disabled = false,
        editType,
        deviceList,
        currentDevice,
        curOsType,
        setRecording,
        onChange,
        setLoading
    } = props;

    if (disabled) {
        return;
    }

    const uploadImageAction = async ({ file, url, screenSize }) => {
        let data = file?.name?.split('.');
        if (data?.length < 1) {
            return message.error('图片上传失败');
        }
        setLoading && setLoading(true);
        base64ImageUpload(url)
            .then(async (res) => {
                onChange && onChange(res?.url, screenSize);
                setLoading && setLoading(false);
            })
            .catch((err) => {
                console.log(err?.message ?? err);
                setLoading && setLoading(false);
            });
    };

    return (
        <span
            className={styles.operatorItem}
            onClick={async () => {
                try {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    // 确定有设备连接
                    let gotDevice = false;
                    for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                        if (device.deviceId === currentDevice?.deviceId) {
                            gotDevice = true;
                            if (![2].includes(device?.status)) {
                                message.error('请确保设备状态正常');
                                return false;
                            }
                            break;
                        }
                    }
                    if (!gotDevice) {
                        message.error('请确保有设备连接');
                        return false;
                    }
                    setRecording(true);
                    let { screenshot, screenSize } = await electron.send(
                        'device.record.screenshot',
                        {
                            deviceType: curOsType,
                            deviceId: currentDevice?.deviceId
                        }
                    );
                    uploadImageAction({
                        file: { name: 'screenShot.png' },
                        url: screenshot,
                        screenSize
                    });
                } catch (err) {
                    message.error(err.message ? err.message : err);
                } finally {
                    setRecording(false);
                }
            }}
        >
            <CameraOutlined />
            &nbsp;截屏
        </span>
    );
}

export default connectModel([baseModel], (state) => ({
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice
}))(CameraOperator);
