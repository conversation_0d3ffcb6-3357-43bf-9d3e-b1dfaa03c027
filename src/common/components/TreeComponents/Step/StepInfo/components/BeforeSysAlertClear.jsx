import {Switch} from 'antd';
import classnames from 'classnames';
import styles from './common.module.less';

export default ({editType, currentStep, handleUpdateStep}) => {
    return (
        <div className={styles.paramsBox}>
            <div className={styles.paramsLeft}>
                <Switch
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    className={classnames('input_editor')}
                    checked={currentStep.stepInfo?.common?.commonAlertClear ?? true}
                    onChange={(value) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        if (!currentStep.stepInfo.common) {
                            currentStep.stepInfo.common = {};
                        }
                        currentStep.stepInfo.common = {
                            ...currentStep.stepInfo.common,
                            commonAlertClear: value
                        };
                        handleUpdateStep(currentStep);
                    }}
                />
            </div>
            <div className={styles.paramsSwitchText}>该步骤开始前点除所有出现的弹框</div>
        </div>
    );
};
