.AIAssertContent,
.AIAssertContentTextArea {
    position: absolute;
    width: calc(100% - 30px);
    background-color: #fafbff;
    border: 1px solid rgba(110, 103, 238, .2);
    border-radius: 3px;
    z-index: 9;
}

.AIAssertContent {
    margin-top: 10px;
    height: 40px;
    line-height: 40px;
}


.AIAssertContentTextArea {
    height: 150px;
}

.AIAssertTitle {
    position: absolute;
    top: 10px;
    left: 15px;
    height: 20px;
    line-height: 20px;
    width: 64px;
    color: #fff;
    background-color: #6c65f0;
    font-size: 11px;
    text-align: center;
    border-radius: 3px;
}

.AIAssertTitleClose {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 11px;
    color: #777;
    cursor: pointer;
}

.AIAssertDesc {
    width: calc(100% - 100px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 90px;
    font-size: 12px;
    cursor: pointer;
}

.AIAssertDescTextArea {
    margin-top: 40px;
    padding: 0 10px;

    :global {
        .ant-input-affix-wrapper-sm {
            font-size: 12px !important;
        }
    }
}