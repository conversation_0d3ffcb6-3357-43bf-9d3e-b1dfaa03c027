import { useState, useEffect, useRef } from 'react';
import { Input, message, Tooltip } from 'antd';
import { FullscreenExitOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import styles from './InputContent.module.less';

const { TextArea } = Input;

function InputContent(props) {
    const { data, editType, onBlur, title, isTextArea } = props;
    const [showTextArea, setShowTextArea] = useState(isTextArea ?? false);
    const [desc, setDesc] = useState(data || '');
    const inputRef = useRef(null);

    useEffect(() => {
        if (data !== undefined) {
            setDesc(data);
        }
    }, [data]);

    // 聚焦文本框 end
    useEffect(() => {
        if (inputRef.current && showTextArea) {
            inputRef.current.focus({
                cursor: 'end'
            });
        }
    }, [showTextArea, inputRef]);

    return (
        <div>
            {!showTextArea && (
                <div
                    className={styles.AIAssertContent}
                    onClick={(e) => {
                        e.stopPropagation();
                        setShowTextArea(true);
                    }}
                >
                    <div>
                        <span className={styles.AIAssertTitle}>{title ?? '描述'}</span>
                    </div>
                    <Tooltip title="点击">
                        <div className={styles.AIAssertDesc}>
                            {desc || (
                                <span style={{ color: '#c6c6ca' }}>
                                    {'请输入' + title ?? '请输入描述'}
                                </span>
                            )}
                        </div>
                    </Tooltip>
                </div>
            )}
            {showTextArea && (
                <div
                    className={styles.AIAssertContentTextArea}
                    onClick={() => setShowTextArea(true)}
                >
                    <div>
                        <span className={styles.AIAssertTitle}>{title ?? '描述'}</span>
                        {!isTextArea && (
                            <Tooltip title="收起">
                                <span
                                    className={styles.AIAssertTitleClose}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        setShowTextArea(false);
                                    }}
                                >
                                    <FullscreenExitOutlined />
                                </span>
                            </Tooltip>
                        )}
                    </div>
                    <div className={styles.AIAssertDescTextArea}>
                        <TextArea
                            ref={inputRef}
                            value={desc}
                            className="input_editor"
                            size="small"
                            showCount
                            maxLength={50}
                            style={{ height: 70, resize: 'none' }}
                            placeholder={'请输入' + title ?? '请输入描述'}
                            variant="borderless"
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return;
                                }
                                setDesc(e.target.value);
                            }}
                            onBlur={() => {
                                onBlur && onBlur(desc);
                            }}
                        />
                    </div>
                </div>
            )}
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace
}))(InputContent);
