import {InputNumber} from 'antd';
import classnames from 'classnames';
import {useState, useEffect} from 'react';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './common.module.less';

export default ({editType, currentStep, handleUpdateStep}) => {
    const [times, setTimes] = useState(1);
    const [interval, setInterval] = useState(0);

    useEffect(() => {
        setTimes(!currentStep.stepInfo.params.findParams?.times ?
            1 : currentStep.stepInfo.params.findParams.times);
        setInterval(!currentStep.stepInfo.params.findParams?.interval ?
            2000 : currentStep.stepInfo.params.findParams.interval);
    }, [currentStep]);

    return (
        <>
            <div className={styles.paramsBox}>
                <div className={styles.paramsLeft}>失败重试</div>
                <InputNumber
                    className={classnames(styles.paramsCenter, 'input_editor')}
                    min={1}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    value={times}
                    onChange={(value) => {
                        setTimes(value);
                    }}
                    onBlur={(e) => {
                        let value = 0;
                        if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                            value = parseInt(e.target.value.trim(), 10);
                        }
                        if (currentStep.stepInfo.params.findParams.times !== value) {
                            currentStep.stepInfo.params.findParams.times = value;
                            handleUpdateStep(currentStep);
                        }
                    }}
                />
                <div className={styles.paramsRight}>次</div>
            </div>
            <div className={styles.paramsBox}>
                <div className={styles.paramsLeft}>重试间隔</div>
                <InputNumber
                    className={classnames(styles.paramsCenter, 'input_editor')}
                    min={0}
                    value={interval}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    onChange={(value) => {
                        setInterval(value);
                    }}
                    onBlur={(e) => {
                        let value = 0;
                        if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                            value = parseInt(e.target.value.trim(), 10);
                        }
                        if (currentStep.stepInfo.params.findParams.interval !== value) {
                            currentStep.stepInfo.params.findParams.interval = value;
                            handleUpdateStep(currentStep);
                        }
                    }}
                />
                <div className={styles.paramsRight}>毫秒</div>
            </div>
        </>
    );
};
