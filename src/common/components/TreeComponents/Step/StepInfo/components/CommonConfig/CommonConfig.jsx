import {useState, useEffect} from 'react';
import {isNaN} from 'lodash';
import classnames from 'classnames';
import {InputNumber, Switch, message} from 'antd';
import { isNumber } from 'COMMON/components/TreeComponents/Step/utils';
import {CardTitle} from 'COMMON/components/common/Card';
import styles from './CommonConfig.module.less';

function CommonConfig(props) {
    const {currentStep, editType, handleUpdateStep} = props;
    const [commonAlertClear, setCommonAlertClear] = useState(false);
    const [stepInterval, setStepInterval] = useState(2);

    useEffect(() => {
        setCommonAlertClear(currentStep?.stepInfo?.common?.commonAlertClear ?? true);
        const stepInterval = currentStep?.stepInfo?.common?.stepInterval;
        setStepInterval(isNumber(stepInterval) ? stepInterval : 2);
    }, [currentStep?.stepId]);

    return (
        <div>
            <CardTitle text='步骤参数' />
            <div className={styles.paramsSelect}>
                <span className={styles.paramsSelectTitle}>
                    <Switch
                        size="small"
                        checked={commonAlertClear}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        onChange={async () => {
                            try {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let newCurrentStep = {...currentStep};
                                if (!newCurrentStep?.stepInfo?.common) {
                                    newCurrentStep.stepInfo.common = {};
                                }
                                newCurrentStep.stepInfo.common.commonAlertClear = !commonAlertClear;
                                await handleUpdateStep(newCurrentStep);
                                setCommonAlertClear(!commonAlertClear);
                            } catch (err) {
                                console.log(err?.message ?? err);
                            }
                        }}
                    />
                </span>
                <span className={styles.paramsSelectData}>
                    该步骤前点除所有出现的弹框
                </span>
            </div>
            <div className={styles.paramsBox}>
                <span className={styles.paramsSelectTitle}>
                    步骤后等待
                </span>
                <InputNumber
                    className={classnames('input_editor', styles.paramsSelectData)}
                    defaultValue="5"
                    addonAfter="秒"
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    min={0}
                    value={stepInterval}
                    onChange={(value) => {
                        setStepInterval(value);
                    }}
                    onBlur={async () => {
                        try {
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return false;
                            }
                            let newCurrentStep = {...currentStep};
                            if (!newCurrentStep?.stepInfo?.common) {
                                newCurrentStep.stepInfo.common = {};
                            }
                            newCurrentStep.stepInfo.common.stepInterval = stepInterval;
                            await handleUpdateStep(newCurrentStep);
                        } catch (err) {
                            console.log(err?.message ?? err);
                        }
                    }}
                />
            </div>
        </div>
    );
};

export default CommonConfig;