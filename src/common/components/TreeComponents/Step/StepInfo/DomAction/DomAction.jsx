import OldDomAction from './OldDomAction';
import NBDomAction from './NBDomAction';
import VenusDomAction from './VenusDomAction';
import MarsDomAction from './MarsDomAction';
import styles from './DomAction.module.less';

function DomAction(props) {
    const { currentStep, descExtra } = props;

    return (
        <div className={styles.action}>
            {descExtra}
            {currentStep?.stepInfo?.type === 2 ? <OldDomAction {...props} /> : null}
            {[5, 6, 7, 8].includes(currentStep?.stepInfo?.type) ? <NBDomAction {...props} /> : null}
            {currentStep?.stepInfo?.type === 9 ? <VenusDomAction {...props} /> : null}
            {currentStep?.stepInfo?.type === 10 ? <MarsDomAction {...props} /> : null}
        </div>
    );
}

export default DomAction;
