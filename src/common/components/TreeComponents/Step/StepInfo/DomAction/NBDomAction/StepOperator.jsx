import { useState, useEffect, useMemo } from 'react';
import classnames from 'classnames';
import { Tooltip, Select, message, Switch, Tag, Dropdown } from 'antd';
import { RedoOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import { DomAPI, DomModelType } from 'COMMON/utils/mappingUtils';
import electron from 'COMMON/utils/electron';
import { getQueryParams, base64ImageUpload } from 'COMMON/utils/utils';
import commonModel from 'COMMON/models/commonModel';
import InitProgress from 'COMMON/components/TreeComponents/Step/InitProgress';
import { SelectOperator } from 'COMMON/components/TreeComponents/Step/StepInfo/components';
import { RECORD_OPTIONS, DOM_OPTIONS } from './config';
import Setting from './Setting';
import { getStepInitActionParamms } from 'COMMON/components/TreeComponents/Step/StepInit/const';
import styles from 'COMMON/components/TreeComponents/Step/StepInfo/StepInfo.module.less';

function StepOperator(props) {
    const {
        currentStep,
        stepList,
        setRecording,
        currentDevice,
        handleUpdateNode,
        currentSpace,
        curOsType,
        editType,
        handleUpdateStepList,
        handleUpdateStep,
        domDetail,
        deviceList,
        setRecordId,
        currentNode,
        pageSourceSwitch,
        setPageSourceSwitch,
        imgSrc
    } = props;
    const query = getQueryParams();
    const { caseNodeId: nodeId } = query;

    const [stepType, setActionType] = useState(null);
    const [percent, setPercent] = useState(0);
    const [open, setOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();

    const changeSwitch = () => {
        setPageSourceSwitch(!pageSourceSwitch);
        localStorage.setItem('pageSourceSwitch', !pageSourceSwitch);
    };

    useEffect(() => {
        setActionType(currentStep?.stepInfo?.params?.actionInfo?.type);
    }, [currentStep]);

    const recordMarsDomStep = async () => {
        try {
            // 确定有设备连接
            let gotDevice = false;
            for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                if (device.deviceId === currentDevice?.deviceId) {
                    gotDevice = true;
                    if (2 !== device?.status) {
                        messageApi.error('请确保设备状态正常');
                        return false;
                    }
                    break;
                }
            }
            if (!gotDevice) {
                messageApi.error('请确保有设备连接');
                return false;
            }
            let { recordId } = await electron.send('device.record.dumpWidgetModel', {
                deviceType: 2 === +curOsType ? 'ios' : 'android',
                deviceId: currentDevice?.deviceId,
                nodeId: +nodeId,
                stepId: +currentStep?.stepId,
                needUi: true,
                needPagesource: pageSourceSwitch,
                productId: currentSpace?.id,
                versionType: '3.0'
            });
            setRecordId(recordId);
            let step = {
                desc: '',
                type: 9,
                params: {},
                common: {
                    commonAlertClear: true
                }
            };
            step.params.findType = 0;
            step.params.pathSummary = [];
            step.params.findParams = {
                times: 1, // 寻找几次，默认 1 次
                interval: 1000, // 每次间隔多少，单位 ms，默认 1000ms
                before: {
                    wait: 0 // 建模前等待多久，单位 0ms，默认 0ms
                },
                until: {
                    enable: false, // 是否启用，默认 false
                    times: 1, // 点击次数，默认 1 次
                    interval: 2000 // 间隔，单位 ms，默认 2000ms
                },
                allowFail: false // 是否允许执行失败，默认 false
            };

            step.params.findInfo = {
                mergeOCR: false, // 是否合并 ocr，默认 true
                screenCount: 1,
                findPath: [],
                findNode: [],
                findType: 0,
                modelType: pageSourceSwitch ? 2 : 0,
                chosenTag: ['visual', 'single']
            };
            let { _stepInfo, _findInfo, _findParams } = getStepInitActionParamms(
                'tap',
                props.widgetParams
            );
            step.params.actionInfo = { ..._stepInfo };
            step.params.findInfo = { ...step.params.findInfo, ..._findInfo };
            step.params.findParams = { ...step.params.findParams, ..._findParams };
            currentStep.stepInfo = step;
            handleUpdateStep({ ...currentStep, stepInfo: step });
        } catch (err) {
            console.log(err?.message ?? err);
        }
    };
    const recordUsualDomStep = async (
        findType = currentStep?.stepInfo?.params?.findType,
        modelType = currentStep?.stepInfo?.params?.findInfo?.modelType
    ) => {
        if (currentStep?.stepInfo?.type === 8) {
            let timer = null;
            try {
                // 确定有设备连接
                let gotDevice = false;
                for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                    if (device.deviceId === currentDevice?.deviceId) {
                        gotDevice = true;
                        if (![2].includes(device?.status)) {
                            messageApi.error('请确保设备状态正常');
                            return false;
                        }
                        break;
                    }
                }
                if (!gotDevice) {
                    messageApi.error('请确保有设备连接');
                    return false;
                }
                // 开启录制模式
                setPercent(0);
                setRecording(true);
                setOpen(true);
                let newPercent = 0;
                timer = setInterval(() => {
                    try {
                        if (newPercent < 75) {
                            newPercent += 10;
                            setPercent(newPercent + 10);
                        } else if (newPercent < 93) {
                            newPercent += 5;
                            setPercent(newPercent + 5);
                        }
                    } catch (error) {
                        clearInterval(timer);
                        setRecording(false);
                        setOpen(false);
                        messageApi.error('建模失败，请重试');
                        return false;
                    }
                    if (newPercent >= 93) {
                        clearInterval(timer);
                    }
                }, 1000);
                let api = DomAPI.multiSyne;
                let rep = {
                    deviceType: 2 === +curOsType ? 'ios' : 'android',
                    deviceId: currentDevice?.deviceId,
                    productId: currentSpace?.id,
                    ...DomModelType[modelType || 0]
                };
                if (0 === findType) {
                    api = DomAPI.singleFeature;
                }
                if (currentStep?.stepInfo?.type === 8) {
                    api = 'device.record.dumpSingleFeatureSourceForBeta';
                    rep.fixedType = currentStep?.stepInfo?.params?.findInfo?.fixedType;
                }
                let {
                    screenshot,
                    widgetInfo: { dom, domInfo },
                    screenSize
                } = await electron.send(api, rep);
                if (JSON.stringify({}) === dom || null === dom || undefined === dom) {
                    messageApi.error('未识别到有效端控件');
                    return false;
                }
                let step = {
                    desc: currentStep?.stepInfo?.desc ?? '',
                    type: currentStep?.stepInfo?.type === 8 ? 8 : 7,
                    params: {},
                    common: currentStep?.stepInfo?.common
                        ? { ...currentStep?.stepInfo?.common }
                        : {
                              commonAlertClear: true
                          }
                };
                base64ImageUpload(screenshot)
                    .then(async (res) => {
                        let bosUrl = res?.url;
                        for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                            if (device.deviceId === currentDevice?.deviceId) {
                                step.params.deviceInfo = {
                                    type: 2 === curOsType ? 'ios' : 'android',
                                    screenSize: {
                                        width: screenSize.width,
                                        height: screenSize.height,
                                        scale: screenSize.scale,
                                        rotation: screenSize.rotation
                                    },
                                    screenshot: bosUrl
                                };
                                break;
                            }
                        }
                        let domUrl = await electron.send('file.uploadDomToBos', {
                            dom: JSON.stringify(dom),
                            nodeId: nodeId,
                            stepId: -1
                        });
                        step.params.dom = domUrl;
                        // findType 为 2时，重新建模需要改为纯多控件建模
                        step.params.findType = findType === 2 ? 1 : findType;
                        step.params.pathSummary = [];
                        step.params.findParams = {
                            times: 1, // 寻找几次，默认 1 次
                            interval: 1000, // 每次间隔多少，单位 ms，默认 1000ms
                            before: {
                                wait: 0 // 建模前等待多久，单位 0ms，默认 0ms
                            },
                            until: {
                                enable: false, // 是否启用，默认 false
                                times: 1, // 点击次数，默认 1 次
                                interval: 2000 // 间隔，单位 ms，默认 2000ms
                            },
                            allowFail: false, // 是否允许执行失败，默认 false
                            ...currentStep?.stepInfo.params.findParams
                        };
                        if (domInfo) {
                            step.params.domInfo = domInfo;
                        }
                        const mergeOCR = currentStep?.stepInfo?.params?.findInfo?.mergeOCR;
                        step.params.findInfo = {
                            mergeOCR: mergeOCR !== undefined ? mergeOCR : true, // 是否合并 ocr，默认 true
                            screenCount: 1,
                            findPath: [],
                            findNode: [],
                            findType: 0,
                            modelType: modelType || 0
                        };
                        if (currentStep?.stepInfo?.type === 8) {
                            step.params.findInfo.fixedType =
                                currentStep?.stepInfo?.params?.findInfo?.fixedType || 0;
                        }
                        step.params.actionInfo = currentStep?.stepInfo.params.actionInfo;
                        currentStep.stepInfo = step;
                        let newStepList = [...stepList];
                        let stepIndex = stepList.findIndex((item) => item.id === currentStep.id);
                        newStepList.splice(stepIndex, 1, { ...currentStep, stepInfo: step });
                        handleUpdateStepList(newStepList);
                        handleUpdateStep({ ...currentStep, stepInfo: step });
                    })
                    .catch((err) => {
                        console.log(err?.message ?? err);
                    });
            } catch (err) {
            } finally {
                setRecording(false);
                setOpen(false);
                setPercent(0);
                clearInterval(timer);
            }
        } else {
            recordMarsDomStep();
        }
    };
    const version = useMemo(() => {
        if (5 === currentStep.stepInfo.type && domDetail?.dom_info?.version) {
            return domDetail.dom_info.version;
        }
        if (
            [6, 7, 8].includes(currentStep.stepInfo.type) &&
            currentStep.stepInfo.params.domInfo?.version
        ) {
            return currentStep.stepInfo.params.domInfo.version;
        }
        return '版本暂无';
    }, [currentStep]);
    return (
        <div className={styles.stepOperator} style={{ textAlign: 'center' }}>
            {contextHolder}
            <InitProgress open={open} percent={percent} />
            {[5, 6, 7, 8].includes(currentStep.stepInfo.type) ? (
                <>
                    <SelectOperator
                        currentStep={currentStep}
                        border={false}
                        domDetail={domDetail}
                        handleUpdateStep={handleUpdateStep}
                        curOsType={curOsType}
                        stepType={stepType}
                        editType={editType}
                        currentNode={currentNode}
                        handleUpdateNode={handleUpdateNode}
                        setActionType={setActionType}
                    />
                </>
            ) : null}
            {isElectron() && (
                <span
                    style={{
                        fontSize: 16,
                        color: '#959292'
                    }}
                >
                    <span className={styles.operatorCut}>|</span>
                    <Dropdown
                        dropdownRender={() => (
                            <span className={styles.switchBtn}>
                                <Switch
                                    size="small"
                                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                    checked={pageSourceSwitch}
                                    onClick={() => changeSwitch()}
                                />
                                &nbsp;建模系统控件
                            </span>
                        )}
                    >
                        <Tooltip title={'当前版本：' + version}>
                            <span
                                style={{ cursor: 'pointer', fontSize: 13 }}
                                onClick={async (e) => {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return;
                                    }
                                    try {
                                        e.stopPropagation();
                                        setRecording(true);
                                        if ([5, 6, 7, 8].includes(currentStep.stepInfo.type)) {
                                            await recordUsualDomStep();
                                        }
                                    } catch (err) {
                                    } finally {
                                        setRecording(false);
                                    }
                                }}
                                className={styles.operatorIcon}
                            >
                                &nbsp;
                                <RedoOutlined />
                                &nbsp;建模
                            </span>
                        </Tooltip>
                    </Dropdown>
                    {[7, 8].includes(currentStep.stepInfo.type) && (
                        <Select
                            size="small"
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            options={DOM_OPTIONS.map((option, index) => {
                                if (index === 1 && currentStep?.stepInfo?.type === 8) {
                                    return { ...option, disabled: true };
                                }
                                return option;
                            })}
                            bordered={false}
                            dropdownMatchSelectWidth={false}
                            value={currentStep?.stepInfo.params.findType}
                            style={{
                                marginLeft: 10
                            }}
                            onChange={(value) => {
                                recordUsualDomStep(value);
                            }}
                        />
                    )}
                    {[7, 8].includes(currentStep.stepInfo.type) && (
                        <Select
                            size="small"
                            options={RECORD_OPTIONS}
                            bordered={false}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            dropdownMatchSelectWidth={false}
                            value={currentStep?.stepInfo.params.findInfo.modelType}
                            onChange={(value) => {
                                recordUsualDomStep(currentStep?.stepInfo.params.findType, value);
                            }}
                        />
                    )}
                </span>
            )}
            {[7, 8].includes(currentStep?.stepInfo.type) && (
                <span
                    style={{
                        fontSize: 16,
                        color: '#959292'
                    }}
                >
                    <span className={styles.operatorCut}>|&nbsp;&nbsp;</span>
                    <Setting
                        {...props}
                        className={styles.operatorDetailIcon}
                        text="设置"
                        imgSrc={imgSrc}
                    />
                </span>
            )}
        </div>
    );
}

export default connectModel([commonModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    recordId: state.common.case.recordId,
    pageSourceSwitch: state.common.case.pageSourceSwitch
}))(StepOperator);
