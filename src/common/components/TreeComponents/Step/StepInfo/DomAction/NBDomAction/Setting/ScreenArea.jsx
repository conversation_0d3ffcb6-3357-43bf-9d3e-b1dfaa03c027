import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {
    FolderOutlined
} from '@ant-design/icons';
import {InputNumber, Select, message, Switch, Badge} from 'antd';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './Setting.module.less';

export default ({currentStep, handleUpdateStep, fuzzyFindOpen}) => {

    return (
        <div>
            <span className={styles.paramsDescInfo}>
                查找区域默认全屏，暂未支持查找区域修改。
            </span>
        </div>
    );
};
