import {Divider, Radio, Input} from 'antd';
import classnames from 'classnames';
import {useState, useEffect} from 'react';
import styles from './Setting.module.less';

export default ({currentStep, handleUpdateStep, editType}) => {
    const [findFormType, setFindFormType] = useState(1);
    const [findFormDetailIndex, setFindFormDetailIndex] = useState(1);

    useEffect(() => {
        setFindFormType(currentStep.stepInfo.params.findInfo?.findForm?.type || 1);
        setFindFormDetailIndex(currentStep.stepInfo.params.findInfo?.findForm?.detail?.index || 1);
    }, [currentStep?.stepId]);

    const onChange = async (e) => {
        setFindFormType(e.target.value);
        if (!currentStep.stepInfo.params.findInfo?.findForm) {
            currentStep.stepInfo.params.findInfo.findForm = {
                type: 1,
                detail: {
                    index: 1
                }
            };
        }
        currentStep.stepInfo.params.findInfo.findForm.type = e.target.value;
        await handleUpdateStep({...currentStep});
    };

    return (
        <>
            <Divider>实验参数</Divider>
            <div className={styles.paramsItem}>
                <div className={styles.paramsLeft}>
                    匹配类型
                </div>
                <Radio.Group onChange={onChange} value={findFormType}>
                    <Radio disabled={['readonly', 'debug', 'execute'].includes(editType)} value={1}>匹配最相似</Radio>
                    <Radio disabled={['readonly', 'debug', 'execute'].includes(editType)} value={2}>按 index 匹配</Radio>
                </Radio.Group>
            </div>
            {
                findFormType === 2 &&
                <div className={styles.paramsItem}>
                    <div className={styles.paramsLeft}>
                        匹配元素 Index
                    </div>
                    <Input
                        className={classnames(styles.paramsCenter, 'input_editor')}
                        size='small'
                        min={1}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        value={findFormDetailIndex}
                        onChange={(e) => {
                            setFindFormDetailIndex(e.target.value);
                        }}
                        onBlur={async (e) => {
                            if (currentStep.stepInfo.params.findInfo.findForm?.detail?.index !== e.target.value) {
                                currentStep.stepInfo.params.findInfo.findForm.detail.index = e.target.value;
                                await handleUpdateStep({...currentStep});
                            }
                        }}
                    />
                    <div className={styles.paramsRight}>个</div>
                </div>
            }
        </>
    );
};
