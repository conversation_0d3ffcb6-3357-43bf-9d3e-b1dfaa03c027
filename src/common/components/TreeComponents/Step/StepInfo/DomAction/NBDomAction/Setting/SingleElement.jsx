import {FolderOutlined, RedoOutlined} from '@ant-design/icons';
import {Button, Input, InputNumber, Select} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import {RECORD_OPTIONS} from '../config';
import ImageAction from '../ImageAction';
import ImageActionv2 from '../ImageActionv2';
import ImageActionv3 from '../ImageActionv3';
import SingleElementType from './SingleElementType';
import styles from './Setting.module.less';

function SingleElement(props) {
    const {currentStep, recordUsualDomStep, editType} = props;
    return (
        <>
            <div className={styles.settingLeft}>
                {
                    (isElectron() &&
                        !['readonlu', 'debug', 'execute'].includes(editType)) &&
                    <div className={styles.settingRedo}>
                        <Button
                            size="small"
                            onClick={() => recordUsualDomStep(currentStep?.stepInfo.params.findType)}
                        >
                            <RedoOutlined />
                            重新建模
                        </Button>
                        {
                            [7, 8].includes(currentStep.stepInfo.type) &&
                            <Select
                                size="small"
                                options={RECORD_OPTIONS}
                                dropdownMatchSelectWidth={false}
                                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                value={currentStep?.stepInfo.params.findInfo.modelType}
                                onChange={(value) => {
                                    recordUsualDomStep(currentStep?.stepInfo.params.findType, value);
                                }}
                            />
                        }
                    </div>
                }
                {
                    currentStep?.stepInfo?.type === 5 &&
                    <ImageAction
                        key={'setting_image_action'}
                        {...props}
                    />
                }
                {
                    currentStep?.stepInfo?.type === 6 &&
                    <ImageActionv2
                        key={'setting_image_action'}
                        {...props}
                    />
                }
                {
                    [7, 8].includes(currentStep.stepInfo.type) &&
                    <ImageActionv3
                        key={'setting_image_action'}
                        {...props}
                    />
                }
            </div>
            <div className={styles.settingRight}>
                {
                    ![7, 8].includes(currentStep?.stepInfo?.type) &&
                    <span className={styles.paramsDescInfo}>
                        请在 左侧截图 中点选单个目标控件。<br />单控件查找默认基于 <span className={styles.paramsDesc}>&nbsp;控件本身的视觉特征</span>
                    </span>
                }
                {
                    [7, 8].includes(currentStep.stepInfo.type) &&
                    <>
                        <SingleElementType currentStep={currentStep} {...props} />
                    </>
                }
            </div>
        </>
    );
};


export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal,
}))(SingleElement);
