import {useState} from 'react';
import {
    FolderOutlined
} from '@ant-design/icons';
import {Switch, Badge} from 'antd';
import styles from './Setting.module.less';
import OldBatDomTree from '../../OldDomAction/Setting/DomTree/OldBatDomTree';

export default (props) => {
    const [open, setOpen] = useState(false);
    return (
        <div>
            <div className={styles.paramsSetting}>
                <FolderOutlined />
                <span>Dom 树</span>
                {
                    open ?
                        <span className={styles.paramsSwitchInfo}><Badge status="success" />&nbsp;已开启</span> :
                        <span className={styles.paramsSwitchInfo}><Badge status="warning" />&nbsp;未开启</span>
                }
                <Switch
                    className={styles.paramsSwitch}
                    checked={open}
                    size='small'
                    onChange={(checked) => {
                        setOpen(checked);
                    }}
                />
            </div>
            {
                open ?
                    <div style={{height: 250, overflow: 'scroll'}}>
                        <OldBatDomTree {...props} />
                    </div> : null
            }
        </div>
    );
};
