import {useState, useEffect} from 'react';
import {Checkbox, Tree} from 'antd';
import {deepcopy} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './BatDomTree.module.less';

const getTreeData = (dom, nowIndexList = []) => {
    return dom.map((element, index) => {
        nowIndexList.push(index);
        let node = {
            key: nowIndexList.join('-'),
            ext: {
                id: element.debug.id,
                actionNode: false,
                parents: element.debug.parents,
                featureFlag: true,
                detailFeature: {
                    type: element.type,
                    rect: element?.rect,
                    ext: {...element.ext},
                    matchType: ['TextArea', 'Text'].includes(element.type) ? 1 : 0
                },
            },
            type: !element.type ? '未知' : element.type,
            title: element.type,
            disabled: !element?.rect,
            children: 'object' === typeof element.children && 0 !== element.children.length ?
                getTreeData(element.children, nowIndexList) : []
        };
        nowIndexList.pop();
        return node;
    });
};

export default ({
    currentStep, selectPath, setSelectPath, handleUpdateStep,
    domDetail, setActivedNode
}) => {
    const [treeExpendList, setTreeExpendList] = useState([]);
    const [selectedKeys, setSelectedKeys] = useState([]);

    useEffect(() => {
        setSelectedKeys([currentStep.stepInfo.params?.pathSummary?.join('-')]);
    }, [currentStep]);

    return (
        <Tree
            style={{maxHeight: window.innerHeight - 550}}
            showLine
            showIcon={false}
            treeData={getTreeData([deepcopy(domDetail)])}
            expandedKeys={treeExpendList}
            selectedKeys={selectedKeys}
            filterTreeNode={(node) => {
                return 0 !== selectedKeys.length && 0 === selectedKeys[0].indexOf(node.key);
            }}
            onExpand={(expandedKeys) => {
                setTreeExpendList(expandedKeys);
            }}
            titleRender={(node) => {
                let findNodes = currentStep?.stepInfo?.params?.findInfo?.findNode ?? [];
                let findNodesIds = findNodes?.map(item => item?.id);
                return (
                    <span>
                        <Checkbox
                            checked={findNodesIds.includes(node?.ext?.id)}
                            onChange={async () => {
                                let newFindNode = [];
                                for (let findNodeItem of findNodes) {
                                    if (findNodeItem?.id !== node?.ext?.id) {
                                        newFindNode.push(findNodeItem);
                                    }
                                }
                                if (newFindNode?.length === findNodes?.length) {
                                    newFindNode.push(node?.ext);
                                }
                                let newCurrentStep = {...currentStep};
                                newCurrentStep.stepInfo.params.findInfo.findNode = newFindNode;
                                await handleUpdateStep({...newCurrentStep});
                            }}
                        />
                        &nbsp;
                        <span
                            onClick={() => {
                                let pathIndexList = node?.key?.split('-').map(item => parseInt(item, 10));
                                // 重新计算选择元素
                                if (pathIndexList.join('-') === selectPath.join('-')) {
                                    setSelectPath([]);
                                } else {
                                    setActivedNode(null);
                                    setSelectPath(pathIndexList);
                                }
                            }}
                        >
                            {node?.type}
                        </span>
                    </span>
                );
            }}
        />
    );
};