@import "RESOURCES/css/common.less";

.action {
    width: calc(100% -30px);
    margin: 15px;
}

.domAction {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.stepOperator {
    padding: 0 5px 5px 5px;
    text-align: center;
    z-index: 99;
}

.defaultAction {
    margin-top: 5px;
    height: 100%;
    overflow: scroll;
}

.canvasDropdown {
    display: none;
    position: absolute;
    z-index: 999;
    width: 120px;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    cursor: pointer;
    border: 1px solid var(--border-color);
}

.canvasDropdown li {
    padding: 5px 10px;
    list-style: none;
}

.canvasDropdown li:hover {
    background-color: #3686f6;
    color: #fff;
}

.stepOperator {
    :global {

        .ant-select-selection-item,
        .ant-select,
        .custom-default-select,
        .custom-dark-select {
            color: #959292 !important;
            font-size: 13px !important;
        }

        .ant-select-selector,
        .custom-default-select-selector,
        .custom-dark-select-selector {
            padding: 0 5px 0 0 !important;
        }

        .ant-select-single,
        .custom-default-select-single,
        .custom-dark-select-single {
            padding: 0 !important;
        }
    }
}