@import "RESOURCES/css/common.less";

.multiParams {
    min-width: 260px;
    overflow-x: scroll;
}

.singleParams {
    min-width: 260px;
    overflow-x: scroll;
}

.operatorIcon:hover {
    color: #3686f6;
}

.stepAddParams {
    position: relative;
    display: inline-block;
    background-color: #e8efff;
    color: #3686f6;
    border: 1px solid #3686f6;
    padding: 0 8px;
    width: 31%;
    margin: 1%;
    border-radius: 2px;
}

.stepAddParams:hover {
    background-color: #3686f6;
    color: var(--color);
}

.stepAddParamsIcon {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
}

.settingLeft,
.settingRight {
    float: left;
}

.settingRedo {
    margin-bottom: 5px;
}

.settingRight {
    width: 400px;
    margin-left: 40px;
}

.paramsSetting {
    position: relative;
    padding: 4px;
    margin: 10px 0;
    background-color: #f5f5f5;
    border-radius: 3px;

    span {
        margin-left: 3px;
        font-size: 14px;
        font-weight: 600;
    }

    .paramsSwitch {
        position: absolute;
        top: 50%;
        right: 5px;
        transform: translateY(-50%);
    }

    .paramsSwitchInfo {
        float: right;
        margin-right: 40px;
    }
}

.redoRecord {
    position: absolute;
    right: 10px;
    top: 5px;
    z-index: 999;
}

.paramsDescInfo {
    color: #777;
    font-size: 12px;
}

.paramsDesc {
    color: #f74740;
}

.paramsSettingIcon {
    display: inline-block;
    padding: 1px 5px;
    font-size: 12px;
    background: #3e91f7;
    color: var(--color);
    cursor: pointer;
    border-radius: 3px;
}

.paramsSettingIcon:hover {
    box-shadow: rgb(24 144 255 / 12%) 0px 0px 0px 3px;
}

.paramsItem,
.paramsSmallItem {
    margin-bottom: 10px;
    overflow: hidden;
}

.paramsName {
    width: 65px;
    height: 22px;
    line-height: 22px;
    margin-right: 5px;
    background-color: var(--background-color1);
    color: var(--color2);
    border-radius: 3px;
    text-align: center;
    float: left;
}

.featurecheckbox {
    position: absolute;
    left: -20px;
}

.paramsItem {
    :global {

        .ant-select-selection-item,
        .ant-select,
        .custom-default-select,
        .custom-dark-select,
        .ant-select-selector,
        .custom-default-select-selector,
        .custom-dark-select-selector,
        .ant-input,
        .custom-default-input,
        .custom-dark-input,
        .ant-input-number-input,
        .custom-default-input-number-input,
        .custom-dark-input-number-input {
            font-size: 14px !important;
            color: var(--color2) !important;
        }
    }
}

.paramsSmallItem {
    :global {

        .ant-select-selection-item,
        .ant-select,
        .custom-default-select,
        .custom-dark-select,
        .ant-select-selector,
        .custom-default-select-selector,
        .custom-dark-select-selector,
        .ant-select-selector-item,
        .custom-default-select-selector-item,
        .custom-dark-select-selector-item,
        .ant-input,
        .custom-default-input,
        .custom-dark-input,
        .ant-input-number-input,
        .custom-default-input-number-input,
        .custom-dark-input-number-input {
            font-size: 12px !important;
            color: var(--color2) !important;

        }

        .ant-select-selection-item,
        .ant-select,
        .custom-default-select,
        .custom-dark-select {
            width: 100% !important;
        }
    }


}

.paramItem {
    position: relative;
    width: 100%;
    margin: 0 20px 8px 20px;
}

.paramsLeft {
    width: 150px;
    float: left;
    font-size: 14px;
    font-weight: bold;
}


.paramsInfo {
    display: inline-block;
    color: var(--color);
}

.paramsSelect {
    width: 100px;
    float: left;

    :global {
        .ant-select-selection-item {
            font-size: 14px !important;
        }
    }
}

.paramsSmallSelect {
    :global {
        .ant-select-selection-item {
            font-size: 12px !important;
            color: #777;
        }

    }
}

.paramsInput {
    width: calc(100% - 120px);

    :global {
        .ant-input {
            font-size: 14px !important;
        }
    }
}

.paramsSmallInput {
    :global {
        .ant-input {
            font-size: 12px !important;
            color: #777 !important;
        }
    }
}

.paramsSysElement {
    width: calc(100% - 100px);

    :global {

        .ant-select-selection-item,
        .ant-select,
        .custom-default-select,
        .custom-dark-select {
            width: 100% !important;
        }

        .ant-select-selection-overflow,
        .custom-default-select-selection-overflow,
        .custom-dark-select-selection-overflow {
            overflow: scroll;
        }
    }
}

.paramsSmallSysElement {
    :global {
        .ant-select-multiple.ant-select-sm .ant-select-selection-overflow .ant-select-selection-item-content {
            font-size: 12px !important;
        }

    }
}

.paramsTextElement {
    width: calc(100% - 100px);
}

.paramsNum {
    margin-bottom: 5px;

    :global {

        .ant-input-number,
        .custom-default-input-number,
        .custom-dark-input-number,
        .ant-input-number-input,
        .custom-default-input-number-input,
        .custom-dark-input-number-input {
            width: 55px !important;
            text-align: left !important;
        }
    }
}

.paramsCenter {
    width: calc(100% - 150px);
    float: left;
}

.paramsRight {
    float: left;
    width: 30px;
    margin-left: 10px;
}

.nodeParams {
    width: 100%;
    margin-left: 80px;
    margin-bottom: 5px;
    cursor: pointer;
}

.nodeParamIndex {
    float: left;
    width: 60px;
    height: 22px;
    line-height: 22px;
    margin-right: 5px;
    background-color: var(--background-color1);
    color: var(--color2);
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
}

.activeParam {
    background-color: #777;
    color: #fff;
}

.nodeParamText {
    display: inline-block;
    width: 60px;
    margin-right: 5px;
}

.nodeParamSelect {
    width: 70px;
}

.nodeParamSelectType {
    width: 100px;
}

.nodeParamLongSelectType {
    width: 150px;
}

.nodeParamInput {
    width: calc(100% - 350px);
}

.nodeParamSelectInput {
    width: calc(100% - 200px);
}

.nodePos {
    position: relative;
    width: 100%;
    font-weight: normal;
}

.nodePosDelete {
    position: absolute;
    color: red;
}

.nodeParamsDelete {
    margin-left: 2px;
    color: red;
}

.newNodeParamsDelete {
    position: absolute;
    right: 0px;
    top: 4px;
    color: red;
}

// DEF 控件信息
.checkedBox {
    position: absolute;
}

.featureInfo {
    display: block;
    margin-left: 20px;
    color: var(--color2);
    width: calc(100% - 20px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .featureInfoFeature {
        display: block;
        float: left;
        font-weight: bold;
        width: 70px;
        color: var(--color2);
        background-color: var(--border-color);
        padding: 1px 3px;
        border-radius: 2px;
        margin-right: 5px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.textSizeNormal {
    font-size: 14px;
}


.textSizeSmall {
    height: 22px;
    line-height: 22px;
    font-size: 12px !important;
}

.modelMarginLeft {
    padding: 0 27px;
}

.rowMarginLeft {
    padding-right: 5px;
    font-size: 12px;

    :global {
        .ant-input-number-input {
            font-size: 12px !important;
            color: #777 !important;
        }
    }

}

.info {
    color: var(--color2);
}


.cvElement {
    height: 22px;
    line-height: 22px;
}