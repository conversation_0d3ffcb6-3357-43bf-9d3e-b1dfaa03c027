import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {InputNumber, Select, Badge, Tooltip, Switch, Alert} from 'antd';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './Setting.module.less';

export default ({editType, currentStep, handleUpdateStep}) => {
    const [screenCount, setScreenCount] = useState(1);
    const [scrollDirection, setScrollDirection] = useState(0);
    const [scrollRatio, setScrollRatio] = useState(1);
    const [scrollType, setScrollType] = useState(1);
    const [open, setOpen] = useState(false);

    useEffect(() => {
        if (currentStep?.stepInfo?.params?.findInfo?.findType &&
            currentStep.stepInfo.params.findInfo.findType !== 0) {
            setScrollDirection(currentStep.stepInfo.params.findInfo.scrollDirection ?? 0);
            setScrollRatio(currentStep.stepInfo.params.findInfo.scrollRatio ?? 1);
            setScreenCount(currentStep.stepInfo.params.findInfo.screenCount ?? 1);
            setScrollType(currentStep.stepInfo.params.findInfo.scrollType ?? 1);
            setOpen(true);
        } else {
            setOpen(false);
        }
    }, [currentStep]);

    const onDel = () => {
        currentStep.stepInfo.params.findInfo.findType = 0;
        delete currentStep.stepInfo.params.findInfo.scrollDirection;
        delete currentStep.stepInfo.params.findInfo.scrollRatio;
        delete currentStep.stepInfo.params.findInfo.scrollType;
        currentStep.stepInfo.params.findInfo.screenCount = 1;
        setScrollDirection(0);
        setScreenCount(1);
        setScrollRatio(1);
        setScrollType(1);
        handleUpdateStep(currentStep);
    };

    return (
        <div>
            <div className={styles.paramsItem}>
                <div className={styles.paramsLeft} style={{fontWeight: 'normal', width: 140}}>
                    开启多屏查找
                </div>
                <div className={styles.paramsRight}>
                    <Switch
                        className={styles.paramsSwitch}
                        checked={open}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        onChange={(checked) => {
                            setOpen(checked);
                            if (!checked) {
                                onDel();
                            } else {
                                currentStep.stepInfo.params.findInfo.scrollDirection = 1;
                                currentStep.stepInfo.params.findInfo.findType = 2;
                                currentStep.stepInfo.params.findInfo.scrollRatio = 1;
                                currentStep.stepInfo.params.findInfo.scrollType = 1;
                                setScrollDirection(1);
                                setScrollRatio(1);
                                setScrollType(1);
                                handleUpdateStep({...currentStep});
                            }
                        }}
                    />
                </div>
            </div>
            {
                open ?
                    <div className={styles.paramsItem}>
                        <div className={styles.paramsLeft} style={{fontWeight: 'normal'}}>最多查找</div>
                        <InputNumber
                            className={classnames(styles.paramsCenter, 'input_editor')}
                            max={50}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            min={1}
                            value={screenCount}
                            onChange={(value) => {
                                setScreenCount(value);
                            }}
                            onBlur={(e) => {
                                let value = 1;
                                if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                                    value = parseInt(e.target.value.trim(), 10);
                                }
                                if (currentStep.stepInfo.params.findInfo.screenCount !== value) {
                                    currentStep.stepInfo.params.findInfo.screenCount = value;
                                    handleUpdateStep(currentStep);
                                }
                            }}
                        />
                        <div className={styles.paramsRight}>屏</div>
                    </div> : null
            }
            {
                open ?
                    <div className={styles.paramsItem}>
                        <div className={styles.paramsLeft} style={{fontWeight: 'normal'}}>单次滑动</div>
                        <Select
                            className={styles.paramsCenter}
                            value={scrollRatio}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onChange={(value) => {
                                setScrollRatio(value);
                                currentStep.stepInfo.params.findInfo.scrollRatio = Number(value);
                                handleUpdateStep(currentStep);
                            }}
                        >
                            <Select.Option key={'sys_swipe_ratio_1'} value={3}>
                                整
                            </Select.Option>
                            <Select.Option key={'sys_swipe_ratio_2'} value={1}>
                                1 / 2
                            </Select.Option>
                            <Select.Option key={'sys_swipe_ratio_3'} value={2}>
                                1 / 4
                            </Select.Option>
                        </Select>
                        <div className={styles.paramsRight}>屏</div>
                    </div> : null
            }
            {
                open ?
                    <div className={styles.paramsItem}>
                        <div className={styles.paramsLeft} style={{fontWeight: 'normal'}}>划动方向</div>
                        <Select
                            className={styles.paramsCenter}
                            value={scrollDirection ?? 1}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onChange={(value) => {
                                setScrollDirection(value);
                                currentStep.stepInfo.params.findInfo.scrollDirection = Number(value);
                                handleUpdateStep(currentStep);
                            }}
                        >
                            <Select.Option key={'sys_swipe_action_1'} value={1}>
                                从下向上划
                            </Select.Option>
                            <Select.Option key={'sys_swipe_action_2'} value={2}>
                                从上向下划
                            </Select.Option>
                            <Select.Option key={'sys_swipe_action_3'} value={3}>
                                从左向右划
                            </Select.Option>
                            <Select.Option key={'sys_swipe_action_4'} value={4}>
                                从右向左划
                            </Select.Option>
                        </Select>
                    </div> : null
            }
            {
                open ?
                    <div className={styles.paramsItem}>
                        <div className={styles.paramsLeft} style={{fontWeight: 'normal'}}>划动速度</div>
                        <Select
                            className={styles.paramsCenter}
                            value={scrollType}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onChange={(value) => {
                                setScrollType(value);
                                currentStep.stepInfo.params.findInfo.scrollType = Number(value);
                                handleUpdateStep(currentStep);
                            }}
                        >
                            <Select.Option key={'sys_swipe_type_1'} value={1}>
                                快划
                            </Select.Option>
                            <Select.Option key={'sys_swipe_type_2'} value={2}>
                                慢划
                            </Select.Option>
                        </Select>
                    </div> : null
            }
        </div>
    );
};
