import {isEmpty} from 'lodash';
import NoContent from 'COMMON/components/common/NoContent';
import {DeleteOutlined} from '@ant-design/icons';
import {Checkbox, Col, Row, Tooltip} from 'antd';
import RenderSysElement from './Element/SysElement';
import RenderTextElement from './Element/TextElement';
import RenderIconElement from './Element/IconElement';
import RenderComponentElement from './Element/ComponentElement';
import RenderCVElement from './Element/CVElement';
import styles from './Setting.module.less';

function NodeFeatures({
    currentStep, handleUpdateStep, handleUpdateSelectPath, domDetail,
    activedNode, setActivedNode, editType, ParamsTitleInfo, sizeType
}) {
    let textIndex = 0;
    let iconIndex = 0;
    let sysIndex = 0;
    let componentIndex = 0;
    let cvIndex = 0;
    let jsx = currentStep.stepInfo.params.findInfo.findNode.map((item, index) => {
        if (item?.detailFeature?.type.includes('Text')) {
            textIndex++;
            return (
                <Col span={24} key={'text_' + String(textIndex)} className={styles.paramItem}>
                    <FeatureCheckBox
                        currentStep={currentStep}
                        index={index}
                        editType={editType}
                        handleUpdateStep={handleUpdateStep}
                    />
                    <ParamsTitleInfo
                        index={textIndex}
                        text={`文本 ${String(textIndex)}`}
                        item={item}
                        domDetail={domDetail}
                        currentStep={currentStep}
                        sizeType={sizeType}
                        activedNode={activedNode}
                        setActivedNode={setActivedNode}
                        handleUpdateSelectPath={handleUpdateSelectPath}
                    />
                    <RenderTextElement
                        index={index}
                        currentStep={currentStep}
                        type={sizeType}
                        className={styles.nodeParamSelectInput}
                        handleUpdateStep={handleUpdateStep}
                        disabled={
                            !item?.featureFlag || ['readonly', 'debug', 'execute'].includes(editType)}
                    />
                </Col>
            );
        }
        if (['Icon'].includes(item?.detailFeature?.type)) {
            iconIndex++;
            return (
                <Col span={24} key={'icon_' + String(iconIndex)} className={styles.paramItem}>
                    <FeatureCheckBox
                        currentStep={currentStep}
                        index={index}
                        editType={editType}
                        handleUpdateStep={handleUpdateStep}
                    />
                    <ParamsTitleInfo
                        index={iconIndex}
                        domDetail={domDetail}
                        text={`图标 ${String(iconIndex)}`}
                        item={item}
                        currentStep={currentStep}
                        sizeType={sizeType}
                        activedNode={activedNode}
                        setActivedNode={setActivedNode}
                        handleUpdateSelectPath={handleUpdateSelectPath}
                    />
                    <RenderIconElement
                        index={index}
                        type={sizeType}
                        disabled={!item?.featureFlag || ['readonly', 'debug', 'execute'].includes(editType)}
                        currentStep={currentStep}
                        handleUpdateStep={handleUpdateStep}
                    />
                </Col>
            );
        }
        if (['Component'].includes(item?.detailFeature?.type)) {
            componentIndex++;
            return (
                <Col span={24} key={'icon_' + String(componentIndex)} className={styles.paramItem}>
                    <FeatureCheckBox
                        currentStep={currentStep}
                        index={index}
                        editType={editType}
                        handleUpdateStep={handleUpdateStep}
                    />
                    <ParamsTitleInfo
                        index={componentIndex}
                        domDetail={domDetail}
                        text={`组件 ${String(componentIndex)}`}
                        item={item}
                        currentStep={currentStep}
                        sizeType={sizeType}
                        activedNode={activedNode}
                        setActivedNode={setActivedNode}
                        handleUpdateSelectPath={handleUpdateSelectPath}
                    />
                    <RenderComponentElement
                        index={index}
                        type={sizeType}
                        disabled={!item?.featureFlag || ['readonly', 'debug', 'execute'].includes(editType)}
                        currentStep={currentStep}
                        handleUpdateStep={handleUpdateStep}
                    />
                </Col>
            );
        }
        if ((['DFE'].includes(item?.detailFeature?.type) && !isEmpty(item?.detailFeature?.ext))
            || ['DOE'].includes(item?.detailFeature?.type)) {
            sysIndex++;
            return (
                <Col span={24} key={'component_' + String(sysIndex)} className={styles.paramItem}>
                    <FeatureCheckBox
                        currentStep={currentStep}
                        index={index}
                        editType={editType}
                        handleUpdateStep={handleUpdateStep}
                    />
                    <ParamsTitleInfo
                        domDetail={domDetail}
                        index={sysIndex}
                        text={`系统 ${String(sysIndex)}`}
                        item={item}
                        currentStep={currentStep}
                        sizeType={sizeType}
                        activedNode={activedNode}
                        setActivedNode={setActivedNode}
                        handleUpdateSelectPath={handleUpdateSelectPath}
                    />
                    <RenderSysElement
                        index={index}
                        type={sizeType}
                        disabled={global.params.CASE_STATUS !== 'edit' ||
                            !item?.featureFlag || ['readonly', 'debug', 'execute'].includes(editType)}
                        currentStep={currentStep}
                        handleUpdateStep={handleUpdateStep}
                    />
                </Col>
            );
        }
        if ((['CVE'].includes(item?.detailFeature?.type))) {
            cvIndex++;
            return (
                <Col span={24} key={'cv_' + String(cvIndex)} className={styles.paramItem}>
                    <FeatureCheckBox
                        currentStep={currentStep}
                        index={index}
                        editType={editType}
                        handleUpdateStep={handleUpdateStep}
                    />
                    <ParamsTitleInfo
                        index={sysIndex}
                        domDetail={domDetail}
                        text={`元素 ${String(cvIndex)}`}
                        item={item}
                        currentStep={currentStep}
                        sizeType={sizeType}
                        activedNode={activedNode}
                        setActivedNode={setActivedNode}
                        handleUpdateSelectPath={handleUpdateSelectPath}
                    />
                    <RenderCVElement
                        index={index}
                        type={sizeType}
                        disabled={!item?.featureFlag || ['readonly', 'debug', 'execute'].includes(editType)}
                        currentStep={currentStep}
                        handleUpdateStep={handleUpdateStep}
                    />
                </Col>
            );
        }
    });
    return <Row>{jsx}</Row>;
}

function FeatureCheckBox({currentStep, index, editType, handleUpdateStep}) {
    return (
        <Checkbox
            className={styles.featurecheckbox}
            disabled={['readonly', 'debug', 'execute'].includes(editType)}
            checked={currentStep.stepInfo.params.findInfo.findNode[index].featureFlag}
            onChange={async (e) => {
                const newCurrentStep = {...currentStep}; // 创建新的currentStep对象
                newCurrentStep.stepInfo.params.findInfo.findNode[index].featureFlag = e.target.checked; // 更新属性
                handleUpdateStep(newCurrentStep);
            }}
        />
    );
}

function DeleteIcon({currentStep, index, handleUpdateSelectPath, handleUpdateStep}) {
    return (
        <Tooltip title="删除该控件" placement='right'>
            <DeleteOutlined
                className={styles.newNodeParamsDelete}
                onClick={async () => {
                    currentStep.stepInfo.params.findInfo.findNode.splice(index, 1);
                    handleUpdateStep({...currentStep});
                    handleUpdateSelectPath([]);
                }}
            />
        </Tooltip>
    );
}

export default NodeFeatures;