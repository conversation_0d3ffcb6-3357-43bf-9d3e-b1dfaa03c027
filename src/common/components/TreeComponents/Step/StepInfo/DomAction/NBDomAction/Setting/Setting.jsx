import {useState, useEffect} from 'react';
import {Spin, Drawer, Tooltip, Tabs, message, Button, Divider} from 'antd';
import {
    SettingOutlined
} from '@ant-design/icons';
import zlib from 'zlib';
import electron from 'COMMON/utils/electron';
import {connectModel} from 'COMMON/middleware';
import {DomAPI, DomModelType} from 'COMMON/utils/mappingUtils';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {getQueryParams, base64ImageUpload} from 'COMMON/utils/utils';
import ParamsTitle from 'COMMON/components/ParamsTitle';
import ClickWait from 'COMMON/components/TreeComponents/Step/StepInfo/components/ClickWait';
import DomWait from 'COMMON/components/TreeComponents/Step/StepInfo/components/DomWait';
import StepInterval from 'COMMON/components/TreeComponents/Step/StepInfo/components/StepInterval';
import BeforeSysAlertClear from 'COMMON/components/TreeComponents/Step/StepInfo/components/BeforeSysAlertClear';
import Retry from 'COMMON/components/TreeComponents/Step/StepInfo/components/Retry';
import IfThen from 'COMMON/components/TreeComponents/Step/StepInfo/components/IfThen';
import {getStepInitActionParamms} from 'COMMON/components/TreeComponents/Step/StepInit/const';
import ScreenFind from './ScreenFind';
import NewScreenFind from './NewScreenFind';
import SingleElement from './SingleElement';
import MultiElement from './MultiElement';
import BetaParams from './BetaParams';
import styles from './Setting.module.less';

function Setting(props) {
    const {setShowModal, currentStep, curOsType, editType, imgSrc,
        innerHeight, innerWidth, currentDevice, deviceList, stepList, setRecordId,
        handleUpdateStepList, text, currentSpace,
        pageSourceSwitch, handleUpdateStep} = props;
    const query = getQueryParams();
    const {caseNodeId: nodeId} = query;
    const [selectPath, setSelectPath] = useState([]);
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [settingWidth, setSettingWidth] = useState(0);
    const [recordStep, setRecordStep] = useState(null);
    const [messageApi, contextHolder] = message.useMessage();

    useEffect(() => {
        updateSettingWidth(window.innerHeight, window.innerWidth);
    }, [innerHeight, innerWidth]);

    const updateSettingWidth = (innerHeight, innerWidth) => {
        let screenWidth = currentStep?.stepInfo.params.deviceInfo.screenSize.width;
        let screenHeight = currentStep?.stepInfo.params.deviceInfo.screenSize.height;
        if (screenWidth < screenHeight || curOsType === 2) {
            let imgHeight = innerHeight - 200;
            let imageScale = imgHeight / screenHeight;
            let imgWidth = imageScale * screenWidth;
            setSettingWidth(imgWidth + 700);
        } else {
            let imgWidth = innerWidth - 200;
            let imageScale = imgWidth / screenWidth;
            let imgHeight = imageScale * screenHeight;
            setSettingWidth(imgHeight + 350);
        }
    };
    const handleUpdateSelectPath = (path) => {
        setSelectPath([...path]);
    };

    const recordVenusDomStep = async () => {
        try {
            // 确定有设备连接
            let gotDevice = false;
            for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                if (device.deviceId === currentDevice?.deviceId) {
                    gotDevice = true;
                    if (2 !== device?.status) {
                        messageApi.open({
                            type: 'error',
                            content: '请确保设备状态正常',
                        });
                        return false;
                    }
                    break;
                }
            }
            if (!gotDevice) {
                messageApi.open({
                    type: 'error',
                    content: '请确保有设备连接',
                });
                return false;
            }

            // 开启录制模式
            let {
                recordId
            } = await electron.send('device.record.dumpWidgetModel', {
                deviceType: 2 === +curOsType ? 'ios' : 'android',
                deviceId: currentDevice?.deviceId,
                nodeId: nodeId,
                stepId: +currentStep?.stepId,
                needUi: true,
                needPagesource: pageSourceSwitch,
                productId: currentSpace?.id,
                versionType: '3.0'
            });
            setRecordId(recordId);
            let step = {
                desc: '',
                type: 9,
                params: {},
                common: {
                    commonAlertClear: true
                }
            };
            step.params.findType = 0;
            step.params.pathSummary = [];
            step.params.findParams = {
                times: 1, // 寻找几次，默认 1 次
                interval: 1000, // 每次间隔多少，单位 ms，默认 1000ms
                before: {
                    wait: 0, // 建模前等待多久，单位 0ms，默认 0ms
                },
                until: {
                    enable: false, // 是否启用，默认 false
                    times: 1, // 点击次数，默认 1 次
                    interval: 2000, // 间隔，单位 ms，默认 2000ms
                },
                allowFail: false // 是否允许执行失败，默认 false
            };

            step.params.findInfo = {
                mergeOCR: false, // 是否合并 ocr，默认 true
                screenCount: 1,
                findPath: [],
                findNode: [],
                findType: 0,
                modelType: pageSourceSwitch ? 2 : 0,
                chosenTag: ['visual', 'single']
            };
            let {_stepInfo, _findInfo, _findParams} = getStepInitActionParamms('tap', props.widgetParams);
            step.params.actionInfo = {..._stepInfo};
            step.params.findInfo = {...step.params.findInfo, ..._findInfo};
            step.params.findParams = {...step.params.findParams, ..._findParams};
            currentStep.stepInfo = step;
            handleUpdateStep({...currentStep, stepInfo: step});
        } catch (err) {
            console.log(err?.message ?? err);
        }
    };

    const recordUsualDomStep = async (
        findType,
        modelType = currentStep?.stepInfo?.params?.findInfo?.modelType
    ) => {
        if (currentStep?.stepInfo?.type === 8) {
            try {
                // 确定有设备连接
                let gotDevice = false;
                for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                    if (device.deviceId === currentDevice?.deviceId) {
                        gotDevice = true;
                        if (![2, 3].includes(device?.status)) {
                            message.error('请确保设备状态正常');
                            return false;
                        }
                        break;
                    }
                }
                if (!gotDevice) {
                    message.error('请确保有设备连接');
                    return false;
                }
                // 开启录制模式

                setLoading(true);
                let api = DomAPI.multiSyne;
                if (0 === findType) {
                    api = DomAPI.singleFeature;
                }
                let recordInfo = {
                    deviceType: 2 === +curOsType ? 'ios' : 'android',
                    deviceId: currentDevice?.deviceId,
                    productId: currentSpace?.id,
                    ...DomModelType[modelType || 0]
                };
                let {
                    screenshot,
                    widgetInfo: {dom, domInfo},
                    screenSize
                } = await electron.send(api, recordInfo);
                // {} === dom 始终返回“false",因为js按引用而不是值比较对象。
                if (JSON.stringify({}) === dom || null === dom || undefined === dom) {
                    message.error('未识别到有效端控件');
                    return false;
                }
                let step = {
                    desc: currentStep?.stepInfo?.desc,
                    type: currentStep?.stepInfo?.type === 8 ? 8 : 7,
                    params: {},
                    common: currentStep?.stepInfo?.common ?
                        {...currentStep?.stepInfo?.common} : {
                            commonAlertClear: true
                        },
                };
                base64ImageUpload(screenshot, 'image').then(async res => {
                    let bosUrl = res?.url;
                    for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                        if (device.deviceId === currentDevice?.deviceId) {
                            step.params.deviceInfo = {
                                type: 2 === curOsType ? 'ios' : 'android',
                                screenSize: {
                                    width: screenSize.width,
                                    height: screenSize.height,
                                    scale: screenSize.scale,
                                    rotation: screenSize.rotation
                                },
                                screenshot: bosUrl
                            };
                            break;
                        }
                    }
                    let domUrl = await electron.send('file.uploadDomToBos', {
                        dom: JSON.stringify(dom),
                        nodeId: nodeId,
                        stepId: -1
                    });
                    step.params.dom = domUrl;
                    if (domInfo) {
                        step.params.domInfo = domInfo;
                    }
                    step.params.findType = findType;
                    step.params.pathSummary = [];
                    step.params.findParams = {
                        times: 1, // 寻找几次，默认 1 次
                        interval: 1000, // 每次间隔多少，单位 ms，默认 1000ms
                        before: {
                            wait: 0 // 建模前等待多久，单位 0ms，默认 0ms
                        },
                        until: {
                            enable: false, // 是否启用，默认 false
                            times: 1, // 点击次数，默认 1 次
                            interval: 2000 // 间隔，单位 ms，默认 2000ms
                        },
                        allowFail: false, // 是否允许执行失败，默认 false
                        ...currentStep.stepInfo.params.findParams
                    };
                    const mergeOCR = currentStep?.stepInfo?.params?.findInfo?.mergeOCR;
                    step.params.findInfo = {
                        mergeOCR: false, // 是否合并 ocr，默认 true
                        screenCount: 1,
                        findPath: [],
                        findNode: [],
                        findType: 0,
                        modelType: modelType || 0
                    };
                    if (findType === 2) {
                        step.params.findInfo.needPagesource = false;
                    }
                    if (findType === 1) {
                        step.params.findInfo.needPagesource = true;
                    }
                    if (currentStep?.stepInfo?.type === 8) {
                        step.params.findInfo.fixedType = currentStep?.stepInfo?.params?.findInfo?.fixedType || 0;
                    }
                    step.params.actionInfo = currentStep.stepInfo.params.actionInfo;
                    currentStep.stepInfo = step;
                    let newStepList = [...stepList];
                    let stepIndex = stepList.findIndex((item) => item.id === currentStep.id);
                    newStepList.splice(stepIndex, 1, {...currentStep, stepInfo: step});
                    handleUpdateStepList(newStepList);
                    handleUpdateStep({...currentStep, stepInfo: step});
                    setSelectPath([]);
                }).catch(err => {
                    console.log(err?.message ?? err);
                });
            } catch (err) {
            } finally {
                setLoading(false);
            }
        } else {
            recordVenusDomStep();
        }
    };

    const getActiveKey = (currentStep) => {
        switch (currentStep?.stepInfo.params.findType) {
            case 0:
                return 'tab_1';
            case 1:
                return 'tab_2';
            case 2:
                return 'tab_3';
        }
    };

    let items = [
        {
            label: '单控件查找',
            key: 'tab_1',
            children: (
                <SingleElement
                    {...props}
                    selectPath={selectPath}
                    sizeType="normal"
                    imgSrc={imgSrc}
                    setLoading={setLoading}
                    recordUsualDomStep={recordUsualDomStep}
                    handleUpdateSelectPath={handleUpdateSelectPath}
                    drawerOpen="true"
                />
            )
        },
        {
            label: '多控件查找',
            key: 'tab_2',
            children: (
                <MultiElement
                    {...props}
                    selectPath={selectPath}
                    type="normal"
                    imgSrc={imgSrc}
                    setLoading={setLoading}
                    setSelectPath={setSelectPath}
                    recordUsualDomStep={recordUsualDomStep}
                    handleUpdateSelectPath={handleUpdateSelectPath}
                    drawerOpen="true"
                />
            ),
            disabled: currentStep?.stepInfo?.type === 8
        }
    ];

    if (currentStep?.stepInfo.params.findType === 2) {
        items.push({
            label: '多控件(纯视觉)查找',
            key: 'tab_3',
            children: <MultiElement
                {...props}
                selectPath={selectPath}
                imgSrc={imgSrc}
                type='normal'
                setLoading={setLoading}
                setSelectPath={setSelectPath}
                recordUsualDomStep={recordUsualDomStep}
                handleUpdateSelectPath={handleUpdateSelectPath}
            />,
        });
    }
    return (
        <>
            {contextHolder}
            <span
                style={{cursor: 'pointer', fontSize: 13}}
                onClick={() => {
                    setShowModal(true);
                    setOpen(true);
                    setRecordStep(JSON.stringify(currentStep?.stepInfo));
                }}
                className={styles.operatorIcon}
            >
                <SettingOutlined />
                &nbsp;{text}
            </span>
            <Drawer
                title="控件查找"
                open={open}
                centered
                width={settingWidth}
                onClose={async () => {
                    if (currentStep?.stepInfo?.params?.findType !== 0 &&
                        currentStep?.stepInfo?.params?.findInfo?.findNode.length <= 1) {
                        message.warning('多控件建模需要设置 2 个以上控件');
                    }
                    // 多控件建模不能超过5个控件
                    if (currentStep?.stepInfo?.params?.findType !== 0 &&
                        currentStep?.stepInfo?.params?.findInfo?.findNode.length > 5) {
                        message.warning('多控件建模最多只能设置 5 个控件');
                        return;
                    }
                    if (currentStep?.stepInfo?.params?.findType === 0 &&
                        currentStep?.stepInfo?.params?.findInfo?.findNode.length < 1) {
                        message.warning('单控件建模需要设置 1 个控件');
                    }
                    if (JSON.stringify(currentStep?.stepInfo) === recordStep) {
                        console.log('diff same');
                        setShowModal(false);
                        setSelectPath([]);
                        setRecordStep(null);
                        setOpen(false);
                        return;
                    }
                    handleUpdateStep({...currentStep});
                    setShowModal(false);
                    setSelectPath([]);
                    setOpen(false);
                }}
                footer={null}
            >
                <Spin spinning={loading}>
                    <Tabs
                        tabPosition="left"
                        defaultActiveKey={2}
                        items={[{
                            label: '基础配置',
                            key: 1,
                            children: <div style={{margin: '0 10%'}}>
                                <ParamsTitle text="查找失败重试配置" />
                                <Retry {...props} />
                                <ParamsTitle text="控件操作配置" />
                                <IfThen {...props} />
                                <DomWait {...props} />
                                <ClickWait {...props} />
                                {
                                    currentStep.stepInfo.params.findType !== 0 &&
                                    ![7, 8].includes(currentStep?.stepInfo?.type) &&
                                    <>
                                        <ParamsTitle text="多屏查找配置" />
                                        <ScreenFind {...props} />
                                    </>
                                }
                                {
                                    [7, 8].includes(currentStep?.stepInfo?.type) &&
                                    <>
                                        <ParamsTitle text="多屏查找配置" />
                                        <NewScreenFind {...props} />
                                    </>
                                }
                                <ParamsTitle text="步骤参数" />
                                <BeforeSysAlertClear {...props} />
                                <StepInterval {...props} />

                                {
                                    (localStorage.getItem('regressionCase_beta') === 'true' ||
                                        currentStep.stepInfo.params.findInfo?.findForm) &&
                                    <BetaParams {...props} />
                                }
                            </div>
                        }, {
                            label: '查找方式',
                            key: 2,
                            children: <>
                                <Tabs
                                    type="card"
                                    activeKey={getActiveKey(currentStep)}
                                    size="small"
                                    onChange={(activeKey) => {
                                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                                            message.warning('Web 不支持切换');
                                            return;
                                        }
                                        switch (activeKey) {
                                            case 'tab_1':
                                                recordUsualDomStep(0);
                                                break;
                                            case 'tab_2':
                                                recordUsualDomStep(1);
                                                break;
                                            case 'tab_3':
                                                recordUsualDomStep(2);
                                                break;
                                            default:
                                                break;
                                        }
                                    }}
                                    items={items}
                                />
                            </>
                        }]}
                    />
                </Spin>
            </Drawer>
        </>
    );
};

export default connectModel([commonModel, baseModel], (state) => ({
        currentSpace: state.common.base.currentSpace,
        currentDevice: state.common.base.currentDevice,
        deviceList: state.common.base.deviceList,
        showModal: state.common.base.showModal,
        recordId: state.common.case.recordId,
        pageSourceSwitch: state.common.case.pageSourceSwitch,
    }))(Setting);
