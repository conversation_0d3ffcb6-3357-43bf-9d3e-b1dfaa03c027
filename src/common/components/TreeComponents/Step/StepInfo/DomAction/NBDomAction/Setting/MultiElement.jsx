import {FolderOutlined, RedoOutlined} from '@ant-design/icons';
import {Button, Select} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import {RECORD_OPTIONS} from '../config';
import ImageAction from '../ImageAction';
import ImageActionv2 from '../ImageActionv2';
import ImageActionv3 from '../ImageActionv3';
import FuzzyFind from './FuzzyFind';
import DomTree from './DomTree';
import styles from './Setting.module.less';
import MultiElementType from './MultiElementType';

function MultiElement(props) {
    const {currentStep, recordUsualDomStep, editType} = props;
    return (
        <>
            <div className={styles.settingLeft}>
                <div className={styles.settingRedo}>
                    {
                        (!['readonly', 'debug', 'execute'].includes(editType) &&
                            isElectron() && currentStep?.stepInfo?.params.findType !== 2) &&
                        <Button
                            size="small"
                            onClick={() => recordUsualDomStep(currentStep?.stepInfo.params.findType)}
                        >
                            <RedoOutlined />
                            重新建模
                        </Button>
                    }
                    {
                        (!['reayonly', 'debug', 'execute'].includes(editType) &&
                           isElectron()
                            && [7, 8].includes(currentStep?.stepInfo?.type)) &&
                        <Select
                            size="small"
                            options={RECORD_OPTIONS}
                            dropdownMatchSelectWidth={false}
                            value={currentStep?.stepInfo.params.findInfo.modelType}
                            onChange={(value) => {
                                recordUsualDomStep(currentStep?.stepInfo.params.findType, value);
                            }}
                        />
                    }
                </div>
                {
                    currentStep?.stepInfo?.type === 5 &&
                    <ImageAction
                        key={'setting_image_action'}
                        {...props}
                    />
                }
                {
                    currentStep?.stepInfo?.type === 6 &&
                    <ImageActionv2
                        key={'setting_image_action'}
                        {...props}
                    />
                }
                {
                    [7, 8].includes(currentStep?.stepInfo?.type) &&
                    <ImageActionv3
                        key={'setting_image_action'}
                        {...props}
                    />
                }
            </div>
            {
                ![7, 8].includes(currentStep?.stepInfo?.type) &&
                <div className={styles.settingRight}>
                    <div className={styles.paramsSetting}>
                        <span><FolderOutlined /></span>
                        <span>控件选择</span>
                    </div>
                    <FuzzyFind {...props} />
                    <DomTree {...props} />
                </div>
            }
            {
                [7, 8].includes(currentStep?.stepInfo?.type) &&
                <div className={styles.settingRight}>
                    <MultiElementType {...props} />
                    {/* <DomTree {...props} /> */}
                </div>
            }
        </>
    );
};

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal,
}))(MultiElement);
