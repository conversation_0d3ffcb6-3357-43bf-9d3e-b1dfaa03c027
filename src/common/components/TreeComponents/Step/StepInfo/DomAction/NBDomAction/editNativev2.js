import {message} from 'antd';
import {
    searchElementByBoundsv2,
    getSingleElementInfo
} from './utils';

const deepcopy = obj => {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    let newObj = {};
    if (Array.isArray(obj)) {
        newObj = obj.map(item => deepcopy(item));
    } else {
        Object.keys(obj).forEach((key) => {
            return newObj[key] = deepcopy(obj[key]);
        });
    }
    return newObj;
};

const getPath = (dom, nodeIdList, nowIndexList = [], pathList = []) => {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (-1 !== nodeIdList.indexOf(element.id)) {
            pathList.push(nowIndexList.join('-'));
        }
        if (-1 !== Object.keys(element).indexOf('children') && 0 !== element.children.length) {
            getPath(element.children, nodeIdList, nowIndexList, pathList);
        }
        if ('object' === typeof element.children && 0 !== element.children.length) {
            getPath(element.children, nodeIdList, nowIndexList, pathList);
        }
        nowIndexList.pop();
    });
    return pathList;
};

/**
 * 修改原生界面操作函数
 *
 * @async
 * @function editNative
 * @param {Object} params 参数对象
 * @param {number} params.stepId 当前步骤ID
 * @param {Object} params.step 当前步骤信息
 * @param {string} params.curOsType 操作系统类型
 * @param {string} params.editType 编辑类型，'template'或'node'
 * @param {string=} params.method 方法名称，默认值为'click'
 * @param {number=} params.xPrecent X轴坐标百分比
 * @param {number=} params.yPrecent Y轴坐标百分比
 * @param {(step: Step)=>void} params.handleUpdateStep 更新步骤回调函数
 * @param {number|string=} params.templateId 模板ID，当编辑类型为'template'时生效
 * @param {(node?: Node)=>void} params.handleUpdateNode 更新节点回调函数
 * @returns {Promise<boolean>} 返回修改结果，true表示成功，false表示失败
 */
export async function editNativev2({step, domDetail, curOsType, method = 'click',
    xPrecent, yPrecent, handleUpdateStep}) {
    try {
        // 找控件
        let {width, height} = step.stepInfo.params.deviceInfo.screenSize;
        if (
            curOsType === 2 &&
            -1 !== Object.keys(step.stepInfo.params.deviceInfo.screenSize).indexOf('rotation')
        ) {
            if (
                -1 !== [90, 270].indexOf(step.stepInfo.params.deviceInfo.screenSize.rotation)
            ) {
                let _width = width;
                width = height;
                height = _width;
            }
        }
        // 计算点击位置在设备上的位置
        let x = xPrecent * width;
        let y = yPrecent * height;
        // 递归检索选择控件的路径
        let {pathIndexList, targetBounds} = searchElementByBoundsv2([domDetail], {
            x,
            y,
        });
        // if (0 === pathIndexList.length) {
        //     message.error('该点击位置没有控件');
        //     return false;
        // }
        if (0 === step.stepInfo.params.findType) {
            let {targetElement} = getSingleElementInfo(pathIndexList, domDetail, true);
            if (targetElement?.debug && targetElement.debug?.id !== undefined) {
                let newFindNode = [{
                    id: targetElement.debug.id,
                    actionNode: true,
                    parents: targetElement.debug.parents,
                    detailDetection: ['Text', 'TEXT']
                        .includes(targetElement.type) ? [1, 2, targetElement.ext.text] : [0, 0, ''],
                }];
                step.stepInfo.params.pathSummary = [...pathIndexList];
                step.stepInfo.params.findInfo.findNode = newFindNode;
            }

        } else if ([1, 2].includes(step.stepInfo.params.findType)) {
            let {targetElement} = getSingleElementInfo(pathIndexList, step, true);
            if (!targetElement?.debug?.id) {
                return;
            }
            let newFindNode = deepcopy(step.stepInfo.params.findInfo.findNode);
            // 单选
            // 判断选择控件 是否已存在
            let existIndex = -1;
            for (let index = 0; index < newFindNode.length; index++) {
                if (newFindNode[index].id === targetElement.debug.id) {
                    existIndex = index;
                    break;
                }
            }
            if ('click' === method) {
                if (-1 === existIndex) {
                    newFindNode.push({
                        id: targetElement.debug.id,
                        actionNode: false,
                        parents: targetElement.debug.parents,
                        detailDetection: [0, 0, ''],
                    });
                } else {
                    if (newFindNode[existIndex].actionNode) {
                        step.stepInfo.params.pathSummary = [];
                    }
                    newFindNode.splice(existIndex, 1);
                }
            }
            step.stepInfo.params.findInfo.findNode = [...newFindNode];
        }
        handleUpdateStep({...step});
    } catch (err) {
        message.warning(err.message ? err.message : err);
    }
};
