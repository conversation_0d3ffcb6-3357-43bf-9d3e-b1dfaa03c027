import classnames from 'classnames';
import {DeleteOutlined} from '@ant-design/icons';
import {Input, Select} from 'antd';
import styles from './Setting.module.less';

const findPath = (data, id, nowIndexList = [], path = []) => {
    data.forEach((element, index) => {
        nowIndexList.push(index);
        if (element.id === Number(id)) {
            path = [...nowIndexList];
            return {path};
        }
        if (element.children && element.children.length > 0) {
            let res = findPath(element.children, id, nowIndexList, path);
            path = res.path;
        }
        nowIndexList.pop();
    });
    return {path};
};

const findNewPath = (data, id, nowIndexList = [], path = []) => {
    data.forEach((element, index) => {
        nowIndexList.push(index);
        if (element?.debug?.id === Number(id)) {
            path = [...nowIndexList];
            return {path};
        }
        if (element.children && element.children.length > 0) {
            let res = findNewPath(element.children, id, nowIndexList, path);
            path = res.path;
        }
        nowIndexList.pop();
    });
    return {path};
};

export default ({currentStep, handleUpdateSelectPath,
    domDetail, handleUpdateStep, editType}) => {
    return (
        <>
            <span className={styles.paramsDescInfo}>
                请在<span className={styles.paramsDesc}>&nbsp;左侧截图&nbsp;</span>中点选多个目标控件，
                以通过<span className={styles.paramsDesc}>&nbsp;多控件布局关系&nbsp;</span>定位目标控件。
                <br />
                在控件上<span className={styles.paramsDesc}>&nbsp;点击右键&nbsp;</span>可叠加，
                <span className={styles.paramsDesc}>&nbsp;单控件视觉特征&nbsp;</span>以增强定位效果。
                <br />
                操作区域默认为<span className={styles.paramsDesc}>&nbsp;中部位置&nbsp;</span>
                在控件上<span className={styles.paramsDesc}>&nbsp;点击右键&nbsp;</span>
                可<span className={styles.paramsDesc}>&nbsp;指定操作具体控件&nbsp;</span>。
                <br />
                鼠标点击下面所设置的控件，如：控件N，可查看对应图中元素位置.
            </span>
            <div className={styles.paramsItem} style={{marginTop: 15}}>
                <div className={styles.paramsLeft} style={{width: 80}}>
                    操作位置
                </div>
                <NodePos
                    editType={editType}
                    domDetail={domDetail}
                    currentStep={currentStep}
                    handleUpdateStep={handleUpdateStep}
                    handleUpdateSelectPath={handleUpdateSelectPath}
                />
            </div>
            {currentStep?.stepInfo?.params?.findInfo?.findNode?.filter(item =>
                item.detailDetection[0] !== 0).length > 0 ? (
                <div className={styles.paramsItem} style={{marginTop: 15}}>
                    <div className={styles.paramsLeft} style={{width: 80}}>特征叠加</div>
                    <NodeFeatures
                        domDetail={domDetail}
                        editType={editType}
                        currentStep={currentStep}
                        handleUpdateStep={handleUpdateStep}
                        handleUpdateSelectPath={handleUpdateSelectPath}
                    />
                </div>
            ) : null
            }
        </>
    );
};

function NodePos({editType, currentStep, domDetail, handleUpdateStep, handleUpdateSelectPath}) {
    let text = '未选择控件';
    let flag = false;
    let findNode = currentStep.stepInfo.params.findInfo.findNode;
    let actionNode = null;
    for (let index in findNode) {
        if (findNode[index].actionNode) {
            flag = true;
            actionNode = ({
                index,
                node: findNode[index]
            });
        }
    }
    if (!flag && 0 !== findNode.length) {
        actionNode = ({
            index: -1,
            node: {}
        });
    }
    if (null !== actionNode && -1 === actionNode.index) {
        text = '区域中部';
    }
    if (null !== actionNode && -1 !== actionNode.index) {
        text = '控件' + String(actionNode.index);
    }
    return (
        <>
            {
                null === actionNode || -1 === actionNode.index ?
                    <Input
                        value={text}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        size='small'
                        className={classnames(styles.paramsCenter, 'input_editor')}
                    /> :
                    <div className={styles.nodeParams}>
                        <span
                            className={styles.nodeParamIndex}
                            onClick={() => {
                                if (-1 !== actionNode.index) {
                                    if (currentStep?.stepInfo.type === 5) {
                                        let {path} = findPath(
                                            [domDetail], actionNode.node.id);
                                        handleUpdateSelectPath(path);
                                    }
                                    if (currentStep?.stepInfo.type === 6) {
                                        let {path} = findNewPath(
                                            [domDetail], actionNode.node.id);
                                        handleUpdateSelectPath(path);
                                    }
                                }
                            }}
                        >
                            {'控件' + String(actionNode.index)}
                        </span>
                        {
                            (!['readonly', 'debug', 'execute'].includes(editType)) &&
                            <DeleteOutlined
                                className={styles.nodeParamsDelete}
                                onClick={() => {
                                    currentStep.stepInfo.params.findInfo.findNode[actionNode.index].actionNode = false;
                                    currentStep.stepInfo.params.pathSummary = [];
                                    handleUpdateStep({...currentStep});
                                    handleUpdateSelectPath([]);
                                }}
                            />
                        }
                    </div>
            }
        </>
    );
};

function NodeFeatures({editType, currentStep, handleUpdateStep, handleUpdateSelectPath}) {
    const options = [{
        key: '1',
        value: 1,
        label: '包含'
    }, {
        key: '2',
        value: 2,
        label: '等于'
    }];
    const typeOptions = [{
        key: '1',
        value: 1,
        label: '文本特征'
    }, {
        key: '2',
        value: 2,
        label: '图标特征'
    }];
    return currentStep.stepInfo.params.findInfo.findNode.map((item, index) => {
        if (item.detailDetection[0] === 1) {
            return (
                <div className={styles.nodeParams} key={'text_' + String(index)}>
                    <span
                        className={styles.nodeParamIndex}
                        onClick={() => {
                            if (currentStep?.stepInfo.type === 5) {
                                let {path} = findPath(
                                    [domDetail], item.id);
                                handleUpdateSelectPath(path);
                            }
                            if (currentStep?.stepInfo.type === 6) {
                                let {path} = findNewPath(
                                    [domDetail], item.id);
                                handleUpdateSelectPath(path);
                            }
                        }}
                    >
                        {'控件' + String(index)}
                    </span>
                    <Select
                        size="small"
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        className={styles.nodeParamSelectType}
                        value={item.detailDetection[0]}
                        options={typeOptions}
                        onChange={(value) => {
                            currentStep.stepInfo.params.findInfo.findNode[index].detailDetection = [value, 1, ''];
                            handleUpdateStep({...currentStep});
                        }}
                    />
                    <Select
                        size="small"
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        className={styles.nodeParamSelect}
                        value={item.detailDetection[1]}
                        options={options}
                        onChange={(value) => {
                            currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[1] = value;
                            handleUpdateStep({...currentStep});
                        }}
                    />
                    <Input
                        value={item.detailDetection[2]}
                        size='small'
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        className={classnames(styles.nodeParamInput, 'input_editor')}
                        onChange={(e) => {
                            currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[2] = e.target.value;
                            handleUpdateStep({...currentStep});
                        }}
                    />
                    {
                        (!['readonly', 'debug', 'execute'].includes(editType)) &&
                        <DeleteOutlined
                            className={styles.nodeParamsDelete}
                            onClick={() => {
                                currentStep.stepInfo.params.findInfo.findNode[index].detailDetection = [0, 0, ''];
                                handleUpdateStep({...currentStep});
                                handleUpdateSelectPath([]);
                            }}
                        />
                    }
                </div>
            );
        }
        if (item.detailDetection[0] === 2) {
            return (
                <div className={styles.nodeParams} key={'icon_' + String(index)}>
                    <span
                        className={styles.nodeParamIndex}
                        onClick={() => {
                            if (currentStep?.stepInfo.type === 5) {
                                let {path} = findPath(
                                    [domDetail], item.id);
                                handleUpdateSelectPath(path);
                            }
                            if (currentStep?.stepInfo.type === 6) {
                                let {path} = findNewPath(
                                    [domDetail], item.id);
                                handleUpdateSelectPath(path);
                            }
                        }}
                    >
                        {'控件' + String(index)}
                    </span>
                    <Select
                        size="small"
                        className={styles.nodeParamSelectType}
                        value={item.detailDetection[0]}
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        options={typeOptions}
                        onChange={(value) => {
                            currentStep.stepInfo.params.findInfo.findNode[index].detailDetection = [value, 1, ''];
                            handleUpdateStep({...currentStep});
                        }}
                    />
                    {
                        (!['readonly', 'debug', 'execute'].includes(editType)) &&
                        <DeleteOutlined
                            className={styles.nodeParamsDelete}
                            onClick={() => {
                                currentStep.stepInfo.params.findInfo.findNode[index].detailDetection = [0, 0, ''];
                                handleUpdateStep({...currentStep});
                                handleUpdateSelectPath([]);
                            }}
                        />
                    }
                </div>
            );
        }
    });
}
