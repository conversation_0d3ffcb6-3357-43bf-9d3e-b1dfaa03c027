import {isEmpty} from 'lodash';
import {useState, useEffect} from 'react';
import {Checkbox, Col, Row, Tooltip} from 'antd';
import RenderSysElement from '../Element/SysElement';
import RenderTextElement from '../Element/TextElement';
import RenderIconElement from '../Element/IconElement';
import RenderComponentElement from '../Element/ComponentElement';
import RenderCVElement from '../Element/CVElement';
import FeatureCheckBox from './FeatureCheckBox';
import styles from '../Setting.module.less';
const FindNodeItem = (props) => {
    const {item, index, findNode, ParamsTitleInfo, sizeType, editType} = props;
    const [findNodeItem, setFindNodeItem] = useState(item);

    useEffect(() => {
        setFindNodeItem(item);
    }, [item?.id]);

    if (findNodeItem?.detailFeature?.type.includes('Text')) {
        let textIndex = findNode?.filter(node =>
            node?.detailFeature?.type?.includes('Text'))?.findIndex(node =>
                node?.id === findNodeItem?.id) + 1;
        return (
            <Col span={24} key={'text_' + String(textIndex)} className={styles.paramItem}>
                <FeatureCheckBox
                    {...props}
                    index={index}
                    findNodeItem={findNodeItem}
                />
                <ParamsTitleInfo
                    {...props}
                    index={textIndex}
                    text={`文本 ${String(textIndex)}`}
                    item={findNodeItem}
                />
                <RenderTextElement
                    {...props}
                    index={index}
                    type={sizeType}
                    className={styles.nodeParamSelectInput}
                    disabled={
                        !findNodeItem?.featureFlag || ['readonly', 'debug', 'execute'].includes(editType)}
                />
            </Col>
        );
    }
    if (['Icon'].includes(findNodeItem?.detailFeature?.type)) {
        let iconIndex = findNode?.filter(node =>
            node?.detailFeature?.type?.includes('Icon'))?.findIndex(node =>
                node?.id === findNodeItem?.id) + 1;
        return (
            <Col span={24} key={'icon_' + String(iconIndex)} className={styles.paramItem}>
                <FeatureCheckBox
                    {...props}
                    index={index}
                    findNodeItem={findNodeItem}
                />
                <ParamsTitleInfo
                    {...props}
                    index={iconIndex}
                    text={`图标 ${String(iconIndex)}`}
                    item={findNodeItem}
                />
                <RenderIconElement
                    {...props}
                    index={index}
                    type={sizeType}
                    disabled={!findNodeItem?.featureFlag || ['readonly', 'debug', 'execute'].includes(editType)}
                />
            </Col>
        );
    }
    if (['Component'].includes(findNodeItem?.detailFeature?.type)) {
        let componentIndex = findNode?.filter(node =>
            node?.detailFeature?.type?.includes('Component'))?.findIndex(node =>
                node?.id === findNodeItem?.id) + 1;
        return (
            <Col span={24} key={'icon_' + String(componentIndex)} className={styles.paramItem}>
                <FeatureCheckBox
                    {...props}
                    index={index}
                    findNodeItem={findNodeItem}
                />
                <ParamsTitleInfo
                    {...props}
                    index={componentIndex}
                    text={`组件 ${String(componentIndex)}`}
                    item={findNodeItem}
                />
                <RenderComponentElement
                    {...props}
                    index={index}
                    type={sizeType}
                    disabled={!findNodeItem?.featureFlag || ['readonly', 'debug', 'execute'].includes(editType)}
                />
            </Col>
        );
    }
    const is_sys = data => (['DFE'].includes(data?.detailFeature?.type) &&
        !isEmpty(data?.detailFeature?.ext))
        || ['DOE'].includes(data?.detailFeature?.type);
    if (is_sys(findNodeItem)) {
        let sysIndex = findNode?.filter(node => is_sys(node))?.findIndex(node =>
            node?.id === findNodeItem?.id) + 1;
        return (
            <Col span={24} key={'component_' + String(sysIndex)} className={styles.paramItem}>
                <FeatureCheckBox
                    {...props}
                    index={index}
                    findNodeItem={findNodeItem}
                />
                <ParamsTitleInfo
                    {...props}
                    index={sysIndex}
                    text={`系统 ${String(sysIndex)}`}
                    item={findNodeItem}
                />
                <RenderSysElement
                    {...props}
                    index={index}
                    type={sizeType}
                    disabled={global.params.CASE_STATUS !== 'edit' ||
                        !findNodeItem?.featureFlag || ['readonly', 'debug', 'execute'].includes(editType)}
                />
            </Col>
        );
    }
    const is_cve = data => (['CVE'].includes(data?.detailFeature?.type));
    if (is_cve(findNodeItem)) {
        let cvIndex = findNode?.filter(node => is_cve(node))?.findIndex(node => node?.id === item?.id) + 1;
        return (
            <Col span={24} key={'cv_' + String(cvIndex)} className={styles.paramItem}>
                <FeatureCheckBox
                    {...props}
                    index={index}
                    findNodeItem={findNodeItem}
                />
                <ParamsTitleInfo
                    {...props}
                    index={sysIndex}
                    text={`元素 ${String(cvIndex)}`}
                    item={findNodeItem}
                />
                <RenderCVElement
                    {...props}
                    index={index}
                    type={sizeType}
                    disabled={!findNodeItem?.featureFlag || ['readonly', 'debug', 'execute'].includes(editType)}
                />
            </Col>
        );
    }
};

export default FindNodeItem;