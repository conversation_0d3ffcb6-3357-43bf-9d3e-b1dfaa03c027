import {<PERSON><PERSON>, Col, Row, InputNumber, Radio, Select, Switch, Tag, Tooltip} from 'antd';
import {useCallback} from 'react';
import classnames from 'classnames';
import {useState} from 'react';
import {DeleteOutlined} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import {findNewPath} from '../../utils';

import ParamsTitle from 'COMMON/components/ParamsTitle';
import ParamsSmallTitle from 'COMMON/components/ParamsSmallTitle';

import ScreenFind from './ScreenFind';
import NodeFeatures from '../NodeFeatures';
import DomTree from '../BatDomTree';

import styles from '../Setting.module.less';


const addStyles = {width: 'calc(100% - 80px)'};

function MultiElementType(props) {
    const {findInfoType, currentStep, handleUpdateStep, domDetail, operationType,
        editType, sizeType = 'normal'} = props;
    const [openDomTree, setOpenDomTree] = useState(false);
    const [activedNode, setActivedNode] = useState(null);

    let text = '全屏';
    let flag = false;
    let findNode = currentStep.stepInfo.params.findInfo[findInfoType]?.findNode ?? [];
    let actionNode = null;
    for (let index in findNode) {
        if (findNode[index].actionNode) {
            flag = true;
            actionNode = ({
                index,
                node: findNode[index]
            });
        }
    }
    if (!flag && 0 !== findNode.length) {
        actionNode = ({
            index: -1,
            node: {}
        });
    }
    if (null !== actionNode && -1 === actionNode.index) {
        text = '区域中部';
    }
    if (null !== actionNode && -1 !== actionNode.index) {
        text = '控件' + String(actionNode.index);
    }
    const Title = sizeType === 'normal' ? ParamsTitle : ParamsSmallTitle;

    const RenderNodeFeatures = useCallback(() => {
        return (
            <NodeFeatures
                {...props}
                activedNode={activedNode}
                setActivedNode={setActivedNode}
                ParamsTitleInfo={ParamsTitleInfo}
            />
        );
    }, [currentStep, activedNode]);

    return (
        <div
            className={classnames(styles.multiParams, {
                [styles.modelMarginLeft]: sizeType === 'small'
            })}
        >
            {findNode.length === 0 ? (
                <div
                    className={classnames(
                        {[styles.paramsItem]: sizeType === 'normal'},
                        {[styles.paramsItemSmall]: sizeType === 'small'}
                    )}
                    style={{marginTop: sizeType === 'normal' ? 15 : 10}}
                >
                    <div style={{fontSize: 12, color: 'red'}}>注意：区域变更后，会清空已选择的控件信息</div>
                    <Title text="区域选择" />
                    <span
                        style={{
                            display: 'inline-block',
                            width: 80,
                            fontSize: sizeType === 'normal' ? 14 : 12
                        }}
                    >
                        全屏
                    </span>
                </div>
            ) : (
                <>
                    {(sizeType === 'small' || sizeType === 'normal') && (
                        <div
                            className={classnames(
                                {[styles.paramsItem]: sizeType === 'normal'},
                                {[styles.paramsItemSmall]: sizeType === 'small'}
                            )}
                            style={{
                                marginTop: sizeType === 'normal' ? 15 : 10,
                                fontSize: sizeType === 'normal' ? 14 : 12
                            }}
                        >
                            <div style={{fontSize: 12, color: 'red'}}>注意：区域变更后，会清空已选择的控件信息</div>
                            <Title text="区域选择" />
                            <div
                                className={classnames(styles.featurePos, {
                                    [styles.rowMarginLeft]: sizeType === 'small'
                                })}
                            >
                                {RenderNodeFeatures()}
                            </div>
                        </div>
                    )}
                </>
            )}
            <div
                className={classnames(
                    {[styles.paramsItem]: sizeType === 'normal'},
                    {[styles.paramsItemSmall]: sizeType === 'small'}
                )}
                style={{marginTop: sizeType === 'normal' ? 15 : 10}}
            >
                <Title text="查找配置" />
                <ScreenFind {...props} />
            </div>
            {sizeType === 'normal' && (
                <div
                    className={classnames(
                        {[styles.paramsItem]: sizeType === 'normal'},
                        {[styles.paramsItemSmall]: sizeType === 'small'}
                    )}
                    style={{marginTop: sizeType === 'normal' ? 15 : 10}}
                >
                    <Title text="配置项" />
                    <div className={classnames({[styles.rowMarginLeft]: sizeType === 'small'})}>
                        <span
                            style={{
                                fontSize: sizeType === 'normal' ? 14 : 12
                            }}
                        >
                            {sizeType === 'normal' ? (
                                <>
                                    <span style={{marginRight: 10}}>使用 FastSAM</span>
                                    <Switch
                                        checkedChildren="开启"
                                        unCheckedChildren="关闭"
                                        disabled={['readonly', 'debug', 'execute'].includes(
                                            editType
                                        )}
                                        checked={
                                            currentStep.stepInfo.params.findInfo[findInfoType]?.useFastSAM ??
                                            false
                                        }
                                        onChange={async (checked) => {
                                            if (!currentStep.stepInfo.params.findInfo[findInfoType]) {
                                                currentStep.stepInfo.params.findInfo[findInfoType] = {};
                                            }
                                            currentStep.stepInfo.params.findInfo[findInfoType].useFastSAM =
                                                checked;
                                            await handleUpdateStep({...currentStep});
                                        }}
                                    />
                                </>
                            ) : (
                                <span>
                                    {currentStep.stepInfo.params.findInfo[findInfoType]?.useFastSAM ? '已' : '未'}
                                    使用 FastSAM
                                </span>
                            )}
                        </span>
                    </div>
                </div>
            )}
            {sizeType === 'normal' && (
                <div className={styles.paramsItem}>
                    <Title text="实验室功能（为了世界和平，请谨慎使用）" />
                    <div>
                        开启 DOM 视图，一键独断万古&nbsp;
                        <Switch
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                            checked={openDomTree}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onChange={setOpenDomTree}
                        />
                        {openDomTree && (
                            <div style={{height: 250, overflow: 'scroll'}}>
                                <DomTree
                                    {...props}
                                    activedNode={activedNode}
                                    setActivedNode={setActivedNode}
                                />
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
}

function ParamsTitleInfo({text, sizeType, item, domDetail,
    activedNode, setActivedNode, handleUpdateSelectPath, currentStep}) {
    return (
        <span
            className={classnames(styles.nodeParamIndex,
                styles.textSizeNormal,
                {[styles.textSizeSmall]: sizeType === 'small'},
                {[styles.activeParam]: +activedNode === +item.id})}
            onClick={() => {
                if (sizeType === 'small') {
                    return;
                }
                if (item.id === activedNode) {
                    setActivedNode(null);
                    handleUpdateSelectPath([]);
                    return;
                }
                if ([7, 8, 9, 10].includes(currentStep.stepInfo.type)) {
                    let {path} = findNewPath([domDetail], item.id);
                    handleUpdateSelectPath(path);
                    setActivedNode(item.id);
                }
            }}
            style={{
                float: 'left'
            }}
        >
            {text}
        </span>
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal,
}))(MultiElementType);
