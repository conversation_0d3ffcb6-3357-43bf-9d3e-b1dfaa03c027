import {useState, useEffect} from 'react';
import {Checkbox} from 'antd';
import styles from '../Setting.module.less';
function FeatureCheckBox(props) {
    const {findNodeItem, index, currentStep,
        findInfoType, editType, handleUpdateStep} = props;
    const [nodeChecked, setNodeChecked] = useState(findNodeItem?.featureFlag);

    return (
        <Checkbox
            className={styles.featurecheckbox}
            disabled={['readonly', 'debug', 'execute'].includes(editType)}
            checked={nodeChecked}
            onChange={async (e) => {
                const newCurrentStep = {...currentStep}; // 创建新的currentStep对象
                newCurrentStep.stepInfo.params.findInfo[findInfoType]
                    .findNode[index].featureFlag = e.target.checked; // 更新属性
                setNodeChecked(e.target.checked);
                handleUpdateStep(newCurrentStep);
            }}
        />
    );
}

export default FeatureCheckBox;