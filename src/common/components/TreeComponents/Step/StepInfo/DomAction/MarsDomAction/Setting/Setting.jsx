import { useState, useEffect } from 'react';
import { Drawer, Tooltip, Tabs, message } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import ParamsTitle from 'COMMON/components/ParamsTitle';
import ClickWait from '@step/StepInfo/components/ClickWait';
import DomWait from '@step/StepInfo/components/DomWait';
import BeforeSysAlertClear from '@step/StepInfo/components/BeforeSysAlertClear';
import StepInterval from '@step/StepInfo/components/StepInterval';
import Retry from '@step/StepInfo/components/RetryMars';
import IfThen from '@step/StepInfo/components/IfThen';
import BaseInfo from './BaseInfo';
import WidgetInfo from './WidgetInfo';
import styles from './Setting.module.less';

function Setting(props) {
    const {
        setShowModal,
        currentStep,
        curOsType,
        findInfoType,
        setFindInfoType,
        innerHeight,
        innerWidth,
        text,
        handleUpdateStep
    } = props;
    const [selectPath, setSelectPath] = useState([]);
    const [open, setOpen] = useState(false);
    const [settingWidth, setSettingWidth] = useState(0);
    const [recordStep, setRecordStep] = useState(null);

    useEffect(() => {
        updateSettingWidth(window.innerHeight, window.innerWidth);
    }, [innerHeight, innerWidth]);

    const updateSettingWidth = (innerHeight, innerWidth) => {
        let screenWidth = currentStep?.stepInfo.params.recordInfo.deviceInfo.screenSize.width;
        let screenHeight = currentStep?.stepInfo.params.recordInfo.deviceInfo.screenSize.height;
        if (screenWidth < screenHeight || curOsType === 2) {
            let imgHeight = innerHeight - 200;
            let imageScale = imgHeight / screenHeight;
            let imgWidth = imageScale * screenWidth;
            setSettingWidth(imgWidth + 700);
        } else {
            let imgWidth = innerWidth - 200;
            let imageScale = imgWidth / screenWidth;
            let imgHeight = imageScale * screenHeight;
            setSettingWidth(imgHeight + 350);
        }
    };

    const tabItems = [
        {
            key: 'widgetInfo',
            label: <Tooltip title="选择实际待定位控件">查找控件</Tooltip>,
            children: (
                <WidgetInfo {...props} selectPath={selectPath} setSelectPath={setSelectPath} />
            )
        },
        {
            key: 'baseInfo',
            label: (
                <Tooltip
                    title={
                        <span>
                            选择待定位控件的区域范围
                            <br />
                            默认:&nbsp;为全屏
                        </span>
                    }
                >
                    查找区域
                </Tooltip>
            ),
            children: <BaseInfo {...props} selectPath={selectPath} setSelectPath={setSelectPath} />
        }
    ];
    return (
        <>
            <span
                style={{ cursor: 'pointer', fontSize: 13 }}
                onClick={() => {
                    setShowModal(true);
                    setOpen(true);
                    setRecordStep(JSON.stringify(currentStep.stepInfo));
                }}
                className={styles.operatorIcon}
            >
                <SettingOutlined />
                &nbsp;{text}
            </span>
            <Drawer
                title="控件查找"
                open={open}
                centered
                width={settingWidth}
                onClose={async () => {
                    if (
                        currentStep?.stepInfo?.params?.findInfo[findInfoType]?.chosenTag?.includes(
                            'multiple'
                        ) &&
                        ((findInfoType === 'widgetInfo' &&
                            currentStep?.stepInfo?.params?.findInfo[findInfoType]?.findNode
                                .length <= 1) ||
                            (findInfoType === 'baseInfo' &&
                                currentStep?.stepInfo?.params?.findInfo[findInfoType]?.findNode
                                    .length === 1))
                    ) {
                        message.warning('多控件建模需要设置 2 个以上控件');
                    }
                    // 多控件建模不能超过5个控件
                    if (
                        currentStep?.stepInfo?.params?.findInfo[findInfoType]?.chosenTag?.includes(
                            'multiple'
                        ) &&
                        ((findInfoType === 'widgetInfo' &&
                            currentStep?.stepInfo?.params?.findInfo[findInfoType]?.findNode
                                .length > 5) ||
                            (findInfoType === 'baseInfo' &&
                                currentStep?.stepInfo?.params?.findInfo[findInfoType]?.findNode
                                    .length > 5))
                    ) {
                        message.warning('多控件建模最多只能设置 5 个控件');
                        return;
                    }
                    if (
                        currentStep?.stepInfo?.params?.findInfo[findInfoType]?.chosenTag?.includes(
                            'single'
                        ) &&
                        findInfoType === 'widgetInfo' &&
                        currentStep?.stepInfo?.params?.findInfo[findInfoType]?.findNode.length < 1
                    ) {
                        message.warning('单控件建模需要设置 1 个控件');
                    }
                    let newActionInfo = JSON.parse(JSON.stringify(currentStep.stepInfo));
                    if (JSON.stringify(newActionInfo) === recordStep) {
                        setShowModal(false);
                        setSelectPath([]);
                        setRecordStep(null);
                        setOpen(false);
                        setFindInfoType('widgetInfo');
                        return;
                    }
                    await handleUpdateStep({
                        ...currentStep,
                        newActionInfo
                    });
                    setShowModal(false);
                    setSelectPath([]);
                    setOpen(false);
                    setFindInfoType('widgetInfo');
                }}
                footer={null}
            >
                <Tabs
                    tabPosition="left"
                    defaultActiveKey={2}
                    items={[
                        {
                            label: '基础配置',
                            key: 1,
                            children: (
                                <div style={{ margin: '0 10%' }}>
                                    <ParamsTitle text="查找失败重试配置" />
                                    <Retry {...props} />
                                    <ParamsTitle text="控件操作配置" />
                                    <IfThen {...props} />
                                    <DomWait {...props} />
                                    <ClickWait {...props} />
                                    <ParamsTitle text="步骤参数" />
                                    <BeforeSysAlertClear {...props} />
                                    <StepInterval {...props} />
                                </div>
                            )
                        },
                        {
                            label: '查找方式',
                            key: 2,
                            children: (
                                <Tabs
                                    type="card"
                                    items={tabItems}
                                    activeKey={findInfoType}
                                    size="small"
                                    onChange={(activeKey) => {
                                        setFindInfoType(activeKey);
                                    }}
                                />
                            )
                        }
                    ]}
                />
            </Drawer>
        </>
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal
}))(Setting);
