import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import ImageActionv3 from '../../ImageActionv3';
import MultiElementType from './MultiElementType';
import SingleElementType from './SingleElementType';
import StepOperator from '../../StepOperator';
import styles from '../Setting.module.less';

function BaseInfo(props) {
    const {currentStep} = props;

    return (
        <>
            <div className={styles.settingLeft}>
                <StepOperator
                    {...props}
                    findInfoType='baseInfo'
                    operaOptions={{
                        showActionType: false,
                        showFindTypeOpera: true,
                        showModelTypeOpera: true,
                        showSettingOpera: false
                    }}
                />
                <ImageActionv3
                    key={'setting_image_action'}
                    {...props}
                    findInfoType='baseInfo'
                />
            </div>
            <div className={styles.settingRight}>
                {
                    currentStep?.stepInfo?.params?.findInfo?.baseInfo?.chosenTag?.includes('single') ?
                        <SingleElementType
                            {...props}
                            findInfoType='baseInfo'
                        /> : <MultiElementType
                            {...props}
                            findInfoType='baseInfo'
                        />
                }
            </div>
        </>
    );
};

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal,
}))(BaseInfo);
