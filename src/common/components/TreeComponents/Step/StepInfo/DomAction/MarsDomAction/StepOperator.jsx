import { useState, useEffect, useMemo } from 'react';
import { Tooltip, Switch, Dropdown, Select, message } from 'antd';
import { RedoOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import { clickThrottle } from 'COMMON/utils/utils';
import { SelectOperator } from 'COMMON/components/TreeComponents/Step/StepInfo/components';
import { RECORD_OPTIONS, DOM_OPTIONS } from './config';
import Setting from './Setting';
import styles from 'COMMON/components/TreeComponents/Step/StepInfo/StepInfo.module.less';

function StepOperator(props) {
    const {
        findInfoType = 'widgetInfo',
        currentStep,
        recordUsualDomStep,
        domDetail,
        changeUsualDomStep,
        handleUpdateNode,
        osType,
        editType,
        handleUpdateStep,
        currentNode,
        operaOptions,
        setRecordId,
        pageSourceSwitch,
        setPageSourceSwitch,
        operationType
    } = props;

    const [stepType, setActionType] = useState(null);
    const [modelType, setModelType] = useState(0);
    const [findType, setFindType] = useState(0);
    const [messageApi, contextHolder] = message.useMessage();

    const changeSwitch = () => {
        setPageSourceSwitch(!pageSourceSwitch);
        localStorage.setItem('pageSourceSwitch', !pageSourceSwitch);
    };

    useEffect(() => {
        setActionType(currentStep.stepInfo.params?.actionInfo?.type);
        let tags = currentStep.stepInfo.params?.findInfo[findInfoType]?.chosenTag ?? [];
        if (tags.includes('single')) {
            setFindType(0);
        }
        if (tags.includes('multiple')) {
            setFindType(1);
        }
        if (tags.includes('visual')) {
            setModelType(0);
        }
        if (tags.includes('visual') && tags.includes('system')) {
            setModelType(2);
        }
    }, [currentStep?.stepId, findInfoType]);

    const version = useMemo(() => {
        if (currentStep?.stepInfo?.params?.recordInfo?.domInfo?.version) {
            return currentStep.stepInfo.params.recordInfo?.domInfo.version;
        }
        return '版本暂无';
    }, [currentStep, findInfoType]);

    return (
        <div className={styles.stepOperator} style={{ textAlign: 'center' }}>
            {contextHolder}
            {operaOptions?.showActionType && (
                <>
                    <SelectOperator
                        currentStep={currentStep}
                        border={false}
                        domDetail={domDetail}
                        handleUpdateStep={handleUpdateStep}
                        osType={osType}
                        stepType={stepType}
                        editType={editType}
                        currentNode={currentNode}
                        handleUpdateNode={handleUpdateNode}
                        setActionType={setActionType}
                        findInfoType={findInfoType}
                        includeOptions={operationType === 'ifThen' ? ['nope', 'absence'] : []}
                    />
                    <span className={styles.operatorCut}>|</span>
                </>
            )}
            {-1 === ['debug', 'execute'].indexOf(editType) && (
                <span
                    style={{
                        fontSize: 16,
                        color: '#959292'
                    }}
                >
                    <Dropdown
                        dropdownRender={() => (
                            <span className={styles.switchBtn}>
                                <Switch
                                    size="small"
                                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                    checked={
                                        !localStorage.getItem('pageSourceSwitch') ||
                                        localStorage.getItem('pageSourceSwitch') === 'true'
                                    }
                                    onClick={() => changeSwitch()}
                                />
                                &nbsp;建模系统控件
                            </span>
                        )}
                    >
                        <Tooltip title={'当前版本：' + version}>
                            <span
                                style={{
                                    cursor: !isElectron() ? 'not-allowed' : 'pointer',
                                    fontSize: 13
                                }}
                                onClick={async (e) => {
                                    if (!isElectron()) {
                                        return false;
                                    }
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    e.stopPropagation();
                                    if (!clickThrottle(3000)) {
                                        messageApi.info('操作太频繁啦！');
                                        return false;
                                    }
                                    setRecordId(null);
                                    await recordUsualDomStep();
                                }}
                                className={styles.operatorIcon}
                            >
                                &nbsp;
                                <RedoOutlined />
                                &nbsp;建模
                            </span>
                        </Tooltip>
                    </Dropdown>
                    {operaOptions?.showFindTypeOpera && (
                        <Select
                            size="small"
                            options={DOM_OPTIONS.map((option, index) => {
                                return option;
                            })}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            variant="borderless"
                            popupMatchSelectWidth={false}
                            value={findType}
                            style={{
                                marginLeft: 10
                            }}
                            onChange={(value) => {
                                setFindType(value);
                                changeUsualDomStep(findInfoType, value, modelType);
                            }}
                        />
                    )}
                    <Select
                        size="small"
                        disabled={['readonly', 'debug', 'execute'].includes(editType)}
                        options={RECORD_OPTIONS.filter((item) => {
                            if (
                                item.value === 2 &&
                                currentStep?.stepInfo?.params?.findInfo?.[findInfoType]
                                    ?.modelType === 0
                            ) {
                                return false;
                            } else {
                                return true;
                            }
                        })}
                        style={{
                            marginLeft: !operaOptions?.showFindTypeOpera ? 10 : 0
                        }}
                        variant="borderless"
                        popupMatchSelectWidth={false}
                        value={modelType}
                        onChange={(value) => {
                            setModelType(value);
                            changeUsualDomStep(findInfoType, findType, value);
                        }}
                    />
                    {currentStep?.stepInfo?.params?.recordInfo?.deviceInfo?.screenSize &&
                        operaOptions?.showSettingOpera && (
                            <>
                                <span className={styles.operatorCut}>&nbsp;|&nbsp;&nbsp;</span>
                                <Setting
                                    {...props}
                                    domDetail={domDetail}
                                    operationType={operationType}
                                    className={styles.operatorDetailIcon}
                                    text="设置"
                                />
                            </>
                        )}
                </span>
            )}
        </div>
    );
}

export default connectModel([commonModel], (state) => ({
    recordId: state.common.case.recordId,
    pageSourceSwitch: state.common.case.pageSourceSwitch
}))(StepOperator);
