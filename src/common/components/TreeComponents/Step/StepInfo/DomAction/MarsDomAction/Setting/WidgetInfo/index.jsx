import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import ImageActionv3 from '../../ImageActionv3';
import MultiElementType from './MultiElementType';
import SingleElementType from './SingleElementType';
import StepOperator from '../../StepOperator';
import styles from '../Setting.module.less';

function WidgetInfo(props) {
    const {currentStep} = props;

    return (
        <>
            <div className={styles.settingLeft}>
                <StepOperator
                    {...props}
                    findInfoType='widgetInfo'
                    operaOptions={{
                        showActionType: true,
                        showFindTypeOpera: true,
                        showModelTypeOpera: true,
                        showSettingOpera: false
                    }}
                />
                <ImageActionv3
                    key={'setting_image_action'}
                    {...props}
                    findInfoType='widgetInfo'
                />
            </div>
            <div className={styles.settingRight}>
                {
                    currentStep?.stepInfo?.params?.params?.findInfo?.widgetInfo?.includes('single') ?
                        <SingleElementType
                            {...props}
                            findInfoType='widgetInfo'
                        /> : <MultiElementType
                            {...props}
                            findInfoType='widgetInfo'
                        />
                }
            </div>
        </>
    );
};

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal,
}))(WidgetInfo);
