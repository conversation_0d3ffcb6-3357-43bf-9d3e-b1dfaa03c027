import {DeleteOutlined} from '@ant-design/icons';
import {Row, Tooltip} from 'antd';
import FindNodeItem from './FindNodeItem';
import styles from '../Setting.module.less';

function NodeFeatures(props) {
    const {
        currentStep, findInfoType
    } = props;
    const findNode = currentStep.stepInfo.params.findInfo[findInfoType]?.findNode || [];

    return (
        <Row>
            {
                findNode.map((item, index) => (
                    <FindNodeItem
                        {...props}
                        key={item?.id + '_' + String(index)}
                        item={item}
                        index={index}
                    />
                ))}
        </Row>
    );
}


function DeleteIcon({currentStep, index, findInfoType, handleUpdateSelectPath, handleUpdateStep}) {
    return (
        <Tooltip title="删除该控件" placement='right'>
            <DeleteOutlined
                className={styles.newNodeParamsDelete}
                onClick={async () => {
                    currentStep.stepInfo.params.findInfo[findInfoType].findNode.splice(index, 1);
                    handleUpdateStep({...currentStep});
                    handleUpdateSelectPath([]);
                }}
            />
        </Tooltip>
    );
}

export default NodeFeatures;