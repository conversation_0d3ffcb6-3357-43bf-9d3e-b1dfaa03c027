import { InputNumber, Select, Switch } from 'antd';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import ParamsTitle from 'COMMON/components/ParamsTitle';
import ParamsSmallTitle from 'COMMON/components/ParamsSmallTitle';
import { SelectOperator } from 'COMMON/components/TreeComponents/Step/StepInfo/components';
import MulitParams from 'COMMON/components/TreeComponents/Step/StepItem/Params/NewMulitParams';
import RenderSysElement from '../Element/SysElement';
import RenderTextElement from '../Element/TextElement';
import RenderIconElement from '../Element/IconElement';
import RenderComponentElement from '../Element/ComponentElement';
import RenderCVElement from '../Element/CVElement';
import styles from '../Setting.module.less';
import commonStyles from 'COMMON/components/TreeComponents/Step/StepInfo/StepInfo.module.less';

const addStyles = {
    width: 'calc(100% - 85px)'
};

function SingleElementType(props) {
    const {
        findInfoType,
        editType,
        currentStep,
        handleUpdateStep,
        operationType,
        sizeType = 'normal'
    } = props;

    const getElementType = (currentStep) => {
        if (isEmpty(currentStep?.stepInfo.params.findInfo?.[findInfoType]?.findNode)) {
            return { text: '未选择控件', value: '' };
        }
        switch (
            currentStep?.stepInfo.params.findInfo[findInfoType].findNode[0].detailFeature.type
        ) {
            case 'Text':
                return {
                    type: 'Text',
                    text: '文本控件',
                    value: ''
                };
            case 'TextArea':
                return {
                    type: 'Text',
                    text: '文本控件',
                    value: ''
                };
            case 'Component':
                return {
                    type: 'Component',
                    text: '组件控件',
                    value: currentStep?.stepInfo.params.findInfo[findInfoType].findNode[0]
                        .detailFeature.name
                };
            case 'Icon':
                return {
                    type: 'Icon',
                    text: '图标控件',
                    value: currentStep?.stepInfo.params.findInfo[findInfoType].findNode[0]
                        .detailFeature.name
                };
            case 'CVE':
                return {
                    type: 'CVE',
                    text: '元素',
                    value: ''
                };
            default:
                let options = [];
                if (
                    currentStep?.stepInfo.params.findInfo[findInfoType].findNode[0].detailFeature
                        .ext?.feature
                ) {
                    for (let item in currentStep?.stepInfo.params.findInfo[findInfoType].findNode[0]
                        .detailFeature.ext?.feature) {
                        options.push({
                            value: item,
                            label:
                                currentStep?.stepInfo.params.findInfo[findInfoType].findNode[0]
                                    .detailFeature.ext?.feature[item]?.data || '123',
                            chosen:
                                currentStep?.stepInfo.params.findInfo[findInfoType].findNode[0]
                                    .detailFeature.ext?.feature[item]?.chosen || true
                        });
                    }
                }
                return {
                    type: 'Sys',
                    text: '系统控件',
                    options,
                    value: currentStep?.stepInfo.params.findInfo[findInfoType].findNode[0]
                        .detailFeature.ext.name
                };
        }
    };
    const { type, text, value } = getElementType(currentStep);

    const Title = sizeType === 'normal' ? ParamsTitle : ParamsSmallTitle;

    return (
        <div
            className={classnames(styles.singleParams, {
                [styles.modelMarginLeft]: sizeType === 'small'
            })}
        >
            {isEmpty(currentStep?.stepInfo.params.findInfo?.[findInfoType]?.findNode) && (
                <div
                    className={classnames(
                        { [styles.paramsItem]: sizeType === 'normal' },
                        { [styles.paramsItemSmall]: sizeType === 'small' }
                    )}
                    style={{ marginTop: sizeType === 'normal' ? 15 : 10 }}
                >
                    <Title text="控件类型" />
                    <div className={classnames({ [styles.rowMarginLeft]: sizeType === 'small' })}>
                        <ParamsInfo
                            text={`${text}${value ? `(${value})` : ''}`}
                            sizeType={sizeType}
                        />
                    </div>
                </div>
            )}
            {!isEmpty(currentStep?.stepInfo.params.findInfo?.[findInfoType]?.findNode) && (
                <>
                    <div
                        className={classnames(
                            { [styles.paramsItem]: sizeType === 'normal' },
                            { [styles.paramsItemSmall]: sizeType === 'small' }
                        )}
                        style={{
                            marginTop: sizeType === 'normal' ? 15 : 10,
                            fontSize: sizeType === 'normal' ? 14 : 12
                        }}
                    >
                        <Title text="查找配置" />
                        <div
                            className={classnames({
                                [styles.rowMarginLeft]: sizeType === 'small'
                            })}
                        >
                            <ParamsInfo text="定位方式" sizeType={sizeType} width={85} />
                            <Select
                                size="small"
                                style={{
                                    width: 'calc(100% - 100px)',
                                    marginBottom: 5
                                }}
                                className={classnames(commonStyles.paramsSelect, {
                                    [commonStyles.paramsSmallSelect]: sizeType === 'small'
                                })}
                                placeholder="选择定位方式"
                                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                options={[
                                    {
                                        value: '1',
                                        label: '基于出现次序定位'
                                    },
                                    {
                                        value: '2',
                                        label: '基于位置相似度定位'
                                    }
                                ]}
                                value={
                                    currentStep?.stepInfo.params.findInfo[findInfoType]
                                        ?.byDistance === true
                                        ? '2'
                                        : '1'
                                }
                                onChange={async (value) => {
                                    currentStep.stepInfo.params.findInfo[findInfoType].byDistance =
                                        value === '2';
                                    await handleUpdateStep({ ...currentStep });
                                }}
                            />
                        </div>
                        <div
                            className={classnames({ [styles.rowMarginLeft]: sizeType === 'small' })}
                        >
                            {currentStep?.stepInfo.params.findInfo[findInfoType]?.byDistance !==
                                true &&
                                currentStep?.stepInfo.params.findInfo[findInfoType].findNode[0]
                                    .detailFeature.ext?.index !== undefined &&
                                (currentStep?.stepInfo?.params?.findInfo[findInfoType]
                                    ?.fixedType === 1 ||
                                    currentStep?.stepInfo?.params?.findInfo[findInfoType]
                                        ?.fixedType === undefined) && (
                                    <div className={styles.paramsNum}>
                                        满足以下条件的第&nbsp;
                                        <InputNumber
                                            size="small"
                                            min={0}
                                            disabled={['readonly', 'debug', 'execute'].includes(
                                                editType
                                            )}
                                            value={
                                                currentStep?.stepInfo.params.findInfo[findInfoType]
                                                    .findNode[0].detailFeature.ext.index
                                            }
                                            onChange={async (value) => {
                                                currentStep.stepInfo.params.findInfo[
                                                    findInfoType
                                                ].findNode[0].detailFeature.ext.index = value;
                                                await handleUpdateStep({ ...currentStep });
                                            }}
                                        />
                                        &nbsp;个控件
                                    </div>
                                )}
                            {currentStep?.stepInfo.params.findInfo[findInfoType]?.byDistance ===
                                true &&
                                currentStep?.stepInfo.params.findInfo[findInfoType].findNode[0]
                                    .detailFeature.ext?.index !== undefined &&
                                (currentStep?.stepInfo?.params?.findInfo[findInfoType]
                                    ?.fixedType === 1 ||
                                    currentStep?.stepInfo?.params?.findInfo[findInfoType]
                                        ?.fixedType === undefined) && (
                                    <div className={styles.paramsNum}>
                                        满足以下条件且与目标位置最接近的控件
                                    </div>
                                )}
                            {type.includes('Text') && (
                                <>
                                    <ParamsTitleInfo text="文本" sizeType={sizeType} />
                                    <RenderTextElement
                                        {...props}
                                        handleUpdateStep={handleUpdateStep}
                                        index={0}
                                        addStyles={addStyles}
                                        disabled={
                                            -1 !==
                                            ['readonly', 'debug', 'execute'].indexOf(props.editType)
                                        }
                                        type={sizeType}
                                    />
                                </>
                            )}
                            {type === 'Icon' && value !== 'others' && (
                                <div>
                                    <ParamsTitleInfo text="图标" sizeType={sizeType} />
                                    <RenderIconElement
                                        {...props}
                                        handleUpdateStep={handleUpdateStep}
                                        index={0}
                                        addStyles={addStyles}
                                        disabled={
                                            -1 !==
                                            ['readonly', 'debug', 'execute'].indexOf(props.editType)
                                        }
                                        type={sizeType}
                                    />
                                </div>
                            )}
                            {('Component' === type || ('Icon' === type && 'others' === value)) && (
                                <div>
                                    <ParamsTitleInfo text="图标" sizeType={sizeType} />
                                    <RenderComponentElement
                                        {...props}
                                        index={0}
                                        addStyles={addStyles}
                                        disabled={
                                            -1 !==
                                            ['readonly', 'debug', 'execute'].indexOf(props.editType)
                                        }
                                        type={sizeType}
                                    />
                                </div>
                            )}
                            {type === 'Sys' && (
                                <div>
                                    <ParamsTitleInfo text="系统" sizeType={sizeType} />
                                    <RenderSysElement
                                        {...props}
                                        index={0}
                                        addStyles={addStyles}
                                        disabled={
                                            -1 !==
                                            ['readonly', 'debug', 'execute'].indexOf(props.editType)
                                        }
                                        type={sizeType}
                                    />
                                </div>
                            )}
                            {type === 'CVE' && (
                                <div>
                                    <ParamsTitleInfo text="元素" sizeType={sizeType} />
                                    <RenderCVElement
                                        {...props}
                                        handleUpdateStep={handleUpdateStep}
                                        index={0}
                                        addStyles={addStyles}
                                        disabled={
                                            -1 !==
                                            ['readonly', 'debug', 'execute'].indexOf(props.editType)
                                        }
                                        type={sizeType}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                    <div
                        className={classnames(
                            { [styles.paramsItem]: sizeType === 'normal' },
                            { [styles.paramsItemSmall]: sizeType === 'small' }
                        )}
                        style={{
                            marginTop: sizeType === 'normal' ? 15 : 10,
                            fontSize: sizeType === 'normal' ? 14 : 12
                        }}
                    >
                        <Title text="操作行为" />
                        <div
                            className={classnames({
                                [styles.rowMarginLeft]: sizeType === 'small'
                            })}
                        >
                            <ParamsInfo text="操作动作" sizeType={sizeType} width={85} />
                            <SelectOperator
                                {...props}
                                styles={{
                                    width: 'calc(100% - 100px)',
                                    marginBottom: 5
                                }}
                                sizeType={sizeType}
                                includeOptions={
                                    operationType === 'ifThen' ? ['nope', 'absence'] : []
                                }
                            />
                            <MulitParams type={sizeType} step={currentStep} {...props} />
                        </div>
                    </div>
                    <div
                        className={classnames(
                            { [styles.paramsItem]: sizeType === 'normal' },
                            { [styles.paramsItemSmall]: sizeType === 'small' }
                        )}
                        style={{ marginTop: sizeType === 'normal' ? 15 : 10 }}
                    >
                        <Title text="操作位置" />
                        <div
                            className={classnames({ [styles.rowMarginLeft]: sizeType === 'small' })}
                        >
                            <ParamsInfo text="区域中部" sizeType={sizeType} />
                        </div>
                    </div>
                </>
            )}
            {sizeType === 'normal' && (
                <div
                    className={classnames(
                        { [styles.paramsItem]: sizeType === 'normal' },
                        { [styles.paramsItemSmall]: sizeType === 'small' }
                    )}
                    style={{ marginTop: sizeType === 'normal' ? 15 : 10 }}
                >
                    <Title text="配置项" />
                    <div className={classnames({ [styles.rowMarginLeft]: sizeType === 'small' })}>
                        {sizeType === 'normal' ? (
                            <>
                                <span style={{ marginRight: 10 }}>使用 FastSAM</span>
                                &nbsp;
                                <Switch
                                    checkedChildren="开启"
                                    unCheckedChildren="关闭"
                                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                    checked={
                                        currentStep.stepInfo.params.findInfo[findInfoType]
                                            ?.useFastSAM ?? false
                                    }
                                    onChange={async (checked) => {
                                        if (!currentStep.stepInfo.params.findInfo[findInfoType]) {
                                            currentStep.stepInfo.params.findInfo[findInfoType] = {};
                                        }
                                        currentStep.stepInfo.params.findInfo[
                                            findInfoType
                                        ].useFastSAM = checked;
                                        await handleUpdateStep({ ...currentStep });
                                    }}
                                />
                            </>
                        ) : (
                            <span>
                                {currentStep.stepInfo.params.findInfo[findInfoType]?.useFastSAM
                                    ? '已'
                                    : '未'}
                                使用 FastSAM
                            </span>
                        )}
                    </div>
                    {currentStep?.stepInfo?.type === 8 && (
                        <div
                            className={classnames({ [styles.rowMarginLeft]: sizeType === 'small' })}
                            style={{ marginTop: 5 }}
                        >
                            {sizeType === 'normal' ? (
                                <>
                                    <ParamsInfo text="栅格定位" sizeType={sizeType} width={85} />
                                    &nbsp;
                                    <Switch
                                        checkedChildren="开启"
                                        unCheckedChildren="关闭"
                                        disabled={['readonly', 'debug', 'execute'].includes(
                                            editType
                                        )}
                                        checked={
                                            currentStep.stepInfo.params.findInfo[findInfoType]
                                                ?.fixedType === 0
                                        }
                                        onChange={async (checked) => {
                                            if (
                                                !currentStep.stepInfo.params.findInfo[findInfoType]
                                            ) {
                                                currentStep.stepInfo.params.findInfo[findInfoType] =
                                                    {};
                                            }
                                            currentStep.stepInfo.params.findInfo[
                                                findInfoType
                                            ].fixedType = checked ? 0 : 1;
                                            await handleUpdateStep({ ...currentStep });
                                        }}
                                    />
                                </>
                            ) : (
                                <ParamsInfo
                                    text={
                                        (currentStep.stepInfo.params.findInfo[findInfoType]
                                            ?.fixedType === 0
                                            ? '已'
                                            : '未') + '开启栅格定位'
                                    }
                                    sizeType={sizeType}
                                    width={85}
                                />
                            )}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}

function ParamsInfo({ text, sizeType, width = '100%' }) {
    return (
        <span
            className={styles.paramsInfo}
            style={{
                fontSize: sizeType === 'normal' ? 14 : 12,
                width: width
            }}
        >
            {text}
        </span>
    );
}

function ParamsTitleInfo({ text, sizeType }) {
    return (
        <span
            className={styles.paramsName}
            style={{
                fontSize: sizeType === 'normal' ? 14 : 12
            }}
        >
            {text}
        </span>
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal
}))(SingleElementType);
