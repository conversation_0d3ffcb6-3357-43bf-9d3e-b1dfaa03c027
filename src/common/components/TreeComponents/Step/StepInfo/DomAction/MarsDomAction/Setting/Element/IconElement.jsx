import {Input, Row} from 'antd';
import styles from '../Setting.module.less';

function RenderIconElement({currentStep, findInfoType, addStyles, index, type = 'normal'}) {
    return (
        <Row className={styles.paramsTextElement} style={{...addStyles}}>
            <Input
                size='small'
                disabled
                className={styles.paramsSelect}
                style={{width: '75px', marginRight: '5px', textAlign: 'center'}}
                value="图标类型"
            />
            <Input
                size='small'
                disabled
                className={styles.nodeParamSelectInput}
                style={{
                    width: 'calc(100% - 80px)'
                }}
                value={currentStep.stepInfo.params.findInfo[findInfoType]?.findNode[index]
                    .detailFeature.ext.cname || currentStep.stepInfo.params.findInfo[findInfoType]?.findNode[index]
                        .detailFeature.ext.name || '--'}
            />
        </Row>
    );
}

export default RenderIconElement;