import {Input, Row} from 'antd';
import classnames from 'classnames';
import styles from '../Setting.module.less';

function RenderComponentElement({currentStep, addStyles, index, type = 'normal'}) {
    return (
        <Row className={styles.paramsTextElement} style={{...addStyles}}>
            <Input
                size='small'
                disabled
                className={styles.paramsSelect}
                style={{width: '75px', marginRight: '5px', textAlign: 'center'}}
                value="组件类型"
            />
            <Input
                size='small'
                disabled
                className={styles.nodeParamSelectInput}
                style={{
                    width: 'calc(100% - 80px)'
                }}
                value="样式完全一致"
            />
        </Row>
    );
}

export default RenderComponentElement;