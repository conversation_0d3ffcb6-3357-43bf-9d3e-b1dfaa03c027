import {message} from 'antd';
import {
    searchElementByBoundsVenus,
    getSingleElementInfo
} from './utils';

const deepcopy = obj => {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    let newObj = {};
    if (Array.isArray(obj)) {
        newObj = obj.map(item => deepcopy(item));
    } else {
        Object.keys(obj).forEach((key) => {
            return newObj[key] = deepcopy(obj[key]);
        });
    }
    return newObj;
};

const getPath = (dom, nodeIdList, nowIndexList = [], pathList = []) => {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (-1 !== nodeIdList.indexOf(element.id)) {
            pathList.push(nowIndexList.join('-'));
        }
        if (-1 !== Object.keys(element).indexOf('children') && 0 !== element.children.length) {
            getPath(element.children, nodeIdList, nowIndexList, pathList);
        }
        if ('object' === typeof element.children && 0 !== element.children.length) {
            getPath(element.children, nodeIdList, nowIndexList, pathList);
        }
        nowIndexList.pop();
    });
    return pathList;
};


export async function editNativev3({setCurrentStep, step, curOsType, domDetail, method = 'click',
    xPrecent, yPrecent, handleUpdateStep, drawerOpen = false, findInfoType = 'widgetInfo'}) {
    try {
        // 找控件
        let {width, height} = step.stepInfo.params.recordInfo.deviceInfo.screenSize;
        if (
            curOsType === 2 &&
            -1 !== Object.keys(step.stepInfo.params.recordInfo.deviceInfo.screenSize).indexOf('rotation')
        ) {
            if (
                -1 !== [90, 270].indexOf(step.stepInfo.params.recordInfo.deviceInfo.screenSize.rotation)
            ) {
                let _width = width;
                width = height;
                height = _width;
            }
        }
        // 计算点击位置在设备上的位置
        let x = xPrecent * width;
        let y = yPrecent * height;

        let parentRect = {};
        // 获取最大外框 baseInfo
        if (step.stepInfo.params.findInfo?.baseInfo) {
            let _findNode = step.stepInfo.params.findInfo?.baseInfo?.findNode ?? [];
            if (_findNode.length >= 1) {
                let _x = 0;
                let _y = 0;
                let _tx = 0;
                let _ty = 0;
                parentRect = deepcopy(_findNode?.[0]?.detailFeature?.rect);
                _x = parentRect?.x; // x
                _y = parentRect?.y; // y
                _tx = parentRect?.x + parentRect?.w; // targerX
                _ty = parentRect?.y + parentRect?.h; // targerY
                for (let item of _findNode) {
                    const _rect = item?.detailFeature?.rect;
                    if (_rect?.x < _x) {
                        _x = _rect?.x;
                    }
                    if (_rect?.y < _y) {
                        _y = _rect?.y;
                    }
                    if (_tx - _x < (_rect.x + _rect.w) - _x) {
                        _tx = _rect.x + _rect.w;
                    }
                    if (_ty - _y < (_rect.y + _rect.h) - _y) {
                        _ty = _rect.y + _rect.h;
                    }
                }
                parentRect = {
                    x: _x,
                    y: _y,
                    w: _tx - _x,
                    h: _ty - _y
                };
                _x = undefined;
                _y = undefined;
                _tx = undefined;
                _ty = undefined;
            } else {
                parentRect = {};
            }
        }

        // widgetInfo 控件信息，判断点击位置是否在控件外框内，不在就不修改控件信息
        if ((parentRect.x > x || parentRect.x + parentRect.w < x ||
            parentRect.y > y || parentRect.y + parentRect.h < y) && findInfoType === 'widgetInfo') {
            return;
        }
        // 递归检索选择控件的路径
        // 绘制虚线
        let newFindInfo = {...step.stepInfo.params.findInfo};
        let eleTag = [];
        let typeTag = [];
        for (let tag of step.stepInfo.params.findInfo?.[findInfoType]?.chosenTag ?? []) {
            if (['single', 'multiple'].includes(tag)) {
                eleTag.push(tag);
            }
            if (['visual', 'system'].includes(tag)) {
                typeTag.push(tag);
            }
        }
        newFindInfo.eleTag = eleTag;
        newFindInfo.typeTag = typeTag;
        let {pathIndexList, targetBounds} = searchElementByBoundsVenus([domDetail], {
            x,
            y,
        }, newFindInfo);

        // if (0 === pathIndexList.length) {
        //     message.error('该点击位置没有控件');
        //     return false;
        // }
        let {targetElement} = getSingleElementInfo(pathIndexList, domDetail, true);

        // 最外层不点击
        if (targetElement.type === 'Page' && findInfoType !== 'baseInfo') {
            return;
        }
        let newFindNode = deepcopy(step.stepInfo.params.findInfo[findInfoType].findNode);
        if (step.stepInfo.params.findInfo[findInfoType].chosenTag.includes('single')) {
            if (newFindNode?.[0]?.id === targetElement.debug.id) {
                step.stepInfo.params.findInfo[findInfoType].findNode = [];
            } else {
                let params = {
                    id: targetElement.debug.id,
                    actionNode: true,
                    parents: targetElement.debug.parents,
                    featureFlag: true,
                    detailFeature: {
                        type: targetElement.type,
                        rect: targetElement.rect,
                        ext: {...targetElement.ext},
                        matchType: 1
                    },
                };
                if (['TextArea', 'Text'].includes(targetElement.type)) {
                    params.detailFeature.ext.index = 0;
                }
                let newFindNode = [params];
                step.stepInfo.params.findInfo[findInfoType].findNode = newFindNode;
            }
        } else if (step.stepInfo.params.findInfo[findInfoType].chosenTag.includes('multiple')) {
            if (!targetElement?.debug?.id) {
                return;
            }
            // 单选
            // 判断选择控件 是否已存在
            let existIndex = -1;
            for (let index = 0; index < newFindNode.length; index++) {
                if (newFindNode[index].id === targetElement.debug.id) {
                    existIndex = index;
                    break;
                }
            }
            if ('click' === method) {
                if (-1 === existIndex) {
                    // 检查是否超过5个控件
                    if (newFindNode.length >= 5) {
                        message.warning('多控件建模最多只能设置 5 个控件');
                        return; // 直接返回，不添加新控件
                    }
                    newFindNode.push({
                        id: targetElement.debug.id,
                        actionNode: false,
                        parents: targetElement.debug.parents,
                        featureFlag: true,
                        detailFeature: {
                            type: targetElement.type,
                            rect: targetElement.rect,
                            ext: {...targetElement.ext},
                            matchType: 1
                        },
                    });
                } else {
                    newFindNode.splice(existIndex, 1);
                }
            }
            step.stepInfo.params.findInfo[findInfoType].findNode = [...newFindNode];
        }
        if (findInfoType === 'baseInfo') {
            step.stepInfo.params.findInfo.widgetInfo.findNode = [];
        }
        setCurrentStep({...step});
        if (!drawerOpen) {
            // 如果无屏数，则更新步骤
            if (!step.stepInfo?.params?.findParams?.scroll?.screenCount) {
                step.stepInfo.params.findParams.scroll = {
                    screenCount: 1
                };
            }
            handleUpdateStep({...step});
        }
    } catch (err) {
        message.warning(err.message ? err.message : err);
    }
};
