import {Row, Select} from 'antd';
import {isEmpty} from 'lodash';
import classnames from 'classnames';
import styles from '../Setting.module.less';

function RenderSysElement({
    currentStep, handleUpdateStep, index, findInfoType, addStyles,
    disabled = false, type = 'normal'
}) {
    let options = [];
    if (currentStep?.stepInfo.params.findInfo[findInfoType].findNode[index].detailFeature.ext?.feature) {
        for (let item in currentStep?.stepInfo.params
            .findInfo[findInfoType].findNode[index].detailFeature.ext?.feature) {
            let _params = {
                value: item,
                label: currentStep?.stepInfo.params.findInfo[findInfoType].findNode[index]
                    .detailFeature.ext?.feature[item]?.data,
                chosen: currentStep?.stepInfo.params.findInfo[findInfoType].findNode[index]
                    .detailFeature.ext?.feature[item]?.chosen,
            };
            if (item === 'type') {
                options.unshift(_params);
            } else {
                options.push(_params);
            }
        }
    }
    if (isEmpty(options)) {
        return '暂无特征';
    }
    return (
        <Row
            className={classnames(styles.paramsSysElement, {
                [styles.paramsSmallSysElement]: type === 'small'
            })}
            style={{...addStyles}}
        >
            <Select
                size='small'
                placeholder="可设置特征"
                className={classnames(styles.paramsSelect,
                    {[styles.paramsSmallSelect]: type === 'small'})}
                mode="multiple"
                maxTagCount={3}
                maxTagTextLength={20}
                disabled={disabled}
                options={
                    options.map((item, index) => ({
                        ...item,
                        label: item.label === null ? '--' : `${item.value}=${item.label}`,
                    }))
                }
                value={options.filter(item => item.chosen).map(item => item.value)}
                onChange={async (value, option) => {
                    for (let item in currentStep?.stepInfo
                        .params.findInfo[findInfoType].findNode[index].detailFeature.ext?.feature) {
                        currentStep.stepInfo.params.findInfo[findInfoType].findNode[index]
                            .detailFeature.ext.feature[item].chosen = value.includes(item);
                    }
                    await handleUpdateStep({...currentStep});
                }}
            />
        </Row>
    );
}

export default RenderSysElement;