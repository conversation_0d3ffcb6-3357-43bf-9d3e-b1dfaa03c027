import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {InputNumber, Select} from 'antd';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from '../Setting.module.less';

export default ({editType, currentStep, handleUpdateStep}) => {
    const [screenCount, setScreenCount] = useState(1);
    const [scrollDirection, setScrollDirection] = useState(1);
    const [scrollRatio, setScrollRatio] = useState(50);
    const [scrollType, setScrollType] = useState(1);

    useEffect(() => {
        setScrollDirection(currentStep.stepInfo.params.findParams?.scroll?.scrollDirection ?? 1);
        setScrollRatio(currentStep.stepInfo.params.findParams?.scroll?.scrollRatio ?? 50);
        setScreenCount(currentStep.stepInfo.params.findParams?.scroll?.screenCount ?? 1);
        setScrollType(currentStep.stepInfo.params.findParams?.scroll?.scrollType ?? 1);
    }, [currentStep?.stepId]);

    return (
        <>
            <div className={styles.paramsItem}>
                <div
                    className={styles.paramsLeft}
                    style={{fontWeight: 'normal', width: 100}}
                >
                    查找屏数
                </div>
                <InputNumber
                    className={classnames(styles.paramsCenter, 'input_editor')}
                    max={50}
                    min={1}
                    size='small'
                    value={screenCount}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    onChange={(value) => {
                        currentStep.stepInfo.params.findParams.scroll.screenCount = value;
                        if (value <= 1) {
                            delete currentStep.stepInfo.params.findParams.scroll.scrollRatio;
                            delete currentStep.stepInfo.params.findParams.scroll.scrollType;
                        } else {
                            currentStep.stepInfo.params.findParams.scroll.scrollRatio = 50;
                            currentStep.stepInfo.params.findParams.scroll.scrollType = 1;
                        }
                        setScreenCount(value);
                        handleUpdateStep(currentStep);
                    }}
                    onBlur={(e) => {
                        let value = 1;
                        if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                            value = parseInt(e.target.value.trim(), 10);
                        }
                        if (currentStep.stepInfo.params.findParams.scroll.screenCount !== value) {
                            currentStep.stepInfo.params.findParams.scroll.screenCount = value;
                            if (value <= 1) {
                                delete currentStep.stepInfo.params.findParams.scroll.scrollRatio;
                                delete currentStep.stepInfo.params.findParams.scroll.scrollType;
                            } else {
                                currentStep.stepInfo.params.findParams.scroll.scrollRatio = 50;
                                currentStep.stepInfo.params.findParams.scroll.scrollType = 1;
                            }
                            handleUpdateStep(currentStep);
                        }
                    }}
                />
                <div className={styles.paramsRight}>屏</div>
            </div>
            {
                screenCount > 1 ?
                    <div className={styles.paramsItem}>
                        <div
                            className={styles.paramsLeft}
                            style={{fontWeight: 'normal', width: 100}}
                        >
                            滑动比例
                        </div>
                        <InputNumber
                            className={styles.paramsCenter}
                            max={90}
                            min={10}
                            size='small'
                            value={scrollRatio}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onChange={(value) => {
                                setScrollRatio(value);
                                currentStep.stepInfo.params.findParams.scroll.scrollRatio = value;
                                handleUpdateStep(currentStep);
                            }}
                        />
                        <div className={styles.paramsRight}>%</div>
                    </div> : null
            }
            {
                screenCount > 1 ?
                    <div className={styles.paramsItem}>
                        <div
                            className={styles.paramsLeft}
                            style={{fontWeight: 'normal', width: 100}}
                        >
                            划动方向
                        </div>
                        <Select
                            className={styles.paramsCenter}
                            size='small'
                            value={scrollDirection ?? 1}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onChange={(value) => {
                                setScrollDirection(value);
                                currentStep.stepInfo.params.findParams.scroll.scrollDirection = Number(value);
                                handleUpdateStep(currentStep);
                            }}
                        >
                            <Select.Option key={'sys_swipe_action_1'} value={1}>
                                从下向上划
                            </Select.Option>
                            <Select.Option key={'sys_swipe_action_2'} value={2}>
                                从上向下划
                            </Select.Option>
                            <Select.Option key={'sys_swipe_action_3'} value={3}>
                                从左向右划
                            </Select.Option>
                            <Select.Option key={'sys_swipe_action_4'} value={4}>
                                从右向左划
                            </Select.Option>
                        </Select>
                    </div> : null
            }
            {
                screenCount > 1 ?
                    <div className={styles.paramsItem}>
                        <div
                            className={styles.paramsLeft}
                            style={{fontWeight: 'normal', width: 100}}
                        >
                            划动速度
                        </div>
                        <Select
                            className={styles.paramsCenter}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            value={scrollType}
                            size='small'
                            onChange={(value) => {
                                setScrollType(value);
                                currentStep.stepInfo.params.findParams.scroll.scrollType = Number(value);
                                handleUpdateStep(currentStep);
                            }}
                        >
                            <Select.Option key={'sys_swipe_type_1'} value={1}>
                                快划
                            </Select.Option>
                            <Select.Option key={'sys_swipe_type_2'} value={2}>
                                慢划
                            </Select.Option>
                        </Select>
                    </div> : null
            }
        </>
    );
};
