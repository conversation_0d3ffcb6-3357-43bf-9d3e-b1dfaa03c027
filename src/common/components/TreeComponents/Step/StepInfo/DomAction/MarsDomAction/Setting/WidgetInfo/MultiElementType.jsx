import { <PERSON>ton, Col, Row, InputNumber, Radio, Select, Switch, Tag, Tooltip } from 'antd';
import { useCallback } from 'react';
import classnames from 'classnames';
import { useState } from 'react';
import { DeleteOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import { findNewPath } from '../../utils';

import ParamsTitle from 'COMMON/components/ParamsTitle';
import ParamsSmallTitle from 'COMMON/components/ParamsSmallTitle';
import { SelectOperator } from 'COMMON/components/TreeComponents/Step/StepInfo/components';
import MulitParams from 'COMMON/components/TreeComponents/Step/StepItem/Params/NewMulitParams';

import NodeFeatures from '../NodeFeatures';
import RenderSysElement from '../Element/SysElement';
import RenderTextElement from '../Element/TextElement';
import RenderIconElement from '../Element/IconElement';
import RenderCVElement from '../Element/CVElement';

import DomTree from '../BatDomTree';
import RenderComponentElement from '../Element/ComponentElement';

import styles from '../Setting.module.less';

const addStyles = { width: 'calc(100% - 80px)' };

function MultiElementType(props) {
    const {
        findInfoType,
        currentStep,
        handleUpdateStep,
        domDetail,
        operationType,
        editType,
        handleUpdateSelectPath = () => {},
        sizeType = 'normal'
    } = props;
    const [openDomTree, setOpenDomTree] = useState(false);
    const [activedNode, setActivedNode] = useState(null);

    let text = '未选择控件';
    let flag = false;
    let findNode = currentStep.stepInfo.params.findInfo[findInfoType]?.findNode ?? [];
    let actionNode = null;
    for (let index in findNode) {
        if (findNode[index].actionNode) {
            flag = true;
            actionNode = {
                index,
                node: findNode[index]
            };
        }
    }
    if (!flag && 0 !== findNode.length) {
        actionNode = {
            index: -1,
            node: {}
        };
    }
    if (null !== actionNode && -1 === actionNode.index) {
        text = '区域中部';
    }
    if (null !== actionNode && -1 !== actionNode.index) {
        text = '控件' + String(actionNode.index);
    }
    const Title = sizeType === 'normal' ? ParamsTitle : ParamsSmallTitle;

    const RenderNodeFeatures = useCallback(() => {
        return (
            <NodeFeatures
                {...props}
                findNode={findNode}
                activedNode={activedNode}
                setActivedNode={setActivedNode}
                ParamsTitleInfo={ParamsTitleInfo}
            />
        );
    }, [currentStep, activedNode]);

    return (
        <div
            className={classnames(styles.multiParams, {
                [styles.modelMarginLeft]: sizeType === 'small'
            })}
        >
            {findNode.length === 0 ? (
                <div
                    className={classnames(
                        { [styles.paramsItem]: sizeType === 'normal' },
                        { [styles.paramsItemSmall]: sizeType === 'small' }
                    )}
                    style={{ marginTop: sizeType === 'normal' ? 15 : 10 }}
                >
                    <Title text="控件类型" />
                    <span
                        style={{
                            display: 'inline-block',
                            width: 80,
                            fontSize: sizeType === 'normal' ? 14 : 12
                        }}
                    >
                        未选择控件
                    </span>
                </div>
            ) : (
                <>
                    {(sizeType === 'small' || sizeType === 'normal') && (
                        <div
                            className={classnames(
                                { [styles.paramsItem]: sizeType === 'normal' },
                                { [styles.paramsItemSmall]: sizeType === 'small' }
                            )}
                            style={{
                                marginTop: sizeType === 'normal' ? 15 : 10,
                                fontSize: sizeType === 'normal' ? 14 : 12
                            }}
                        >
                            <Title text="叠加特征识别" />
                            <div
                                className={classnames(styles.featurePos, {
                                    [styles.rowMarginLeft]: sizeType === 'small'
                                })}
                            >
                                {RenderNodeFeatures()}
                            </div>
                        </div>
                    )}
                    <div
                        className={classnames(
                            { [styles.paramsItem]: sizeType === 'normal' },
                            { [styles.paramsItemSmall]: sizeType === 'small' }
                        )}
                        style={{
                            marginTop: sizeType === 'normal' ? 15 : 10,
                            fontSize: sizeType === 'normal' ? 14 : 12
                        }}
                    >
                        <Title text="操作行为" />
                        <div
                            className={classnames({ [styles.rowMarginLeft]: sizeType === 'small' })}
                        >
                            <span
                                style={{
                                    display: 'inline-block',
                                    width: 85,
                                    fontSize: sizeType === 'normal' ? 14 : 12
                                }}
                            >
                                操作动作
                            </span>
                            <SelectOperator
                                {...props}
                                styles={{
                                    width: 'calc(100% - 100px)',
                                    marginBottom: 5
                                }}
                                includeOptions={
                                    operationType === 'ifThen' ? ['nope', 'absence'] : []
                                }
                            />
                            <MulitParams type={sizeType} step={currentStep} {...props} />
                        </div>
                    </div>
                    <div
                        className={classnames(
                            { [styles.paramsItem]: sizeType === 'normal' },
                            { [styles.paramsItemSmall]: sizeType === 'small' }
                        )}
                        style={{
                            marginTop: sizeType === 'normal' ? 15 : 10,
                            fontSize: sizeType === 'normal' ? 14 : 12
                        }}
                    >
                        <Title text="操作位置" />
                        <Row
                            className={classnames({ [styles.rowMarginLeft]: sizeType === 'small' })}
                        >
                            {currentStep.stepInfo.params.findInfo[findInfoType].findNode.filter(
                                (item) => item.actionNode === true
                            ).length === 0 ? (
                                '区域中部'
                            ) : (
                                <div className={styles.nodePos}>
                                    <p style={{ marginBottom: 5 }}>操作区域为图中的黄虚框控件</p>
                                    {currentStep.stepInfo.params.findInfo[
                                        findInfoType
                                    ].findNode.map((item, index) => {
                                        let jsx = [];
                                        if (item?.actionNode) {
                                            if (item?.detailFeature?.type.includes('Text')) {
                                                jsx.push(
                                                    <Col span={24} key={'text_' + String(index)}>
                                                        <ParamsTitleInfo
                                                            {...props}
                                                            index={index}
                                                            text="文本"
                                                            item={item}
                                                            activedNode={activedNode}
                                                            setActivedNode={setActivedNode}
                                                        />
                                                        <RenderTextElement
                                                            {...props}
                                                            index={index}
                                                            type={sizeType}
                                                            disabled="true"
                                                            addStyles={addStyles}
                                                            className={styles.nodeParamSelectInput}
                                                        />
                                                    </Col>
                                                );
                                            }
                                            if (['Component'].includes(item?.detailFeature?.type)) {
                                                jsx.push(
                                                    <Col span={24} key={'icon_' + String(index)}>
                                                        <ParamsTitleInfo
                                                            {...props}
                                                            index={index}
                                                            text="图标"
                                                            item={item}
                                                            activedNode={activedNode}
                                                            setActivedNode={setActivedNode}
                                                        />
                                                        <RenderComponentElement
                                                            {...props}
                                                            index={index}
                                                            type={sizeType}
                                                            disabled="true"
                                                            addStyles={addStyles}
                                                        />
                                                    </Col>
                                                );
                                            }
                                            if (['Icon'].includes(item?.detailFeature?.type)) {
                                                jsx.push(
                                                    <Col span={24} key={'icon_' + String(index)}>
                                                        <ParamsTitleInfo
                                                            {...props}
                                                            index={index}
                                                            text="图标"
                                                            item={item}
                                                            activedNode={activedNode}
                                                            setActivedNode={setActivedNode}
                                                        />
                                                        <RenderIconElement
                                                            {...props}
                                                            findInfoType={findInfoType}
                                                            index={index}
                                                            type={sizeType}
                                                            disabled="true"
                                                            addStyles={addStyles}
                                                        />
                                                    </Col>
                                                );
                                            }
                                            if (
                                                ['DFE', 'DOE'].includes(item?.detailFeature?.type)
                                            ) {
                                                jsx.push(
                                                    <Col
                                                        span={24}
                                                        key={'component_' + String(index)}
                                                    >
                                                        <ParamsTitleInfo
                                                            {...props}
                                                            index={index}
                                                            text="系统"
                                                            item={item}
                                                            activedNode={activedNode}
                                                            setActivedNode={setActivedNode}
                                                        />
                                                        <RenderSysElement
                                                            {...props}
                                                            index={index}
                                                            type={sizeType}
                                                            addStyles={addStyles}
                                                            disabled="true"
                                                        />
                                                    </Col>
                                                );
                                            }
                                            if (['CVE'].includes(item?.detailFeature?.type)) {
                                                jsx.push(
                                                    <Col span={24} key={'cv_' + String(index)}>
                                                        <ParamsTitleInfo
                                                            {...props}
                                                            index={index}
                                                            text="元素"
                                                            item={item}
                                                            activedNode={activedNode}
                                                            setActivedNode={setActivedNode}
                                                        />
                                                        <RenderCVElement
                                                            {...props}
                                                            index={index}
                                                            type={sizeType}
                                                            addStyles={addStyles}
                                                            disabled="true"
                                                        />
                                                    </Col>
                                                );
                                            }
                                            if (
                                                !['readonly', 'debug', 'execute'].includes(editType)
                                            ) {
                                                jsx.push(
                                                    <Tooltip
                                                        title="清空该操作位置"
                                                        placement="right"
                                                    >
                                                        <DeleteOutlined
                                                            className={styles.nodePosDelete}
                                                            style={{
                                                                left:
                                                                    sizeType === 'small'
                                                                        ? 157
                                                                        : 185,
                                                                top: sizeType === 'small' ? 2 : 4
                                                            }}
                                                            onClick={async () => {
                                                                currentStep.stepInfo.params.findInfo[
                                                                    findInfoType
                                                                ].findNode[
                                                                    index
                                                                ].actionNode = false;
                                                                currentStep.stepInfo.params.pathSummary =
                                                                    [];
                                                                await handleUpdateStep({
                                                                    ...currentStep
                                                                });
                                                                handleUpdateSelectPath([]);
                                                            }}
                                                        />
                                                    </Tooltip>
                                                );
                                            }
                                            return jsx;
                                        }
                                    })}
                                </div>
                            )}
                        </Row>
                    </div>
                </>
            )}
            {sizeType === 'normal' && (
                <div
                    className={classnames(
                        { [styles.paramsItem]: sizeType === 'normal' },
                        { [styles.paramsItemSmall]: sizeType === 'small' }
                    )}
                    style={{ marginTop: sizeType === 'normal' ? 15 : 10 }}
                >
                    <Title text="配置项" />
                    <div className={classnames({ [styles.rowMarginLeft]: sizeType === 'small' })}>
                        <span
                            style={{
                                fontSize: sizeType === 'normal' ? 14 : 12
                            }}
                        >
                            {sizeType === 'normal' ? (
                                <>
                                    <span style={{ marginRight: 10 }}>使用 FastSAM</span>
                                    <Switch
                                        checkedChildren="开启"
                                        unCheckedChildren="关闭"
                                        disabled={['readonly', 'debug', 'execute'].includes(
                                            editType
                                        )}
                                        checked={
                                            currentStep.stepInfo.params.findInfo[findInfoType]
                                                ?.useFastSAM ?? false
                                        }
                                        onChange={async (checked) => {
                                            if (
                                                !currentStep.stepInfo.params.findInfo[findInfoType]
                                            ) {
                                                currentStep.stepInfo.params.findInfo[findInfoType] =
                                                    {};
                                            }
                                            currentStep.stepInfo.params.findInfo[
                                                findInfoType
                                            ].useFastSAM = checked;
                                            await handleUpdateStep({ ...currentStep });
                                        }}
                                    />
                                </>
                            ) : (
                                <span>
                                    {currentStep.stepInfo.params.findInfo[findInfoType]?.useFastSAM
                                        ? '已'
                                        : '未'}
                                    使用 FastSAM
                                </span>
                            )}
                        </span>
                    </div>
                </div>
            )}
            {sizeType === 'normal' && (
                <div className={styles.paramsItem}>
                    <Title text="实验室功能（为了世界和平，请谨慎使用）" />
                    <div>
                        开启 DOM 视图，一键独断万古&nbsp;
                        <Switch
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                            checked={openDomTree}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onChange={setOpenDomTree}
                        />
                        {openDomTree && (
                            <div style={{ height: 250, overflow: 'scroll' }}>
                                <DomTree
                                    {...props}
                                    activedNode={activedNode}
                                    setActivedNode={setActivedNode}
                                />
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
}

function ParamsTitleInfo({
    text,
    sizeType,
    item,
    domDetail,
    activedNode,
    setActivedNode,
    handleUpdateSelectPath,
    currentStep
}) {
    return (
        <span
            className={classnames(
                styles.nodeParamIndex,
                styles.textSizeNormal,
                { [styles.textSizeSmall]: sizeType === 'small' },
                { [styles.activeParam]: +activedNode === +item.id }
            )}
            onClick={() => {
                if (sizeType === 'small') {
                    return;
                }
                if (item.id === activedNode) {
                    setActivedNode(null);
                    handleUpdateSelectPath([]);
                    return;
                }
                if ([7, 8, 9, 10].includes(currentStep.stepInfo.type)) {
                    let { path } = findNewPath([domDetail], item.id);
                    handleUpdateSelectPath(path);
                    setActivedNode(item.id);
                }
            }}
            style={{
                float: 'left'
            }}
        >
            {text}
        </span>
    );
}

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal
}))(MultiElementType);
