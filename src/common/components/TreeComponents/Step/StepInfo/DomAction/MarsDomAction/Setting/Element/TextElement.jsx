import { Row, Input, Select, Tooltip, message } from 'antd';
import { useState, useEffect } from 'react';
import classnames from 'classnames';
import { InfoCircleOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import { TEXT_OPTIONS } from '../../config';
import styles from '../Setting.module.less';

function RenderTextElement({
    currentStep,
    handleUpdateStep,
    index,
    findInfoType,
    disabled = false,
    addStyles,
    ocrCharset,
    type = 'normal'
}) {
    const [findText, setFindText] = useState('');
    const [matchType, setMatchType] = useState(0);

    useEffect(() => {
        setMatchType(
            currentStep?.stepInfo.params.findInfo[findInfoType]?.findNode[index].detailFeature
                .matchType
        );
        setFindText(
            currentStep.stepInfo.params.findInfo[findInfoType]?.findNode[index].detailFeature.ext
                .text
        );
    }, [currentStep]);

    return (
        <Row className={styles.paramsTextElement} style={{ ...addStyles }}>
            <Select
                size="small"
                disabled={disabled}
                className={classnames(styles.paramsSelect, {
                    [styles.paramsSmallSelect]: type === 'small'
                })}
                options={TEXT_OPTIONS}
                style={{ width: '75px' }}
                value={matchType}
                onChange={async (value) => {
                    currentStep.stepInfo.params.findInfo[findInfoType].findNode[
                        index
                    ].detailFeature.matchType = value;
                    if (value === 2) {
                        currentStep.stepInfo.params.findInfo[findInfoType].findNode[
                            index
                        ].detailFeature.ext.text = '';
                        setFindText('');
                    }
                    if (matchType === 2 && value !== 2) {
                        currentStep.stepInfo.params.findInfo[findInfoType].findNode[
                            index
                        ].detailFeature.ext.text = '';
                        setFindText('');
                    }
                    setMatchType(value);
                    await handleUpdateStep({ ...currentStep });
                }}
            />
            <Tooltip title={findText}>
                <Input
                    size="small"
                    placeholder="可输入文本"
                    className={classnames(styles.paramsInput, {
                        [styles.paramsSmallInput]: type === 'small'
                    })}
                    style={{ width: 'calc(100% - 75px)' }}
                    value={findText}
                    title={findText}
                    disabled={disabled}
                    onChange={(e) => {
                        if (disabled) {
                            return false;
                        }
                        let pattern = new RegExp(ocrCharset);
                        if (
                            currentStep.stepInfo.params.findInfo[findInfoType].findNode[index]
                                .detailFeature.matchType === 2 ||
                            !isElectron() ||
                            (isElectron() && pattern.test(e.nativeEvent.data))
                        ) {
                            setFindText(e.target.value);
                        } else {
                            message.warning('输入字符当前不支持');
                        }
                    }}
                    onBlur={async (e) => {
                        if (
                            e.target.value.trim() ===
                            currentStep.stepInfo.params.findInfo[findInfoType].findNode[index]
                                .detailFeature.ext.text
                        ) {
                            return;
                        }
                        currentStep.stepInfo.params.findInfo[findInfoType].findNode[
                            index
                        ].detailFeature.ext.text = e.target.value;
                        await handleUpdateStep({ ...currentStep });
                    }}
                    suffix={
                        currentStep.stepInfo.params.findInfo[findInfoType]?.findNode[index]
                            .detailFeature.matchType !== 2 && (
                            <Tooltip title="仅支持汉字、大小写字母、数字">
                                <InfoCircleOutlined className={styles.info} />
                            </Tooltip>
                        )
                    }
                />
            </Tooltip>
        </Row>
    );
}

export default connectModel([baseModel], (state) => ({
    ocrCharset: state.common.base.ocrCharset
}))(RenderTextElement);
