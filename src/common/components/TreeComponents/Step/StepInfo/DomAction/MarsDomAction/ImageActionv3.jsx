import {useState, useEffect, useRef} from 'react';
import {isEmpty} from 'lodash';
import {Spin} from 'antd';
import {
    findNewSelectElements,
    getVenusDomElementsBounds, searchNewElementByBoundsv2
} from './utils';
import styles from './DomAction.module.less';

const deepcopy = obj => {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    let newObj = {};
    if (Array.isArray(obj)) {
        newObj = obj.map(item => deepcopy(item));
    } else {
        Object.keys(obj).forEach((key) => {
            return newObj[key] = deepcopy(obj[key]);
        });
    }
    return newObj;
};


const getCanvasSize = (currentNode, currentStep, innerHeight, innerWidth, editType, curOsType) => {
    if (!currentStep?.stepInfo?.params?.recordInfo?.deviceInfo?.screenSize) {
        return;
    }
    const imgScreen = Object.keys(currentStep.stepInfo.params
        .recordInfo.deviceInfo.screenSize).indexOf('rotation') !== -1 &&
        [90, 270].indexOf(currentStep.stepInfo.params.recordInfo.deviceInfo.screenSize.rotation) !== -1;

    let screenWidth = currentStep?.stepInfo.params.recordInfo.deviceInfo.screenSize.width;
    let screenHeight = currentStep?.stepInfo.params.recordInfo.deviceInfo.screenSize.height;
    // 限制最小屏宽高
    screenWidth = screenWidth > 62 ? screenWidth : 160;
    screenHeight = screenHeight > 115 ? screenHeight : 300;
    let imgHeight = -1 === ['readonly', 'debug', 'execute'].indexOf(editType) ?
        innerHeight - 220 : innerHeight - 370;
    if ([0, 2].includes(currentNode?.adoptStatus)) {
        imgHeight -= 20;
    }
    let imageScale = imgHeight / screenHeight;
    let imgWidth = imageScale * screenWidth;

    if (imgScreen) {
        if (curOsType === 2) {
            screenWidth = currentStep?.stepInfo.params.recordInfo.deviceInfo.screenSize.height;
            screenHeight = currentStep?.stepInfo.params.recordInfo.deviceInfo.screenSize.width;
        }
        imageScale = (((innerWidth - 20) * 0.6 - 50) * 10) / 24 / screenWidth;
        imgWidth = (((innerWidth - 20) * 0.6 - 50) * 10) / 24;
        imgHeight = imageScale * screenHeight;
    }
    return {imgHeight, imgWidth, imageScale};
};

const getCheckedKeys = (dom, nodeIdList, checkedKeys = [], nowIndexList = []) => {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (-1 !== nodeIdList.indexOf(element.id)) {
            checkedKeys.push(nowIndexList.join('-'));
        }
        let flag = true;
        if (undefined !== element?.setModelMethod && 1 === element?.setModelMethod) {
            flag = false;
            if (-1 !== Object.keys(element).indexOf('ui_children') && 0 !== element.ui_children.length) {
                getCheckedKeys(element.ui_children, nodeIdList, checkedKeys, nowIndexList);
            }
        }
        if (flag && -1 !== Object.keys(element).indexOf('children') && 0 !== element.children.length) {
            getCheckedKeys(element.children, nodeIdList, checkedKeys, nowIndexList);
        }
        nowIndexList.pop();
    });
    return checkedKeys;
};


function ImageActionv3(props) {
    const {currentStep, currentNode, handleUpdateStep,
        domDetail, curOsType, selectPath, editType,
        innerHeight, imgSrc,
        innerWidth, findInfoType = 'widgetInfo'
    } = props;
    const [showDropdown, setShowDropdown] = useState(false);
    const [dropdownRealLeft, setDropdownRealLeft] = useState(0);
    const [dropdownRealTop, setDropdownRealTop] = useState(0);
    const [dropdownLeft, setDropdownLeft] = useState(0);
    const [dropdownTop, setDropdownTop] = useState(0);
    const [dropdownWidth, setDropdownWidth] = useState(0);
    const [dropdownHeight, setDropdownHeight] = useState(0);
    const [clickElement, setClickElement] = useState(null);
    const [loading, setLoading] = useState(false);
    const canvas = useRef();

    let element;
    let checkElement;
    let selectElements = [];
    let parentRect = {};

    if (currentStep.stepInfo.type === 10) {
        // 获取最大外框 baseInfo
        if (currentStep.stepInfo.params.findInfo?.baseInfo) {
            let _findNode = currentStep.stepInfo.params.findInfo?.baseInfo?.findNode ?? [];
            parentRect = {};
            if (_findNode.length >= 1) {
                let _x = 0;
                let _y = 0;
                let _tx = 0;
                let _ty = 0;
                parentRect = deepcopy(_findNode?.[0]?.detailFeature?.rect);
                _x = parentRect?.x; // x
                _y = parentRect?.y; // y
                _tx = parentRect?.x + parentRect?.w; // targerX
                _ty = parentRect?.y + parentRect?.h; // targerY
                for (let item of _findNode) {
                    const _rect = item?.detailFeature?.rect;
                    if (_rect?.x < _x) {
                        _x = _rect?.x;
                    }
                    if (_rect?.y < _y) {
                        _y = _rect?.y;
                    }
                    if (_tx - _x < (_rect.x + _rect.w) - _x) {
                        _tx = _rect.x + _rect.w;
                    }
                    if (_ty - _y < (_rect.y + _rect.h) - _y) {
                        _ty = _rect.y + _rect.h;
                    }
                }
                parentRect = {
                    x: _x,
                    y: _y,
                    w: _tx - _x,
                    h: _ty - _y
                };
            }
        }

        element = {
            children: [domDetail],
            id: -1
        };

        let _findNode = currentStep.stepInfo.params.findInfo?.[findInfoType]?.findNode ?? [];
        // 获取 点击元素
        for (let item of _findNode) {
            if (item.actionNode) {
                element = {rect: item.detailFeature.rect};
                break;
            }
        }
        // 获取 查看元素
        checkElement = {
            children: [domDetail],
            id: -1
        };
        if (
            undefined !== selectPath &&
            undefined !== checkElement
        ) {
            for (let pathIndex of selectPath) {
                if (undefined !== checkElement.children && 0 !== checkElement.children.length) {
                    checkElement = checkElement.children[pathIndex];
                }
            }
        }
        // 获取 选择元素
        if (
            0 !== (currentStep.stepInfo.params.findInfo?.[findInfoType]?.findNode ?? []).length
        ) {
            let nodeIdList = (currentStep.stepInfo.params?.findInfo?.[findInfoType]?.findNode ?? [])?.map((item) => item.id);
            selectElements = findNewSelectElements([deepcopy(domDetail)], nodeIdList);
        }
    }

    useEffect(() => {
        const context = canvas.current.getContext('2d');
        const img = new Image();
        // setLoading(true);
        let url = imgSrc;
        if (url?.startsWith('http://')) {
            let res = url.split(':');
            url = 'https:' + res[1];
        }
        img.src = url;
        img.onload = () => {
            context.drawImage(img, 0, 0, imgWidth * ratio, imgHeight * ratio);
            draw(context);
            setLoading(false);
        };
    }, [imgSrc]);

    useEffect(() => {
        if (!imgSrc || imgSrc === '') {
            return;
        }
        const context = canvas.current.getContext('2d');
        const img = new Image();
        let url = imgSrc;
        if (url.startsWith('http://')) {
            let res = url.split(':');
            url = 'https:' + res[1];
        }
        img.src = url;
        img.onload = () => {
            context.drawImage(img, 0, 0, imgWidth * ratio, imgHeight * ratio);
            draw(context);
        };
    }, [parentRect, element, selectElements, checkElement, innerHeight, innerWidth]);


    const {imgHeight, imgWidth, imageScale} = getCanvasSize(currentNode,
        currentStep, innerHeight, innerWidth, editType, curOsType);
    const ratio = window.devicePixelRatio || 1;
    const draw = (context) => {
        // 绘制虚线
        let newFindInfo = {...currentStep.stepInfo.params.findInfo[findInfoType]};
        let eleTag = [];
        let typeTag = [];
        for (let tag of currentStep.stepInfo.params.findInfo[findInfoType]?.chosenTag ?? []) {
            if (['single', 'multiple'].includes(tag)) {
                eleTag.push(tag);
            }
            if (['visual', 'system'].includes(tag)) {
                typeTag.push(tag);
            }
        }
        newFindInfo.eleTag = eleTag;
        newFindInfo.typeTag = typeTag;

        let boundsList = getVenusDomElementsBounds(newFindInfo, [
            domDetail
        ]);

        for (let {x, y, h: height, w: width, name, id} of boundsList) {
            // widgetInfo 只展示 baseInfo 范围内的控件
            if (findInfoType === 'widgetInfo' && (x < parentRect.x || y < parentRect.y ||
                x + width > parentRect.x + parentRect.w ||
                y + height > parentRect.y + parentRect.h)) {
                continue;
            }
            x *= imageScale * ratio;
            y *= imageScale * ratio;
            height *= imageScale * ratio;
            width *= imageScale * ratio;
            context.beginPath();

            context.setLineDash([2, 5]);
            context.lineWidth = 1.5;
            context.strokeStyle = '#d9d9d9';

            if (
                currentStep.stepInfo.params.findInfo?.[findInfoType].chosenTag.includes('single') &&
                !isEmpty(currentStep?.stepInfo?.params?.findInfo[findInfoType]?.findNode) &&
                name &&
                id !== currentStep?.stepInfo?.params?.findInfo[findInfoType]?.findNode[0]?.id &&
                name ===
                currentStep?.stepInfo?.params?.findInfo[findInfoType]?.findNode[0]?.detailFeature?.ext
                    ?.name
            ) {
                context.fillStyle = 'rgba(43, 116, 243, 22%)';
                context.fillRect(x, y, width, height);
            }
            // 顶部
            context.moveTo(x, y);
            context.lineTo(x + width, y);
            context.stroke();
            // 左部
            context.moveTo(x, y);
            context.lineTo(x, y + height);
            context.stroke();
            // 右部
            context.moveTo(x + width, y);
            context.lineTo(x + width, y + height);
            context.stroke();
            // 底部
            context.moveTo(x, y + height);
            context.lineTo(x + width, y + height);
            context.stroke();

            context.closePath();
        }

        // 绘制蓝框 父亲 element
        if (parentRect) {
            let {x, y, w: width, h: height} = parentRect;
            x *= imageScale * ratio;
            y *= imageScale * ratio;
            width *= imageScale * ratio;
            height *= imageScale * ratio;
            context.beginPath();
            context.fillStyle = 'rgba(43, 116, 243, 8%)';
            context.fillRect(x, y, width, height);

            context.setLineDash([2, 0]);
            context.lineWidth = 1.5;
            context.strokeStyle = 'rgb(68, 142, 247)';
            // 顶部
            context.moveTo(x, y);
            context.lineTo(x + width, y);
            context.stroke();
            // 左部
            context.moveTo(x, y);
            context.lineTo(x, y + height);
            context.stroke();
            // 右部
            context.moveTo(x + width, y);
            context.lineTo(x + width, y + height);
            context.stroke();
            // 底部
            context.moveTo(x, y + height);
            context.lineTo(x + width, y + height);
            context.stroke();
            context.closePath();
        }

        // 绘制多个控件框
        if (selectElements.length !== 0) {
            for (let _element of selectElements) {
                if (_element?.rect && element?.debug?.id !== _element?.debug?.id) {
                    let {x, y, h: height, w: width} = _element?.rect;
                    x *= imageScale * ratio;
                    y *= imageScale * ratio;
                    height *= imageScale * ratio;
                    width *= imageScale * ratio;
                    context.beginPath();
                    context.setLineDash([6, 3]);
                    context.lineWidth = 2;
                    context.strokeStyle = 'red';
                    context.strokeRect(x, y, width, height);
                    context.closePath();
                }
            }
        }
        // 绘制黄框 点击 element
        if (element?.rect) {
            let {x, y, h: height, w: width} = element?.rect;
            x *= imageScale * ratio;
            y *= imageScale * ratio;
            height *= imageScale * ratio;
            width *= imageScale * ratio;
            context.beginPath();
            context.setLineDash([6, 3]);
            context.lineWidth = 2;
            context.fillStyle = 'rgba(250, 177, 54, 12%)';
            context.strokeStyle = 'orange';
            context.fillRect(x, y, width, height);
            context.strokeRect(x, y, width, height);
            context.closePath();
        }

        if (checkElement?.rect) {
            let {x, y, h: height, w: width} = checkElement.rect;
            x *= imageScale * ratio;
            y *= imageScale * ratio;
            height *= imageScale * ratio;
            width *= imageScale * ratio;
            context.beginPath();
            context.setLineDash([10, 0]);
            context.lineWidth = 2;
            context.strokeStyle = '#2f7bf5';
            context.strokeRect(x, y, width, height);
            context.closePath();
        }
    };
    return (
        <Spin spinning={loading}>
            {
                currentStep.stepInfo.params.findInfo[findInfoType]?.chosenTag?.includes('single') ?
                    <canvas
                        ref={canvas}
                        width={imgWidth * ratio}
                        height={imgHeight * ratio}
                        style={{
                            width: imgWidth,
                            height: imgHeight,
                            border: '1px solid #eee'
                        }}
                        className={-1 === ['execute', 'debug'].indexOf(editType) ? editType + 'StepImage' : ''}
                    /> : <div
                        onClick={() => {
                            setShowDropdown(false);
                        }}
                        onContextMenu={(e) => {
                            if (findInfoType === 'baseInfo') {
                                return;
                            }
                            let _clickElement = null;
                            let _clickLeft = e.nativeEvent.offsetX;
                            let _clickTop = e.nativeEvent.offsetY;
                            let curLeft = -1;
                            let curTop = -1;
                            for (let _element of selectElements) {
                                if (_element?.rect) {
                                    let {x, y, w: width, h: height} = _element?.rect;
                                    x *= imageScale;
                                    y *= imageScale;
                                    height *= imageScale;
                                    width *= imageScale;
                                    // 1: 判断元素在点击位置内
                                    // 2: 判断多个元素中最接近的那个元素
                                    if (x < _clickLeft && x + width > _clickLeft &&
                                        y < _clickTop && y + height > _clickTop) {
                                        if (-1 === curLeft ||
                                            (-1 !== curLeft && x >= curLeft && y >= curTop)) {
                                            setClickElement(_element);
                                            _clickElement = _element;
                                            curLeft = x;
                                            curTop = y;
                                        }
                                    }
                                }
                            }
                            if (_clickElement) {
                                if (imgHeight - _clickTop < 80) {
                                    // 太靠下，展示在右上
                                    setDropdownTop(_clickTop - 80);
                                    setDropdownLeft(_clickLeft + 10);
                                } else if (imgWidth - _clickLeft < 180) {
                                    // 太靠右，展示在左下
                                    setDropdownTop(_clickTop + 10);
                                    setDropdownLeft(_clickLeft - 110);
                                } else {
                                    // 默认右下
                                    setDropdownTop(_clickTop + 10);
                                    setDropdownLeft(_clickLeft + 10);
                                }
                                setDropdownRealLeft(_clickLeft);
                                setDropdownRealTop(_clickTop);
                                setDropdownWidth(e.nativeEvent.target.width);
                                setDropdownHeight(e.nativeEvent.target.height);
                                setShowDropdown(true);
                            } else {
                                setShowDropdown(false);
                            }
                        }}
                        onMouseLeave={() => {
                            setTimeout(() => {
                                setShowDropdown(false);
                            }, 1000);
                        }}
                    >
                        <canvas
                            ref={canvas}
                            width={imgWidth * ratio}
                            height={imgHeight * ratio}
                            style={{
                                width: imgWidth,
                                height: imgHeight,
                                border: '1px solid #eee'
                            }}
                            className={-1 === ['execute', 'debug'].indexOf(editType)
                                ? editType + 'StepImage' : ''}
                        />
                        {
                            checkElement?.rect ?
                                <span style={{color: '#777', fontSize: 10}}>
                                    <br />
                                    x: {checkElement.rect.x}&nbsp;
                                    y: {checkElement.rect.y}&nbsp;
                                    width: {checkElement.rect.w}&nbsp;
                                    height: {checkElement.rect.h}
                                </span> : null
                        }
                        <ul
                            style={{
                                display: showDropdown ? 'block' : 'none',
                                left: dropdownLeft,
                                top: dropdownTop
                            }}
                            className={styles.canvasDropdown}
                        >
                            <li
                                onClick={async () => {
                                    let newFindNode = deepcopy(currentStep.stepInfo.params.
                                        findInfo[findInfoType].findNode);
                                    for (let _index in newFindNode) {
                                        if (newFindNode[_index].id === clickElement.debug.id) {
                                            newFindNode[_index].actionNode = true;
                                        } else {
                                            newFindNode[_index].actionNode = false;
                                        }
                                    }
                                    let xPrecent = ((dropdownRealLeft) * ratio) / dropdownWidth;
                                    let yPrecent = ((dropdownRealTop) * ratio) / dropdownHeight;
                                    // 找控件
                                    let {width, height} = currentStep.stepInfo.params.recordInfo.deviceInfo.screenSize;
                                    if (
                                        curOsType === 2 &&
                                        -1 !== Object.keys(currentStep.stepInfo.
                                            params.recordInfo.deviceInfo.screenSize).indexOf('rotation')
                                    ) {
                                        if (
                                            -1 !== [90, 270].indexOf(currentStep.stepInfo.params.recordInfo.
                                                deviceInfo.screenSize.rotation)
                                        ) {
                                            let _width = width;
                                            width = height;
                                            height = _width;
                                        }
                                    }
                                    // 计算点击位置在设备上的位置
                                    let x = xPrecent * width;
                                    let y = yPrecent * height;
                                    // 递归检索选择控件的路径
                                    let {pathIndexList} = searchNewElementByBoundsv2(
                                        [domDetail], {
                                        x,
                                        y,
                                    }, clickElement);
                                    currentStep.stepInfo.params.findInfo[findInfoType].findNode = [...newFindNode];
                                    currentStep.stepInfo.params.pathSummary = pathIndexList;
                                    await handleUpdateStep({
                                        ...currentStep,
                                    });
                                }}
                            >
                                设置为操作控件
                            </li>
                            <li
                                onClick={async () => {
                                    let newFindNode = deepcopy(currentStep.stepInfo.params.
                                        findInfo[findInfoType].findNode);
                                    for (let _index in newFindNode) {
                                        if (newFindNode[_index].id === clickElement.debug.id) {
                                            newFindNode[_index].featureFlag = true;
                                            newFindNode[_index].detailFeature.ext = {
                                                ...clickElement.ext
                                            };
                                        }
                                    }
                                    currentStep.stepInfo.params.findInfo[findInfoType].findNode = [...newFindNode];
                                    let newActionInfo = JSON.parse(JSON.stringify((currentStep.stepInfo)));
                                    if (
                                        newActionInfo?.params?.recordInfo?.deviceInfo?.screenshot &&
                                        !newActionInfo?.params?.recordInfo?.deviceInfo?.screenshot?.startsWith('http')
                                    ) {
                                        newActionInfo.params.recordInfo.deviceInfo.screenshot = '';
                                    }
                                    handleUpdateStep({...currentStep});
                                    setShowDropdown(false);
                                    setDropdownTop(0);
                                    setDropdownLeft(0);
                                }}
                            >
                                叠加视觉特征
                            </li>
                        </ul>
                    </div>
            }
        </Spin>
    );
};

export default ImageActionv3;
