.action {
    width: calc(100% -30px);
    margin: 15px;
}

.domAction {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.stepOperator {
    padding: 0 5px 5px 5px;
    border-bottom: 1px solid var(--border-color);
    z-index: 99;
}

.defaultAction {
    margin-top: 5px;
}

.canvasDropdown {
    display: none;
    position: absolute;
    z-index: 999;
    width: 120px;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    cursor: pointer;
    border: 1px solid var(--border-color);
}

.canvasDropdown li {
    padding: 5px 10px;
    list-style: none;
}

.canvasDropdown li:hover {
    background-color: #3686f6;
    color: #fff;
}