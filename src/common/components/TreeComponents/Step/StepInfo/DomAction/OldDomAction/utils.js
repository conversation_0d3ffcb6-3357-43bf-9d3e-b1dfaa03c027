import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
const deepcopy = obj => {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    let newObj = {};
    if (Array.isArray(obj)) {
        newObj = obj.map(item => deepcopy(item));
    } else {
        Object.keys(obj).forEach((key) => {
            return newObj[key] = deepcopy(obj[key]);
        });
    }
    return newObj;
};

export function searchElementByBounds(
    dom,
    {x, y},
    pathIndexList = [],
    nowIndexList = [],
    targetBounds = {}
) {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (element?.rect) {
            if (
                x >= element?.rect.x &&
                x <= element?.rect.x + element?.rect.width &&
                y >= element?.rect.y &&
                y <= element?.rect.y + element?.rect.height
            ) {
                let needCover = true;
                if (0 !== Object.keys(targetBounds).length) {
                    if (
                        element?.rect.width <= targetBounds.width &&
                        element?.rect.height <= targetBounds.height
                    ) {
                        needCover = true;
                    } else {
                        needCover = false;
                    }
                }
                if (needCover) {
                    pathIndexList = [...nowIndexList];
                    targetBounds = {...element?.rect};
                }
            }
        }
        // 判断是否为视觉建模
        let flag = true;
        if (undefined !== element?.setModelMethod && 1 === element?.setModelMethod) {
            flag = false;
            if ('object' === typeof element.ui_children && 0 !== element.ui_children.length) {
                let res = searchElementByBounds(
                    element.ui_children,
                    {x, y},
                    pathIndexList,
                    nowIndexList,
                    targetBounds
                );
                pathIndexList = res.pathIndexList;
                targetBounds = res.targetBounds;
            }
        }
        if (flag && 'object' === typeof element.children && 0 !== element.children.length) {
            let res = searchElementByBounds(
                element.children,
                {x, y},
                pathIndexList,
                nowIndexList,
                targetBounds
            );
            pathIndexList = res.pathIndexList;
            targetBounds = res.targetBounds;
        }
        nowIndexList.pop();
    });
    return {pathIndexList, targetBounds};
};


export function searchNewElementByBounds(
    dom,
    {x, y},
    clickElement,
    pathIndexList = [],
    nowIndexList = [],
    targetBounds = {}
) {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (element?.rect) {
            if (
                x >= element?.rect.x &&
                x <= element?.rect.x + element?.rect.width &&
                y >= element?.rect.y &&
                y <= element?.rect.y + element?.rect.height
                && clickElement
                && clickElement.id === element.id
            ) {
                let needCover = true;
                if (0 !== Object.keys(targetBounds).length) {
                    if (
                        element?.rect.width <= targetBounds.width &&
                        element?.rect.height <= targetBounds.height
                    ) {
                        needCover = true;
                    } else {
                        needCover = false;
                    }
                }
                if (clickElement && needCover) {
                    pathIndexList = [...nowIndexList];
                    targetBounds = {...element?.rect};
                }
            }
        }
        if ('object' === typeof element.children && 0 !== element.children.length) {
            let res = searchNewElementByBounds(
                element.children,
                {x, y},
                clickElement,
                pathIndexList,
                nowIndexList,
                targetBounds
            );
            pathIndexList = res.pathIndexList;
            targetBounds = res.targetBounds;
        }
        nowIndexList.pop();
    });
    return {pathIndexList, targetBounds};
};

export function getVisionModelNodeInfo(dom, modelingElemet = [], nowIndexList = []) {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (!element?.setModelMethod) {
            element.setModelMethod = 0;
        }
        if (1 === element?.setModelMethod) {
            let ele = deepcopy(element);
            ele.key = nowIndexList.join('-');
            modelingElemet.push(ele);
        }
        if (element?.children && 0 !== element?.children.length) {
            getVisionModelNodeInfo(element.children, modelingElemet, nowIndexList);
        }
        nowIndexList.pop();
    });
    return modelingElemet;
};

export function getElementsBounds(elements, type = 'normal', boundsList = []) {
    for (let element of elements) {
        if (element?.rect && element?.rect.x >= 0 && element?.rect.y >= 0) {
            if ('ui' === type) {
                if (!isNumber(element.id)) {
                    boundsList.push(element?.rect);
                }
            } else {
                boundsList.push(element?.rect);
            }
        }
        // 判断是否为视觉建模
        let flag = true;
        if (
            undefined !== element?.setModelMethod && 1 === element?.setModelMethod
        ) {
            flag = false;
            if (undefined !== element.ui_children && 0 !== element.ui_children.length) {
                boundsList = getElementsBounds(element.ui_children, type, boundsList);
            }
        }
        if (flag && undefined !== element?.children && 0 !== element?.children.length) {
            boundsList = getElementsBounds(element.children, type, boundsList);
        }
    }
    return boundsList;
};

export function findSelectElements(dom, nodeIdList, selectElements = []) {
    dom.forEach((element, index) => {
        if (-1 !== nodeIdList.indexOf(element.id)) {
            selectElements.push(element);
        }
        // 判断是否为视觉建模
        let flag = true;
        if (
            undefined !== element?.setModelMethod && 1 === element?.setModelMethod
        ) {
            flag = false;
            if (undefined !== element.ui_children && 0 !== element.ui_children.length) {
                findSelectElements(element.ui_children, nodeIdList, selectElements);
            }
        }
        if (flag && undefined !== element?.children && 0 !== element?.children.length) {
            findSelectElements(element.children, nodeIdList, selectElements);
        }
    });
    return selectElements;
};

export function longestCommonPrefix(strs) {
    if (strs.length === 0) {
        return '';
    }
    let arrs = [];
    for (let str of strs) {
        arrs.push(str.split('-').map(item => parseInt(item, 10)));
    }
    let commonArr = arrs[0];
    let newArr = [];
    for (let i = 1; i < arrs.length; i++) {
        for (let j = 0; j <= arrs[i].length; j++) {
            if (commonArr[j] === arrs[i][j]) {
                newArr.push(commonArr[j]);
            } else {
                commonArr = newArr;
                break;
            }
        }
        newArr = [];
    }
    return commonArr.join('-');
};

export function recursionDomElement(dom, pathIndexList, path = []) {
    let pathItem = [dom[pathIndexList[0]].type, 0];
    let targetElement = dom[pathIndexList[0]];

    // 计算该元素是同层的第几个
    for (let index = 0; (index < dom.length) & (index < pathIndexList[0]); index++) {
        if (pathItem[0] === dom[index].type) {
            pathItem[1]++;
        }
    }
    // 递归继续
    path.push(pathItem);
    if (1 === pathIndexList.length) {
        return {path, targetElement, pathIndexList};
    } else {
        pathIndexList.shift();
        // 判断是否为视觉建模
        let flag = true;
        if (
            undefined !== targetElement.setModelMethod && 1 === targetElement.setModelMethod
        ) {
            flag = false;
            if (undefined !== targetElement.ui_children && 0 !== targetElement.ui_children.length) {
                return recursionDomElement(targetElement.ui_children, pathIndexList, path);
            }
        }
        if (flag && undefined !== targetElement.children && 0 !== targetElement.children.length) {
            return recursionDomElement(targetElement.children, pathIndexList, path);
        }
    }
};


/*  递归检索选择控件信息
    path: 父控件的路径
    ootPathIndexList: 选择控件的父控件路径
*/
export function getSingleElementInfo(rootPathIndexList, domDetail, isMulti = false) {
    let path = [];
    let targetElement = deepcopy(domDetail);
    if (!isMulti) {
        rootPathIndexList.pop();
    }
    if (0 !== rootPathIndexList.length) {
        let res = recursionDomElement(
            [domDetail],
            deepcopy(rootPathIndexList)
        );
        path = res.path;
        targetElement = res.targetElement;
    }
    return {path, targetElement, rootPathIndexList};
}