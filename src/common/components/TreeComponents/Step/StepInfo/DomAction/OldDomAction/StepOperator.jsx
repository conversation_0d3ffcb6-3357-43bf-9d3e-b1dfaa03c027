import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {Tooltip, Select, message, Popconfirm, Tag} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import InitProgress from 'COMMON/components/TreeComponents/Step/InitProgress/InitProgress';
import styles from 'COMMON/components/TreeComponents/Step/StepInfo/StepInfo.module.less';


function StepOperator(props) {
    const {currentStep, editType, domDetail,
        handleUpdateStep
    } = props;
    const [stepType, setActionType] = useState(null);

    useEffect(() => {
        setActionType(currentStep.stepInfo.params.actionInfo.type);
    }, [currentStep]);

    return (
        <div className={styles.stepOperator}>
            <span className={styles.stepIndex}>
                <Tooltip
                    title={
                        2 === currentStep.stepInfo.type &&
                            undefined !== domDetail.dom_info &&
                            undefined !== domDetail.dom_info.version ?
                            `版本: ${domDetail.dom_info.version}` : '版本暂无'
                    }
                >
                    {4 === currentStep.stepInfo.params.findType ? '视觉' : '控件'}
                </Tooltip>
            </span>
            <InitProgress />
            {currentStep.stepInfo.type === 2 && [0, 1, 4, 5].indexOf(currentStep.stepInfo.params.findType) !== -1 ?
                (<SelectOperator
                    currentStep={currentStep}
                    domDetail={domDetail}
                    handleUpdateStep={handleUpdateStep}
                    stepType={stepType}
                    editType={editType}
                    setActionType={setActionType}
                />) : null}

        </div>
    );
};


function SelectOperator({currentStep, stepType, handleUpdateStep,
    setActionType, editType}) {
    const options = [
        {
            value: 'tap',
            label: '点击',
        },
        {
            value: 'input',
            label: '输入',
        },
        {
            value: 'swipe',
            label: '滑动',
        },
        {
            value: 'addTap',
            label: '长按',
        },
        {
            value: 'nope',
            label: '存在',
        },
        {
            value: 'absence',
            label: '不存在',
        }
    ];
    return (
        <Tooltip title='切换操作' placement='left'>
            <Select
                size='small'
                dropdownMatchSelectWidth={false}
                value={stepType === 'tap' &&
                    currentStep?.stepInfo.params.actionInfo.params?.duration ? 'addTap' : stepType}
                options={options}
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                onChange={async (type) => {
                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                        return false;
                    }
                    try {
                        if (-1 === ['tap'].indexOf(type) &&
                            currentStep.stepInfo.params.findParams?.until) {
                            delete currentStep.stepInfo.params.findParams.until;
                        }
                        if ('tap' === type) {
                            currentStep.stepType = 'tap';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'tap',
                                params: {},
                            };
                        } else if ('addTap' === type) {
                            currentStep.stepType = 'tap';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'tap',
                                params: {
                                    duration: 1000,
                                },
                            };
                        } else if ('input' === type) {
                            currentStep.stepType = 'input';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'input',
                                params: {
                                    text: '',
                                },
                            };
                        } else if ('nope' === type) {
                            currentStep.stepType = 'nope';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'nope',
                                params: {},
                            };
                        } else if ('absence' === type) {
                            currentStep.stepType = 'absence';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'absence',
                                params: {},
                            };
                        } else if ('swipe' === type) {
                            currentStep.stepType = 'swipe';
                            currentStep.stepInfo.params.actionInfo = {
                                type: 'swipe',
                                params: {
                                    direction: 1,
                                    duration: 500,
                                    times: 1,
                                },
                            };
                            if (currentStep.stepInfo.params.findInfo?.findType) {
                                currentStep.stepInfo.params.findInfo.findType = 0;
                            }
                            if (currentStep.stepInfo.params.findInfo?.scrollDirection) {
                                delete currentStep.stepInfo.params.findInfo.scrollDirection;
                            }
                            if (currentStep.stepInfo.params.findInfo?.screenCount) {
                                currentStep.stepInfo.params.findInfo.screenCount = 1;
                            }
                        }
                        setActionType(currentStep.stepInfo.params.actionInfo.type);
                        await handleUpdateStep(currentStep);
                    } catch (err) {
                    }
                }}
            />
        </Tooltip>
    );
};

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
}))(StepOperator);
