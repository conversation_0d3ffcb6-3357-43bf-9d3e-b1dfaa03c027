import {Input, Select} from 'antd';

export default ({editType, currentStep, element, handleUpdateStep, index}) => {
    let matchTypeOptions = [
        {
            key: 0,
            value: 0,
            label: '普通匹配',
        },
        {
            key: 1,
            value: 1,
            label: '严格匹配',
        },
    ];
    let options = [
        {
            key: 1,
            value: 1,
            label: 2 !== element.detailDetection[0] ? '包含' : '相似',
        },
        {
            key: 2,
            value: 2,
            label: 2 !== element.detailDetection[0] ? '相似' : '不相似',
        },
    ];
    let typeOptions = [
        {
            key: 0,
            value: 0,
            label: '仅结构校验',
        },
        {
            key: 1,
            value: 1,
            label: '文本',
        },
    ];
    if (element.hasImage) {
        typeOptions.push({
            key: 2,
            value: 2,
            label: '图标',
        });
    }
    let matchTyoeOptionJsx = [];
    for (let item of matchTypeOptions) {
        matchTyoeOptionJsx.push(
            <Select.Option key={item.key} value={item.value} label={item.label}>
                {item.label}
            </Select.Option>
        );
    }
    let optionJsx = [];
    for (let item of options) {
        optionJsx.push(
            <Select.Option key={item.key} value={item.value} label={item.label}>
                {item.label}
            </Select.Option>
        );
    }
    let typeOptionJsx = [];
    for (let item of typeOptions) {
        typeOptionJsx.push(
            <Select.Option key={item.key} value={item.value} label={item.label}>
                {item.label}
            </Select.Option>
        );
    }
    return (
        <div
            style={{
                fontSize: 12,
            }}
        >
            <span
                style={{
                    display: 'inline-block',
                    height: 24,
                    width: 60,
                    padding: '0 5px',
                    backgroundColor: '#fff',
                    border: '1px solid #d9d9d9',
                    textAlign: 'center',
                    fontWeight: 500,
                    marginTop: 3,
                }}
            >
                该控件
            </span>
            <Select
                style={{width: 110}}
                size='small'
                optionLabelProp='label'
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                value={element.detailDetection[0]}
                onChange={(value) => {
                    currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[0] =
                        Number(value);
                    if (0 === Number(value)) {
                        currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[1] = 0;
                        currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[2] = '';
                    } else {
                        currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[1] = 1;
                        currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[2] = '';
                    }
                    handleUpdateStep(currentStep);
                }}
            >
                {typeOptionJsx}
            </Select>
            {0 !== element.detailDetection[0] ? (
                <Select
                    style={{width: 75}}
                    size='small'
                    optionLabelProp='label'
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    value={element.detailDetection[1]}
                    onChange={(value) => {
                        currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[1] =
                            Number(value);
                        if (
                            0 !== Number(value) &&
                            2 === currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[0]
                        ) {
                            currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[2] =
                                element.value;
                        }
                        if (0 === Number(value)) {
                            currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[2] = '';
                        }
                        handleUpdateStep(currentStep);
                    }}
                >
                    {optionJsx}
                </Select>
            ) : null}
            {-1 === [0, 2].indexOf(element.detailDetection[0]) ? (
                <Input
                    style={{width: 120}}
                    size='small'
                    className='inputEditor'
                    min={0}
                    placeholder='请输入'
                    disabled={['readonly', 'debug', 'execute'].includes(editType) || 2 === element.detailDetection[0] || 0 === element.detailDetection[1]}
                    value={
                        0 === element.detailDetection[1] ? '仅结构校验' : element.detailDetection[2]
                    }
                    onChange={(e) => {
                        currentStep.stepInfo.params.findInfo.findNode[index].detailDetection[2] =
                            e.target.value;
                        handleUpdateStep(currentStep);
                    }}
                />
            ) : null}
            {undefined !== element.matchType && !element.hasImage ? <br /> : null}
            {undefined !== element.matchType && !element.hasImage ? (
                <span
                    style={{
                        display: 'inline-block',
                        height: 24,
                        width: 60,
                        padding: '0 5px',
                        backgroundColor: '#fff',
                        border: '1px solid #d9d9d9',
                        textAlign: 'center',
                        fontWeight: 500,
                        marginTop: 3,
                    }}
                >
                    子控件
                </span>
            ) : null}
            {undefined !== element.matchType && !element.hasImage ? (
                <Select
                    style={{width: 100}}
                    size='small'
                    optionLabelProp='label'
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    value={element.matchType}
                    onChange={(value) => {
                        currentStep.stepInfo.params.findInfo.findNode[index].matchType = Number(value);
                        handleUpdateStep(currentStep);
                    }}
                >
                    {matchTyoeOptionJsx}
                </Select>
            ) : null}
        </div>
    );
};
