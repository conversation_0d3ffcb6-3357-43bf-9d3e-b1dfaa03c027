import {message} from 'antd';
import {
    searchElementByBounds, recursionDomElement, longestCommonPrefix,
    getSingleElementInfo, getVisionModelNodeInfo
} from './utils';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';

const deepcopy = obj => {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    let newObj = {};
    if (Array.isArray(obj)) {
        newObj = obj.map(item => deepcopy(item));
    } else {
        Object.keys(obj).forEach((key) => {
            return newObj[key] = deepcopy(obj[key]);
        });
    }
    return newObj;
};

// 获取FindNode(单控件)
const getSingleElementFindNode = (targetElement, rootPathIndexList, pathIndexList, path) => {
    let pathLength = pathIndexList.length - path.length;
    let leafPathIndexList = deepcopy(pathIndexList);
    let leafPath = leafPathIndexList.splice(path.length, pathLength);
    let nodePath = [];
    // 判断是否为视觉建模
    let flag = true;
    if (undefined !== targetElement.setModelMethod && 1 === targetElement.setModelMethod) {
        flag = false;
        if (
            0 !== rootPathIndexList.length &&
            'object' === typeof targetElement.ui_children &&
            0 !== targetElement.ui_children.length
        ) {
            let res = recursionDomElement(
                targetElement.ui_children,
                deepcopy(leafPath)
            );
            nodePath = res.path;
        }
    }
    if (
        flag &&
        0 !== rootPathIndexList.length &&
        'object' === typeof targetElement.children &&
        0 !== targetElement.children.length
    ) {
        let res = initBatElement.recursionDomElement(targetElement.children, deepcopy(leafPath));
        nodePath = res.path;
    }
    return {nodePath};
};

const getPath = (dom, nodeIdList, nowIndexList = [], pathList = []) => {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (-1 !== nodeIdList.indexOf(element.id)) {
            pathList.push(nowIndexList.join('-'));
        }
        if (-1 !== Object.keys(element).indexOf('children') && 0 !== element.children.length) {
            getPath(element.children, nodeIdList, nowIndexList, pathList);
        }
        // 判断是否为视觉建模
        let flag = true;
        if (undefined !== element?.setModelMethod && 1 === element?.setModelMethod) {
            flag = false;
            if ('object' === typeof element.ui_children && 0 !== element.ui_children.length) {
                getPath(element.ui_children, nodeIdList, nowIndexList, pathList);
            }
        }
        if (flag && 'object' === typeof element.children && 0 !== element.children.length) {
            getPath(element.children, nodeIdList, nowIndexList, pathList);
        }
        nowIndexList.pop();
    });
    return pathList;
};

/**
 * 修改原生代码函数
 *
 * @param {*} currentNode 当前编辑节点
 * @param {*} step 当前编辑步骤
 * @param {*} osType 操作系统类型
 * @param {*} editType 编辑类型（template、nodeId）
 * @param {*} method 方法名（click、contextmenu）
 * @param {*} xPrecent X轴坐标百分比
 * @param {*} yPrecent Y轴坐标百分比
 * @param {*} handleUpdateStep 更新步骤回调函数
 * @param {*} templateId 模板ID
 * @param {*} handleUpdateNode 更新节点回调函数
 */
export async function editNative({domDetail, step, osType, editType, method = 'click',
    xPrecent, yPrecent, handleUpdateStep, templateId, handleUpdateNode}) {
    try {
        // 老dom
        if (0 === step.stepInfo.params.findType && 'contextmenu' === method) {
            message.warning('当前建模方法不支持多控件选择，请重新建模哦！');
            return false;
        }
        // 文本 / icon
        if (
            -1 !== [2, 3].indexOf(step.stepInfo.params.findType) &&
            'contextmenu' === method
        ) {
            return false;
        }
        // 找控件
        let {width, height} = step.stepInfo.params.deviceInfo.screenSize;
        if (
            -1 !== Object.keys(step.stepInfo.params.deviceInfo.screenSize).indexOf('rotation')
        ) {
            if (
                -1 !== [90, 270].indexOf(step.stepInfo.params.deviceInfo.screenSize.rotation)
            ) {
                let _width = width;
                width = height;
                height = _width;
            }
        }
        // 计算点击位置在设备上的位置
        let x = xPrecent * width;
        let y = yPrecent * height;
        // 递归检索选择控件的路径
        let {pathIndexList} = searchElementByBounds([domDetail], {
            x,
            y,
        });
        if (0 === pathIndexList.length) {
            message.error('该点击位置没有控件');
            return false;
        }
        // 更新步骤信息
        if (-1 !== [0, 2, 3].indexOf(step.stepInfo.params.findType)) {
            let {path, targetElement, rootPathIndexList} = getSingleElementInfo(
                deepcopy(pathIndexList),
                step,
                -1 !== [2, 3].indexOf(step.stepInfo.params.findType)
            );
            // 用于前端展示
            step.stepInfo.params.pathSummary = pathIndexList;
            // 获取findPath 选择控件的父控件路径
            if (0 === step.stepInfo.params.findType) {
                step.stepInfo.params.findInfo.findPath = path;
                // 获取findNode 选择控件的信息
                let {nodePath} = getSingleElementFindNode(
                    step,
                    targetElement,
                    rootPathIndexList,
                    pathIndexList,
                    path
                );
                step.stepInfo.params.findInfo.findNode = {
                    rect: targetElement.rect,
                    nodeType: targetElement.nodeType,
                    nodeInfo: nodePath,
                    verifyInfo: {},
                };
            }
            if (2 === step.stepInfo.params.findType) {
                let _index = 0;
                if (0 !== rootPathIndexList.length) {
                    _index = -1;
                    domDetail.children.forEach((element, index) => {
                        if (index <= rootPathIndexList[rootPathIndexList.length - 1]) {
                            if (
                                targetElement.info &&
                                element.info.value === targetElement.info.value
                            ) {
                                _index += 1;
                            }
                        }
                    });
                }
                step.stepInfo.params.findInfo.findText = targetElement.info
                    ? targetElement.info.label
                    : '';
                step.stepInfo.params.findInfo.index = _index;
            }
            if (3 === step.stepInfo.params.findType) {
                let _index = 0;
                if (0 !== rootPathIndexList.length) {
                    _index = -1;
                    domDetail.children.forEach((element, index) => {
                        if (index <= rootPathIndexList[rootPathIndexList.length - 1]) {
                            if (
                                targetElement.info &&
                                element.info.value === targetElement.info.value
                            ) {
                                _index += 1;
                            }
                        }
                    });
                }
                step.stepInfo.params.findInfo.icon = targetElement.info
                    ? targetElement.info.value
                    : '';
                step.stepInfo.params.findInfo.index = _index;
            }
        } else if (1 === step.stepInfo.params.findType) {
            let {targetElement} = getSingleElementInfo(pathIndexList, step, true);
            let newFindNode = deepcopy(step.stepInfo.params.findInfo.findNode);
            // 单选
            // 点击操作 更新操作节点
            if (targetElement.modelType && 1 === targetElement.modelType) {
                let _index = newFindNode.findIndex((item) => !isNumber(item.id));
                if (-1 !== _index) {
                    newFindNode.splice(_index, 1);
                }
            }
            if ('click' === method) {
                if (1 === newFindNode.length) {
                    if (targetElement.id !== newFindNode[0].id && newFindNode[0].actionNode) {
                        newFindNode = [];
                    }
                } else {
                    for (let item of newFindNode) {
                        if (targetElement.id === item.id) {
                            item.actionNode = true;
                        }
                        if (targetElement.id !== item.id) {
                            item.actionNode = false;
                        }
                    }
                }
            }
            // 判断选择控件 是否已存在
            let existIndex = -1;
            for (let index = 0; index < newFindNode.length; index++) {
                if (newFindNode[index].id === targetElement.id) {
                    existIndex = index;
                    break;
                }
            }
            let _newModelingNodeFlag = false;
            for (let item of newFindNode) {
                if (1 === item.modelMethod) {
                    _newModelingNodeFlag = true;
                }
            }
            if (
                'contextmenu' === method &&
                targetElement.modelType &&
                1 === targetElement.modelType
            ) {
                return false;
            } else if (-1 === existIndex) {
                if (0 !== newFindNode.length && newFindNode[0].textDetection) {
                    newFindNode.push({
                        id: targetElement.id,
                        actionNode: 'click' === method,
                        parents: targetElement.parents,
                        textDetection: [0, ''],
                    });
                } else if (
                    0 !== newFindNode.length && !newFindNode[0].modelMethod
                ) {
                    newFindNode.push({
                        id: targetElement.id,
                        hasImage:
                            -1 !== Object.keys(targetElement).indexOf('children') &&
                            0 === targetElement.children.length,
                        matchType: 0,
                        actionNode: 'click' === method,
                        parents: targetElement.parents,
                        detailDetection: [0, 0, ''],
                    });
                } else {
                    let modelingNode = [];
                    if (!_newModelingNodeFlag) {
                        modelingNode = getVisionModelNodeInfo([domDetail]);
                    }
                    let nodeIdList = newFindNode.map(item => item.id);
                    if (0 !== modelingNode.length && targetElement.id !== modelingNode[0].id) {
                        if (-1 === nodeIdList.indexOf(modelingNode[0].id)) {
                            newFindNode.push({
                                id: modelingNode[0].id,
                                hasImage:
                                    -1 !== Object.keys(modelingNode[0]).indexOf('children') &&
                                    0 === modelingNode[0].children.length,
                                matchType: 0,
                                modelMethod:
                                    !modelingNode[0].setModelMethod
                                        ? 0
                                        : modelingNode[0].setModelMethod,
                                modelType:
                                    !modelingNode[0].modelType
                                        ? 0
                                        : modelingNode[0].modelType,
                                actionNode:
                                    (modelingNode[0].modelType &&
                                        1 === modelingNode[0].modelType) ||
                                    'click' === method,
                                parents: modelingNode[0].parents,
                                detailDetection: [0, 0, ''],
                            });
                            nodeIdList.push(modelingNode[0].id);
                        }
                    }
                    if (-1 === nodeIdList.indexOf(targetElement.id)) {
                        newFindNode.push({
                            id: targetElement.id,
                            hasImage:
                                -1 !== Object.keys(targetElement).indexOf('children') &&
                                0 === targetElement.children.length,
                            matchType: 0,
                            modelMethod:
                                !targetElement.setModelMethod
                                    ? 0
                                    : targetElement.setModelMethod,
                            modelType:
                                !targetElement.modelType ? 0 : targetElement.modelType,
                            actionNode:
                                (targetElement.modelType && 1 === targetElement.modelType) ||
                                'click' === method,
                            parents: targetElement.parents,
                            detailDetection: [0, 0, ''],
                        });
                    }
                }
            } else if ('contextmenu' === method) {
                // 2 次则取消
                let actionElement = newFindNode.filter((item) => item.actionNode);

                // 点击控件取消，路径也变为[]
                if (0 !== actionElement.length && actionElement[0].id === targetElement.id) {
                    step.stepInfo.params.pathSummary = [];
                }
                if (0 !== deepcopy(step.stepInfo.params.findInfo.findNode).length) {
                    if (step.stepInfo.params.findInfo.findNode[0].id === targetElement.id) {
                        step.stepInfo.params.findInfo.findNode = [];
                        step.stepInfo.params.pathSummary = [];
                        step.stepInfo.params.findInfo.findType = 0;
                        step.stepInfo.params.findInfo.findPath = [];
                        newFindNode = [];
                    } else {
                        newFindNode.splice(existIndex, 1);
                    }
                }
            }
            // 获取 findPath
            let pathList = getPath(
                [domDetail],
                newFindNode.map((item) => item.id)
            );
            let parentPath = longestCommonPrefix(pathList)
                .split('-')
                .filter((item) => '' !== item)
                .map((item) => parseInt(item, 10));

            if (newFindNode.length > 1) {
                let nodeIndex = pathList.indexOf(parentPath.join('-'));
                let {targetElement: parentElement} = getSingleElementInfo(
                    parentPath,
                    step,
                    true
                );
                if (-1 === nodeIndex) {
                    if (newFindNode[0].textDetection) {
                        newFindNode.unshift({
                            id: parentElement.id,
                            actionNode: false,
                            parents: parentElement.parents,
                            textDetection: [0, ''],
                        });
                    } else if (!newFindNode[0].modelMethod) {
                        newFindNode.unshift({
                            id: parentElement.id,
                            hasImage: true,
                            actionNode: false,
                            matchType: 0,
                            parents: parentElement.parents,
                            detailDetection: [0, 0, ''],
                        });
                    } else {
                        newFindNode.unshift({
                            id: parentElement.id,
                            hasImage:
                                -1 !== Object.keys(parentElement).indexOf('children') &&
                                0 === parentElement.children.length,
                            modelMethod:
                                !parentElement.setModelMethod
                                    ? 0
                                    : parentElement.setModelMethod,
                            modelType:
                                !parentElement.modelType
                                    ? 0
                                    : parentElement.modelType,
                            actionNode: false,
                            matchType: 0,
                            parents: parentElement.parents,
                            detailDetection: [0, 0, ''],
                        });
                    }
                } else {
                    let _index = newFindNode.findIndex((item) => item.id === parentElement.id);
                    let ele = deepcopy(newFindNode)[_index];
                    newFindNode.splice(_index, 1);
                    newFindNode.unshift(ele);
                }
            }
            parentPath.pop();
            for (let item of newFindNode) {
                if (!item.modelType) {
                    item.modelType = 0;
                }
            }
            // 用于前端展示
            if ('click' === method) {
                step.stepInfo.params.pathSummary = pathIndexList;
            }
            let {path} = getSingleElementInfo(parentPath, step, true);
            // 获取多控件findNode
            step.stepInfo.params.findInfo.findPath = [...path];
            step.stepInfo.params.findInfo.findNode = [...newFindNode];
        } else if (4 === step.stepInfo.params.findType) {
            let {targetElement} = getSingleElementInfo(pathIndexList, step, true);
            let newFindNode = deepcopy(step.stepInfo.params.findInfo.findNode);
            if ('click' === method) {
                if (1 === newFindNode.length) {
                    if (targetElement.id !== newFindNode[0].id && newFindNode[0].actionNode) {
                        newFindNode = [];
                    }
                } else {
                    for (let item of newFindNode) {
                        if (targetElement.id === item.id) {
                            item.actionNode = true;
                        }
                        if (targetElement.id !== item.id) {
                            item.actionNode = false;
                        }
                    }
                }
            }
            // 判断选择控件 是否已存在
            let existIndex = -1;
            for (let index = 0; index < newFindNode.length; index++) {
                if (newFindNode[index].id === targetElement.id) {
                    existIndex = index;
                    break;
                }
            }
            if ('contextmenu' !== method && -1 === existIndex) {
                newFindNode.push({
                    id: targetElement.id,
                    hasImage:
                        -1 !== Object.keys(targetElement).indexOf('children') &&
                        0 === targetElement.children.length,
                    matchType: 0,
                    modelMethod: 1,
                    modelType: 1,
                    actionNode: 'click' === method,
                    parents: targetElement.parents,
                    detailDetection: 'TEXT' === targetElement.type ? [1, 2, targetElement.text] : [0, 0, ''],
                });
            }

            let pathList = getPath(
                [domDetail],
                newFindNode.map((item) => item.id)
            );
            let parentPath = longestCommonPrefix(pathList)
                .split('-')
                .filter((item) => '' !== item)
                .map((item) => parseInt(item, 10));
            parentPath.pop();
            // 用于前端展示
            if ('click' === method) {
                step.stepInfo.params.pathSummary = pathIndexList;
            }
            let {path} = getSingleElementInfo(parentPath, domDetail, true);
            // 获取多控件findNode
            step.stepInfo.params.findInfo.findPath = [...path];
            step.stepInfo.params.findInfo.findNode = [...newFindNode];

        } else if (5 === step.stepInfo.params.findType && 4 !== step.stepInfo.params.findInfo.domType) {
            let {targetElement} = getSingleElementInfo(pathIndexList, domDetail, true);
            let newFindNode = deepcopy(step.stepInfo.params.findInfo.findNode);
            // 单选
            // 判断选择控件 是否已存在
            let existIndex = -1;
            for (let index = 0; index < newFindNode.length; index++) {
                if (newFindNode[index].id === targetElement.id) {
                    existIndex = index;
                    break;
                }
            }
            if ('click' === method) {
                if (-1 === existIndex) {
                    newFindNode.push({
                        id: targetElement.id,
                        actionNode: false,
                        parents: targetElement.parents,
                        detailDetection: [0, 0, ''],
                    });
                } else {
                    if (newFindNode[existIndex].actionNode) {
                        step.stepInfo.params.pathSummary = [];
                    }
                    newFindNode.splice(existIndex, 1);
                }
            }
            if ('contextmenu' === method) {

            }
            step.stepInfo.params.findInfo.findNode = [...newFindNode];
        }
        await handleUpdateStep(step);
    } catch (err) {
        message.warning(err.message ? err.message : err);
    }
};
