import {FileWordOutlined} from '@ant-design/icons';
import {Input, Select} from 'antd';

export default ({editType, currentStep, element, handleUpdateStep, index}) => {
    let options = [
        {
            key: 0,
            value: 0,
            label: '无'
        },
        {
            key: 1,
            value: 1,
            label: '包含'
        },
        {
            key: 2,
            value: 2,
            label: '相似'
        }
    ];
    let optionJsx = [];
    for (let item of options) {
        optionJsx.push(
            <Select.Option
                key={item.key}
                value={item.value}
                label={item.label}
            >
                {item.label}
            </Select.Option>
        );
    }
    return (
        <div
            style={{
                fontSize: 12
            }}
        >
            <div
                style={{
                    display: 'inline-block',
                    height: 24,
                    padding: '0 5px',
                    backgroundColor: '#fff',
                    border: '1px solid #d9d9d9',
                    textAlign: 'center',
                    fontWeight: 500,
                    width: '30%',
                    marginTop: 3
                }}
            >
                <FileWordOutlined
                    style={{
                        marginRight: 2
                    }}
                />
                文本
            </div>
            <Select
                style={{width: '30%'}}
                size='small'
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                optionLabelProp='label'
                value={element.textDetection[0]}
                onChange={(value) => {
                    currentStep.stepInfo.params.findInfo.findNode[index].textDetection[0] = Number(value);
                    handleUpdateStep(currentStep);
                }}
            >
                {optionJsx}
            </Select>
            <Input
                style={{width: '40%'}}
                size='small'
                className='inputEditor'
                min={0}
                placeholder='请输入'
                disabled={0 === element.textDetection[0] || ['readonly', 'debug', 'execute'].includes(editType)}
                value={0 === element.textDetection[0] ? '仅结构校验' : element.textDetection[1]}
                onChange={e => {
                    currentStep.stepInfo.params.findInfo.findNode[index].textDetection[1] = e.target.value;
                    handleUpdateStep(currentStep);
                }}
            />
        </div>
    );
};