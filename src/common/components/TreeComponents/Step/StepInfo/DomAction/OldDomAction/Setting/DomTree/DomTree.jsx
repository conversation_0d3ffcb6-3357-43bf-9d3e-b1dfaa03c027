import {Divider} from 'antd';
import BatDomTree from './OldBatDomTree';
import BatDomPlusTree from './BatDomPlusTree';

const deepcopy = obj => {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    let newObj = {};
    if (Array.isArray(obj)) {
        newObj = obj.map(item => deepcopy(item));
    } else {
        Object.keys(obj).forEach((key) => {
            return newObj[key] = deepcopy(obj[key]);
        });
    }
    return newObj;
};


export default (props) => {
    const {currentStep} = props;
    return (
        <div>
            <div
                style={{
                    maxHeight: window.innerHeight - 300,
                    overflow: 'scroll',
                }}
            >
                {-1 === [1, 4].indexOf(currentStep.stepInfo.params.findType) ? (
                    <BatDomTree {...props} />
                ) : null}
                {1 === currentStep.stepInfo.params.findType ? (
                    <BatDomPlusTree {...props} />
                ) : null
                }
            </div>
        </div>
    );
};
