import {useState, useEffect} from 'react';
import {Tree} from 'antd';
import styles from '../Setting.module.less';
import {deepcopy} from 'COMMON/components/TreeComponents/Step/utils';

const getTreeData = (dom, nowIndexList = []) => {
    return dom.map((element, index) => {
        nowIndexList.push(index);
        let node = {
            key: nowIndexList.join('-'),
            type: !element.type ? '未知' : element.type,
            title: element.type,
            disabled: !element?.rect,
            children: 'object' === typeof element.children && 0 !== element.children.length ?
                getTreeData(element.children, nowIndexList) : []
        };
        nowIndexList.pop();
        return node;
    });
};

export default ({
    currentStep, setSelectPath, domDetail
}) => {
    const [treeExpendList, setTreeExpendList] = useState([]);
    const [selectedKeys, setSelectedKeys] = useState([]);

    useEffect(() => {
        setSelectedKeys([currentStep.stepInfo.params.pathSummary.join('-')]);
    }, [currentStep]);

    return (
        <Tree
            className={styles.dom_tree}
            style={{maxHeight: window.innerHeight - 550}}
            showLine
            showIcon={false}
            treeData={getTreeData([deepcopy(domDetail)])}
            expandedKeys={treeExpendList}
            selectedKeys={selectedKeys}
            filterTreeNode={(node) => {
                return 0 !== selectedKeys.length && 0 === selectedKeys[0].indexOf(node.key);
            }}
            onExpand={(expandedKeys) => {
                setTreeExpendList(expandedKeys);
            }}
            onSelect={(selectedKeys, {selected}) => {
                if (selected) {
                    let pathIndexList = selectedKeys[0].split('-').map(item => parseInt(item, 10));
                    // 重新计算选择元素
                    setSelectPath(pathIndexList);
                }
            }}
        />
    );
};