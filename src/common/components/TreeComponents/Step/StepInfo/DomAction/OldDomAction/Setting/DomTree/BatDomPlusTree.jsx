import {useState, useEffect} from 'react';
import {Dropdown, Radio, Tree, Tooltip} from 'antd';
import TextDetection from './TextDetection';
import DetailDetection from './DetailDetection';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import {getSingleElementInfo, getVisionModelNodeInfo, longestCommonPrefix} from '../../utils';
import styles from '../Setting.module.less';
import {selectDom} from './selecDom';

const deepcopy = obj => {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    let newObj = {};
    if (Array.isArray(obj)) {
        newObj = obj.map(item => deepcopy(item));
    } else {
        Object.keys(obj).forEach((key) => {
            return newObj[key] = deepcopy(obj[key]);
        });
    }
    return newObj;
};

const changeNodeToKey = (dom, nodeIdList, checkedKeys = [], nowIndexList = []) => {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        let _index = nodeIdList.indexOf(element.id);
        if (-1 !== _index) {
            checkedKeys.push(nowIndexList.join('-'));
        }
        let flag = true;
        if (undefined !== element?.setModelMethod && 1 === element?.setModelMethod) {
            flag = false;
            if ('object' === typeof element.ui_children && 0 !== element.ui_children.length) {
                changeNodeToKey(element.ui_children, nodeIdList, checkedKeys, nowIndexList);
            }
        }
        if (flag && 'object' === typeof element.children && 0 !== element.children.length) {
            changeNodeToKey(element.children, nodeIdList, checkedKeys, nowIndexList);
        }
        nowIndexList.pop();
    });
    return checkedKeys;
};

function BatDomPluseTree(props) {
    const {editType, currentStep, handleUpdateStep,
        domDetail, selectPath, handleUpdateSelectPath} = props;
    const [treeExpendList, setTreeExpendList] = useState([]);
    const [checkedKeys, setCheckedKeys] = useState([]);
    const [selectedKeys, setSelectedKeys] = useState([]);

    useEffect(() => {
        setSelectedKeys([selectPath.join('-')]);
        // // 转换id 对应 key
        let nodeIdList = currentStep.stepInfo.params.findInfo.findNode.map((item) => item.id);
        let newCheckedKeys = changeNodeToKey([deepcopy(domDetail)], nodeIdList);
        setCheckedKeys(newCheckedKeys);
    }, [currentStep, selectPath]);

    const onCheck = (checkedKeys, {checked, checkedNodes, node}) => {
        let newCurrentStep = {...currentStep};
        let newCheckedKeys = checkedKeys.checked;
        let newFindNode = [];
        let oldFindNode = deepcopy(newCurrentStep.stepInfo.params.findInfo.findNode);
        // 若取消
        if (!checked && 0 !== oldFindNode.length) {
            let nodeId = node.id;
            // if: 删掉父控件所有子控件
            // else: 删掉当前控件
            if (newCurrentStep.stepInfo.params.findInfo.findNode[0].id === nodeId) {
                newCheckedKeys = [];
                newCurrentStep.stepInfo.params.findInfo.findNode = [];
                newCurrentStep.stepInfo.params.pathSummary = [];
                newCurrentStep.stepInfo.params.findInfo.findType = 0;
                delete newCurrentStep.stepInfo.params.findInfo.scrollDirection;
                newCurrentStep.stepInfo.params.findInfo.screenCount = 1;
                newCurrentStep.stepInfo.params.findInfo.findPath = [];
            } else {
                let _index = oldFindNode.findIndex((item) => item.id === nodeId);
                if (-1 !== _index) {
                    if (oldFindNode[_index].stepInfo) {
                        newCurrentStep.stepInfo.params.pathSummary = [];
                    }
                    // 判断是否存在视觉建模节点被取消
                    // 若存在则删掉该节点和ui节点
                    if (1 === oldFindNode[_index].modelMethod) {
                        let _oldFindNode = oldFindNode.filter((item) => isNumber(item.id));
                        oldFindNode = _oldFindNode;
                        let _newIndex = oldFindNode.findIndex((item) => item.id === nodeId);
                        if (-1 !== _newIndex) {
                            oldFindNode.splice(_newIndex, 1);
                        }
                    } else {
                        oldFindNode.splice(_index, 1);
                    }
                }
                newCurrentStep.stepInfo.params.findInfo.findNode = oldFindNode;
            }
            handleUpdateStep(newCurrentStep);
            return false;
        }
        // 单选
        // 若选上
        if (checked && !isNumber(node.id)) {
            // 看当前findNode里面是否存在视觉建模节点
            // 若无 则获取视觉建模节点信息
            let modelingNode = [];
            let _oldFindNode = [];
            let _checkedNodes = [];
            let _delId = -1;
            let _oldModelingNodeFlag = false;
            for (let item of oldFindNode) {
                if (1 === item.modelMethod) {
                    _oldModelingNodeFlag = true;
                }
                if (isNumber(item.id)) {
                    _oldFindNode.push(item);
                } else {
                    _delId = item.id;
                }
            }
            if (!_oldModelingNodeFlag) {
                modelingNode = getVisionModelNodeInfo([domDetail]);
            }
            let _checkedNodesFlag = false;
            for (let item of checkedNodes) {
                if (_delId !== item.id) {
                    _checkedNodes.push(item);
                }
                if (0 !== modelingNode.length && item.id === modelingNode[0].id) {
                    _checkedNodesFlag = true;
                }
            }
            if (!_checkedNodesFlag && 0 !== modelingNode.length) {
                _checkedNodes.push(modelingNode[0]);
                if (-1 === newCheckedKeys.indexOf(modelingNode[0].key)) {
                    newCheckedKeys.push(modelingNode[0].key);
                }
            }
            // 获取视觉建模的控件信息
            oldFindNode = _oldFindNode;
            checkedNodes = _checkedNodes;
        }
        let oldNodeIdList = oldFindNode.map((item) => item.id);
        // 获取当前选中控件作为
        checkedNodes.forEach((element) => {
            let nodeIndex = oldNodeIdList.indexOf(element.id);
            if (-1 === nodeIndex) {
                if (0 !== oldFindNode.length && oldFindNode[0].textDetection) {
                    newFindNode.push({
                        id: element.id,
                        textDetection: [0, ''],
                        parents: element.parents,
                        actionNode: false,
                    });
                } else if (0 !== oldFindNode.length && !oldFindNode[0]?.modelMethod) {
                    newFindNode.push({
                        id: element.id,
                        hasImage: true,
                        matchType: 0,
                        detailDetection: [0, 0, ''],
                        parents: element.parents,
                        actionNode: false,
                    });
                } else {
                    newFindNode.push({
                        id: element.id,
                        hasImage:
                            -1 !== Object.keys(element).indexOf('children') &&
                            0 === element.children.length,
                        modelMethod:
                            !element?.setModelMethod ? 0 : element?.setModelMethod,
                        modelType: !element?.modelType ? 0 : element.modelType,
                        matchType: 0,
                        detailDetection: [0, 0, ''],
                        parents: element.parents,
                        actionNode: element.modelType && 1 === element.modelType,
                    });
                    if (element.modelType && 1 === element.modelType) {
                        newCurrentStep.stepInfo.params.pathSummary =
                            element.key.split('-').map(item => parseInt(item, 10));
                    }
                }
            } else {
                newFindNode.push(oldFindNode[nodeIndex]);
            }
        });

        // 默认选上父控件逻辑
        let parentPath = longestCommonPrefix(newCheckedKeys)
            .split('-')
            .filter((item) => '' !== item)
            .map((item) => parseInt(item, 10));
        // 最外元素，调到最前面
        if (newFindNode.length > 1) {
            let nodeIndex = newCheckedKeys.indexOf(parentPath.join('-'));
            let {targetElement: parentElement} = getSingleElementInfo(parentPath, domDetail, true);
            if (-1 === nodeIndex) {
                if (newFindNode[0].textDetection) {
                    newFindNode.unshift({
                        id: parentElement.id,
                        actionNode: false,
                        parents: parentElement.parents,
                        textDetection: [0, ''],
                    });
                } else if (!newFindNode[0]?.modelMethod) {
                    newFindNode.unshift({
                        id: parentElement.id,
                        hasImage: true,
                        matchType: 0,
                        actionNode: false,
                        parents: parentElement.parents,
                        detailDetection: [0, 0, ''],
                    });
                } else {
                    newFindNode.unshift({
                        id: parentElement.id,
                        hasImage:
                            -1 !== Object.keys(parentElement).indexOf('children') &&
                            0 === parentElement.children.length,
                        matchType: 0,
                        modelMethod:
                            !parentElement.setModelMethod
                                ? 0
                                : parentElement.setModelMethod,
                        modelType:
                            !parentElement.modelType ? 0 : parentElement.modelType,
                        actionNode: false,
                        parents: parentElement.parents,
                        detailDetection: [0, 0, ''],
                    });
                }
            } else {
                let _index = newFindNode.findIndex((item) => item.id === parentElement.id);
                let ele = deepcopy(newFindNode)[_index];
                newFindNode.splice(_index, 1);
                newFindNode.unshift(ele);
            }
        }
        parentPath.pop();
        let {path} = getSingleElementInfo(parentPath, newCurrentStep, true);
        // 获取多控件findNode
        newCurrentStep.stepInfo.params.findInfo.findPath = path;
        //  获取隐藏控件
        let oldCheckedNodesIdList = checkedNodes.map((item) => item.id);
        oldFindNode.forEach((element) => {
            let nodeIndex = oldCheckedNodesIdList.indexOf(element.id);
            if (-1 === nodeIndex) {
                newFindNode.push(element);
            }
        });
        newCurrentStep.stepInfo.params.findInfo.findNode = newFindNode;
        setCheckedKeys(newCheckedKeys);
        handleUpdateStep(newCurrentStep);
    };

    const getOperatorJsx = (currentStep, element) => {
        let _element = null;
        let _index = -1;
        currentStep.stepInfo.params.findInfo.findNode.forEach((item, index) => {
            if (item.id === element.id) {
                _element = item;
                _index = index;
            }
        });
        return (
            <>
                {
                    element?.setModelMethod && 1 === element?.setModelMethod ?
                        <Radio style={{marginLeft: 5, fontSize: 12}} checked>
                            视觉建模
                        </Radio> : null
                }
                {
                    _element !== null && _element.actionNode ?
                        <Radio style={{marginLeft: 5, fontSize: 12}} checked>
                            选为操作区域
                        </Radio> : null
                }
                {
                    _element !== null && _element.textDetection ?
                        <TextDetection
                            element={_element}
                            domDetail={domDetail}
                            index={_index}
                            currentStep={currentStep}
                            handleUpdateStep={handleUpdateStep}
                        /> : null
                }
                {
                    _element !== null && !_element.modelType || 1 !== _element.modelType ?
                        <DetailDetection
                            element={_element}
                            index={_index}
                            domDetail={domDetail}
                            currentStep={currentStep}
                            handleUpdateStep={handleUpdateStep}
                        /> : null
                }
            </>
        );
    };
    let hasUiNode = getVisionModelNodeInfo([domDetail]);

    const getTreeData = (dom, nowIndexList = []) => {
        return dom.map((element, index) => {
            nowIndexList.push(index);
            element.key = nowIndexList.join('-');
            let children = [];
            // 判断是否为视觉建模
            let flag = true;
            if (undefined !== element?.setModelMethod && 1 === element?.setModelMethod) {
                flag = false;
                if ('object' === typeof element.ui_children && 0 !== element.ui_children.length) {
                    children = getTreeData(element.ui_children, nowIndexList);
                }
            }
            if (flag && 'object' === typeof element.children && 0 !== element.children.length) {
                children = getTreeData(element.children, nowIndexList);
            }
            const onClick = ({domEvent, key}) => {
                domEvent.stopPropagation();
                currentStep.stepInfo.params.findInfo.findNode.forEach((item) => {
                    if (item.id === element.id) {
                        if ('0' === key) {
                            item.actionNode = true;
                            currentStep.stepInfo.params.pathSummary = element.key.split('-')
                                .map((item) => parseInt(item, 10));
                        }
                        if ('1' === key) {
                            item.modelMethod = 0 === item.modelMethod ? 1 : 0;
                        }
                    } else {
                        if ('0' === key) {
                            item.actionNode = false;
                        }
                    }
                });
                handleUpdateStep({...currentStep});
            };
            let title = [];
            let items = [];
            if (!element.modelType && domDetail.dom_info) {
                let version = domDetail.dom_info.version.split('.')[0];
                version = version.substring(1);
                if (5 <= Number(version) && undefined !== element?.setModelMethod &&
                    1 === element?.setModelMethod) {
                    items.push({
                        label: '控件建模',
                        key: '1',
                    });
                }
            }
            currentStep.stepInfo.params.findInfo.findNode.forEach((item) => {
                if (item.id === element.id) {
                    items.unshift({
                        label: '设置为操作区域',
                        key: '0',
                    });
                }
            });
            if (0 === items.length) {
                title = (<span>{element.type}</span>);
            } else {
                title = (
                    <>
                        <Dropdown
                            menu={{
                                items,
                                onClick,
                            }}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            trigger={['contextMenu']}
                        >
                            <Tooltip title='右键设置' placement='right'>
                                <span>{element.type}</span>
                            </Tooltip>
                        </Dropdown>
                        {getOperatorJsx(currentStep, element)}
                    </>
                );
            }
            let node = {
                id: !element.id ? -1 : element.id,
                key: nowIndexList.join('-'),
                type: !element.type ? '未知' : element.type,
                title,
                parents: element.parents,
                setModelMethod: !element?.setModelMethod ? 0 : element?.setModelMethod,
                disabled: (0 !== hasUiNode.length && isNumber(element.id)) || !element?.rect,
                children
            };
            nowIndexList.pop();
            return node;
        });
    };

    return (
        <Tree
            className={styles.dom_tree}
            style={{maxHeight: window.innerHeight - 550}}
            showLine
            checkable
            checkStrictly='true'
            showIcon='false'
            treeData={getTreeData([deepcopy(domDetail)])}
            expandedKeys={treeExpendList}
            selectedKeys={selectedKeys}
            checkedKeys={checkedKeys}
            filterTreeNode={(node) => {
                if (0 !== selectedKeys.length) {
                    return 0 === selectedKeys[0].indexOf(node.key);
                }
            }}
            onExpand={(expandedKeys) => {
                setTreeExpendList(expandedKeys);
            }}
            onSelect={(selectedKeys, {selected}) => {
                if (selected) {
                    let pathIndexList = selectedKeys[0]
                        .split('-')
                        .map((item) => parseInt(item, 10));
                    // 重新计算选择元素
                    handleUpdateSelectPath(pathIndexList);
                    setSelectedKeys(selectedKeys);
                    if (1 !== currentStep.stepInfo.params.findType) {
                        selectDom({
                            pathIndexList,
                            domDetail,
                            currentStep
                        });
                    }
                }
            }}
            onCheck={onCheck}
        />
    );
};

export default BatDomPluseTree;
