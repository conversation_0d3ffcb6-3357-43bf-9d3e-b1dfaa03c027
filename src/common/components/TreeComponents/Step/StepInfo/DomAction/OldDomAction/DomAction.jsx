import classnames from 'classnames';
import {useEffect, useState} from 'react';
import StepOperator from './StepOperator';
import ImageAction from './ImageAction';
import {editNative} from './editNative';
import styles from './DomAction.module.less';
import {message} from 'antd';

function DomAction(props) {
    const {descExtra,
        editType, currentStep, currentNode, osType, handleUpdateStep, templateId, handleUpdateNode} = props;
    const [innerHeight, setInnerHeight] = useState(window.innerHeight);
    const [innerWidth, setInnerWidth] = useState(window.innerWidth);

    // dom 解析
    const [domDetail, setDomDetail] = useState({});
    useEffect(() => {
        async function func() {
            try {
                let domInfo = currentStep?.stepInfo?.params?.dom;
                // 解析url
                if (typeof (domInfo) === 'string' && domInfo?.startsWith('http')) {
                    let res = await fetch(domInfo).then(response => response.json());
                    setDomDetail(res);
                } else {
                    setDomDetail(domInfo);
                }
            } catch (error) {
                console.log(error);
                message.error(error);
            }
        }
        func();
    }, [currentStep?.stepInfo?.params?.dom]);

    // 页面大小变动后，图片resize
    useEffect(() => {
        window.onresize = () => {
            setInnerHeight(window.innerHeight);
            setInnerWidth(window.innerWidth);
        };
    });

    // 单击事件监听
    useEffect(() => {
        window.addEventListener('click', clickAction);
        return () => window.removeEventListener('click', clickAction);
    }, [currentStep, domDetail]);

    // 单击事件监听
    useEffect(() => {
        window.addEventListener('contextmenu', contextMenuAction);
        return () => window.removeEventListener('contextmenu', contextMenuAction);
    }, [currentStep, domDetail]);

    const contextMenuAction = (e) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            return false;
        }
        if (e.target.className === editType + 'StepImage') {
            let ratio = window.devicePixelRatio || 1;
            editNative({
                step: currentStep,
                domDetail,
                editType,
                osType,
                domDetail,
                currentNode,
                handleUpdateNode,
                method: 'contextmenu',
                templateId,
                handleUpdateStep,
                xPrecent: (e.offsetX * ratio) / e.target.width,
                yPrecent: (e.offsetY * ratio) / e.target.height,
                ...props
            });
        }
    };

    const clickAction = (e) => {
        if (['readonly', 'debug', 'execute'].includes(editType)) {
            return false;
        }
        if (editType + 'StepImage' === e.target.className) {
            let ratio = window.devicePixelRatio || 1;
            editNative({
                step: currentStep,
                editType,
                domDetail,
                osType,
                domDetail,
                method: 'click',
                templateId,
                currentNode,
                handleUpdateNode,
                handleUpdateStep,
                xPrecent: (e.offsetX * ratio) / e.target.width,
                yPrecent: (e.offsetY * ratio) / e.target.height,
                ...props
            });
        }
    };
    return (
        <div className={styles.action}>
            {
                -1 === ['readonly', 'debug', 'execute'].indexOf(editType) &&
                <StepOperator
                    {...props}
                    domDetail={domDetail}
                    innerHeight={innerHeight}
                    innerWidth={innerWidth}
                    setInnerHeight={setInnerHeight}
                    setInnerWidth={setInnerWidth}
                />
            }
            {descExtra}
            <div
                className={classnames(styles.defaultAction,
                    {[styles.domAction]: -1 === ['execute'].indexOf(editType)}
                )}
            >
                <ImageAction
                    {...props}
                    domDetail={domDetail}
                    innerHeight={innerHeight}
                    innerWidth={innerWidth}
                    setInnerHeight={setInnerHeight}
                    setInnerWidth={setInnerWidth}
                />
            </div>
        </div>
    );
};

export default DomAction;
