.stepAddParams {
    position: relative;
    display: inline-block;
    background-color: #e8efff;
    color: #3686f6;
    border: 1px solid #3686f6;
    padding: 0 8px;
    width: 31%;
    margin: 1%;
    border-radius: 2px;
}

.stepAddParams:hover {
    background-color: #3686f6;
    color: #fff;
}

.stepAddParamsIcon {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
}

.settingLeft,
.settingRight {
    float: left;
}

.settingRight {
    width: 400px;
    margin-left: 40px;
}

.paramsSetting {
    position: relative;
    padding: 4px;
    margin: 10px 0;
    background-color: #f5f5f5;
    border-radius: 3px;

    span {
        margin-left: 3px;
        font-size: 14px;
        font-weight: 600;
    }

    .paramsSwitch {
        position: absolute;
        top: 50%;
        right: 5px;
        transform: translateY(-50%);
    }

    .paramsSwitchInfo {
        float: right;
        margin-right: 40px;
    }
}

.paramsDescInfo {
    color: #777;
    font-size: 12px;
}

.paramsDesc {
    color: #f74740;
}

.paramsSettingIcon {
    display: inline-block;
    padding: 1px 5px;
    font-size: 12px;
    background: #3e91f7;
    color: #fff;
    cursor: pointer;
    border-radius: 3px;
}

.paramsSettingIcon:hover {
    box-shadow: rgb(24 144 255 / 12%) 0px 0px 0px 3px;
}

.paramsItem {
    margin-bottom: 10px;
    overflow: hidden;
}

.paramsSwitch {
    float: left;
    width: 100px;
}

.paramsSwitchText {
    float: left;
}

.paramsLeft {
    width: 100px;
    float: left;
}

.paramsCenter {
    width: calc(100% - 150px);
    float: left;
}

.paramsRight {
    float: left;
    width: 30px;
    margin-left: 10px;
}

.nodeParams {
    width: 100%;
    margin-left: 80px;
    margin-bottom: 5px;
    cursor: pointer;
}

.nodeParamIndex {
    display: inline-block;
    width: 60px;
    margin-right: 5px;
    background-color: #3686f6;
    color: #fff;
    border-radius: 3px;
    text-align: center;
}

.nodeParamText {
    display: inline-block;
    width: 60px;
    margin-right: 5px;
}

.nodeParamSelect {
    width: 70px;
}

.nodeParamSelectType {
    width: 100px;
}

.nodeParamInput {
    width: calc(100% - 350px);
}

.nodeParamsDelete {
    margin-left: 2px;
    color: red;
}