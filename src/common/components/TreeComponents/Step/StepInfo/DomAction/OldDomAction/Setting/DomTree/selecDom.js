export function selectDom({pathIndexList, currentStep, domDetail}) {
    // 处理找到的控件
    try {
        let selectPath = [];
        // 处理找到的控件
        let expandedKeys = [];
        let key = '';
        for (let index of pathIndexList) {
            if ('' === key) {
                key += `${index}`;
            }
            else {
                key += `-${index}`;
            }
            expandedKeys.push(key);
        }
        if (showTreeModal && 1 === currentStep.stepInfo.params.findType) {
            selectPath = pathIndexList;
            return {selectPath, expandedKeys};
        } else {
            // 重新初始化控件路径数据
            currentStep.stepInfo.params.pathSummary = pathIndexList;
        }
        // update步骤信息
        if (0 === currentStep.stepInfo.params.findType) {
            // 获取findPath根路径
            let rootPathIndexList = deepcopy(pathIndexList);
            let path = [];
            let targetElement = deepcopy(domDetail);
            rootPathIndexList.pop();
            if (0 !== rootPathIndexList.length) {
                let res = initBatElement.recursionDomElement(
                    [domDetail],
                    deepcopy(rootPathIndexList)
                );
                path = res.path;
                targetElement = res.targetElement;
            }
            currentStep.stepInfo.params.findInfo.findPath = path;
            // 获取findNode
            let pathLength = pathIndexList.length - path.length;
            let leafPathIndexList = deepcopy(pathIndexList);
            let leafPath = leafPathIndexList.splice(path.length, pathLength);
            let nodePath = [];
            if (
                0 !== rootPathIndexList.length &&
                'object' === typeof targetElement.children &&
                0 !== targetElement.children.length
            ) {
                let res = initBatElement.recursionDomElement(targetElement.children, deepcopy(leafPath));
                nodePath = res.path;
            }
            currentStep.stepInfo.params.findInfo.findNode = {
                rect: targetElement.rect,
                nodeType: targetElement.nodeType,
                nodeInfo: nodePath,
                verifyInfo: {}
            };
        }
        if (2 === currentStep.stepInfo.params.findType) {
            let rootPathIndexList = deepcopy(pathIndexList);
            let targetElement = deepcopy(domDetail);
            let _index = 0;
            if (0 !== rootPathIndexList.length) {
                let res = initBatElement.recursionDomElement(
                    [domDetail],
                    deepcopy(rootPathIndexList)
                );
                targetElement = res.targetElement;
                _index = -1;
                domDetail.children.forEach((element, index) => {
                    if (index <= rootPathIndexList[rootPathIndexList.length - 1]) {
                        if (targetElement.info && element.info.value === targetElement.info.value) {
                            _index += 1;
                        }
                    }
                });
            }
            currentStep.stepInfo.params.findInfo.findText = targetElement.info ? targetElement.info.label : '';
            currentStep.stepInfo.params.findInfo.index = _index;
        }
        if (3 === currentStep.stepInfo.params.findType) {
            let rootPathIndexList = deepcopy(pathIndexList);
            let targetElement = deepcopy(domDetail);
            let _index = 0;
            if (0 !== rootPathIndexList.length) {
                let res = initBatElement.recursionDomElement(
                    [domDetail],
                    deepcopy(rootPathIndexList)
                );
                targetElement = res.targetElement;
                _index = -1;
                domDetail.children.forEach((element, index) => {
                    if (index <= rootPathIndexList[rootPathIndexList.length - 1]) {
                        if (targetElement.info && element.info.value === targetElement.info.value) {
                            _index += 1;
                        }
                    }
                });
            }
            currentStep.stepInfo.params.findInfo.icon = targetElement.info ? targetElement.info.value : '';
            currentStep.stepInfo.params.findInfo.index = _index;
        }
        return {expandedKeys, selectPath};
    } catch (err) {
        message.warning(err.message);
    }
};