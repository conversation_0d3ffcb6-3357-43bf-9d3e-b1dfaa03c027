import {useState, useEffect, useRef} from 'react';
import {isEmpty} from 'lodash';
import {Spin} from 'antd';
import {
    getVisionModelNodeInfo, findSelectElements, getSingleElementInfo,
    getElementsBounds, longestCommonPrefix, searchNewElementByBounds
} from './utils';
import styles from './DomAction.module.less';

const deepcopy = obj => {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    let newObj = {};
    if (Array.isArray(obj)) {
        newObj = obj.map(item => deepcopy(item));
    } else {
        Object.keys(obj).forEach((key) => {
            return newObj[key] = deepcopy(obj[key]);
        });
    }
    return newObj;
};


const getCanvasSize = (currentNode, currentStep, innerHeight, innerWidth, editType) => {
    const imgScreen = Object.keys(currentStep.stepInfo.params.deviceInfo.screenSize).indexOf('rotation') !== -1 &&
        [90, 270].indexOf(currentStep.stepInfo.params.deviceInfo.screenSize.rotation) !== -1;

    let screenWidth = currentStep?.stepInfo.params.deviceInfo.screenSize.width;
    let screenHeight = currentStep?.stepInfo.params.deviceInfo.screenSize.height;
    // 限制最小屏宽高
    screenWidth = screenWidth > 62 ? screenWidth : 160;
    screenHeight = screenHeight > 115 ? screenHeight : 300;
    let imgHeight = -1 === ['readonly', 'debug', 'execute'].indexOf(editType) ? innerHeight - 200 : innerHeight - 350;
    if ([0, 2].includes(currentNode?.adoptStatus)) {
        imgHeight -= 20;
    }
    // 新版本的屏幕宽高
    if (window.location.hash.includes('/integration/')) {
        imgHeight -= 90;
    }
    let imageScale = imgHeight / screenHeight;
    let imgWidth = imageScale * screenWidth;

    if (imgScreen) {
        screenWidth = currentStep?.stepInfo.params.deviceInfo.screenSize.height;
        screenHeight = currentStep?.stepInfo.params.deviceInfo.screenSize.width;
        imageScale = (((innerWidth - 20) * 0.6 - 50) * 10) / 24 / screenWidth;
        imgWidth = (((innerWidth - 20) * 0.6 - 50) * 10) / 24;
        imgHeight = imageScale * screenHeight;
    }
    return {imgHeight, imgWidth, imageScale};
};

const getCheckedKeys = (dom, nodeIdList, checkedKeys = [], nowIndexList = []) => {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (-1 !== nodeIdList.indexOf(element.id)) {
            checkedKeys.push(nowIndexList.join('-'));
        }
        let flag = true;
        if (undefined !== element?.setModelMethod && 1 === element?.setModelMethod) {
            flag = false;
            if (-1 !== Object.keys(element).indexOf('ui_children') && 0 !== element.ui_children.length) {
                getCheckedKeys(element.ui_children, nodeIdList, checkedKeys, nowIndexList);
            }
        }
        if (flag && -1 !== Object.keys(element).indexOf('children') && 0 !== element.children.length) {
            getCheckedKeys(element.children, nodeIdList, checkedKeys, nowIndexList);
        }
        nowIndexList.pop();
    });
    return checkedKeys;
};


function ImageAction(props) {
    const {currentStep, currentNode, handleUpdateStep,
        innerHeight,
        innerWidth,
        domDetail, templateId, osType, selectPath, editType} = props;
    const [showDropdown, setShowDropdown] = useState(false);
    const [dropdownLeft, setDropdownLeft] = useState(0);
    const [dropdownTop, setDropdownTop] = useState(0);
    const [dropdownWidth, setDropdownWidth] = useState(0);
    const [dropdownHeight, setDropdownHeight] = useState(0);
    const [clickElement, setClickElement] = useState(null);
    const [loading, setLoading] = useState(true);
    const canvas = useRef();

    let element;
    let checkElement;
    let selectElements = [];
    let parentElement = {};
    let checkedKeys = [];
    let parentPath = [];
    if (1 === currentStep.stepInfo.params.findType && 1 === currentStep.stepInfo.params.findInfo.findType) {
        // 获取checkedKeys 方便寻找父控件
        let nodeIdList = currentStep.stepInfo.params.findInfo.findNode.map((item) => item.id);
        checkedKeys = getCheckedKeys([deepcopy(domDetail)], nodeIdList);
        let commonPath = longestCommonPrefix(checkedKeys);
        let commonArr = commonPath
            .split('-')
            .filter((item) => '' !== item)
            .map((item) => parseInt(item, 10));
        if (-1 !== checkedKeys.indexOf(commonArr.join('-'))) {
            commonArr.pop();
        }
        parentPath = commonArr;
        // 递归检索选择控件的路径
        let {targetElement} = getSingleElementInfo(parentPath, domDetail, true);
        parentElement = targetElement;
    }
    if (-1 !== [2].indexOf(currentStep.stepInfo.type)) {
        element = {
            children: [domDetail],
            id: -1
        };
        // 获取 点击元素
        if (undefined !== currentStep.stepInfo.params.pathSummary && undefined !== element) {
            for (let pathIndex of currentStep.stepInfo.params.pathSummary) {
                // 判断是否为视觉建模
                let flag = true;
                if (
                    undefined !== element?.setModelMethod && 1 === element?.setModelMethod
                ) {
                    flag = false;
                    if (undefined !== element.ui_children && 0 !== element.ui_children.length) {
                        element = element.ui_children[pathIndex];
                    }
                }
                if (flag && undefined !== element?.children && 0 !== element?.children.length) {
                    element = element.children[pathIndex];
                }
            }
        }

        // 获取 查看元素
        checkElement = {
            children: [domDetail],
            id: -1
        };
        if (
            -1 !== [1, 5].indexOf(currentStep.stepInfo.params.findType) &&
            undefined !== selectPath &&
            undefined !== checkElement
        ) {
            for (let pathIndex of selectPath) {
                // 判断是否为视觉建模
                let flag = true;
                if (
                    checkElement && undefined !== checkElement.setModelMethod && 1 === checkElement.setModelMethod
                ) {
                    flag = false;
                    if (checkElement && undefined !== checkElement.ui_children &&
                        0 !== checkElement.ui_children.length) {
                        checkElement = checkElement.ui_children[pathIndex];
                    }
                }
                if (flag && checkElement && undefined !== checkElement.children &&
                    0 !== checkElement.children.length) {
                    checkElement = checkElement.children[pathIndex];
                }
            }
        }
        if (
            4 === currentStep.stepInfo.params.findType &&
            undefined !== selectPath &&
            undefined !== checkElement
        ) {
            for (let pathIndex of selectPath) {
                // 判断是否为视觉建模
                if (checkElement && undefined !== checkElement.children && 0 !== checkElement.children.length) {
                    checkElement = checkElement.children[pathIndex];
                }
            }
        }
        // 获取 选择元素
        if (
            -1 !== [1, 4, 5].indexOf(currentStep.stepInfo.params.findType) &&
            0 !== currentStep.stepInfo.params.findInfo.findNode.length
        ) {
            let nodeIdList = currentStep.stepInfo.params.findInfo.findNode.map((item) => item.id);
            selectElements = findSelectElements([deepcopy(domDetail)], nodeIdList);
        }
    }

    useEffect(() => {
        setLoading(true);
        drawCanvas();
    }, [currentNode, currentStep]);

    useEffect(() => {
        drawCanvas();
    }, [innerHeight, innerWidth,
        element, parentElement, selectElements, checkElement]);

    const drawCanvas = () => {
        const context = canvas.current.getContext('2d');
        const img = new Image();
        let url = currentStep?.stepInfo.params.deviceInfo.screenshot;
        if (url.startsWith('http://')) {
            let res = url.split(':');
            url = 'https:' + res[1];
        }
        img.src = url;
        img.onload = () => {
            context.drawImage(img, 0, 0, imgWidth * ratio, imgHeight * ratio);
            draw(context);
            setLoading(false);
        };
    };

    const {imgHeight, imgWidth, imageScale} = getCanvasSize(currentNode,
        currentStep, innerHeight, innerWidth, editType);
    const ratio = window.devicePixelRatio || 1;
    const draw = (context) => {
        // 绘制虚线
        if ([2, 4].indexOf(currentStep?.stepInfo.type) !== -1) {
            let hasUiNode = [];
            if (currentStep.stepInfo.params.findType === 1) {
                hasUiNode = getVisionModelNodeInfo([domDetail]);
            }
            let boundsList = getElementsBounds(
                [domDetail], hasUiNode.length !== 0 ? 'ui' : 'normal');
            for (let {x, y, height, width} of boundsList) {
                x *= imageScale * ratio;
                y *= imageScale * ratio;
                height *= imageScale * ratio;
                width *= imageScale * ratio;
                context.beginPath();

                context.setLineDash([2, 5]);
                context.lineWidth = 1.5;
                context.strokeStyle = '#d9d9d9';
                // 顶部
                context.moveTo(x, y);
                context.lineTo(x + width, y);
                context.stroke();
                // 左部
                context.moveTo(x, y);
                context.lineTo(x, y + height);
                context.stroke();
                // 右部
                context.moveTo(x + width, y);
                context.lineTo(x + width, y + height);
                context.stroke();
                // 底部
                context.moveTo(x, y + height);
                context.lineTo(x + width, y + height);
                context.stroke();

                context.closePath();
            }
        }

        // 绘制多个控件框
        if (selectElements.length !== 0) {
            for (let _element of selectElements) {
                if (_element?.rect) {
                    let {x, y, height, width} = _element?.rect;
                    x *= imageScale * ratio;
                    y *= imageScale * ratio;
                    height *= imageScale * ratio;
                    width *= imageScale * ratio;
                    context.beginPath();
                    context.setLineDash([10, 0]);
                    context.lineWidth = 3;
                    context.strokeStyle = 'red';
                    context.strokeRect(x, y, width, height);
                    context.closePath();
                }
            }
        }

        // 绘制黄框 点击 element
        if (element?.rect) {
            let {x, y, height, width} = element?.rect;
            x *= imageScale * ratio;
            y *= imageScale * ratio;
            height *= imageScale * ratio;
            width *= imageScale * ratio;
            context.beginPath();
            context.setLineDash([10, 0]);
            context.lineWidth = 3;
            context.fillStyle = 'rgba(140, 140, 140, 12%)';
            context.strokeStyle = 'orange';
            context.fillRect(x, y, width, height);
            context.strokeRect(x, y, width, height);
            context.closePath();
        }

        // 绘制绿框 查看 element
        if (checkElement?.rect) {
            let {x, y, height, width} = checkElement.rect;
            x *= imageScale * ratio;
            y *= imageScale * ratio;
            height *= imageScale * ratio;
            width *= imageScale * ratio;
            context.beginPath();
            context.setLineDash([10, 5]);
            context.lineWidth = 5;
            context.strokeStyle = 'green';
            context.strokeRect(x, y, width, height);
            context.closePath();
        }

        // 绘制虚线蓝框 展示父 element
        if (
            !isEmpty(selectElements) &&
            parentElement?.rect
        ) {
            let {x, y, height, width} = parentElement.rect;
            x *= imageScale * ratio;
            y *= imageScale * ratio;
            height *= imageScale * ratio;
            width *= imageScale * ratio;
            context.beginPath();
            context.setLineDash([10, 5]);
            context.lineWidth = 3;
            context.strokeStyle = '#f89b28';
            context.strokeRect(x, y, width, height);
            context.closePath();
        }
    };

    return (
        <Spin spinning={loading}>
            {
                5 !== currentStep.stepInfo.params.findType ?
                    <canvas
                        ref={canvas}
                        width={imgWidth * ratio}
                        height={imgHeight * ratio}
                        style={{
                            width: imgWidth,
                            height: imgHeight,
                            border: '1px solid #eee'
                        }}
                        className={!['readonly', 'debug', 'execute'].includes(editType) ? editType + 'StepImage' : ''}
                    /> : <div
                        onClick={() => {
                            setShowDropdown(false);
                        }}
                        onContextMenu={(e) => {
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return;
                            }
                            let _clickElement = null;
                            let _clickLeft = e.nativeEvent.offsetX;
                            let _clickTop = e.nativeEvent.offsetY;
                            for (let _element of selectElements) {
                                if (_element?.rect) {
                                    let {x, y, width, height} = _element?.rect;
                                    x *= imageScale * ratio;
                                    y *= imageScale * ratio;
                                    height *= imageScale * ratio;
                                    width *= imageScale * ratio;
                                    if (x < _clickLeft && x + width > _clickLeft &&
                                        y < _clickTop && y + height > _clickTop) {
                                        setClickElement(_element);
                                        _clickElement = _element;
                                    }
                                }
                            }
                            if (_clickElement) {
                                setDropdownTop(_clickTop + 10);
                                setDropdownLeft(_clickLeft + 10);
                                setDropdownWidth(e.nativeEvent.target.width);
                                setDropdownHeight(e.nativeEvent.target.height);
                                setShowDropdown(true);
                            } else {
                                setShowDropdown(false);
                            }
                        }}
                        onMouseLeave={() => {
                            setShowDropdown(false);
                        }}
                    >
                        <canvas
                            ref={canvas}
                            width={imgWidth * ratio}
                            height={imgHeight * ratio}
                            style={{
                                width: imgWidth,
                                height: imgHeight,
                                border: '1px solid #eee'
                            }}
                            className={!['readonly', 'debug', 'execute'].includes(editType) ? editType + 'StepImage' : ''}
                        />
                        <ul
                            style={{
                                display: showDropdown ? 'block' : 'none',
                                left: dropdownLeft,
                                top: dropdownTop
                            }}
                            className={styles.canvasDropdown}
                        >
                            <li
                                onClick={async () => {
                                    let newFindNode = deepcopy(currentStep.stepInfo.params.findInfo.findNode);
                                    for (let _index in newFindNode) {
                                        if (newFindNode[_index].id === clickElement.id) {
                                            newFindNode[_index].actionNode = true;
                                        } else {
                                            newFindNode[_index].actionNode = false;
                                        }
                                    }
                                    let xPrecent = ((dropdownLeft - 10) * ratio) / dropdownWidth;
                                    let yPrecent = ((dropdownTop - 10) * ratio) / dropdownHeight;
                                    // 找控件
                                    let {width, height} = currentStep.stepInfo.params.deviceInfo.screenSize;
                                    if (
                                        -1 !== Object.keys(currentStep.stepInfo.
                                            params.deviceInfo.screenSize).indexOf('rotation')
                                    ) {
                                        if (
                                            -1 !== [90, 270].indexOf(currentStep.stepInfo.params.
                                                deviceInfo.screenSize.rotation)
                                        ) {
                                            let _width = width;
                                            width = height;
                                            height = _width;
                                        }
                                    }
                                    // 计算点击位置在设备上的位置
                                    let x = xPrecent * width;
                                    let y = yPrecent * height;
                                    // 递归检索选择控件的路径
                                    let {pathIndexList} = searchNewElementByBounds(
                                        [domDetail], {
                                        x,
                                        y,
                                    }, clickElement);
                                    currentStep.stepInfo.params.findInfo.findNode = [...newFindNode];
                                    currentStep.stepInfo.params.pathSummary = pathIndexList;
                                    await handleUpdateStep({...currentStep});
                                    setShowDropdown(false);
                                    setDropdownTop(0);
                                    setDropdownLeft(0);
                                }}
                            >
                                设置为操作控件
                            </li>
                            <li
                                onClick={async () => {
                                    let newFindNode = deepcopy(currentStep.stepInfo.params.findInfo.findNode);
                                    for (let _index in newFindNode) {
                                        if (newFindNode[_index].id === clickElement.id) {
                                            newFindNode[_index].detailDetection =
                                                [1, 1, clickElement.word ? clickElement.word : ''];
                                        }
                                    }
                                    currentStep.stepInfo.params.findInfo.findNode = [...newFindNode];
                                    await handleUpdateStep({...currentStep});
                                    setShowDropdown(false);
                                    setDropdownTop(0);
                                    setDropdownLeft(0);
                                }}
                            >
                                叠加视觉特征
                            </li>
                        </ul>
                    </div>
            }
        </Spin>
    );
};

export default ImageAction;
