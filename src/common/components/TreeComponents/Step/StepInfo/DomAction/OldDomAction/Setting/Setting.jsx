import {useState, useEffect} from 'react';
import {Drawer, Tooltip} from 'antd';
import {
    SettingOutlined, FolderOutlined
} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ImageAction from '../ImageAction';
import FindType from '../../components/FindType';
import ClickUntil from '../../components/ClickUntil';
import ClickWait from '../../components/ClickWait';
import DomWait from '../../components/DomWait';
import Retry from '../../components/Retry';
import IfThen from '../../components/IfThen';
import DomTree from './DomTree';
import styles from './Setting.module.less';


function Setting(props) {
    const {setShowModal, currentStep, currentNode,
        innerHeight, innerWidth, domDetail,
        className, handleUpdateStep, handleUpdateNode} = props;
    const [selectPath, setSelectPath] = useState([]);
    const [open, setOpen] = useState(false);
    const [settingWidth, setSettingWidth] = useState(0);

    useEffect(() => {
        updateSettingWidth(window.innerHeight);
    }, [innerHeight, innerWidth]);

    const updateSettingWidth = (innerHeight) => {
        let screenWidth = currentStep?.stepInfo.params.deviceInfo.screenSize.width;
        let screenHeight = currentStep?.stepInfo.params.deviceInfo.screenSize.height;
        let imgHeight = innerHeight - 200;
        let imageScale = imgHeight / screenHeight;
        let imgWidth = imageScale * screenWidth;
        setSettingWidth(imgWidth + 500);
    };

    const handleUpdateSelectPath = (path) => {
        setSelectPath([...path]);
    };

    return (
        <>
            <Tooltip title="高级设置">
                <SettingOutlined
                    className={className}
                    onClick={() => {
                        setShowModal(true);
                        setOpen(true);
                    }}
                />
            </Tooltip>
            <Drawer
                title="高级设置"
                open={open}
                centered
                width={settingWidth}
                onClose={async () => {
                    await handleUpdateStep({...currentStep});
                    handleUpdateStep({...currentStep});
                    handleUpdateNode({...currentNode}, {});
                    setShowModal(false);
                    setOpen(false);
                }}
                footer={null}
            >
                <div className={styles.settingLeft}>
                    <ImageAction
                        key={'setting_image_action'}
                        {...props}
                        selectPath={selectPath}
                        isSetting={open}
                        domDetail={domDetail}
                    />
                </div>
                <div className={styles.settingRight}>
                    <div className={styles.paramsSetting}>
                        <span><FolderOutlined /></span>
                        <span>基础参数</span>
                    </div>
                    <Retry {...props} />
                    <ClickWait {...props} />
                    <DomWait {...props} />
                    {
                        -1 !== [1].indexOf(currentStep.stepInfo.params.findType) ?
                            <ClickUntil {...props} /> : null
                    }
                    <IfThen {...props} />
                    {/* 旧 dom 多屏查找 */}
                    {
                        -1 !== [1, 4].indexOf(currentStep.stepInfo.params.findType) ?
                            <FindType {...props} /> : null
                    }
                    {/* 旧 dom tree */}
                    {
                        -1 === [4, 5].indexOf(currentStep.stepInfo.params.findType) ?
                            <div className={styles.paramsSetting}>
                                <FolderOutlined />
                                <span>DOM 树</span>
                            </div> : null
                    }
                    {
                        -1 === [4, 5].indexOf(currentStep.stepInfo.params.findType) ?
                            <DomTree
                                {...props}
                                selectPath={selectPath}
                                handleUpdateSelectPath={handleUpdateSelectPath}
                            /> : null
                    }
                </div>
            </Drawer>
        </>
    );
};

export default connectModel([commonModel, baseModel], (state) => ({
    showModal: state.common.base.showModal,
}))(Setting);
