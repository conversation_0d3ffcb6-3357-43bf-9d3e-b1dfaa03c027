import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';


// 数组取交集
export const getIntersection = (setting, arr) => {
    let eleTag = setting?.eleTag ?? [];
    let typeTag = setting?.typeTag ?? [];
    let flag1 = false;
    let flag2 = false;
    for (let tag of eleTag) {
        if (arr.includes(tag)) {
            flag1 = true;
        }
    }
    for (let tag of typeTag) {
        if (arr.includes(tag)) {
            flag2 = true;
        }
    }
    return flag1 && flag2;
};

const deepcopy = obj => {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    let newObj = {};
    if (Array.isArray(obj)) {
        newObj = obj.map(item => deepcopy(item));
    } else {
        Object.keys(obj).forEach((key) => {
            return newObj[key] = deepcopy(obj[key]);
        });
    }
    return newObj;
};

export function searchElementByBounds(
    dom,
    {x, y},
    pathIndexList = [],
    nowIndexList = [],
    targetBounds = {}
) {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (element?.rect) {
            if (
                x >= element?.rect.x &&
                x <= element?.rect.x + element?.rect.width &&
                y >= element?.rect.y &&
                y <= element?.rect.y + element?.rect.height &&
                ((element.parents && element.parents.length > 1 && !['App'].includes(element.type)) ||
                    (!element.parents && !['OTHERS', 'GROUP', 'Device'].includes(element.type)))
            ) {
                let needCover = true;
                if (0 !== Object.keys(targetBounds).length) {
                    if (
                        element?.rect.width <= targetBounds.width &&
                        element?.rect.height <= targetBounds.height
                    ) {
                        needCover = true;
                    } else {
                        needCover = false;
                    }
                }
                if (needCover) {
                    pathIndexList = [...nowIndexList];
                    targetBounds = {...element?.rect};
                }
            }
        }
        // 判断是否为视觉建模
        let flag = true;
        if (undefined !== element?.setModelMethod && 1 === element?.setModelMethod) {
            flag = false;
            if ('object' === typeof element.ui_children && 0 !== element.ui_children.length) {
                let res = searchElementByBounds(
                    element.ui_children,
                    {x, y},
                    pathIndexList,
                    nowIndexList,
                    targetBounds
                );
                pathIndexList = res.pathIndexList;
                targetBounds = res.targetBounds;
            }
        }
        if (flag && 'object' === typeof element.children && 0 !== element.children.length) {
            let res = searchElementByBounds(
                element.children,
                {x, y},
                pathIndexList,
                nowIndexList,
                targetBounds
            );
            pathIndexList = res.pathIndexList;
            targetBounds = res.targetBounds;
        }
        nowIndexList.pop();
    });
    return {pathIndexList, targetBounds};
};

export function searchElementByBoundsv2(
    dom,
    {x, y},
    mergeOCR = undefined,
    pathIndexList = [],
    nowIndexList = [],
    targetBounds = {}
) {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (element?.rect) {
            if (
                x >= element?.rect.x &&
                x <= element?.rect.x + element?.rect.w &&
                y >= element?.rect.y &&
                y <= element?.rect.y + element?.rect.h &&
                ((element.parents && element.parents.length > 1 && !['App'].includes(element.type)) ||
                    (!element.parents && !['OTHERS', 'GROUP', 'Device'].includes(element.type)))
            ) {
                let needCover = false;
                if (0 !== Object.keys(targetBounds).length) {
                    if (
                        element?.rect.w <= targetBounds.w &&
                        element?.rect.h <= targetBounds.h
                    ) {
                        if (element.type.includes('Text')) {
                            if (element.type === 'TextArea' && mergeOCR) {
                                needCover = true;
                            }
                            if (element.type === 'Text' && !mergeOCR) {
                                needCover = true;
                            }
                        } else {
                            needCover = true;
                        }
                    } else {
                        needCover = false;
                    }
                } else {
                    needCover = true;
                }
                if (needCover) {
                    pathIndexList = [...nowIndexList];
                    targetBounds = {...element?.rect};
                }
            }
        }
        // 判断是否为视觉建模
        let flag = true;
        if (undefined !== element?.setModelMethod && 1 === element?.setModelMethod) {
            flag = false;
            if ('object' === typeof element.ui_children && 0 !== element.ui_children.length) {
                let res = searchElementByBoundsv2(
                    element.ui_children,
                    {x, y},
                    mergeOCR,
                    pathIndexList,
                    nowIndexList,
                    targetBounds
                );
                pathIndexList = res.pathIndexList;
                targetBounds = res.targetBounds;
            }
        }
        if (flag && 'object' === typeof element.children && 0 !== element.children.length) {
            let res = searchElementByBoundsv2(
                element.children,
                {x, y},
                mergeOCR,
                pathIndexList,
                nowIndexList,
                targetBounds
            );
            pathIndexList = res.pathIndexList;
            targetBounds = res.targetBounds;
        }
        nowIndexList.pop();
    });
    return {pathIndexList, targetBounds};
};

export function searchElementByBoundsVenus(
    dom,
    {x, y},
    setting,
    pathIndexList = [],
    nowIndexList = [],
    targetBounds = {}
) {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (element?.rect && getIntersection(setting, element.ext.tag ?? [])) {
            if (
                x >= element?.rect.x &&
                x <= element?.rect.x + element?.rect.w &&
                y >= element?.rect.y &&
                y <= element?.rect.y + element?.rect.h &&
                ((element.parents && element.parents.length > 1 && !['App'].includes(element.type)) ||
                    (!element.parents && !['OTHERS', 'GROUP', 'Device'].includes(element.type)))
            ) {
                let needCover = false;
                if (0 !== Object.keys(targetBounds).length) {
                    if (
                        element?.rect.w <= targetBounds.w &&
                        element?.rect.h <= targetBounds.h
                    ) {
                        if (element.type.includes('Text')) {
                            if (element.type === 'TextArea' && setting?.mergeOCR) {
                                needCover = true;
                            }
                            if (element.type === 'Text' && !setting?.mergeOCR) {
                                needCover = true;
                            }
                        } else {
                            needCover = true;
                        }
                    } else {
                        needCover = false;
                    }
                } else {
                    needCover = true;
                }
                if (needCover) {
                    pathIndexList = [...nowIndexList];
                    targetBounds = {...element?.rect};
                }
            }
        }
        // 判断是否为视觉建模
        if ('object' === typeof element.children && 0 !== element.children.length) {
            let res = searchElementByBoundsVenus(
                element.children,
                {x, y},
                setting,
                pathIndexList,
                nowIndexList,
                targetBounds
            );
            pathIndexList = res.pathIndexList;
            targetBounds = res.targetBounds;
        }
        nowIndexList.pop();
    });
    return {pathIndexList, targetBounds};
};

export function searchNewElementByBounds(
    dom,
    {x, y},
    clickElement,
    pathIndexList = [],
    nowIndexList = [],
    targetBounds = {}
) {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (element?.rect) {
            if (
                x >= element?.rect.x &&
                x <= element?.rect.x + element?.rect.width &&
                y >= element?.rect.y &&
                y <= element?.rect.y + element?.rect.height
                && clickElement
                && clickElement.id === element.id
            ) {
                let needCover = true;
                if (0 !== Object.keys(targetBounds).length) {
                    if (
                        element?.rect.width <= targetBounds.width &&
                        element?.rect.height <= targetBounds.height
                    ) {
                        needCover = true;
                    } else {
                        needCover = false;
                    }
                }
                if (clickElement && needCover) {
                    pathIndexList = [...nowIndexList];
                    targetBounds = {...element?.rect};
                }
            }
        }
        if ('object' === typeof element.children && 0 !== element.children.length) {
            let res = searchNewElementByBounds(
                element.children,
                {x, y},
                clickElement,
                pathIndexList,
                nowIndexList,
                targetBounds
            );
            pathIndexList = res.pathIndexList;
            targetBounds = res.targetBounds;
        }
        nowIndexList.pop();
    });
    return {pathIndexList, targetBounds};
};

export function searchNewElementByBoundsv2(
    dom,
    {x, y},
    clickElement,
    pathIndexList = [],
    nowIndexList = [],
    targetBounds = {}
) {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (element?.rect) {
            if (
                x >= element?.rect.x &&
                x <= element?.rect.x + element?.rect.w &&
                y >= element?.rect.y &&
                y <= element?.rect.y + element?.rect.h
                && clickElement
                && clickElement.debug.id === element.debug.id
            ) {
                let needCover = true;
                if (0 !== Object.keys(targetBounds).length) {
                    if (
                        element?.rect.w <= targetBounds.w &&
                        element?.rect.h <= targetBounds.h
                    ) {
                        needCover = true;
                    } else {
                        needCover = false;
                    }
                }
                if (clickElement && needCover) {
                    pathIndexList = [...nowIndexList];
                    targetBounds = {...element?.rect};
                }
            }
        }
        if ('object' === typeof element.children && 0 !== element.children.length) {
            let res = searchNewElementByBoundsv2(
                element.children,
                {x, y},
                clickElement,
                pathIndexList,
                nowIndexList,
                targetBounds
            );
            pathIndexList = res.pathIndexList;
            targetBounds = res.targetBounds;
        }
        nowIndexList.pop();
    });
    return {pathIndexList, targetBounds};
};

export function getParentRect(dom, parents, rect = {}) {
    dom.forEach((element, index) => {
        let _index = parents.indexOf(element.id);
        if (-1 !== _index) {
            if (0 === Object.keys(rect).length && element?.rect) {
                rect = {
                    x: element?.rect.x,
                    y: element?.rect.y,
                    targetX: element?.rect.x + element?.rect.width,
                    targetY: element?.rect.y + element?.rect.height
                };
            } else {
                if (element?.rect && element?.rect.x < rect.x) {
                    rect.x = element?.rect.x;
                }
                if (element?.rect && element?.rect.y < rect.y) {
                    rect.y = element?.rect.y;
                }
                if (element?.rect && element?.rect.x + element?.rect.width > rect.targetX) {
                    rect.targetX = element?.rect.x + element?.rect.width;
                }
                if (element?.rect && element?.rect.y + element?.rect.height > rect.targetY) {
                    rect.targetY = element?.rect.y + element?.rect.height;
                }
            }
            return {rect};
        }
        if (element?.children && 0 !== element?.children.length) {
            let res = getParentRect(element.children, parents, rect);
            rect = res.rect;
        }
    });
    return {rect};
};

export function getNewParentRect(dom, parents, rect = {}) {
    dom.forEach((element, index) => {
        let _index = parents.indexOf(element?.debug?.id);
        if (-1 !== _index) {
            if (0 === Object.keys(rect).length && element?.rect) {
                rect = {
                    x: element?.rect.x,
                    y: element?.rect.y,
                    targetX: element?.rect.x + element?.rect.w,
                    targetY: element?.rect.y + element?.rect.h
                };
            } else {
                if (element?.rect && element?.rect.x < rect.x) {
                    rect.x = element?.rect.x;
                }
                if (element?.rect && element?.rect.y < rect.y) {
                    rect.y = element?.rect.y;
                }
                if (element?.rect && element?.rect.x + element?.rect.w > rect.targetX) {
                    rect.targetX = element?.rect.x + element?.rect.w;
                }
                if (element?.rect && element?.rect.y + element?.rect.h > rect.targetY) {
                    rect.targetY = element?.rect.y + element?.rect.h;
                }
            }
            return {rect};
        }
        if (element?.children && 0 !== element?.children.length) {
            let res = getNewParentRect(element.children, parents, rect);
            rect = res.rect;
        }
    });
    return {rect};
};

export function getVisionModelNodeInfo(dom, modelingElemet = [], nowIndexList = []) {
    dom.forEach((element, index) => {
        nowIndexList.push(index);
        if (undefined === element?.setModelMethod) {
            element.setModelMethod = 0;
        }
        if (1 === element?.setModelMethod) {
            let ele = deepcopy(element);
            ele.key = nowIndexList.join('-');
            modelingElemet.push(ele);
        }
        if (element?.children && 0 !== element?.children.length) {
            getVisionModelNodeInfo(element.children, modelingElemet, nowIndexList);
        }
        nowIndexList.pop();
    });
    return modelingElemet;
};

export function getElementsBounds(elements, type = 'normal', boundsList = []) {
    for (let element of elements) {
        if (element?.rect && element?.rect.x >= 0 && element?.rect.y >= 0 &&
            element?.rect.width >= 0 && element?.rect.height >= 0) {
            if ('ui' === type) {
                if (!isNumber(element.id)) {
                    boundsList.push(element?.rect);
                }
            } else if (type === 'nbdom') {
                if (element.parents.length > 1 && !['App'].includes(element.type)) {
                    boundsList.push(element?.rect);
                }
            } else if (type === 'nbdom_ui') {
                if (!['OTHERS', 'GROUP', 'Device'].includes(element.type)) {
                    boundsList.push(element?.rect);
                }
            } else {
                boundsList.push(element?.rect);
            }
        }
        // 判断是否为视觉建模
        let flag = true;
        if (
            undefined !== element?.setModelMethod && 1 === element?.setModelMethod
        ) {
            flag = false;
            if (undefined !== element.ui_children && 0 !== element.ui_children.length) {
                boundsList = getElementsBounds(element.ui_children, type, boundsList);
            }
        }
        if (flag && undefined !== element?.children && 0 !== element?.children.length) {
            boundsList = getElementsBounds(element.children, type, boundsList);
        }
    }
    return boundsList;
};


export function getNewElementsBounds(elements, type = 'normal', boundsList = []) {
    for (let element of elements) {
        if (element?.rect && element?.rect.x >= 0 && element?.rect.y >= 0 &&
            element?.rect.w >= 0 && element?.rect.h >= 0) {
            if ('ui' === type) {
                if (!isNumber(element.id)) {
                    boundsList.push(element?.rect);
                }
            } else if (type === 'nbdom') {
                if (element.debug.parents.length > 1 && !['App'].includes(element.type)) {
                    boundsList.push(element?.rect);
                }
            } else if (type === 'nbdom_ui') {
                if (!['OTHERS', 'GROUP', 'Device'].includes(element.type)) {
                    boundsList.push(element?.rect);
                }
            } else {
                boundsList.push(element?.rect);
            }
        }
        if (undefined !== element?.children && 0 !== element?.children.length) {
            boundsList = getNewElementsBounds(element.children, type, boundsList);
        }
    }
    return boundsList;
};

export function getNewElementsBoundsv3(settings, elements, boundsList = []) {
    for (let element of elements) {
        if (element?.rect && element?.rect.x >= 0 && element?.rect.y >= 0 &&
            element?.rect.w >= 0 && element?.rect.h >= 0) {
            if (element.type === 'Icon' && element.ext?.index !== undefined) {
                boundsList.push({...element?.rect, name: element?.ext?.name, id: element?.debug?.id});
            } else {
                if (['Text', 'TextArea'].includes(element.type)) {
                    if (settings?.mergeOCR && element.type === 'TextArea') {
                        boundsList.push({...element?.rect});
                    }
                    if (!settings?.mergeOCR && element.type === 'Text') {
                        boundsList.push({...element?.rect});
                    }
                } else {
                    boundsList.push({...element?.rect});
                }
            }
        }
        if (element?.children && 0 !== element?.children?.length) {
            boundsList = getNewElementsBoundsv3(settings, element.children, boundsList);
        }
    }
    return boundsList;
};


export function getVenusDomElementsBounds(settings, elements, boundsList = []) {
    for (let element of elements) {
        if (getIntersection(settings, element?.ext?.tag ?? [])) {
            if (element?.rect && element?.rect.x >= 0 && element?.rect.y >= 0 &&
                element?.rect.w >= 0 && element?.rect.h >= 0) {
                if (element.type === 'Icon' && element.ext?.index !== undefined) {
                    boundsList.push({...element?.rect, name: element?.ext?.name, id: element?.debug?.id});
                } else {
                    if (['Text', 'TextArea'].includes(element.type)) {
                        if (settings?.mergeOCR && element.type === 'TextArea') {
                            boundsList.push({...element?.rect});
                        }
                        if (!settings?.mergeOCR && element.type === 'Text') {
                            boundsList.push({...element?.rect});
                        }
                    } else {
                        boundsList.push({...element?.rect});
                    }
                }
            }
        }
        if (element?.children && 0 !== element.children.length) {
            boundsList = getVenusDomElementsBounds(settings, element.children, boundsList);
        }
    }
    return boundsList;
};

export function findSelectElements(dom, nodeIdList, selectElements = []) {
    dom.forEach((element, index) => {
        if (-1 !== nodeIdList.indexOf(element.id)) {
            selectElements.push(element);
        }
        // 判断是否为视觉建模
        let flag = true;
        if (
            undefined !== element?.setModelMethod && 1 === element?.setModelMethod
        ) {
            flag = false;
            if (undefined !== element.ui_children && 0 !== element.ui_children.length) {
                findSelectElements(element.ui_children, nodeIdList, selectElements);
            }
        }
        if (flag && undefined !== element?.children && 0 !== element?.children.length) {
            findSelectElements(element.children, nodeIdList, selectElements);
        }
    });
    return selectElements;
};


export function findNewSelectElements(dom, nodeIdList, selectElements = []) {
    dom.forEach((element, index) => {
        if (-1 !== nodeIdList.indexOf(element?.debug?.id)) {
            selectElements.push(element);
        }
        if (undefined !== element?.children && 0 !== element?.children.length) {
            findNewSelectElements(element.children, nodeIdList, selectElements);
        }
    });
    return selectElements;
};

export function longestCommonPrefix(strs) {
    if (strs.length === 0) {
        return '';
    }
    let arrs = [];
    for (let str of strs) {
        arrs.push(str.split('-').map(item => parseInt(item, 10)));
    }
    let commonArr = arrs[0];
    let newArr = [];
    for (let i = 1; i < arrs.length; i++) {
        for (let j = 0; j <= arrs[i].length; j++) {
            if (commonArr[j] === arrs[i][j]) {
                newArr.push(commonArr[j]);
            } else {
                commonArr = newArr;
                break;
            }
        }
        newArr = [];
    }
    return commonArr.join('-');
};

export function recursionDomElement(dom, pathIndexList, path = []) {
    let pathItem = [dom[pathIndexList[0]].type, 0];
    let targetElement = dom[pathIndexList[0]];

    // 计算该元素是同层的第几个
    for (let index = 0; (index < dom.length) & (index < pathIndexList[0]); index++) {
        if (pathItem[0] === dom[index].type) {
            pathItem[1]++;
        }
    }
    // 递归继续
    path.push(pathItem);
    if (1 === pathIndexList.length) {
        return {path, targetElement, pathIndexList};
    } else {
        pathIndexList.shift();
        // 判断是否为视觉建模
        let flag = true;
        if (
            undefined !== targetElement.setModelMethod && 1 === targetElement.setModelMethod
        ) {
            flag = false;
            if (undefined !== targetElement.ui_children && 0 !== targetElement.ui_children.length) {
                return recursionDomElement(targetElement.ui_children, pathIndexList, path);
            }
        }
        if (flag && undefined !== targetElement.children && 0 !== targetElement.children.length) {
            return recursionDomElement(targetElement.children, pathIndexList, path);
        }
    }
};


/*  递归检索选择控件信息
    path: 父控件的路径
    ootPathIndexList: 选择控件的父控件路径
*/
export function getSingleElementInfo(rootPathIndexList, domDetail, isMulti = false) {
    let path = [];
    let targetElement = deepcopy(domDetail);
    if (!isMulti) {
        rootPathIndexList.pop();
    }
    if (0 !== rootPathIndexList.length) {
        let res = recursionDomElement(
            [domDetail],
            deepcopy(rootPathIndexList)
        );
        path = res.path;
        targetElement = res.targetElement;
    }
    return {path, targetElement, rootPathIndexList};
}

// 查找查看控件的路径
export const findPath = (data, id, nowIndexList = [], path = []) => {
    data.forEach((element, index) => {
        nowIndexList.push(index);
        if (element.id === Number(id)) {
            path = [...nowIndexList];
            return {path};
        }
        if (element.children && element.children.length > 0) {
            let res = findPath(element.children, id, nowIndexList, path);
            path = res.path;
        }
        nowIndexList.pop();
    });
    return {path};
};

// （new）查找查看控件的路径
export const findNewPath = (data, id, nowIndexList = [], path = []) => {
    data.forEach((element, index) => {
        nowIndexList.push(index);
        if (element?.debug?.id === Number(id)) {
            path = [...nowIndexList];
            return {path};
        }
        if (element.children && element.children.length > 0) {
            let res = findNewPath(element.children, id, nowIndexList, path);
            path = res.path;
        }
        nowIndexList.pop();
    });
    return {path};
};