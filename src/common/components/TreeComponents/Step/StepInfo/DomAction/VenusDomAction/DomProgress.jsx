import {Progress} from 'antd';
import styles from './DomAction.module.less';

function DomProgress(props) {
    const {percent, style} = props;
    return (
        <div className={styles.domProgress} style={style}>
            <span className={styles.progressName}>文本建模中</span>
            <Progress
                className={styles.progressInfo}
                strokeWidth={10}
                percent={percent}
                strokeColor={{
                    from: '#d3a6fa',
                    to: '#61c2fa',
                }}
                status="active"
                showInfo={false}
            />
        </div>

    );
};

export default DomProgress;