import {useState, useEffect} from 'react';
import {Spin, Drawer, Tooltip, Tabs, message, But<PERSON>, Divider} from 'antd';
import {
    SettingOutlined
} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import ParamsTitle from 'COMMON/components/ParamsTitle';
import ClickWait from 'COMMON/components/TreeComponents/Step/StepInfo/components/ClickWait';
import DomWait from 'COMMON/components/TreeComponents/Step/StepInfo/components/DomWait';
import BeforeSysAlertClear from 'COMMON/components/TreeComponents/Step/StepInfo/components/BeforeSysAlertClear';
import StepInterval from 'COMMON/components/TreeComponents/Step/StepInfo/components/StepInterval';
import Retry from 'COMMON/components/TreeComponents/Step/StepInfo/components/Retry';
import IfThen from 'COMMON/components/TreeComponents/Step/StepInfo/components/IfThen';
import NewScreenFind from 'COMMON/components/TreeComponents/Step/StepInfo/DomAction/NBDomAction/Setting/NewScreenFind';
import SingleElement from './SingleElement';
import MultiElement from './MultiElement';
import BetaParams from 'COMMON/components/TreeComponents/Step/StepInfo/DomAction/NBDomAction/Setting/BetaParams';
import styles from 'COMMON/components/TreeComponents/Step/StepInfo/DomAction/NBDomAction/Setting/Setting.module.less';

function Setting(props) {
    const {setShowModal, currentStep, curOsType, imgSrc, editType, domDetail,
        innerHeight, innerWidth, text, recordUsualDomStep, changeUsualDomStep,
        handleUpdateStep, operationType} = props;
    const [selectPath, setSelectPath] = useState([]);
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [settingWidth, setSettingWidth] = useState(0);
    const [recordStep, setRecordStep] = useState(null);

    useEffect(() => {
        updateSettingWidth(window.innerHeight, window.innerWidth);
    }, [innerHeight, innerWidth]);

    const updateSettingWidth = (innerHeight, innerWidth) => {
        let screenWidth = currentStep?.stepInfo.params.deviceInfo.screenSize.width;
        let screenHeight = currentStep?.stepInfo.params.deviceInfo.screenSize.height;
        if (screenWidth < screenHeight || curOsType === 2) {
            let imgHeight = innerHeight - 200;
            let imageScale = imgHeight / screenHeight;
            let imgWidth = imageScale * screenWidth;
            setSettingWidth(imgWidth + 700);
        } else {
            let imgWidth = innerWidth - 200;
            let imageScale = imgWidth / screenWidth;
            let imgHeight = imageScale * screenHeight;
            setSettingWidth(imgHeight + 350);
        }
    };

    const handleUpdateSelectPath = (path) => {
        setSelectPath([...path]);
    };

    const getActiveKey = (currentStep) => {
        let tags = currentStep?.stepInfo.params.findInfo?.chosenTag ?? [];
        if (tags.includes('single')) {
            return 'tab_1';
        }
        if (tags.includes('multiple')) {
            return 'tab_2';
        }
    };

    let items = [
        {
            label: '单控件查找',
            key: 'tab_1',
            children: (
                <SingleElement
                    {...props}
                    imgSrc={imgSrc}
                    domDetail={domDetail}
                    selectPath={selectPath}
                    sizeType="normal"
                    setLoading={setLoading}
                    recordUsualDomStep={recordUsualDomStep}
                    handleUpdateSelectPath={handleUpdateSelectPath}
                    drawerOpen="true"
                />
            )
        },
        {
            label: '多控件查找',
            key: 'tab_2',
            children: (
                <MultiElement
                    {...props}
                    imgSrc={imgSrc}
                    domDetail={domDetail}
                    selectPath={selectPath}
                    type="normal"
                    setLoading={setLoading}
                    setSelectPath={setSelectPath}
                    recordUsualDomStep={recordUsualDomStep}
                    handleUpdateSelectPath={handleUpdateSelectPath}
                    drawerOpen="true"
                />
            ),
            disabled: currentStep?.stepInfo?.type === 8
        }
    ];

    return (
        <>
            <span
                style={{cursor: 'pointer', fontSize: 13}}
                onClick={() => {
                    setShowModal(true);
                    setOpen(true);
                    setRecordStep(JSON.stringify(currentStep.stepInfo));
                }}
                className={styles.operatorIcon}
            >
                <SettingOutlined />
                &nbsp;{text}
            </span>
            <Drawer
                title="控件查找"
                open={open}
                centered
                width={settingWidth}
                onClose={async () => {
                    if (currentStep?.stepInfo?.params?.findInfo?.chosenTag?.includes('multiple') &&
                        currentStep?.stepInfo?.params?.findInfo?.findNode.length <= 1) {
                        message.warning('多控件建模需要设置 2 个以上控件');
                    }
                    // 多控件建模不能超过5个控件
                    if (currentStep?.stepInfo?.params?.findInfo?.chosenTag?.includes('multiple') &&
                        currentStep?.stepInfo?.params?.findInfo?.findNode.length > 5) {
                        message.warning('多控件建模最多只能设置 5 个控件');
                        return;
                    }
                    if (currentStep?.stepInfo?.params?.findInfo?.chosenTag?.includes('single') &&
                        currentStep?.stepInfo?.params?.findInfo?.findNode.length < 1) {
                        message.warning('单控件建模需要设置 1 个控件');
                    }
                    let newActionInfo = JSON.parse(JSON.stringify((currentStep.stepInfo)));
                    if (JSON.stringify(newActionInfo) === recordStep) {
                        setShowModal(false);
                        setSelectPath([]);
                        setRecordStep(null);
                        setOpen(false);
                        return;
                    }
                    await handleUpdateStep({
                        ...currentStep,
                        newActionInfo
                    });
                    setShowModal(false);
                    setSelectPath([]);
                    setOpen(false);
                }}
                footer={null}
            >
                <Spin spinning={loading}>
                    <Tabs
                        tabPosition="left"
                        defaultActiveKey={2}
                        items={[{
                            label: '基础配置',
                            key: 1,
                            children: <div style={{margin: '0 10%'}}>
                                <ParamsTitle text="查找失败重试配置" />
                                <Retry {...props} />
                                <ParamsTitle text="控件操作配置" />
                                <IfThen {...props} />
                                <DomWait {...props} />
                                <ClickWait {...props} />
                                <ParamsTitle text="多屏查找配置" />
                                <NewScreenFind {...props} />
                                <ParamsTitle text="步骤参数" />
                                <BeforeSysAlertClear {...props} />
                                <StepInterval {...props} />
                                {
                                    (localStorage.getItem('regressionCase_beta') === 'true' ||
                                        currentStep.stepInfo.params.findInfo?.findForm) && <BetaParams {...props} />
                                }
                            </div>
                        }, {
                            label: '查找方式',
                            key: 2,
                            children: <>
                                <Tabs
                                    type="card"
                                    activeKey={getActiveKey(currentStep)}
                                    size="small"
                                    onChange={(activeKey) => {
                                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                                            return;
                                        }
                                        let tags = currentStep.stepInfo.params.findInfo?.chosenTag ?? [];
                                        let modelType = 0;
                                        if (tags.includes('visual')) {
                                            modelType = 0;
                                        }
                                        if (tags.includes('visual') && tags.includes('system')) {
                                            modelType = 2;
                                        }
                                        switch (activeKey) {
                                            case 'tab_1':
                                                changeUsualDomStep(0, modelType);
                                                break;
                                            case 'tab_2':
                                                changeUsualDomStep(1, modelType);
                                                break;
                                            default:
                                                break;
                                        }
                                    }}
                                    items={items}
                                />
                            </>
                        },
                            // {
                            //     label: '查找区域',
                            //     key: 3,
                            //     children: <div>
                            //         <ScreenArea {...props} />
                            //     </div>
                            // }
                        ]}
                    />
                </Spin>
            </Drawer>
        </>
    );
};

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal,
}))(Setting);
