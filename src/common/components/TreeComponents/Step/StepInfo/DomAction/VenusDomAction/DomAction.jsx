import classnames from 'classnames';
import {forwardRef, useRef, useState, useEffect, useCallback, useImperativeHandle} from 'react';
import {message, Skeleton, Tooltip} from 'antd';
import {isEmpty} from 'lodash';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import electron from 'COMMON/utils/electron';
import Loading from 'COMMON/components/common/Loading';
import StepOperator from './StepOperator';
import ImageActionv3 from './ImageActionv3';
import {editNativev3} from './editNativev3';
import styles from './DomAction.module.less';
import DomProgress from './DomProgress';

function DomAction(props, ref) {
    const {
        descExtra,
        nodeId,
        editType,
        currentStep,
        curOsType,
        currentSpace,
        setRecordId,
        pageSourceSwitch,
        setCurrentStep,
        currentDevice,
        deviceList,
        handleUpdateStep,
        operationType,
    } = props;
    const [innerHeight, setInnerHeight] = useState(window.innerHeight);
    const [innerWidth, setInnerWidth] = useState(window.innerWidth);
    const [open, setOpen] = useState(false);
    const [imgSrc, setImgSrc] = useState('');
    const [loading, setLoading] = useState(true);
    const [messageApi, contextHolder] = message.useMessage();
    useImperativeHandle(
        ref,
        () => {
            return {
                start: recordUsualDomStep
            };
        },
        [recordUsualDomStep]
    );
    // dom 解析
    const [domDetail, setDomDetail] = useState({});
    useEffect(() => {
        async function func() {
            try {
                let domInfo = currentStep?.stepInfo?.params?.dom;
                // 解析url
                if (typeof domInfo === 'string' && domInfo?.startsWith('http')) {
                    let res = await fetch(domInfo).then((response) => response.json());
                    setDomDetail(res);
                } else {
                    setDomDetail(domInfo);
                }
            } catch (error) {
                console.log(error);
                message.error(error);
            }
        }
        func();
    }, [currentStep?.stepInfo?.params?.dom]);

    // 页面大小变动后，图片resize
    useEffect(() => {
        window.onresize = () => {
            setInnerHeight(window.innerHeight);
            setInnerWidth(window.innerWidth);
        };
    });

    useEffect(() => {
        let newImg = currentStep?.stepInfo?.params?.deviceInfo?.screenshot;
        if (newImg?.startsWith('http') && -1 !== newImg.indexOf('https://hydra.bj.bcebos.com')) {
            setLoading(true);
            getBosToken({bosLink: newImg}).then((res) => {
                if (!res?.token) {
                    message.error('身份校验失败，获取图片失败');
                }
                newImg += '?authorization=' + res.token;
                setImgSrc(newImg);
                setLoading(false);
            });
        } else {
            setImgSrc(newImg);
            setLoading(false);
        }
    }, [currentStep?.stepInfo?.params?.deviceInfo?.screenshot]);

    // 单击事件监听
    useEffect(() => {
        window.addEventListener('click', clickAction);
        return () => window.removeEventListener('click', clickAction);
    }, [currentStep, domDetail]);

    const editStep = async (step, domInfo) => {
        if (domInfo && domInfo.type === 'sketchyRecord' && isEmpty(domDetail)) {
            console.log('update sketchyRecord');
            step.stepInfo.params.recordInfo.dom = domInfo.dom;
            step.stepInfo.params.recordInfo.domInfo = domInfo.domInfo;
        }
        if (domInfo && domInfo.type === 'intactRecord') {
            step.stepInfo.params.recordInfo.dom = domInfo.dom;
            step.stepInfo.params.recordInfo.domInfo = domInfo.domInfo;
            console.log('update intactRecord');
        }
        await handleUpdateStep(step);
    };

    const clickAction = (e) => {
        if (['readonly', 'debug', 'execute'].includes(editType) || isEmpty(domDetail)) {
            return false;
        }
        if (editType + 'StepImage' === e.target.className) {
            let ratio = window.devicePixelRatio || 1;
            editNativev3({
                step: currentStep,
                editType,
                domDetail,
                setCurrentStep,
                curOsType: curOsType,
                method: 'click',
                handleUpdateStep,
                xPrecent: (e.offsetX * ratio) / e.target.width,
                yPrecent: (e.offsetY * ratio) / e.target.height,
                ...props
            });
        }
    };
    const recordUsualDomStep = async () => {
        try {
            // 确定有设备连接
            let gotDevice = false;
            for (let device of deviceList[2 === +curOsType ? 'iOS' : 'android']) {
                if (device.deviceId === currentDevice?.deviceId) {
                    gotDevice = true;
                    if (2 !== device?.status) {
                        messageApi.error('请确保设备状态正常');
                        return;
                    }
                    break;
                }
            }
            if (!gotDevice) {
                messageApi.error('请确保有设备连接');
                return;
            }
            // 开启录制模式
            let {recordId} = await electron.send('device.record.dumpWidgetModel', {
                deviceType: 2 === +curOsType ? 'ios' : 'android',
                deviceId: currentDevice?.deviceId,
                nodeId: +nodeId,
                stepId: +currentStep?.stepId,
                needUi: true,
                needPagesource: pageSourceSwitch,
                productId: currentSpace?.id,
                versionType: '3.0'
            });

            setRecordId(recordId);
            let step = {
                desc: currentStep?.stepInfo?.desc ?? '',
                common: currentStep?.stepInfo?.common
                    ? {...currentStep?.stepInfo?.common}
                    : {
                        commonAlertClear: true
                    },
                type: 10,
                params: {}
            };
            step.params.findType = 0;
            // findInfo 参数
            step.params.findInfo = {
                baseInfo: {
                    findType: 0,
                    useFastSAM: false,
                    chosenTag: ['visual', 'single'],
                    modelType: pageSourceSwitch ? 2 : 0,
                    findNode: [],
                },
                widgetInfo: {
                    findType: 0,
                    useFastSAM: false,
                    chosenTag: ['visual', 'single'],
                    modelType: pageSourceSwitch ? 2 : 0,
                    findNode: [],
                }
            };
            step.params.recordInfo = {};
            step.params.findParams = currentStep?.stepInfo?.params?.findParams;
            step.params.actionInfo = currentStep?.stepInfo?.params?.actionInfo;
            handleUpdateStep({...currentStep, stepInfo: step});
        } catch (err) {
            console.log(err?.message ?? err);
        }
    };

    const changeUsualDomStep = async (findType, modelType) => {
        try {
            let step = {...currentStep.stepInfo};
            step.params.pathSummary = [];
            step.params.findInfo.findPath = [];
            step.params.findInfo.findNode = [];
            let chosenTagEle = ['single'];
            if (findType === 0) {
                chosenTagEle = ['single'];
            }
            if (findType === 1) {
                chosenTagEle = ['multiple'];
            }
            let chosenTagMethod = ['visual'];
            if (modelType === 0) {
                chosenTagMethod = ['visual'];
            }
            if (modelType === 2) {
                chosenTagMethod = ['visual', 'system'];
            }
            step.params.findInfo.chosenTag = [...chosenTagEle, ...chosenTagMethod];
            step.params.actionInfo = currentStep.stepInfo?.params?.actionInfo;
            currentStep.stepInfo = step;
            editStep({...currentStep, stepInfo: step});
        } catch (err) {
        }
    };

    if (loading) {
        return <Loading />;
    }

    return (
        <div className={styles.action}>
            {contextHolder}
            {descExtra}
            {-1 === ['debug', 'execute'].indexOf(editType) ? (
                <StepOperator
                    {...props}
                    imgSrc={imgSrc}
                    domDetail={domDetail}
                    innerHeight={innerHeight}
                    innerWidth={innerWidth}
                    setInnerHeight={setInnerHeight}
                    setInnerWidth={setInnerWidth}
                    recordUsualDomStep={recordUsualDomStep}
                    changeUsualDomStep={changeUsualDomStep}
                    open={open}
                    setOpen={setOpen}
                    operaOptions={{
                        showFindTypeOpera: true,
                        showModelTypeOpera: true,
                        showSettingOpera: true
                    }}
                    operationType={operationType}
                />
            ) : null}
            <Skeleton
                loading={!currentStep?.stepInfo?.params?.deviceInfo?.screenSize}
                active
                style={{padding: 50}}
                paragraph={{rows: 14}}
            >
                <div
                    className={classnames(
                        styles.defaultAction,
                        {[styles.domActionWithExecute]: -1 !== ['execute'].indexOf(editType)},
                        {[styles.domAction]: -1 === ['debug', 'execute'].indexOf(editType)}
                    )}
                    style={{
                        height: 'calc(100% - 45px)'
                    }}
                >
                    <ImageActionv3
                        {...props}
                        domDetail={domDetail}
                        imgSrc={imgSrc}
                        innerHeight={innerHeight}
                        innerWidth={innerWidth}
                        setInnerHeight={setInnerHeight}
                        setInnerWidth={setInnerWidth}
                        recordUsualDomStep={recordUsualDomStep}
                        changeUsualDomStep={changeUsualDomStep}
                    />
                </div>
            </Skeleton>
        </div>
    );
}

export default connectModel([commonModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    deviceList: state.common.base.deviceList,
    currentDevice: state.common.base.currentDevice,
    recordId: state.common.case.recordId,
    pageSourceSwitch: state.common.case.pageSourceSwitch
}))(forwardRef(DomAction));
