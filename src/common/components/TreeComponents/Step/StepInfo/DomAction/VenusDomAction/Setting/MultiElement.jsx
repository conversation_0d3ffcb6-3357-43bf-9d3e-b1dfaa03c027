import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import ImageActionv3 from '../ImageActionv3';
import styles from '../../NBDomAction/Setting/Setting.module.less';
import MultiElementType from '../../NBDomAction/Setting/MultiElementType';
import StepOperator from '../StepOperator';

function MultiElement(props) {
    return (
        <>
            <div className={styles.settingLeft}>
                <StepOperator
                    {...props}
                    operaOptions={{
                        showFindTypeOpera: false,
                        showModelTypeOpera: true,
                        showSettingOpera: false
                    }}
                />
                <ImageActionv3
                    key={'setting_image_action'}
                    {...props}
                />
            </div>
            <div className={styles.settingRight}>
                <MultiElementType {...props} />
            </div>
        </>
    );
};

export default connectModel([baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal,
}))(MultiElement);
