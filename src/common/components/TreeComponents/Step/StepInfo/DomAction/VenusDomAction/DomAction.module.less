@import "RESOURCES/css/common.less";

.action {
    width: calc(100% - 30px);
    margin: 15px;
}

.noContent {
    margin-top: 100px;
}

.domAction {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.stepOperator {
    padding: 0 5px 5px 5px;
    text-align: center;
    z-index: 99;
}

.defaultAction {
    margin-top: 5px;
    height: 100%;
    overflow: scroll;
}

.domActionWithExecute {
    text-align: center;
}

.canvasDropdown {
    display: none;
    position: absolute;
    z-index: 999;
    width: 120px;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    cursor: pointer;
    border: 1px solid var(--border-color);
}

.canvasDropdown li {
    padding: 5px 10px;
    list-style: none;
}

.canvasDropdown li:hover {
    background-color: #3686f6;
    color: #fff;
}


// progress
.domProgress {
    position: absolute;
    bottom: 20px;
    padding: 0 40px;
    width: 100%;
    height: 22px;
    line-height: 22px;
}

.progressName {
    float: left;
    width: 80px;
    font-size: 12px;
    color: var(--color2);
}

.progressInfo {
    display: inline-block;
    width: calc(100% - 100px);
}

.domCreateStatus {
    position: absolute;
    width: 100%;
    bottom: 0;
    height: 40px;
}

.domErrMsg {
    width: calc(100% - 25px);
    position: absolute;
    bottom: 2px;
    padding: 0 40px;
    font-size: 12px;
    color: var(--error-color);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: center;
}