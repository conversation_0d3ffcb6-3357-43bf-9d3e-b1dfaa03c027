import Direction from './Operator/Direction';
import Interval from './Operator/Interval';
import Times from './Operator/OperatorTimes';
import Duration from './Operator/Duration';
import InputText from './Operator/TextInput';
import UiTextDetail from './Operator/UiTextDetail';
import Expression from './Operator/Expression';
import styles from './Params.module.less';

function MulitParams(props) {
    const { step, fontSize } = props;
    let flag = false;
    let findNode =
        0 === step.stepInfo.params.findType ||
        step?.stepInfo?.params?.findInfo?.chosenTag?.includes('single')
            ? step.stepInfo.params.findInfo.findNode
            : [];
    let nodeIndex = -1;
    if (0 === step.stepInfo.params.findType && ![7, 8, 9, 10].includes(step.stepInfo.type)) {
        for (let item of step.stepInfo.params.findInfo.findNode) {
            if (item.actionNode && 1 === item.detailDetection[0]) {
                flag = true;
                break;
            }
        }
        for (let index in findNode) {
            if (findNode[index].actionNode) {
                nodeIndex = index;
                break;
            }
        }
    }
    if ('input' === step.stepInfo.params.actionInfo.type) {
        if (undefined !== step.stepInfo.params.actionInfo.params.text) {
            flag = true;
        }
    } else if ('tap' === step.stepInfo.params.actionInfo.type) {
        if (
            undefined !== step.stepInfo.params.actionInfo.params.duration ||
            undefined !== step.stepInfo.params.actionInfo.params.interval ||
            undefined !== step.stepInfo.params.actionInfo.params.times
        ) {
            flag = true;
        }
    } else if ('swipe' === step.stepInfo.params.actionInfo.type) {
        if (
            undefined !== step.stepInfo.params.actionInfo.params.duration ||
            undefined !== step.stepInfo.params.actionInfo.params.direction ||
            undefined !== step.stepInfo.params.actionInfo.params.times
        ) {
            flag = true;
        }
    }

    return (
        <>
            {flag ? <div className={styles.stepParamsDivider} /> : null}
            {0 === step.stepInfo.params.findType ||
            step?.stepInfo?.params?.findInfo?.chosenTag?.includes('single') ? (
                <UiTextDetail {...props} findNode={findNode} nodeIndex={nodeIndex} />
            ) : null}
            {'input' === step.stepInfo.params.actionInfo.type ? <InputText {...props} /> : null}
            {'tap' === step.stepInfo.params.actionInfo.type &&
                step.stepInfo.params.actionInfo.params.duration && (
                    <>
                        <Duration {...props} />
                        <Interval {...props} />
                        <Times {...props} />
                    </>
                )}
            {'swipe' === step.stepInfo.params.actionInfo.type ? (
                <>
                    <Direction {...props} />
                    <Times {...props} />
                    <Duration {...props} />
                </>
            ) : null}
            {'drag' === step.stepInfo.params.actionInfo.type ? (
                <>
                    <Direction {...props} />
                </>
            ) : null}
            {'expression' === step.stepInfo.params.actionInfo.type ? (
                <>
                    <Expression {...props} />
                </>
            ) : null}
        </>
    );
}

export default MulitParams;
