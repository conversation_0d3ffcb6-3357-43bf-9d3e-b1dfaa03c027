import TextEdit from './Operator/TextContent';
import Direction from './Operator/Direction';
import Interval from './Operator/Interval';
import Times from './Operator/OperatorTimes';
import Duration from './Operator/Duration';
import InputText from './Operator/TextInput';
import UiTextDetail from './Operator/UiTextDetail';
import styles from './Params.module.less';

function ElementParams(props) {
    const {step, editType} = props;
    let flag = false;
    let findNode = 4 === step.stepInfo.params.findType ?
        step.stepInfo.params.findInfo.findNode : [];
    let nodeIndex = -1;
    if ((4 === step.stepInfo.params.findType) || (
        5 === step.stepInfo.params.findType && 4 === step.stepInfo.params.findInfo.domType
    )) {
        for (let item of step.stepInfo.params.findInfo.findNode) {
            if (item.actionNode && 1 === item.detailDetection[0]) {
                flag = true;
                break;
            }
        }
        for (let index in findNode) {
            if (findNode[index].actionNode) {
                nodeIndex = index;
                break;
            }
        }
    }
    if (2 === step.stepInfo.params.findType) {
        if (undefined !== step.stepInfo.params.findInfo.findText) {
            flag = true;
        }
    } else if ('input' === step.stepInfo.params.actionInfo.type) {
        if (undefined !== step.stepInfo.params.actionInfo.params.text) {
            flag = true;
        }
    } else if ('tap' === step.stepInfo.params.actionInfo.type) {
        if (undefined !== step.stepInfo.params.actionInfo.params.duration ||
            undefined !== step.stepInfo.params.actionInfo.params.interval ||
            undefined !== step.stepInfo.params.actionInfo.params.times) {
            flag = true;
        }
    } else if ('swipe' === step.stepInfo.params.actionInfo.type) {
        if (undefined !== step.stepInfo.params.actionInfo.params.duration ||
            undefined !== step.stepInfo.params.actionInfo.params.direction ||
            undefined !== step.stepInfo.params.actionInfo.params.times) {
            flag = true;
        }
    }
    return (
        <>
            {
                flag ? <div className={styles.stepParamsDivider} /> : null
            }
            {
                4 === step.stepInfo.params.findType ?
                    <UiTextDetail {...props} findNode={findNode} nodeIndex={nodeIndex} /> : null
            }
            {
                2 === step.stepInfo.params.findType ?
                    <TextEdit {...props} /> : null
            }
            {
                2 !== step.stepInfo.params.findType && 'input' === step.stepInfo.params.actionInfo.type ?
                    <InputText {...props} /> : null
            }
            {
                2 !== step.stepInfo.params.findType && 'tap' === step.stepInfo.params.actionInfo.type &&
                    step.stepInfo.params.actionInfo.params.duration ?
                    <>
                        <Duration {...props} />
                        <Interval {...props} />
                        <Times {...props} />
                    </> : null
            }
            {
                2 !== step.stepInfo.params.findType && 'swipe' === step.stepInfo.params.actionInfo.type ?
                    <>
                        <Direction {...props} />
                        <Times {...props} />
                        <Duration {...props} />
                    </> : null
            }
        </>
    );
};

export default ElementParams;

