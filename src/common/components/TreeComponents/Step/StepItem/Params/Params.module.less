@import "RESOURCES/css/common.less";

.stepParamsDivider {
    width: 100%;
    margin: 10px 0 10px 0;
    border-bottom: 1px solid var(--border-color);
    height: 1px;
}

.stepParamsTitle {
    height: 22px;
    line-height: 22px;
    width: 60px;
    margin: 0 25px 8px 0;
    font-size: 12px;
    color: #000 !important;
}

.stepExtraParams {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    color: var(--color2);
    cursor: pointer;
}

.stepParamsInfo {
    flex: 1;
}

.stepParamsInfo {
    :global {

        .ant-select-selection-item,
        .ant-select-selector,
        .custom-default-select-selector,
        .custom-dark-select-selector,
        .ant-input,
        .custom-default-input,
        .custom-dark-input,
        .ant-input-number-input,
        .custom-default-input-number-input,
        .custom-dark-input-number-input {
            font-size: 12px !important;
        }

    }
}

.params {
    width: calc(100% - 15px);
}

.stepTemplateInfo {
    width: 100%;
    margin-top: 5px;
    padding: 10px 5px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    border-radius: 5px;

    .desc {
        width: calc(100% - 90px);
        font-size: 10px !important;
        color: rgb(119, 119, 119);
    }

    .defaultStepName {
        display: inline-block;
        margin-left: 5px;
        width: 72px;
        height: 18px;
        line-height: 18px;
        color: var(--color);
        text-align: center;
        font-size: 10px;
        background-color: #3e91f7;
        border-radius: 3px;
        cursor: pointer;
    }

    .templateStepName {
        color: var(--color2);
        background-color: var(--border-color);
    }
}

.setting {
    font-size: 12px;
    color: var(--color2);
    text-decoration: underline;
}

.imageIcon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #777;
    cursor: pointer;
}

.infoIcon {
    position: absolute;
    top: 4px;
    right: 22px;
    color: #777;
}