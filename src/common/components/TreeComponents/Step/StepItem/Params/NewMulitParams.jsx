import Direction from './Operator/Direction';
import Interval from './Operator/Interval';
import Times from './Operator/OperatorTimes';
import Duration from './Operator/Duration';
import InputText from './Operator/TextInput';
import Expression from './Operator/Expression';

function NewMulitParams(props) {
    const {step} = props;
    return (
        <>
            {
                'input' === step.stepInfo.params.actionInfo.type ?
                    <InputText {...props} /> : null
            }
            {'tap' === step.stepInfo.params.actionInfo.type &&
                step.stepInfo.params.actionInfo.params.duration && <>
                    <Duration {...props} />
                    <Interval {...props} />
                    <Times {...props} />
                </>}
            {
                'swipe' === step.stepInfo.params.actionInfo.type ?
                    <>
                        <Direction {...props} />
                        <Times {...props} />
                        <Duration {...props} />
                    </> : null
            }
            {
                'drag' === step.stepInfo.params.actionInfo.type ?
                    <>
                        <Direction {...props} text='拖拽' />
                    </> : null
            }
            {
                'expression' === step.stepInfo.params.actionInfo.type ?
                    <>
                        <Expression {...props} />
                    </> : null
            }
        </>
    );
};

export default NewMulitParams;

