import {useState, useEffect} from 'react';
import {InputNumber, message} from 'antd';
import classnames from 'classnames';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from '../Params.module.less';

function Interval(props) {
    const {step, editType, type = 'small', handleUpdateStep, onFocus, onBlur} = props;
    const [interval, setInterval] = useState(100);

    useEffect(() => {
        setInterval(step?.stepInfo.params.actionInfo.params.interval);
    }, [step?.stepId]);

    return (
        <>
            {
                null !== interval && undefined !== interval ?
                    <div
                        style={{
                            display: 'flex',
                            position: 'relative',
                            padding: ![7, 8, 9, 10].includes(step?.stepInfo?.type) ? '0 10px' : 0,
                            margin: '2px 0'
                        }}
                    >
                        <div className={styles.stepParamsTitle} style={{fontSize: type === 'normal' ? 14 : 12}}>
                            点击间隔
                        </div>
                        <div className={styles.stepParamsInfo}>
                            <InputNumber
                                size='small'
                                max={5000}
                                min={100}
                                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                className={classnames(styles.params, 'inputEditor')}
                                addonAfter="ms"
                                value={interval}
                                onFocus={() => {
                                    onFocus && onFocus();
                                }}
                                onChange={value => {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    if (null === value) {
                                        value = 100;
                                    }
                                    setInterval(value);
                                }}
                                onBlur={async (e) => {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    let value = 0;
                                    if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                                        value = parseInt(e.target.value.trim(), 10);
                                    }
                                    if (value > 5000) {
                                        value = 5000;
                                    }
                                    if (value < 100) {
                                        value = 100;
                                    }
                                    if (step.stepInfo.params.actionInfo.params.interval !== value) {
                                        step.stepInfo.params.actionInfo.params.interval = Number(value);
                                        await handleUpdateStep({
                                            ...step,
                                        });
                                    }
                                    onBlur && onBlur();
                                }}
                            />
                        </div>
                    </div> : null}
        </>
    );
};

export default Interval;