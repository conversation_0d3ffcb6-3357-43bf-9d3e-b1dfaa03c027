import {useState, useEffect} from 'react';
import {Input, Tooltip, message} from 'antd';
import classnames from 'classnames';
import styles from '../Params.module.less';

function TextContent(props) {
    const {step, editType, type = 'small', handleUpdateStep, onBlur, onFocus} = props;
    const [findText, setFindText] = useState('');

    useEffect(() => {
        setFindText(step?.stepInfo.params.findInfo.findText);
    }, [step?.stepId]);

    return (
        <>
            {
                null !== findText && undefined !== findText ?
                    <div
                        style={{
                            display: 'flex',
                            position: 'relative',
                            padding: ![7, 8, 9, 10].includes(step?.stepInfo?.type) ? '0 10px' : 0,
                            margin: '2px 0'
                        }}
                    >
                        <div className={styles.stepParamsTitle} style={{fontSize: type === 'normal' ? 14 : 12}}>
                            文字内容
                        </div>
                        <div className={styles.stepParamsInfo}>
                            <Input
                                size='small'
                                className={classnames(styles.params, 'inputEditor')}
                                value={findText}
                                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                onFocus={() => {
                                    onFocus && onFocus();
                                }}
                                onChange={e => {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    setFindText(e.target.value);
                                }}
                                onBlur={async (e) => {
                                    if (['readonly', 'debug', 'execute'].includes(editType)) {
                                        return false;
                                    }
                                    if (step.stepInfo.params.findInfo.findText !== e.target.value) {
                                        step.stepInfo.params.findInfo.findText = e.target.value;
                                        await handleUpdateStep({...step});
                                    }
                                    onBlur && onBlur();
                                }}
                            />
                        </div>
                    </div> : null
            }
        </>
    );
};

export default TextContent;