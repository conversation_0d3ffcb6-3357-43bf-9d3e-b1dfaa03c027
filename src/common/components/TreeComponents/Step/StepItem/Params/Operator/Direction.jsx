import {useState, useEffect} from 'react';
import {message, Select} from 'antd';
import styles from '../Params.module.less';

function Direction(props) {
    const {step, editType, type = 'small', handleUpdateStep, text} = props;
    const [direction, setDirection] = useState(1);
    useEffect(() => {
        setDirection(step?.stepInfo.params.actionInfo.params.direction);
    }, [step?.stepId]);
    return (
        <>
            {null !== direction && undefined !== direction ?
                <div
                    style={{
                        display: 'flex',
                        position: 'relative',
                        padding: ![7, 8, 9, 10].includes(step?.stepInfo?.type) ? '0 10px' : 0,
                        margin: '2px 0'
                    }}
                >
                    <div className={styles.stepParamsTitle} style={{fontSize: type === 'normal' ? 14 : 12}}>
                        {text ?? '滑动'}方向
                    </div>
                    <div className={styles.stepParamsInfo}>
                        <Select
                            className={styles.params}
                            size='small'
                            value={direction}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            onChange={async (value) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                step.stepInfo.params.actionInfo.params.direction = Number(value);
                                await handleUpdateStep({
                                    ...step,
                                });
                                setDirection(value);
                            }}
                            options={[
                                {
                                    value: 1,
                                    label: ' 从下向上'
                                }, {
                                    value: 2,
                                    label: ' 从上向下'
                                }, {
                                    value: 3,
                                    label: ' 从左向右'
                                }, {
                                    value: 4,
                                    label: ' 从右向左'
                                }
                            ]}
                        />
                    </div>
                </div> : null}
        </>
    );
};

export default Direction;