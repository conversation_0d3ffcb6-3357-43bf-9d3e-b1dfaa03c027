import {useState, useEffect} from 'react';
import {InputNumber, message} from 'antd';
import classnames from 'classnames';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from '../Params.module.less';

function OperatorTimes(props) {
    const {step, editType, type = 'small', handleUpdateStep, onFocus, onBlur} = props;
    const [times, setTimes] = useState(1);

    if (!step.stepInfo.params.actionInfo.params.times) {
        return false;
    }

    useEffect(() => {
        setTimes(step?.stepInfo.params.actionInfo.params.times);
    }, [step?.stepId]);

    const getName = () => {
        switch (step.stepInfo.params.actionInfo.type) {
            case 'tap':
                return '点击次数';
            case 'swipe':
                return '滑动次数';
            default:
                break;
        }
    };
    return (
        <div
            style={{
                display: 'flex',
                position: 'relative',
                padding: ![7, 8, 9, 10].includes(step?.stepInfo?.type) ? '0 10px' : 0,
                margin: '2px 0'
            }}
        >
            <div className={styles.stepParamsTitle} style={{fontSize: type === 'normal' ? 14 : 12}}>{getName()}</div>
            <div className={styles.stepParamsInfo}>
                <InputNumber
                    className={classnames(styles.params, 'inputEditor')}
                    size='small'
                    max={50}
                    disabled={['readonly', 'debug', 'execute'].includes(editType)}
                    min={1}
                    addonAfter="次"
                    value={times}
                    onFocus={() => {
                        onFocus && onFocus();
                    }}
                    onChange={value => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        if (null === value || '' === value) {
                            value = 1;
                        }
                        setTimes(value);
                    }}
                    onBlur={async (e) => {
                        if (['readonly', 'debug', 'execute'].includes(editType)) {
                            return false;
                        }
                        let value = 0;
                        if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                            value = parseInt(e.target.value.trim(), 10);
                        }
                        if (value > 50) {
                            value = 50;
                        }
                        if (value < 1) {
                            value = 1;
                        }
                        if (step.stepInfo.params.actionInfo.params.times !== value) {
                            step.stepInfo.params.actionInfo.params.times = value;
                            await handleUpdateStep({
                                ...step,
                            });
                        }
                        onBlur && onBlur();
                    }}
                />
            </div>
        </div>
    );
};

export default OperatorTimes;
