import {useState, useEffect} from 'react';
import {Input, Tooltip} from 'antd';
import {InfoCircleOutlined} from '@ant-design/icons';
import classnames from 'classnames';
import styles from '../Params.module.less';

function Expression(props) {
    const {
        step, editType, handleUpdateStep, type = 'small', onFocus, onBlur
    } = props;
    const [expression, setExpression] = useState('');

    useEffect(() => {
        setExpression(step?.stepInfo.params.actionInfo.params.expression);
    }, [step?.stepId]);

    return (
        <>
            {null !== expression && undefined !== expression ?
                <div
                    style={{
                        display: 'flex',
                        position: 'relative',
                        padding: ![7, 8, 9, 10].includes(step?.stepInfo?.type) ? '0 10px' : 0,
                        margin: '2px 0'
                    }}
                >
                    <div className={styles.stepParamsTitle} style={{fontSize: type === 'normal' ? 14 : 12}}>表达式校验</div>
                    <div className={styles.stepParamsInfo}>
                        <Input
                            className={classnames(styles.params, 'inputEditor')}
                            style={{
                                width: 'calc(100% - 15px)'
                            }}
                            size='small'
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            placeholder='请输入表达式'
                            value={expression}
                            onFocus={() => {
                                onFocus && onFocus();
                            }}
                            onChange={async (e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                setExpression(e.target.value);
                            }}
                            onBlur={async (e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                if (step.stepInfo.params.actionInfo.params.expression !== e.target.value) {
                                    step.stepInfo.params.actionInfo.params.expression = e.target.value;
                                    await handleUpdateStep({...step});
                                }
                                onBlur && onBlur();
                            }}
                        />
                    </div>
                    <Tooltip title="${ONE_WIDGET_CONTENT} 表示当前元素的文本内容">
                        <InfoCircleOutlined className={styles.infoIcon} />
                    </Tooltip>
                </div> : null}
        </>
    );
};

export default Expression;