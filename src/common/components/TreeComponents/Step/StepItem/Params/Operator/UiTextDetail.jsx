import {useState, useEffect} from 'react';
import {Input, message} from 'antd';
import classnames from 'classnames';
import styles from '../Params.module.less';

function UiTextDetail(props) {
    const {step, findNode, type = 'small', nodeIndex, editType, handleUpdateStep,
        onFocus, onBlur
    } = props;
    const [text, setText] = useState('');

    useEffect(() => {
        setText(-1 === nodeIndex || 1 !== findNode[nodeIndex].detailDetection[0] ?
            null : findNode[nodeIndex].detailDetection[2]);
    }, [findNode, step?.stepId]);

    return (
        <>
            {null !== text && undefined !== text ?
                <div
                    style={{
                        display: 'flex',
                        position: 'relative',
                        padding: ![7, 8, 9, 10].includes(step?.stepInfo?.type) ? '0 10px' : 0,
                        margin: '2px 0'
                    }}
                >
                    <div className={styles.stepParamsTitle} style={{fontSize: type === 'normal' ? 14 : 12}}>文字内容</div>
                    <div className={styles.stepParamsInfo}>
                        <Input
                            className={classnames(styles.params, 'inputEditor')}
                            size='small'
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            placeholder="请输入内容"
                            value={text}
                            onFocus={() => {
                                onFocus && onFocus();
                            }}
                            onChange={(e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                setText(e.target.value);
                            }}
                            onBlur={async (e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                if (step.stepInfo.params.findInfo.findNode[nodeIndex].detailDetection[2] !==
                                    e.target.value.trim()) {
                                    step.stepInfo.params.findInfo.findNode[nodeIndex].detailDetection[2] =
                                        e.target.value.trim();
                                    await handleUpdateStep({...step});
                                }
                                onBlur && onBlur();
                            }}
                        />
                    </div>
                </div> : null}
        </>
    );
};

export default UiTextDetail;

