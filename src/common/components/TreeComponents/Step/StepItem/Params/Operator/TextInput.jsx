import { useState, useEffect } from 'react';
import { Input, message } from 'antd';
import classnames from 'classnames';
import styles from '../Params.module.less';

function TextInput(props) {
    const { step, editType, handleUpdateStep, type = 'small', onBlur, onFocus } = props;
    const [findText, setFindText] = useState('');

    useEffect(() => {
        setFindText(step?.stepInfo.params.actionInfo.params.text);
    }, [step?.stepId]);

    return (
        <>
            {null !== findText && undefined !== findText ? (
                <div
                    style={{
                        display: 'flex',
                        position: 'relative',
                        padding:
                            ![7, 8, 9, 10].includes(step?.stepInfo?.type) && step?.stepType !== 601
                                ? '0 10px'
                                : 0,
                        margin: '2px 0'
                    }}
                >
                    <div
                        className={styles.stepParamsTitle}
                        style={{ fontSize: type === 'normal' ? 14 : 12 }}
                    >
                        输入内容
                    </div>
                    <div className={styles.stepParamsInfo}>
                        <Input
                            className={classnames(styles.params, 'inputEditor')}
                            style={{
                                width: 'calc(100% - 15px)'
                            }}
                            size="small"
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            placeholder="请输入内容"
                            value={findText}
                            onFocus={() => {
                                onFocus && onFocus();
                            }}
                            onChange={async (e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                setFindText(e.target.value);
                            }}
                            onBlur={async (e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                if (
                                    step.stepInfo.params.actionInfo.params.text !== e.target.value
                                ) {
                                    step.stepInfo.params.actionInfo.params.text = e.target.value;
                                    await handleUpdateStep({ ...step });
                                }
                                onBlur && onBlur();
                            }}
                        />
                    </div>
                </div>
            ) : null}
        </>
    );
}

export default TextInput;
