import {useState, useEffect} from 'react';
import classnames from 'classnames';
import {Tooltip, InputNumber, message} from 'antd';
import {isNumber} from 'COMMON/components/TreeComponents/Step/utils';
import styles from '../Params.module.less';

function Duration(props) {
    const {step, editType, type = 'small', handleUpdateStep, onFocus, onBlur} = props;
    const [duration, setDuration] = useState('swipe' === step.stepInfo.params.actionInfo.type ? 500 : 100);
    useEffect(() => {
        setDuration(step.stepInfo.params.actionInfo.params.duration);
    }, [step?.stepId]);

    const getName = () => {
        switch (step.stepInfo.params.actionInfo.type) {
            case 'tap':
                return '长按时间';
            case 'swipe':
                return '每次滑动';
            default:
                break;
        }
    };
    return (
        <>
            {null !== duration && undefined !== duration ?
                <div
                    style={{
                        display: 'flex',
                        position: 'relative',
                        padding: ![7, 8, 9, 10].includes(step?.stepInfo?.type) ? '0 10px' : 0,
                        margin: '2px 0'
                    }}
                >
                    {
                        'tap' === step.stepInfo.params.actionInfo.type ?
                            <Tooltip title='超过 500ms 为长按'>
                                <ContentName getName={getName} type={type} />
                            </Tooltip> :
                            <ContentName getName={getName} type={type} />
                    }
                    <div className={styles.stepParamsInfo}>
                        <InputNumber
                            className={classnames(styles.params, 'inputEditor')}
                            size='small'
                            max={5000}
                            disabled={['readonly', 'debug', 'execute'].includes(editType)}
                            min={'swipe' === step.stepInfo.params.actionInfo.type ? 500 : 100}
                            addonAfter="ms"
                            value={duration}
                            onFocus={() => {
                                onFocus && onFocus();
                            }}
                            onChange={value => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                if (null === value) {
                                    value = 100;
                                }
                                setDuration(value);
                            }}
                            onBlur={async (e) => {
                                if (['readonly', 'debug', 'execute'].includes(editType)) {
                                    return false;
                                }
                                let value = 0;
                                if ('' !== e.target.value.trim() && isNumber(e.target.value.trim())) {
                                    value = parseInt(e.target.value.trim(), 10);
                                }
                                if (value > 5000) {
                                    value = 5000;
                                }
                                if ('swipe' === step.stepInfo.params.actionInfo.type) {
                                    value = 500 > value ? 500 : value;
                                } else {
                                    value = 100 > value ? 100 : value;
                                }
                                if (step.stepInfo.params.actionInfo.params.duration !== value) {
                                    step.stepInfo.params.actionInfo.params.duration = value;
                                    await handleUpdateStep({...step});
                                }
                                onBlur && onBlur();
                            }}
                        />
                    </div>
                </div>
                : null}
        </>
    );
};

function ContentName({getName, type}) {
    return (
        <div
            className={styles.stepParamsTitle}
            style={{fontSize: type === 'normal' ? 14 : 12}}
        >
            {getName()}
        </div>
    );
}

export default Duration;