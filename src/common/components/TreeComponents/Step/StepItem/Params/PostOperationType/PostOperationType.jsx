import React, {useState, useEffect} from 'react';
import styles from './PostOperationType.module.less'; // 引入样式文件
import {Tag, Input} from 'antd';
const PostOperationType = (props) => {
    const {
        editType,
        postOperation,
        handleUpdateStep = () => { },
        currentStep
    } = props;
    const [stepDesc, setStepDesc] = useState(postOperation?.stepDesc);
    useEffect(() => {
        setStepDesc(postOperation?.stepDesc);
    }, [postOperation?.stepDesc]);
    return (
        <div className={styles.postOperationType}>
            <Input
                variant="borderless"
                disabled={['editType', 'debug', 'execute']?.includes(editType)}
                className="inputEditor"
                size="small"
                tabIndex={-1}
                placeholder="步骤描述"
                prefix={
                    <Tag size="small" color="green">
                        后置SQL
                    </Tag>
                }
                onChange={(e) => {
                    setStepDesc(e.target.value);
                    // 更新树
                }}
                onBlur={(e) => {
                    const newCurrentStep = {...currentStep};
                    // 使用新的数组
                    newCurrentStep.stepInfo.postOperation =
                        newCurrentStep.stepInfo.postOperation.map((item) => ({
                            ...item,
                            stepDesc: item.id === postOperation.id ? e.target.value : item.stepDesc
                        }));

                    handleUpdateStep(newCurrentStep);
                }}
                value={stepDesc}
            />
        </div>
    );
};

export default PostOperationType;
