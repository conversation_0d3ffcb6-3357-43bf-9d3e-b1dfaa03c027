import PostOperationType from './PostOperationType';
import { useEffect } from 'react';
const PostOperationTypeList = (props) => {
    const { step } = props;
    return (
        <>
            {(step ?? {})?.stepInfo?.postOperation?.map((item, index) => (
                <PostOperationType {...props} key={item?.key} postOperation={item} />
            ))}
        </>
    );
};

export default PostOperationTypeList;
