import Duration from './Operator/Duration';
import Interval from './Operator/Interval';
import Times from './Operator/OperatorTimes';
import styles from './Params.module.less';

function ScreenParams(props) {
    const {step, editType} = props;
    return (
        <>
            {
                step.stepInfo.params.actionInfo.params.duration ||
                    step.stepInfo.params.actionInfo.params.interval ||
                    step.stepInfo.params.actionInfo.params.times ? <div
                    className={styles.stepParamsDivider}
                /> : null
            }
            {
                step.stepInfo.params.actionInfo.params.duration ?
                    <Duration {...props} /> : null
            }
            {
                step.stepInfo.params.actionInfo.params.interval ?
                    <Interval {...props} /> : null
            }
            {
                step.stepInfo.params.actionInfo.params.times ?
                    <Times {...props} /> : null
            }
        </>
    );
};

export default ScreenParams;
