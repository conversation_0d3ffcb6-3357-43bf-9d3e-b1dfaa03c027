import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import SingleElementType from '@step/StepInfo/DomAction/NBDomAction/Setting/SingleElementType';
import MultiElementType from '@step/StepInfo/DomAction/NBDomAction/Setting/MultiElementType';
import MarsSingleElementType from '@step/StepInfo/DomAction/MarsDomAction/Setting/WidgetInfo/SingleElementType';
import MarsMultiElementType from '@step/StepInfo/DomAction/MarsDomAction/Setting/WidgetInfo/MultiElementType';
import ElementType from '@step/StepInfo/AIAction/AILocate/Setting/ElementType';
import ElementParams from '../Params/ElementParams';
import MulitParams from '../Params/MulitParams';
import ScreenParams from '../Params/ScreenParams';
import StepListOverview from '../Params/StepListOverview';
import PostOperationTypeList from '../../StepItem/Params/PostOperationType/PostOperationTypeList';

import styles from './StepItemWithList.module.less';

function StepItemParams(props) {
    const { step, showParams } = props;

    return (
        <div style={{ display: !showParams ? 'none' : 'block' }}>
            {step?.stepInfo?.postOperation && <PostOperationTypeList {...props} step={step} />}
            {1 === step?.stepInfo?.type && 'runTemplate' === step?.stepInfo?.params?.type ? (
                <StepListOverview {...props} step={step} />
            ) : null}
            {2 === step?.stepInfo?.type ? <ElementParams {...props} step={step} /> : null}
            {4 === step?.stepInfo?.type ? <ScreenParams {...props} step={step} /> : null}
            {[5, 6].includes(step?.stepInfo?.type) ? <MulitParams {...props} step={step} /> : null}
            {step?.stepType === 601 ? (
                <div style={{ width: '100%', overflow: 'scroll' }}>
                    <div className={styles.stepParamsDivider} />
                    <ElementType {...props} currentStep={step} sizeType="small" />
                </div>
            ) : null}
            {[7, 8].includes(step?.stepInfo?.type) && step?.stepInfo.params.findType === 0 ? (
                <div style={{ width: '100%', overflow: 'scroll' }}>
                    <div className={styles.stepParamsDivider} />
                    <SingleElementType {...props} currentStep={step} sizeType="small" />
                </div>
            ) : null}
            {[7, 8].includes(step?.stepInfo?.type) && step?.stepInfo.params.findType !== 0 ? (
                <div style={{ width: '100%', overflow: 'scroll' }}>
                    <div className={styles.stepParamsDivider} />
                    <MultiElementType {...props} currentStep={step} sizeType="small" />
                </div>
            ) : null}
            {[9].includes(step?.stepInfo?.type) &&
                step?.stepInfo.params?.findInfo?.chosenTag?.includes('single') && (
                    <div style={{ width: '100%', overflow: 'scroll' }}>
                        <div className={styles.stepParamsDivider} />
                        <SingleElementType {...props} currentStep={step} sizeType="small" />
                    </div>
                )}
            {[9].includes(step?.stepInfo?.type) &&
                step?.stepInfo.params?.findInfo?.chosenTag?.includes('multiple') && (
                    <div style={{ width: '100%', overflow: 'scroll' }}>
                        <div className={styles.stepParamsDivider} />
                        <MultiElementType {...props} currentStep={step} sizeType="small" />
                    </div>
                )}
            {[10].includes(step?.stepInfo?.type) && (
                <div style={{ width: '100%', overflow: 'scroll' }}>
                    <div className={styles.stepParamsDivider} />
                    {step?.stepInfo.params?.findInfo?.widgetInfo?.chosenTag?.includes(
                        'multiple'
                    ) ? (
                        <MarsMultiElementType
                            {...props}
                            currentStep={step}
                            findInfoType="widgetInfo"
                            sizeType="small"
                        />
                    ) : (
                        <MarsSingleElementType
                            {...props}
                            currentStep={step}
                            findInfoType="widgetInfo"
                            sizeType="small"
                        />
                    )}
                </div>
            )}
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace,
    showParams: state.common.base.showParams,
    copyStep: state.common.case.copyStep,
    treeData: state.common.case.treeData
}))(StepItemParams);
