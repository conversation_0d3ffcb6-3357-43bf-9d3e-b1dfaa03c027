.stepInfo {
    margin-left: -3px;
    padding: 10px 5px;
    overflow: hidden;
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 5px;
}

.stepGroupExpandedIcon {
    position: absolute;
    left: 2px;
    color: #777;
    cursor: pointer;
}

.stepGroupInfo {
    background-color: transparent;
    border: none;
    border-radius: 0;
}

.stepInfo:hover {
    background-color: var(--background-color-hover);
}

.activedStepInfo {
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-background-color);
}

.activedStepGroupInfo {
    border: none;
    border-bottom: 1px solid var(--primary-color);
    box-shadow: none;
}

.disabledStepInfo {
    opacity: .5;
}

.hoverDragInsideStepInfo {
    border: 1px dashed var(--primary-color);
    background-color: var(--primary-background-color);
}

.stepNameInfo {
    position: relative;
    display: flex;
    width: 100%;
}

.left {
    max-width: 125px;
    overflow: hidden;

    .stepCheckbox {
        margin-right: 4px;
    }

    // 步骤索引
    .stepIndex {
        display: inline-block;
        width: 20px;
        text-align: center;
        color: #777;
        font-size: 12px;
    }

    .groupWithStepIndex {
        opacity: 0;
    }

}


.stepDescInfo {
    flex: 1;
    position: relative;
    height: 100%;
    overflow: hidden;

    :global {
        .ant-input {
            font-size: 12px !important;
            color: var(--color2) !important;
        }
    }
}

.stepCount {
    float: left;
    text-align: center;
    color: #777;
    font-size: 12px;
}

.defaultStepName {
    display: inline-block;
    min-width: 60px;
    max-width: 140px;
    height: 22px;
    line-height: 22px;
    color: #1890ff;
    font-size: 12px;
    font-weight: 800;
    cursor: pointer;
}

.groupStepName {
    color: gray;
}

.manualStepName {
    color: var(--maunal-color);
}

.assestStepName {
    color: var(--assest-color);
}

.aiAssertStepName {
    color: var(--ai-color);
}

.errorStepName {
    color: var(--error-color);
}

.clearStepName {
    background-color: #e5f1fe;
    color: #1890ff;
}


.stepParamsDivider {
    width: 100%;
    margin: 10px 0 10px 0;
    border-bottom: 1px solid var(--border-color);
    height: 1px;
}

.operationGroup {
    padding-right: 3px;
    background-color: transparent;
    border-radius: 4px;
    display: inline-flex;
    justify-content: center;
    align-items: center;

    .addIcon,
    .divier {
        color: #eee;
        font-size: 12px;
    }
}