import classnames from 'classnames';
import { useState, useMemo, useEffect } from 'react';
import { isEmpty } from 'lodash';
import { Input, Checkbox, Tag, Tooltip } from 'antd';
import {
    CaretDownOutlined,
    CaretRightOutlined,
    CloseOutlined,
    LineOutlined
} from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { getName, isErrorStep } from 'COMMON/components/TreeComponents/Step/utils';
import { STEP_TYPE } from 'COMMON/components/TreeComponents/Step/const';
import DebugStepAnchor from '../Operation/DebugStepAnchor';
import StepTest from '../Operation/StepTest';
import DeleteOperation from '../Operation/Delete';
import AddStepGroupAttr from '../Operation/AddStepGroupAttr';
import AIAssertResTag from '../Operation/AIAssertResTag';
import { getStepStyles } from './utils';
import { ScreenRecordIcon } from './components';
import styles from './StepItemWithList.module.less';
import StepItemParams from './StepItemParams';

function StepItemWithList(props) {
    const {
        curOsType,
        editType = 'edit', // 用例展示位置,参数 - edit: 编辑态; debug: 调试态; execute: 执行态
        index,
        step,
        handleUpdateStep,
        currentStep,
        expanded,
        setExpanded,
        // 勾选能力
        showCheckbox = false,
        isChecked,
        onCheck,
        // 步骤组内步骤
        stepInGroup = false,
        // 拖拽能力
        setDragging,
        // 删除能力
        showDelete = false,
        // 步骤组属性
        showGroupAttr = false,
        // 单步调试
        showTest = false,
        // AI智能校验是否通过展示
        showExecuteAIAssertRes = false,
        // 调试额外信息展示
        showDebugRes = false,
        // 是否展示拖拽区域
        hoverDragInside = false,
        // 刷新步骤列表
        refreshStepList,
        // 类型
        type,
        hookType,
        onAfterDelete
    } = props;
    const { status: isError, msg: errorMsg } = isErrorStep(step);
    const [stepDesc, setStepDesc] = useState(
        currentStep?.stepId === step?.stepId ? currentStep?.stepDesc : step?.stepDesc
    );

    useEffect(() => {
        if (currentStep?.stepId === step?.stepId) {
            setStepDesc(currentStep?.stepDesc || '');
        } else {
            setStepDesc(step?.stepDesc || '');
        }
    }, [currentStep?.stepId, currentStep?.stepDesc, step?.stepId, step?.stepDesc]);
    // 后置操作 展示
    const hasPostOperation = useMemo(() => {
        return step?.stepInfo?.postOperation?.length > 0;
    }, [step?.stepInfo?.postOperation]);

    // ifthen操作 展示
    const showIfThen = useMemo(() => {
        return (
            1 === step?.stepInfo?.type &&
            'runTemplate' === step?.stepInfo?.params?.type &&
            !isEmpty(step?.stepInfo?.params?.params?.condition)
        );
    }, [step?.stepInfo?.params?.params?.condition]);

    // 生成步骤标签 展示
    const showGenerateStepTag = useMemo(() => {
        return step?.creationType === 1;
    }, [step?.creationType]);

    const onFocus = () => {
        setDragging(false);
    };

    const onBlur = () => {
        setDragging(true);
    };

    return (
        <div
            className={classnames(
                styles.stepInfo,
                { [styles.beforeStepInfo]: index === -1 },
                { [styles.activedStepInfo]: step?.stepId === currentStep?.stepId },
                { [styles.hasPostOperation]: hasPostOperation },
                { [styles.disabledStepInfo]: step?.stepAttr?.disabledInfo?.status },
                // 步骤组内步骤移入时的特殊样式
                { [styles.hoverDragInsideStepInfo]: hoverDragInside }
            )}
            style={{ ...getStepStyles(step, currentStep) }}
        >
            <div className={styles.stepNameInfo}>
                <div className={styles.left}>
                    {/* 勾选功能 */}
                    {showCheckbox && !stepInGroup && (
                        <Checkbox
                            className={styles.stepCheckbox}
                            checked={isChecked}
                            onChange={(e) => {
                                onCheck && onCheck(e);
                            }}
                        />
                    )}
                    {/* 步骤索引 */}
                    {(!showCheckbox || stepInGroup) && (
                        <span
                            className={classnames(styles.stepIndex, {
                                [styles.groupWithStepIndex]:
                                    step?.stepType === 1401 && step?.stepChildren?.length > 0
                            })}
                        >
                            {index + 1}
                        </span>
                    )}
                    {/* 步骤索引 */}
                    {!showCheckbox && step?.stepType === 1401 && step?.stepChildren?.length > 0 && (
                        <span
                            className={classnames(styles.stepGroupExpandedIcon)}
                            onClick={(e) => {
                                e.stopPropagation();
                                setExpanded(!expanded);
                            }}
                        >
                            {expanded ? <CaretRightOutlined /> : <CaretDownOutlined />}
                        </span>
                    )}
                    {/* 步骤类型 */}
                    <Tooltip
                        title={'Step ID:' + step?.stepId + (errorMsg ? ' ' + errorMsg : '')}
                        placement="right"
                    >
                        <span
                            className={classnames(styles.defaultStepName, {
                                [styles.groupStepName]: STEP_TYPE?.GROUP?.includes(step?.stepType),
                                [styles.aiAssertStepName]: STEP_TYPE?.AI?.includes(step?.stepType),
                                [styles.errorStepName]: isError,
                                [styles.manualStepName]: step?.stepType === 101,
                                [styles.assestStepName]: step?.stepType === 102,
                                [styles.clearStepName]: step?.stepType === 0
                            })}
                        >
                            {isError && <CloseOutlined />}
                            {getName(
                                step?.stepInfo?.type,
                                step?.stepInfo?.params || step?.stepInfo,
                                step?.stepType
                            )}
                            {step?.stepType === 1401 && (
                                <span>({step?.stepChildren?.length ?? 0})</span>
                            )}
                        </span>
                    </Tooltip>
                </div>
                {/* 步骤描述 */}
                <div className={styles.stepDescInfo}>
                    <Input
                        variant="borderless"
                        className="inputEditor"
                        size="small"
                        tabIndex={-1}
                        placeholder="步骤描述"
                        value={stepDesc}
                        onFocus={onFocus}
                        onChange={(e) => {
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return false;
                            }
                            setStepDesc(e.target.value);
                        }}
                        onBlur={async (e) => {
                            if (['readonly', 'debug', 'execute'].includes(editType)) {
                                return false;
                            }
                            if (step?.stepDesc !== e.target.value) {
                                step.stepDesc = e.target.value;
                                await handleUpdateStep(step, 'desc');
                                setStepDesc(e.target.value);
                            }
                            onBlur && onBlur();
                        }}
                    />
                </div>
                <div className={styles.operationGroup}>
                    {showGroupAttr && (
                        <AddStepGroupAttr
                            {...props}
                            disabled={{
                                aiAssert: step?.stepInfo?.params?.params?.assert?.type === 1
                            }}
                            step={step}
                            refreshStepList={refreshStepList}
                        />
                    )}
                    {showGroupAttr && (showDelete || showTest) && (
                        <LineOutlined className={styles.divier} rotate={90} />
                    )}
                    {showDelete && (
                        <DeleteOperation
                            {...props}
                            step={step}
                            refreshStepList={refreshStepList}
                            type={type}
                            hookType={hookType}
                            onAfterDelete={onAfterDelete}
                        />
                    )}
                    {showTest && (
                        <StepTest step={step} curOsType={curOsType} currentStep={currentStep} />
                    )}
                    {step?.stepType === 1401 &&
                        step?.stepInfo?.params?.params?.assert?.type === 1 && (
                            <Tag color="purple">智能校验</Tag>
                        )}
                    {step?.stepType === 1401 &&
                        step?.stepInfo?.params?.params?.assert?.params?.withScreenRecord && (
                            <ScreenRecordIcon />
                        )}
                    {showIfThen && <Tag color="processing">含判断条件</Tag>}
                    {showGenerateStepTag && <Tag color="green">生成</Tag>}
                    {showExecuteAIAssertRes && <AIAssertResTag currentStep={step} />}
                    {['debug', 'execute']?.includes(editType) && showDebugRes && (
                        <DebugStepAnchor
                            step={step}
                            currentStep={currentStep}
                            curOsType={curOsType}
                        />
                    )}
                </div>
            </div>
            {/* 步骤简要参数 */}
            <StepItemParams {...props} onFocus={onFocus} onBlur={onBlur} />
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace,
    showParams: state.common.base.showParams,
    copyStep: state.common.case.copyStep,
    treeData: state.common.case.treeData
}))(StepItemWithList);
