// 获取展示样式
export const getStepStyles = (step, currentStep) => {
    let bgc;
    let bd;
    let tc;
    let tagC;
    if (3 === step?.stepInfo?.type) {
        switch (step?.stepInfo?.params?.type) {
            case 'assest':
                tagC = '#ea722b';
                break;
            default:
                tagC = '#f9a52d';
                break;
        }
    }
    let boxS = '';
    let isClick = currentStep?.stepId === step.stepId;
    switch (step?.result?.status) {
        case 2:
            // 执行中
            bgc = '#def0ff';
            bd = '1px solid #3686f6';
            tagC = '#3686f6';
            boxS = '0 0 0 3px rgb(54 134 246 / 12%)';
            break;
        case 0:
            bgc = '#f5ffea';
            bd = '1px solid #aee884';
            tagC = '#48bc19';
            boxS = '0 0 0 3px rgb(105 185 61 / 12%)';
            if (
                3 === step?.stepInfo?.type &&
                'assest' === step?.stepInfo?.params?.type
            ) {
                bgc = '#fae1d0';
                bd = '1px solid #ea722b';
                tagC = '#ea722b';
                boxS = '0 0 0 3px rgb(236 84 78 / 12%)';
            }
            break;
        case -1:
            bgc = '#fff0ee';
            bd = '1px solid #ffc5c0';
            tagC = '#ff4446';
            boxS = '0 0 0 3px rgb(236 84 78 / 12%)';
            break;
        case 1:
            bgc = '#fae1d0';
            bd = '1px solid #fa541c';
            tagC = '#fa541c';
            boxS = '0 0 0 3px rgb(236 84 78 / 12%)';
            break;
        default:
            break;
    }
    return {
        backgroundColor: bgc,
        color: tc,
        border: bd,
        boxShadow: isClick ? boxS : '',
    };
};