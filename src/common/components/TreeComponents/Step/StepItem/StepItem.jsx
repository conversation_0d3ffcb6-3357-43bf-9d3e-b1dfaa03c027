import { message, Dropdown } from 'antd';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { createPoint } from 'COMMON/api/front_qe_tools/points';
import { updateStep, batchCopyStepList } from 'COMMON/api/front_qe_tools/step';
import { deepcopy } from 'COMMON/components/TreeComponents/Step/utils';
import styles from './StepItem.module.less';
import StepItemWithList from './StepItemWithList';

function StepItem(props) {
    const {
        editType = 'edit', // 用例展示位置,参数 - edit: 编辑态; debug: 调试态; execute: 执行态
        index,
        nodeId,
        step,
        handleUpdateStep,
        curOsType,
        currentStep,
        stepList,
        handleUpdateStepList,
        copyStep,
        setCopyStep,
        setRecording,
        currentSpace,
        refreshStepList,
        type,
        hookType
    } = props;

    const toggleDisableStep = async () => {
        const newStatus = !step.stepAttr?.disabledInfo?.status;
        const updatedStep = {
            // ...step,
            stepId: step.stepId,
            stepAttr: {
                ...step.stepAttr,
                disabledInfo: {
                    status: newStatus,
                    type: 1,
                    message: newStatus ? '禁用' : '启用'
                }
            }
        };

        updateStep(updatedStep).then(() => {
            message.success(newStatus ? '步骤已禁用' : '步骤已启用');
            const newStepList = [...stepList];
            const stepIndex = newStepList.findIndex((item) => item.stepId === step.stepId);
            if (stepIndex !== -1) {
                newStepList[stepIndex] = {
                    ...step,
                    ...updatedStep
                };
                handleUpdateStepList(newStepList, {
                    ...step,
                    ...updatedStep
                });
            }
        });
    };

    const items = [
        {
            label: '复制',
            key: '1'
        },
        {
            label: '粘贴',
            key: '2'
        }
    ];

    const onClick = async ({ key }) => {
        try {
            if (-1 === index) {
                message.warning('前置步骤不可复制粘贴');
                return false;
            }
            if (
                [9, 10].includes(step.stepInfo.type) &&
                (step?.stepInfo?.params?.deviceInfo?.screenshot === '' ||
                    step?.stepInfo?.params?.deviceInfo?.screenshot?.startsWith('data'))
            ) {
                message.warning('图片未上传成功，步骤不可复制粘贴');
                return false;
            }
            switch (key) {
                case '1':
                    setCopyStep(deepcopy(step));
                    message.success('复制成功');
                    break;
                case '2':
                    if (null === copyStep) {
                        message.warning('请先复制步骤');
                        return false;
                    }
                    if (
                        editType === 'template' &&
                        copyStep?.stepInfo?.params.type === 'runTemplate'
                    ) {
                        message.warning('测试片段内不可复制粘贴 测试片段步骤');
                        return false;
                    }
                    // 获取步骤添加位置
                    setRecording(true);
                    let stepIdList = [copyStep?.stepId];
                    // stepGroup 补充子步骤id
                    if (copyStep?.stepType === 1401) {
                        stepIdList = [
                            ...stepIdList,
                            ...(copyStep?.stepInfo?.params?.params?.stepIdList || [])
                        ];
                    }
                    let res = await batchCopyStepList({
                        stepIdList: stepIdList,
                        targetCaseNodeId: +nodeId,
                        targetOsType: curOsType,
                        preSibId: step?.stepId
                    });
                    let newStepId = res?.stepIdList[0];
                    let otherStepIdList = res?.stepIdList?.slice(1);
                    let newStep = {
                        ...copyStep,
                        stepId: newStepId
                    };
                    // 若复制的步骤组
                    if (copyStep?.stepType === 1401) {
                        newStep.stepChildren = newStep.stepChildren?.map((stepChild, index) => ({
                            ...stepChild,
                            stepId: otherStepIdList[index]
                        }));
                        newStep.stepInfo.params.params.stepIdList = otherStepIdList;
                    }
                    let newStepList = [];
                    let hasStep = false;
                    // 复制的步骤
                    for (let _step of stepList) {
                        newStepList.push(_step);
                        if (_step.stepId === step?.stepId) {
                            hasStep = true;
                            newStepList.push(newStep);
                        }
                    }
                    // 复制的步骤组里的步骤
                    if (!hasStep) {
                        newStepList = [];
                        for (let _step of stepList) {
                            if (_step.stepType === 1401) {
                                let _childList = [];
                                for (let _stepChild of _step.stepChildren) {
                                    _childList.push(_stepChild);
                                    if (_stepChild.stepId === step?.stepId) {
                                        hasStep = true;
                                        _childList.push({
                                            ...copyStep,
                                            stepId: newStepId
                                        });
                                    }
                                }
                                _step.stepInfo.params.params.stepIdList = _childList.map(
                                    (item) => item.stepId
                                );
                                _step.stepChildren = _childList;
                            }
                            newStepList.push(_step);
                        }
                    }
                    await handleUpdateStepList(newStepList, newStep);
                    createPoint({
                        moduleId: currentSpace?.id, // 业务模块id；int；必填
                        caseNodeId: nodeId, // 用例节点Id；int；必填
                        stepId: currentStep?.stepId, // 步骤Id；int；必填
                        osType: curOsType, // 端类型，int；必填 1-Android 2-iOS 3-Android&iOS 4-server 5-web
                        pointType: 1002, // 打点类型；int；必填 1000-创建步骤 1001-选中步骤 1002-复制步骤 1100-update
                        pointInfo: {}, // 点位内容；json；选填（预留字段，当前传空 json 就行）
                        createTime: Math.floor(new Date().getTime() / 1000) // 打点时间；int；必填
                    }).catch(() => {});
                    setRecording(false);
                    break;
                case '3':
                    await toggleDisableStep();
                    break;
                default:
                    break;
            }
        } catch (err) {
            setRecording(false);
        }
    };

    return (
        <Dropdown
            menu={{
                items:
                    curOsType === 4
                        ? [
                              ...items,
                              {
                                  label: step.stepAttr?.disabledInfo?.status ? '启用' : '禁用',
                                  key: '3'
                              }
                          ]
                        : items,
                onClick
            }}
            disabled={['readonly', 'debug', 'execute'].includes(editType)}
            placement="topRight"
            trigger={['contextMenu']}
        >
            <div className={styles.stepItem}>
                <StepItemWithList
                    {...props}
                    handleUpdateStep={handleUpdateStep}
                    refreshStepList={refreshStepList}
                    type={type}
                    hookType={hookType}
                />
            </div>
        </Dropdown>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    recording: state.common.base.recording,
    currentSpace: state.common.base.currentSpace,
    showParams: state.common.base.showParams,
    copyStep: state.common.case.copyStep,
    treeData: state.common.case.treeData
}))(StepItem);
