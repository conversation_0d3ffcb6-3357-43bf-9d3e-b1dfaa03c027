import {isEmpty} from 'lodash';

// 获取节点类型 自动化 / 半自动化 / 手动
export const getExecutionType = (item, stepList) => {
        let executionType = item.executionType;
        if (isEmpty(stepList)) {
            executionType = 0;
        } else if (stepList
            .find(item => item.stepType === 101)) {
            executionType = 1;
        } else if (stepList
            .find(item => item.stepType === 102)) {
            executionType = 3;
        } else {
            executionType = 2;
        }
        return executionType;
    };
