import { Tooltip } from 'antd';
import MobileStepTest from './MobileStepTest';
import ServerStepTest from './ServerStepTest';
import styles from './StepTest.module.less';

function StepTest(props) {
    const {
        curOsType,
    } = props;

    return (
        <Tooltip title={'单步调试'} placement='right'>
            <span className={styles.abbrIcon}>
                {[1, 2]?.includes(curOsType) && <MobileStepTest {...props} />}
                {[4]?.includes(curOsType) && <ServerStepTest {...props} />}
            </span>
        </Tooltip>
    );
};

export default StepTest;
