import {message} from 'antd';
import classnames from 'classnames';
import {
    PlayCircleOutlined,
} from '@ant-design/icons';
import electron from 'COMMON/utils/electron';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {getRealStepDetail} from './utils';
import styles from './StepTest.module.less';

function StepTest(props) {
    const {step, currentDevice, setRecording, curOsType, schemeList, mockList,
        setMockList, currentEnv,
        paramList, setTestStep, setTestRes,
        snippetList, className} = props;

    // 安装 APP / 登录账号 步骤 不支持单步调试
    if ([424, 425]?.includes(step?.stepType)) {
        return;
    }
    // 安卓 和 iOS 单步调试
    const mobileStepTest = async () => {
        try {
            if (!currentDevice?.deviceId) {
                message.error('暂无设备');
                return;
            }
            let realStep = await getRealStepDetail(step, {currentEnv, curOsType, snippetList, schemeList, mockList});
            let finalRes = null;
            setTestStep(realStep);
            let body = {
                deviceType: curOsType,
                deviceId: currentDevice?.deviceId,
                step: realStep.stepInfo,
                needDeviceInfo: -1 === ['mock', 'requestVerify'
                ].indexOf(realStep.stepInfo.params.type),
                paramsList: paramList.map(item => ({
                    name: item.paramKey,
                    value: item.paramValue
                }))
            };
            setRecording(true);
            finalRes = await electron.send('device.replay.step', body);
            setTestRes(finalRes);
            if (0 !== finalRes.status) {
                message.error(finalRes.msg);
            } else {
                message.success('执行成功');
            }
            if (1 === realStep.stepInfo.type && 'mock' === realStep.stepInfo.params.type) {
                if (currentDevice) {
                    let res = await electron.send('proxy.mock.get',
                        {deviceType: curOsType, deviceId: currentDevice?.deviceId});
                    setMockList(res);
                }
            }
            if (-1 !== ['clearProxy', 'clearMock'].indexOf(realStep.stepInfo.params.type)) {
                if (currentDevice) {
                    let res = await electron.send('proxy.mock.get',
                        {deviceType: curOsType, deviceId: currentDevice?.deviceId});
                    setMockList(res);
                }
            }
        } catch (err) {
            console.log(err);

        }
    };

    let jsx = (
        <PlayCircleOutlined
            className={classnames(styles.stepTest, className)}
            onClick={async (e) => {
                try {
                    setTestRes(null);
                    if ([1, 2]?.includes(curOsType)) {
                        await mobileStepTest();
                    }
                } catch (err) {
                } finally {
                    setRecording(false);
                }
            }}
        />
    );

    return jsx;
};

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    currentEnv: state.common.base.currentEnv,
    username: state.common.base.username,
    recording: state.common.base.recording,
    testStep: state.common.case.testStep,
    testRes: state.common.case.testRes,
    paramList: state.common.case.paramList,
    mockList: state.common.case.mockList,
    snippetList: state.common.case.snippetList,
    schemeList: state.common.case.schemeList,
}))(StepTest);
