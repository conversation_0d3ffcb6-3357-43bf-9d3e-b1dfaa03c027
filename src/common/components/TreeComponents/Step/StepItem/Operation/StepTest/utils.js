import { message } from 'antd';
import { getStepList } from 'COMMON/api/front_qe_tools/step';

// 获取模板步骤详情
const getTemplateDetail = async (stepList, configData) => {
    let steps = [];
    for (let item of stepList) {
        let data = await getRealStepDetail(item, configData);
        steps.push(data);
    }
    return steps;
};

// 获取真实步骤信息
export const getRealStepDetail = async (step, configData) => {
    try {
        const { currentEnv, curOsType, snippetList, schemeList, mockList } = configData;
        if (['launchApp', 'authApp', 'clearApp'].includes(step.stepInfo.params.type)) {
            if (!currentEnv?.envId) {
                message.error('请先选择或配置运行环境');
                return;
            }
            let app = currentEnv?.appList?.find((_item) => _item.appId === step.stepInfo.params.params?.id);
            if (!app) {
                message.error('该 APP 包名查找失败，请确认 APP 库中是否存在 ID:' + step.stepInfo.params.params?.id);
                throw new Error();
            }
            step.stepInfo.params.params = {
                ...step.stepInfo.params.params,
                packageName: app.envValue
            };
        }
        if (['scheme'].includes(step.stepInfo.params.type)) {
            let scheme =
                schemeList?.[curOsType]?.find((_item) => _item.schemeId === step.stepInfo.params.params?.id)
                    ?.schemeContent ||
                step?.stepInfo?.params?.params?.scheme ||
                step?.stepInfo?.params?.params?.schemeContent;
            if (!scheme) {
                message.error('该 Scheme 查找失败，请确认 Scheme 库中是否存在');
                throw new Error();
            }
            step.stepInfo.params.params = {
                ...step.stepInfo.params.params,
                scheme
            };
        }
        // stepGroup
        if (step.stepType === 1401) {
            step.stepInfo.params.params.step = await getTemplateDetail(step.stepChildren, configData);
        }
        if (['clearPop'].includes(step.stepInfo.params.type)) {
            let popDetail = [];
            for (let item of snippetList?.[curOsType]) {
                if (-1 !== step.stepInfo.params.params.popList.indexOf(item.templateId)) {
                    let { stepList } = await getStepList({
                        caseNodeId: item.caseNodeId,
                        osType: curOsType
                    });
                    popDetail.push({
                        id: item.templateId,
                        step: stepList
                    });
                }
            }
            step.stepInfo.params.params.popDetail = popDetail;
        }
        if (['mock'].includes(step.stepInfo.params.type)) {
            let isMock = false;
            mockList.forEach((element) => {
                if (
                    Object.entries(element.mockRequest.matchRequest).toString() ===
                    Object.entries(step.stepInfo.params.params.mockRequest.matchRequest).toString()
                ) {
                    isMock = true;
                }
            });
            if (isMock) {
                message.warning('该 Mock 已存在!');
                throw new Error();
            }
        }
        if (['runTemplate'].includes(step.stepInfo.params.type)) {
            if (null === step.stepInfo.params.params.id) {
                message.error('请先选择测试片段');
                throw new Error();
            }
            let templateSteps = [];
            for (let item of snippetList?.[curOsType]) {
                if (item.templateId === step.stepInfo.params.params.id) {
                    let { stepList } = await getStepList({
                        caseNodeId: item.caseNodeId,
                        osType: curOsType
                    });
                    templateSteps = stepList;
                    if (0 === templateSteps.length) {
                        message.error('测试片段步骤为空');
                        throw new Error();
                    }
                    break;
                }
            }
            step.stepInfo.params.params.step = await getTemplateDetail(templateSteps, configData);
        }
        return step;
    } catch (err) {
        console.log(err);
    }
};
