import {useState} from 'react';
import {Tooltip} from 'antd';
import {ApiOutlined} from '@ant-design/icons';
import ApiModal from 'COMMON/components/TreeComponents/components/ApiModal/ApiModal';
import styles from './RequestRes.module.less';

function RequestRes(props) {
    const {
        currentStep
    } = props;
    const [openApiModal, setOpenApiModal] = useState(false);

    return (
        <>
            <Tooltip title="查看校验结果" placement="right">
                <ApiOutlined
                    className={styles.abbrIcon}
                    onClick={() => {
                        setOpenApiModal(true);
                    }}
                />
            </Tooltip>
            <ApiModal
                testRes={currentStep?.result}
                currentStep={currentStep}
                open={openApiModal}
                setOpen={setOpenApiModal}
            />
        </>
    );
}

export default RequestRes;
