import {useState} from 'react';
import {Tooltip} from 'antd';
import {AlignLeftOutlined} from '@ant-design/icons';
import VerifyModal from 'COMMON/components/TreeComponents/components/VerifyModal/VerfiyModal';
import styles from './RequestVerifyRes.module.less';

function RequestVerifyRes(props) {
    const {
        currentStep
    } = props;
    const [openVerfiyModal, setOpenVerfiyModal] = useState(false);

    return (
        <>
            <Tooltip title="查看校验结果" placement="right">
                <AlignLeftOutlined
                    className={styles.abbrIcon}
                    onClick={() => {
                        setOpenVerfiyModal(true);
                    }}
                />
            </Tooltip>
            <VerifyModal
                key='debug_verifyModal'
                testRes={currentStep?.result}
                currentStep={currentStep}
                open={openVerfiyModal}
                setOpen={setOpenVerfiyModal}
            />
        </>
    );
}

export default RequestVerifyRes;
