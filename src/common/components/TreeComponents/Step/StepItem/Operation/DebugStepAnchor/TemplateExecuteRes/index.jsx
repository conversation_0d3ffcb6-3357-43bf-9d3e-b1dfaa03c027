import { useState } from 'react';
import { Tooltip } from 'antd';
import { OrderedListOutlined } from '@ant-design/icons';
import TemplateExecuteInfoModal from 'COMMON/components/TreeComponents/components/TemplateExecuteInfoModal';
import { getTemplateResult } from 'FEATURES/front_qe_tools/case/edit/EditPage/util';
import styles from './TemplateExecuteRes.module.less';

function TemplateExecuteRes(props) {
    const { currentStep, curOsType } = props;
    const [openTemplateExecuteInfoModal, setOpenTemplateExecuteInfoModal] = useState(false);

    return (
        <>
            <Tooltip title="查看校验结果" placement="right">
                <OrderedListOutlined
                    className={styles.abbrIcon}
                    onClick={() => {
                        setOpenTemplateExecuteInfoModal(true);
                    }}
                />
            </Tooltip>
            <TemplateExecuteInfoModal
                testRes={getTemplateResult(currentStep?.result)}
                currentStep={currentStep}
                curOsType={curOsType}
                open={openTemplateExecuteInfoModal}
                setOpen={setOpenTemplateExecuteInfoModal}
            />
        </>
    );
}

export default TemplateExecuteRes;
