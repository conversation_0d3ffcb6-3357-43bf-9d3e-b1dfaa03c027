import {FileImageOutlined} from '@ant-design/icons';
import RequestVerifyRes from './RequestVerifyRes';
import RequestRes from './RequestRes';
import TemplateExecuteRes from './TemplateExecuteRes';
import styles from './DebugStepAnchor.module.less';

function DebugStepAnchor(props) {
    const {
        currentStep, step
    } = props;
    // 默认展示
    const showDebugDefaultRes = currentStep?.stepId === step.stepId &&
        currentStep?.result?.status !== -2;
    // 查看校验结果（调试/执行）
    const showRequestVerifyRes = currentStep?.stepId === step.stepId &&
        [0, -1].indexOf(currentStep?.result?.status) !== -1 &&
        'requestVerify' === currentStep.stepInfo.params?.type;
    // 查看请求结果（调试/执行）
    const showApiRes = currentStep?.stepId === step.stepId &&
        [0, -1].indexOf(currentStep?.result?.status) !== -1 &&
        'request' === currentStep.stepInfo?.params?.type;
    // 查看测试片段结果
    const showTemplateExecuteRes = currentStep?.stepId === step.stepId &&
        [0, -1].indexOf(currentStep?.result?.status) !== -1 &&
        'runTemplate' === currentStep.stepInfo?.params?.type;

    return (
        <>
            {showDebugDefaultRes && (!showRequestVerifyRes && !showApiRes && !showTemplateExecuteRes) &&
                <FileImageOutlined
                    className={styles.abbrIcon}
                />}
            {showRequestVerifyRes &&
                <RequestVerifyRes {...props} />}
            {showApiRes &&
                <RequestRes {...props} />}
            {showTemplateExecuteRes &&
                <TemplateExecuteRes {...props} />}
        </>
    );
}

export default DebugStepAnchor;
