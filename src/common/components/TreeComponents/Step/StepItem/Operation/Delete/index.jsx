import { Popconfirm, Tooltip, message } from 'antd';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import { DeleteOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { updateStepAttrInIsDel, deleteSetupTeardownNode } from 'COMMON/api/front_qe_tools/step';
import { treeAction } from 'FEATURES/front_qe_tools/case/edit/EditPage/CaseDetailView/LayoutSider/utils';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import styles from './Delete.module.less';

function Delete(props) {
    const {
        step,
        currentNode,
        stepList,
        treeData,
        setTreeData,
        setRecording,
        handleUpdateStepList,
        refreshStepList,
        type,
        hookType,
        curOsType,
        onAfterDelete
    } = props;
    const query = getQueryParams();
    // 查找caseNodeId
    const findNodeByCaseNodeId = (tree, targetCaseNodeId) => {
        if (!tree || tree.length === 0) {
            return null;
        }

        for (const node of tree) {
            if (+node.caseNodeId === +targetCaseNodeId) {
                return node;
            }
            if (node.children && node.children.length > 0) {
                const found = findNodeByCaseNodeId(node.children, targetCaseNodeId);
                if (found) {
                    return found;
                }
            }
        }

        return null;
    };

    // 更新树中节点的hookInfo
    const updateNodeHookInfo = (targetNodeId, hookKey) => {
        const osTypeKey = convertOsTypeToType(curOsType);
        const nodeToUpdate = findNodeByCaseNodeId(treeData, targetNodeId);
        if (nodeToUpdate) {
            const originalNodeType = nodeToUpdate.nodeType;
            if (nodeToUpdate.extra && nodeToUpdate.extra[hookKey]) {
                if (nodeToUpdate.extra[hookKey][osTypeKey]) {
                    delete nodeToUpdate.extra[hookKey][osTypeKey];
                }
            }
            setTreeData([...treeData]);
            return true;
        }
    };

    // 删除步骤
    const handleDeleteStep = async (stepIdList) => {
        try {
            // 保存原始类型
            const originalNodeType = currentNode?.nodeType;
            let _stepList = [];
            let _curStep = null;
            // 步骤索引
            let stepIndex = {
                flag: false,
                value: -1
            };
            // 步骤组中的步骤索引
            let stepInGroupIndex = {
                groupId: null,
                flag: false,
                value: -1
            };
            // 构造新stepList, 删除当前选中的步骤
            stepList.forEach((step, index) => {
                if (!stepIdList?.includes(step.stepId)) {
                    // 步骤组
                    if (step?.stepType === 1401) {
                        step.stepChildren = step.stepChildren.filter((child, _index) => {
                            let flag = !stepIdList?.includes(child?.stepId);
                            if (!flag && !stepInGroupIndex.flag) {
                                stepInGroupIndex = {
                                    groupId: step.stepId,
                                    value: _index,
                                    flag: true
                                };
                            }
                            return flag;
                        });
                        step.stepInfo.params.params.stepIdList =
                            step.stepInfo.params.params.stepIdList.filter(
                                (id) => !stepIdList?.includes(id)
                            );
                    }
                    _stepList.push(step);
                } else {
                    if (!stepIndex.flag) {
                        stepIndex = {
                            value: index,
                            flag: true
                        };
                    }
                }
            });
            // 找到第一个删除步骤，删除后展示它的上一个步骤
            // 步骤组中删除的
            if (stepInGroupIndex.value !== -1) {
                let groupStep = _stepList.find((item) => item.stepId === stepInGroupIndex.groupId);
                if (stepInGroupIndex.value === 0 && groupStep?.stepChildren?.length === 0) {
                    _curStep = groupStep;
                } else {
                    _curStep = _stepList.find((item) => item.stepId === stepInGroupIndex.groupId)
                        ?.stepChildren[
                        stepInGroupIndex.value - 1 > 0
                            ? stepInGroupIndex.value - 1
                            : stepInGroupIndex.value
                    ];
                }
            }
            // 步骤中删除的
            if (stepIndex.value !== -1) {
                _curStep = _stepList[+stepIndex.value > 0 ? stepIndex.value - 1 : stepIndex.value];
            }

            setRecording(true);
            await updateStepAttrInIsDel({
                stepIdList: stepIdList,
                isDel: true
            });
            // 获取新步骤展示
            if (isEmpty(_stepList)) {
                handleUpdateStepList([], null);
                // 如果删除的是最后一项，且类型为setupTeardown，则删除对应节点
                if (type === 'setupTeardown') {
                    if (query?.caseNodeId) {
                        try {
                            // 删除setup或teardown节点
                            await deleteSetupTeardownNode({
                                caseNodeId: query?.caseNodeId,
                                osType: curOsType,
                                hookType: hookType // 1-setup 2-teardown
                            });
                            // 查找目标节点 - 优先使用URL中的caseNodeId参数
                            const targetNodeId = query?.caseNodeId || currentNode?.caseNodeId;
                            if (targetNodeId && setTreeData && treeData) {
                                // 更新树信息中的hookInfo
                                const treeHookKey = hookType === 1 ? 'setupInfo' : 'teardownInfo';
                                updateNodeHookInfo(targetNodeId, treeHookKey);
                            }
                        } catch (deleteError) {
                            console.error('删除节点API调用失败:', deleteError);
                        }
                    }
                }
            } else {
                handleUpdateStepList(_stepList, _curStep);
            }

            // 更新executionType
            if (currentNode?.caseNodeId) {
                setTreeData(
                    treeAction(
                        [...treeData],
                        'children',
                        +currentNode.caseNodeId,
                        (node, item, index) => {
                            const itemOriginalType = item.nodeType;
                            if (item.nodeType !== itemOriginalType && originalNodeType) {
                                item.nodeType = originalNodeType;
                            }
                        }
                    )
                );
            }
            setRecording(false);
            if (type === 'setupTeardown') {
                onAfterDelete && onAfterDelete();
                refreshStepList();
            }
        } catch (err) {
            console.error('err:', err);
        } finally {
            setRecording(false);
        }
    };

    return (
        <Tooltip title="删除" placement="top">
            <Popconfirm
                placement="right"
                title={'确定要删除吗？'}
                okText="确定"
                cancelText="取消"
                destroyTooltipOnHide
                disabled={global.params.CASE_STATUS !== 'edit'}
                onConfirm={(e) => {
                    e.stopPropagation();
                    let stepIdList = [step?.stepId];
                    if (step?.stepType === 1401) {
                        stepIdList = [
                            step?.stepId,
                            ...(step?.stepInfo?.params?.params?.stepIdList ?? [])
                        ];
                    }
                    handleDeleteStep(stepIdList);
                }}
                onClick={(e) => {
                    e.stopPropagation();
                }}
            >
                <DeleteOutlined
                    className={classnames(styles.delete, styles.abbrIcon)}
                    onClick={(e) => {
                        e.stopPropagation();
                        if (global.params.CASE_STATUS !== 'edit') {
                            message.warning('暂无权限删除');
                            return;
                        }
                    }}
                />
            </Popconfirm>
        </Tooltip>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    treeData: state.common.case.treeData,
    curOsType: state.common.case.curOsType
}))(Delete);
