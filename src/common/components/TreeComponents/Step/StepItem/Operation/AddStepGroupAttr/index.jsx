import {Dropdown} from 'antd';
import {PlusOutlined} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { intelligentCheckWhitelist } from 'COMMON/config/whiteList';

import styles from './AddStepGroupAttr.module.less';

function AddStepGroupAttr(props) {
    const {
        step,
        disabled,
        handleUpdateStep,
        onChange,
        title,
        children,
        currentSpace,
        spaceList
    } = props;

    // 检查是否在白名单中
    if (!intelligentCheckWhitelist(currentSpace, spaceList)) {
        return null;
    }

    let items = [{
        key: '1',
        disabled: disabled?.aiAssert ?? false,
        label: '智能校验',
        onClick: async () => {
            let newStep = {...step};
            newStep.stepInfo.params.params.assert = {
                type: 1,
                params: {
                    inputAssert: '',
                    withScreenRecord: false,
                    modelName: 'doubao-1.5-vision-pro-250328'
                }
            };
            await handleUpdateStep(newStep);
            onChange && onChange();
        }
    }];

    return (
        <Dropdown
            menu={{items}}
            placement="bottomLeft"
            trigger={['hover']}
        >
            {children ? children : <span
                className={styles.abbrIcon}
                onClick={(e) => {
                    e.stopPropagation();
                }}
            >
                <PlusOutlined />
                {title}
            </span>}
        </Dropdown>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    treeData: state.common.case.treeData,
    curOsType: state.common.case.curOsType,
    currentSpace: state.common.base.currentSpace,
    spaceList: state.common.base.spaceList
}))(AddStepGroupAttr);
