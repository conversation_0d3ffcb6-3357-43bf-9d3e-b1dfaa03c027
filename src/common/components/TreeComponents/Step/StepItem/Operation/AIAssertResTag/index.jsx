import classnames from 'classnames';
import styles from './AIAssertResTag.module.less';

function AIAssertResTag(props) {
    const {
        currentStep
    } = props;

    return (
        <span
            className={classnames(
                {
                    [styles.aiAssertStepSuccessInfo]:
                        currentStep?.result?.status === 0
                },
                {
                    [styles.aiAssertStepErrorInfo]:
                        currentStep?.result?.status !== 0
                }
            )}
        >
            {
                currentStep?.result?.status === 0 ? '校验通过' : '校验不通过'
            }
        </span>
    );
}

export default AIAssertResTag;
