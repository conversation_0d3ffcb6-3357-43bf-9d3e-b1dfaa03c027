import {useState} from 'react';
import classnames from 'classnames';
import {Input, Tooltip} from 'antd';
import {FullscreenExitOutlined} from '@ant-design/icons';
import styles from './AIAssertRes.module.less';

const {TextArea} = Input;

function AIAssertRes(props) {
    const {status, reason, msg, stepType} = props;
    const [showTextArea, setShowTextArea] = useState(false);

    return (
        <>
            {
                !showTextArea &&
                <div
                    className={classnames(
                        {[styles.AIAssertContentSuccess]: status === 0},
                        {[styles.AIAssertContentError]: status !== 0}

                    )}
                    onClick={(e) => {
                        e.stopPropagation();
                        setShowTextArea(true);
                    }}
                >
                    <div>
                        <span className={styles.AIAssertTitle}>
                            {stepType === 601
                                ? status === 0
                                    ? '定位成功'
                                    : '定位失败'
                                : status === 0
                                ? '校验成功'
                                : '校验失败'}
                        </span>
                    </div>
                    <Tooltip title="点击">
                        <div className={styles.AIAssertDesc}>
                            {
                                status === 0 ?
                                    <span>
                                        思考内容：{reason}
                                    </span> :
                                    <span>
                                        错误原因：{reason ?? msg}
                                    </span>
                            }
                        </div>
                    </Tooltip>
                </div>
            }
            {
                showTextArea &&
                <div
                    className={classnames(
                        {[styles.AIAssertContentTextAreaSuccess]: status === 0},
                        {[styles.AIAssertContentTextAreaError]: status !== 0}

                    )}
                    onClick={() => setShowTextArea(true)}
                >
                    <div>
                        <span className={styles.AIAssertTitle}>
                            {status === 0 ? '校验成功' : '校验失败'}
                        </span>
                        <Tooltip title="收起">
                            <span
                                className={styles.AIAssertTitleClose}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setShowTextArea(false);
                                }}
                            >
                                <FullscreenExitOutlined />
                            </span>
                        </Tooltip>
                    </div>
                    <div className={styles.AIAssertDescTextArea}>
                        <TextArea
                            value={status === 0 ?
                                '思考内容：' + reason :
                                '错误原因：' + (reason ?? msg)
                            }
                            className='input_editor'
                            size='small'
                            style={{height: 70, resize: 'none'}}
                            placeholder='暂无校验内容'
                            variant='borderless'
                        />
                    </div>
                </div>
            }
        </>
    );
};

export default AIAssertRes;