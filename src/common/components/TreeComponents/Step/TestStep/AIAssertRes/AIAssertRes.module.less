.AIAssertContentSuccess,
.AIAssertContentError,
.AIAssertContentTextAreaSuccess,
.AIAssertContentTextAreaError {
    position: absolute;
    width: calc(100% - 30px);
    border-radius: 3px;
    z-index: 9;

    :global {
        .ant-input-affix-wrapper-sm {
            font-size: 12px !important;
        }

        .ant-input-sm {
            font-size: 12px !important;
        }
    }
}

.AIAssertContentSuccess,
.AIAssertContentTextAreaSuccess {
    background-color: var(--success-background-color);
    border: 1px solid var(--success-border-color);

    .AIAssertTitle {
        background-color: var(--success-color);
    }
}

.AIAssertContentError,
.AIAssertContentTextAreaError {
    background-color: var(--error-background-color);
    border: 1px solid var(--error-border-color);

    .AIAssertTitle {
        background-color: var(--error-color);
    }
}

.AIAssertContentSuccess,
.AIAssertContentError {
    height: 40px;
    line-height: 40px;
}

.AIAssertContentTextAreaSuccess,
.AIAssertContentTextAreaError {
    height: 150px;
}

.AIAssertTitle {
    position: absolute;
    top: 10px;
    left: 15px;
    height: 20px;
    line-height: 20px;
    width: 64px;
    color: #fff;
    font-size: 11px;
    text-align: center;
    border-radius: 3px;
}

.AIAssertTitleClose {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 11px;
    color: #777;
    cursor: pointer;
}

.AIAssertDesc {
    width: calc(100% - 100px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 90px;
    font-size: 12px;
    cursor: pointer;
}

.AIAssertDescTextArea {
    margin-top: 40px;
    padding: 0 10px;
}