import { Row, Col, Tooltip, Modal } from 'antd';
import { useRef, useEffect, useState } from 'react';
import classnames from 'classnames';
import { CheckCircleTwoTone, CloseCircleTwoTone, CloseOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { getName } from 'COMMON/components/TreeComponents/Step/utils';
import TemplateExecuteInfoModal from 'COMMON/components/TreeComponents/components/TemplateExecuteInfoModal';
import { getTemplateResult } from 'FEATURES/front_qe_tools/case/edit/EditPage/util';
import StepRes from 'COMMON/components/TreeComponents/Step/StepRes/StepRes';
import StepInfo from '../StepInfo';
import VerfiyModal from '../VerifyModal/VerfiyModal';
import MockModal from '../MockModal';
import ApiModal from '../ApiModal/ApiModal';
import styles from './TestStep.module.less';

function TestStep({ open, testStep, setTestStep, curOsType, setOpen, setShowModal, testRes }) {
    const mockModalRef = useRef();

    if (!testStep?.stepId || testRes === null || testStep === null) {
        return;
    }

    return (
        <>
            <span
                className={classnames(styles.caseInfoTest, {
                    [styles.successInfoTest]: 0 === testRes.status,
                    [styles.errorInfoTest]: 0 !== testRes.status
                })}
            >
                <div
                    className={styles.close}
                    onClick={() => {
                        setTestStep(null);
                    }}
                >
                    关闭
                </div>
                <Tooltip title={0 === testRes.status ? '' : testRes.msg} placement="left">
                    <div className={styles.caseInfoName}>
                        {0 === testRes.status ? (
                            <CheckCircleTwoTone twoToneColor={'#8ace55'} />
                        ) : (
                            <CloseCircleTwoTone twoToneColor={'#ec544e'} />
                        )}
                        {getName(
                            testStep?.stepInfo?.type,
                            testStep.stepInfo.params || testStep.stepInfo,
                            testStep?.stepType
                        )}
                    </div>
                </Tooltip>
                <div
                    className={styles.caseInfoDetail}
                    onClick={() => {
                        if (
                            1 === testStep.stepInfo.type &&
                            'mock' === testStep.stepInfo.params.type
                        ) {
                            mockModalRef.current?.show(curOsType);
                        } else {
                            setOpen(true);
                        }
                        setShowModal(true);
                    }}
                >
                    <span>查看详情</span>
                </div>
            </span>
            {1 === testStep.stepInfo.type && 'runTemplate' === testStep.stepInfo.params.type ? (
                <TemplateExecuteInfoModal
                    testRes={getTemplateResult(testRes)}
                    currentStep={testStep}
                    curOsType={curOsType}
                    open={open}
                    setOpen={setOpen}
                />
            ) : null}
            {1 === testStep.stepInfo.type && 'mock' === testStep.stepInfo.params.type ? (
                <MockModal ref={mockModalRef} setModalOpen={setOpen} />
            ) : null}
            {1 === testStep.stepInfo.type && 'requestVerify' === testStep.stepInfo.params.type ? (
                <VerfiyModal
                    testRes={testRes}
                    currentStep={testStep}
                    open={open}
                    setOpen={setOpen}
                />
            ) : null}
            {1 === testStep.stepInfo.type && 'request' === testStep.stepInfo.params.type && (
                <ApiModal testRes={testRes} currentStep={testStep} open={open} setOpen={setOpen} />
            )}
            {-1 === ['mock', 'requestVerify', 'request'].indexOf(testStep.stepInfo.params.type) ? (
                <TestModal
                    testRes={testRes}
                    currentStep={testStep}
                    setShowModal={setShowModal}
                    open={open}
                    setOpen={setOpen}
                    curOsType={curOsType}
                />
            ) : null}
        </>
    );
}

function TestModal({ testRes, currentStep, open, setOpen, setShowModal, curOsType }) {
    return (
        <Modal
            open={open}
            title="调试结果"
            centered
            width={window.innerWidth * 0.8}
            onCancel={() => {
                setOpen(false);
                setShowModal(false);
            }}
            footer={null}
            destroyOnClose
        >
            <Row
                style={{
                    height: window.innerHeight * 0.8,
                    overflow: 'scroll'
                }}
            >
                <Col span="12">
                    <StepInfo
                        editType="execute"
                        curOsType={curOsType}
                        currentStep={{
                            ...currentStep,
                            stepChildren: currentStep?.stepChildren?.map((item) => {
                                return (
                                    testRes?.data?.extra?.stepResult?.find(
                                        (step) => step.stepId === item.stepId
                                    ) ?? item
                                );
                            }),
                            result: testRes
                        }}
                    />
                </Col>
                <Col span="12" style={{ marginTop: 10 }}>
                    {null !== testRes &&
                    0 === testRes.status &&
                    undefined !== testRes.data &&
                    undefined !== testRes.data.screenshot ? (
                        <StepRes
                            editType="execute"
                            testRes={testRes}
                            open={open}
                            curOsType={curOsType}
                            currentStep={currentStep}
                        />
                    ) : null}
                    {null !== testRes &&
                    0 !== testRes.status &&
                    undefined !== testRes.data &&
                    undefined !== testRes.data.extra ? (
                        <StepRes
                            editType="execute"
                            testRes={testRes}
                            open={open}
                            curOsType={curOsType}
                            currentStep={currentStep}
                        />
                    ) : null}
                </Col>
            </Row>
        </Modal>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    showModal: state.common.base.showModal,
    testStep: state.common.case.testStep,
    testRes: state.common.case.testRes
}))(TestStep);
