.caseInfoTest {
    position: fixed;
    right: 25px;
    bottom: 20px;
    padding: 5px;
    border-radius: 8px;
    font-size: 12px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    z-index: 999;

    span {
        margin: 5px;
    }

    &:hover {
        .close {
            display: block;
            background-color: #eee;
            color: #777;
            border-radius: 5px;
        }
    }
}

.close {
    width: 30px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    position: absolute;
    right: -10px;
    top: -7px;
    display: none;
}

.successInfoTest {
    background-color: #f5ffea;
}

.errorInfoTest {
    background-color: #fff0ee;
}

.caseInfoName {
    text-align: center;
    color: #777;
    margin-bottom: 5px;
}

.caseInfoLevel,
.caseInfoUrl {
    height: 24px;
    margin-left: 20px;
    line-height: 24px;
}

.caseInfoIcon {
    position: absolute;
    right: -2px;
    top: 50%;
    transform: translateY(-50%);
}

.caseInfoEditTime {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

.caseInfoDetail {
    text-align: center;
    color: #777;
    text-decoration: underline;
}