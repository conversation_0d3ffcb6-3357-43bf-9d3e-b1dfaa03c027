import {forwardRef, useState, useCallback, useImperativeHandle, useRef} from 'react';
import {Modal, Form, Input, Space, Button, message} from 'antd';
import {PlusOutlined, MinusCircleOutlined} from '@ant-design/icons';

function LinkInfo(props, ref) {
    const {link, updateCaseLinkInit, open, setOpen, setShowModal} = props;
    const form = useRef();

    const showModal = useCallback(
        () => {
            setShowModal(true);
            setOpen(true);
        },
        []
    );

    useImperativeHandle(ref, () => {
        return {
            show: showModal
        };
    }, [showModal]);

    const close = useCallback(() => {
        setOpen(false);
        setShowModal(false);
    }, []);

    const handleSubmit = () => {
        form.current?.validateFields()
            .then(values => {
                updateCaseLinkInit(values);
                close();
            })
            .catch(() => {
                message.warning('请填写完整');
            });
    };

    return (
        <Modal
            open={open}
            title="插入超链接"
            okText="确定"
            cancelText="取消"
            onCancel={close}
            onOk={handleSubmit}
            width={700}
            destroyOnClose
        >
            <Form autoComplete="off" ref={form}>
                <Form.List name="links" initialValue={link}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name, ...restField}) => (
                                <Space
                                    key={key}
                                    align="baseline"
                                >
                                    <Form.Item
                                        style={{width: '200px'}}
                                        {...restField}
                                        name={[name, 'name']}
                                        rules={[
                                            {
                                                required: true,
                                                message: '未填写描述信息',
                                            },
                                        ]}
                                    >
                                        <Input placeholder="填写描述信息" />
                                    </Form.Item>
                                    <Form.Item
                                        {...restField}
                                        style={{width: '400px'}}
                                        name={[name, 'path']}
                                        rules={[
                                            {
                                                required: true,
                                                message: '未填写链接',
                                            },
                                        ]}
                                    >
                                        <Input placeholder="填写链接" />
                                    </Form.Item>
                                    <MinusCircleOutlined onClick={() => remove(name)} />
                                </Space>
                            ))}
                            <Form.Item>
                                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                                    新增链接
                                </Button>
                            </Form.Item>
                        </>
                    )}
                </Form.List>
            </Form>
        </Modal>
    );
}

export default forwardRef(LinkInfo);