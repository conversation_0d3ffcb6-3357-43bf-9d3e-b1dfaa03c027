import { getHttpMethodLabel } from 'COMMON/components/TreeComponents/Step/InterfaceStepRes/utils.js';
import { STEP_TYPE } from 'COMMON/components/TreeComponents/Step/const';


// 获取整棵树的keys
export function getAllKeysFromData(treeData, nowIndexList = [], allCheckedKeys = []) {
    treeData.forEach((element, index) => {
        nowIndexList.push(index);
        allCheckedKeys.push(nowIndexList.join('-'));
        if (element.children) {
            getAllKeysFromData(element.children, nowIndexList, allCheckedKeys);
        }
        nowIndexList.pop();
    });
    return allCheckedKeys;
}

export function getExecuteCaseFilter(list, taskList, status, type, caseList = []) {
    list.forEach((element, index) => {
        let nodeIndex = taskList.findIndex((item) => item.caseId === element.nodeId);
        // 若在执行节点中，判断筛选的2个类型是否满足条件
        // 若不在且有孩子则深入遍历
        if (
            (-1 !== nodeIndex &&
                (0 === status || status === taskList[nodeIndex].status) &&
                (!element.info ||
                    (element.info &&
                        (-1 === type ||
                            (1 === type && [0, 1].includes(element.info.taskType)) ||
                            type === element.info.taskType)))) ||
            (-1 === nodeIndex && -1 !== Object.keys(element).indexOf('child') && 0 !== element.child.length)
        ) {
            let _ele = { ...element };
            _ele.child = [];
            caseList.push(_ele);
            getExecuteCaseFilter(element.child, taskList, status, type, caseList[caseList.length - 1].child);
            if (-1 === nodeIndex && 0 === caseList[caseList.length - 1].child.length) {
                caseList.pop();
            }
        }
    });
    return caseList;
}

// 获取选择的用例是否含有App设置
export function getAppSetting(appList, appMap = []) {
    let appIdList = [];
    let packageName = [];
    appList.forEach((app) => {
        packageName.push({
            id: app.id,
            appName: app.appName,
            packageName: app.packageName
        });
        appIdList.push(app.id);
    });
    appMap.forEach((item) => {
        let index = appIdList.indexOf(item.id);
        if (-1 !== index) {
            packageName[index].packageName = item.packageName;
        }
    });
    return packageName;
}

export function deepcopy(obj) {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }
    let newObj = {};
    if (Array.isArray(obj)) {
        newObj = obj.map((item) => deepcopy(item));
    } else {
        Object.keys(obj).forEach((key) => {
            return (newObj[key] = deepcopy(obj[key]));
        });
    }
    return newObj;
}

// 执行的node选择
export function getExecutedKeys(executeList, nodeList, nowIndexList = [], expandedKeys = []) {
    executeList.forEach((element, index) => {
        nowIndexList.push(index);
        if (-1 !== nodeList.indexOf(element.nodeId)) {
            let key = '';
            for (let index of nowIndexList) {
                if ('' === key) {
                    key += `${index}`;
                } else {
                    key += `-${index}`;
                }
                if (-1 === expandedKeys.indexOf(key)) {
                    expandedKeys.push(key);
                }
            }
        }
        if (-1 !== Object.keys(element).indexOf('child') && 0 !== element.child.length) {
            getExecutedKeys(element.child, nodeList, nowIndexList, expandedKeys);
        }
        nowIndexList.pop();
    });
    return expandedKeys;
}

// 执行的node选择
export function getExecutedNewKeys(executeList, nodeList, nowIndexList = [], expandedKeys = []) {
    executeList.forEach((element, index) => {
        nowIndexList.push(index);
        if (-1 !== nodeList.indexOf(element.nodeId)) {
            if (-1 === expandedKeys.indexOf('node_' + element.nodeId)) {
                expandedKeys.push('node_' + element.nodeId);
            }
        }
        if (-1 !== Object.keys(element).indexOf('child') && 0 !== element.child.length) {
            getExecutedNewKeys(element.child, nodeList, nowIndexList, expandedKeys);
        }
        nowIndexList.pop();
    });
    return expandedKeys;
}

// 执行的node选择
export function getErrExecutedKeys(executeList, nodeList, taskList, nowIndexList = [], expandedKeys = []) {
    executeList.forEach((element, index) => {
        nowIndexList.push(index);
        if (-1 !== nodeList.indexOf(element.nodeId) && -1 !== taskList.indexOf(element.nodeId)) {
            let key = '';
            for (let index of nowIndexList) {
                if ('' === key) {
                    key += `${index}`;
                } else {
                    key += `-${index}`;
                }
                if (-1 === expandedKeys.indexOf(key)) {
                    expandedKeys.push(key);
                }
            }
        }
        if (-1 !== Object.keys(element).indexOf('child') && 0 !== element.child.length) {
            getErrExecutedKeys(element.child, nodeList, taskList, nowIndexList, expandedKeys);
        }
        nowIndexList.pop();
    });
    return expandedKeys;
}

// 解析url
export function parseURL(url) {
    let parse_url = /^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/;
    let result = parse_url.exec(url);
    let params = {};
    let names = ['url', 'protocol', 'slash', 'host', 'port', 'path', 'query', 'hash'];
    if (!result) {
        return;
    }

    for (let i = 0; i < names.length; i++) {
        params[names[i]] = result[i];
        if ('port' === names[i]) {
            params.port = Number(result[i]);
        }
        if ('path' === names[i] && undefined !== result[i]) {
            params.path = '/' + result[i];
        }
        if ('query' === names[i] && undefined !== result[i]) {
            params.query = '?' + result[i];
        }
    }

    return params;
}

export function getNewIpRedirect(createPlanOption, osType) {
    const ipRedirect = osType ? createPlanOption[osType]?.ipRedirect : createPlanOption?.ipRedirect;
    let newIpRedirect = [];
    if (ipRedirect) {
        ipRedirect.forEach((item) => {
            if (undefined !== item.oriAddress.url && '' !== item.oriAddress.url) {
                let params = parseURL(item.oriAddress.url);
                if (undefined !== params.protocol && '' !== params.protocol) {
                    item.oriAddress.protocol = params.protocol;
                }
                if (undefined !== params.host && '' !== params.host) {
                    item.oriAddress.hostname = params.host;
                }
                if (!isNaN(params.port) && '' !== params.port) {
                    item.oriAddress.port = params.port;
                } else {
                    delete item.oriAddress.port;
                }
                if (undefined !== params.path && '' !== params.path) {
                    item.oriAddress.path = params.path;
                } else {
                    delete item.oriAddress.path;
                }
                if (undefined !== params.query && '' !== params.query) {
                    item.oriAddress.path += params.query;
                }
                delete item.oriAddress.url;
            }
            if (undefined !== item.targetAddress.url && '' !== item.targetAddress.url) {
                let params = parseURL(item.targetAddress.url);
                if (undefined !== params.protocol && '' !== params.protocol) {
                    item.targetAddress.protocol = params.protocol;
                }
                if (undefined !== params.host && '' !== params.host) {
                    item.targetAddress.hostname = params.host;
                }
                if (!isNaN(params.port) && '' !== params.port) {
                    item.targetAddress.port = params.port;
                } else {
                    delete item.targetAddress.port;
                }
                if (undefined !== params.path && '' !== params.path) {
                    item.targetAddress.path = params.path;
                } else {
                    delete item.targetAddress.path;
                }
                if (undefined !== params.query && '' !== params.query) {
                    item.targetAddress.path += params.query;
                }
                delete item.targetAddress.url;
            }
            delete item.isNew;
            newIpRedirect.push(item);
            // }
        });
    }
    return newIpRedirect;
}

// 将上面的getNewIpRedirect 反向解析, 用于回组装成url
export function getNewIpRedirectReverse(ipRedirect) {
    let newIpRedirect = [];
    if (ipRedirect) {
        ipRedirect.forEach((item) => {
            // if (
            //     undefined !== item.oriAddress.hostname &&
            //     '' !== item.oriAddress.hostname &&
            //     undefined !== item.targetAddress.hostname &&
            //     '' !== item.targetAddress.hostname
            // ) {
            if (undefined !== item.oriAddress.protocol && '' !== item.oriAddress.protocol) {
                item.oriAddress.url = item.oriAddress.protocol + '://';
            } else {
                item.oriAddress.url = '';
            }
            if (undefined !== item.oriAddress.hostname && '' !== item.oriAddress.hostname) {
                item.oriAddress.url += item.oriAddress.hostname;
            }
            if (undefined !== item.oriAddress.port && '' !== item.oriAddress.port) {
                item.oriAddress.url += ':' + item.oriAddress.port;
            }
            if (undefined !== item.oriAddress.path && '' !== item.oriAddress.path) {
                item.oriAddress.url += item.oriAddress.path;
                if (undefined !== item.oriAddress.query && '' !== item.oriAddress.query) {
                    item.oriAddress.url += item.oriAddress.query;
                }
            } else if (undefined !== item.oriAddress.query && '' !== item.oriAddress.query) {
                item.oriAddress.url += item.oriAddress.query;
            }
            delete item.oriAddress.protocol;
            delete item.oriAddress.hostname;
            delete item.oriAddress.port;
            delete item.oriAddress.path;
            delete item.oriAddress.query;
            if (undefined !== item.targetAddress.protocol && '' !== item.targetAddress.protocol) {
                item.targetAddress.url = item.targetAddress.protocol + '://';
            } else {
                item.targetAddress.url = '';
            }
            if (undefined !== item.targetAddress.hostname && '' !== item.targetAddress.hostname) {
                item.targetAddress.url += item.targetAddress.hostname;
            }
            if (undefined !== item.targetAddress.port && '' !== item.targetAddress.port) {
                item.targetAddress.url += ':' + item.targetAddress.port;
            }
            if (undefined !== item.targetAddress.path && '' !== item.targetAddress.path) {
                item.targetAddress.url += item.targetAddress.path;
                if (undefined !== item.targetAddress.query && '' !== item.targetAddress.query) {
                    item.targetAddress.url += item.targetAddress.query;
                }
            } else if (undefined !== item.targetAddress.query && '' !== item.targetAddress.query) {
                item.targetAddress.url += item.targetAddress.query;
            }
            delete item.targetAddress.protocol;
            delete item.targetAddress.hostname;
            delete item.targetAddress.port;
            delete item.targetAddress.path;
            delete item.targetAddress.query;
            newIpRedirect.push({
                oriAddress: {
                    url: item.oriAddress.url
                },
                targetAddress: {
                    url: item.targetAddress.url
                }
            });
        });
    }
    return newIpRedirect;
}

export function getNewPackageName(createPlanOption) {
    let newPackageName = [];
    if (createPlanOption.packageName) {
        createPlanOption.packageName.forEach((item) => {
            if ('' !== item.packageName && undefined !== item.packageName) {
                newPackageName.push({
                    id: item.id,
                    packageName: item.packageName
                });
            }
        });
    }
    return newPackageName;
}

export function getNewCloudAppList(createPlanOption) {
    let newPackageName = [];
    if (createPlanOption.packageName) {
        createPlanOption.packageName.forEach((item) => {
            if (item?.fileLink && item?.fileLink !== '' && item?.packageName && item?.packageName !== '') {
                newPackageName.push({
                    id: item.id,
                    packageName: item.packageName,
                    fileLink: item.fileLink
                });
            } else if (item?.fileLink && item?.fileLink !== '') {
                newPackageName.push({
                    id: item.id,
                    fileLink: item.fileLink
                });
            } else if (item?.packageName && item?.packageName !== '') {
                newPackageName.push({
                    id: item.id,
                    packageName: item.packageName
                });
            }
        });
    }
    return newPackageName;
}

// 获取自动化case
export function getAutoType(list, autoList = []) {
    list.forEach((element, index) => {
        // nodeType 为2 直接加入自动化用例树
        // nodeType 为1 直接中断
        // nodeType 为0 不存在孩子则不加入，若存在则继续遍历孩子信息
        if (
            2 === element.nodeType ||
            (-1 !== [0, 3].indexOf(element.nodeType) &&
                -1 !== Object.keys(element).indexOf('child') &&
                0 !== element.child.length)
        ) {
            let _ele = deepcopy(element);
            _ele.child = [];
            _ele.children = [];
            autoList.push(_ele);
            getAutoType(element.child, autoList[autoList.length - 1].child);
            // nodeType 为0 遍历孩子信息后，没有合适节点，则删除之前加入的节点
            if (0 === autoList[autoList.length - 1].child.length && -1 !== [0, 3].indexOf(element.nodeType)) {
                autoList.pop();
            }
        }
    });
    return autoList;
}

export function getName(type, params, stepType) {
    let desc = '操作';
    if (+stepType === 1401) {
        desc = '步骤组';
    } else if ([504, 602]?.includes(+stepType)) {
        desc = '智能断言';
    } else if (+stepType === 601) {
        desc = '智能定位';
    } else if (Number(type) === 1) {
        switch (params?.type) {
            case 'whiteScreen':
                desc = '白屏检测';
                break;
            case 'closeApp':
                desc = '关闭APP';
                break;
            case 'launchApp':
                desc = '开启APP';
                break;
            case 'authApp':
                desc = '授权APP';
                break;
            case 'clearApp':
                desc = '清理APP';
                break;
            case 'back':
                desc = '返回上一页';
                break;
            case 'scheme':
                desc = 'Scheme';
                break;
            case 'wait':
                desc = '休眠等待';
                break;
            case 'swipe':
                desc = 'Swipe';
                break;
            case 'home':
                desc = '返回主屏幕';
                break;
            case 'requestVerify':
                desc = '请求校验';
                break;
            case 'clearProxy':
                desc = '前置操作';
                break;
            case 'mock':
                desc = '数据模拟';
                break;
            case 'clearPop':
                desc = '弹窗点除';
                break;
            case 'runTemplate':
                desc = '测试片段';
                break;
            case 'networkConnect':
                if (params.params?.type === 0) {
                    desc = '断网模拟';
                } else {
                    desc = '网络连接';
                }
                break;
            case 'request':
                desc = '请求调用';
                break;
            case 'requestRedirect':
                desc = '数据转发';
                break;
            case 'clearRequest':
                desc = '请求清空';
                break;
            case 'clearMock':
                desc = 'Mock清空';
                break;
            case 'logCheck':
                desc = '点位校验';
                break;
            case 'pushFile':
                desc = '推送文件';
                break;
            case 'shell':
                desc = 'Shell';
                break;
            case 'installApp':
                desc = '安装APP';
                break;
            case 'login':
                desc = '登录账号';
                break;
            default:
                break;
        }
    } else if (Number(type) === 3) {
        switch (params?.type) {
            case 'operator':
                desc = '人工操作';
                break;
            case 'assest':
                desc = '人工复验';
                break;
            default:
                break;
        }
    } else if (Number(type) === 2 && [0, 1, 5].indexOf(params.findType) !== -1) {
        if (params?.actionInfo?.type === 'input') {
            desc = '控件输入';
        } else if (params?.actionInfo?.type === 'tap') {
            desc = Object.keys(params?.actionInfo?.params).indexOf('duration') === -1 ? '控件点击' : '控件长按';
        } else if (params?.actionInfo?.type === 'nope') {
            desc = '控件存在';
        } else if (params?.actionInfo?.type === 'swipe') {
            desc = '控件滑动';
        } else if (params?.actionInfo?.type === 'absence') {
            desc = '控件不存在';
        } else if (params?.actionInfo?.type === 'drag') {
            desc = '控件拖拽';
        } else if (params?.actionInfo?.type === 'expression') {
            desc = '控件校验';
        }
    } else if (Number(type) === 2 && params?.findType === 4) {
        if (params?.actionInfo?.type === 'input') {
            desc = '视觉输入';
        } else if (params?.actionInfo?.type === 'tap') {
            desc = Object.keys(params?.actionInfo?.params).indexOf('duration') === -1 ? '视觉点击' : '视觉长按';
        } else if (params?.actionInfo?.type === 'nope') {
            desc = '视觉存在';
        } else if (params?.actionInfo?.type === 'swipe') {
            desc = '视觉滑动';
        } else if (params?.actionInfo?.type === 'absence') {
            desc = '视觉不存在';
        }
    } else if (Number(type) === 2 && params?.findType === 2) {
        if (params?.actionInfo?.type === 'tap') {
            desc = '文本点击';
        } else if (params?.actionInfo?.type === 'nope') {
            desc = '文本存在';
        }
    } else if (Number(type) === 4) {
        if (params?.actionInfo?.type === 'tap') {
            desc = '屏幕点按';
        } else if (params?.actionInfo?.type === 'swipe') {
            desc = '屏幕划动';
        }
    } else if (Number(type) === 2 && params?.findType === 3) {
        if (params?.actionInfo?.type === 'tap') {
            desc = '图标点击';
        } else if (params?.actionInfo?.type === 'nope') {
            desc = '图标存在';
        }
    } else if ([5, 6, 7, 8, 9, 10].includes(Number(type))) {
        if (params?.actionInfo?.type === 'input') {
            desc = '控件输入';
        } else if (params?.actionInfo?.type === 'tap') {
            desc = Object.keys(params?.actionInfo?.params).indexOf('duration') === -1 ? '控件点击' : '控件长按';
        } else if (params?.actionInfo?.type === 'nope') {
            desc = '控件存在';
        } else if (params?.actionInfo?.type === 'swipe') {
            desc = '控件滑动';
        } else if (params?.actionInfo?.type === 'absence') {
            desc = '控件不存在';
        } else if (params?.actionInfo?.type === 'drag') {
            desc = '控件拖拽';
        } else if (params?.actionInfo?.type === 'expression') {
            desc = '控件校验';
        }
    } else if (params?.findType === 3) {
        if (params?.actionInfo?.type === 'tap') {
            desc = '图标点击';
        } else if (params?.actionInfo?.type === 'nope') {
            desc = '图标存在';
        }
    } else if (+stepType === 301) {
        // 接口调用
        // desc = (
        //     <span style={{ color: getHttpMethodColor(params?.method ?? params?.params?.method) }}>
        //         {getHttpMethodLabel(params?.method ?? params?.params?.method)}
        //     </span>
        // );
    } else if (1001 === +stepType) {
        desc = getHttpMethodLabel(params?.method ?? params?.params?.method);
    } else if (type === 'sql' || +stepType === 1101) {
        desc = 'SQL';
    } else if (type === 'redis' || +stepType === 1201) {
        desc = 'Redis';
    }
    return desc;
}

// 判断是否为number型
export function isNumber(val) {
    return parseFloat(val).toString() !== 'NaN';
}

export function contactUrl(params) {
    let url = '';
    if (-1 !== Object.keys(params).indexOf('protocol') && '' !== params.protocol) {
        url += params.protocol + '://';
    }
    if (-1 !== Object.keys(params).indexOf('hostname') && '' !== params.hostname) {
        url += params.hostname;
    }
    if (-1 !== Object.keys(params).indexOf('port') && '' !== params.port) {
        url += ':' + params.port;
    }
    return url;
}

export function getUrl(record, whole = true) {
    let url = '';
    if (record.protocol && '' !== record.protocol) {
        url += record.protocol + '://';
    }
    if (record.hostname && '' !== record.hostname) {
        url += record.hostname;
    }
    if (record.host && '' !== record.host) {
        url += record.host;
    }
    if (record.port && '' !== record.port) {
        url += ':' + record.port;
    }
    if (whole && record.url && '' !== record.url) {
        url += record.url;
    }
    return url;
}

export function getNewUrl(path, query) {
    let data = path.split('?');
    let pathList = [];
    query.forEach((element) => {
        pathList.push(element.key + '=' + element.value);
    });
    if (pathList.length > 0) {
        return data[0] + '?' + pathList.join('&');
    } else {
        return data[0];
    }
}

export function getQuery(info) {
    let data = info.split('?');
    let query = [];
    if (data.length < 2) {
        return query;
    }
    let search = data[1];
    let key_value = search.split('&');
    if (key_value.length !== 0 && '?' === key_value[0][0]) {
        key_value[0] = key_value[0].slice(1);
    }
    for (let _value of key_value) {
        let _data = _value.split('=');
        query.push({
            key: _data[0],
            value: _data[1],
            isChecked: false
        });
    }
    return query;
}

export function changeKeyValue(newVertifyQuery) {
    let query = {};
    for (let data of newVertifyQuery) {
        query[data.key] = data.value;
    }
    return query;
}

export function getNewBody(info) {
    let body = {};
    for (let _value of info) {
        body[_value.key] = _value.value;
    }
    return JSON.stringify(body);
}

export function getVerifyNewBody(info) {
    let body = {};
    for (let _value of info) {
        body[_value.key] = _value.value;
    }
    return body;
}

export function getObjToArray(dataObj) {
    let arr = [];
    for (let data in dataObj) {
        arr.push({
            key: data,
            value: dataObj[data],
            isChecked: false
        });
    }
    return arr;
}

export function getNewVertifyQueryArry(query) {
    let newVertifyQuery = [];
    for (let data in query) {
        newVertifyQuery.push({
            key: data,
            value: query[data]
        });
    }
    return newVertifyQuery;
}

export function getBody(matchRequest) {
    let newBody = {
        type: 'text',
        data: ''
    };
    if (
        matchRequest?.headers['Content-Type']?.includes('application/text-plain') ||
        matchRequest?.headers['Content-Type']?.includes('application/json') ||
        matchRequest?.headers['Content-Type']?.includes('text/plain')
    ) {
        newBody = {
            type: 'text',
            data: matchRequest.body
        };
    } else if (matchRequest?.headers['Content-Type']?.includes('application/x-www-form-urlencoded')) {
        let bodyQuery = [];
        let body = '' === matchRequest.body ? {} : JSON.parse(matchRequest.body);
        for (let item in body) {
            bodyQuery.push({
                key: item,
                value: body[item],
                isChecked: false
            });
        }
        newBody = {
            type: 'json',
            data: bodyQuery
        };
    }
    return newBody;
}

// 改写请求格式 方便前端展示
export function formatRequest(str) {
    if (isJSON(str)) {
        return JSON.stringify(JSON.parse(str), undefined, 2);
    } else {
        return str;
    }
}

// 判断是否为json
export function isJSON(str) {
    if (typeof str === 'string') {
        try {
            let obj = JSON.parse(str);
            if (typeof obj === 'object' && obj) {
                return true;
            } else {
                return false;
            }
        } catch (e) {
            return false;
        }
    }
}

// 增加省略号
export function getEllipsis(str, length = 7) {
    return str.length > length ? str.slice(0, length) + '...' : str;
}

export function getDate(timestamp) {
    timestamp = parseInt(timestamp, 10);
    timestamp = timestamp < 10000000000 ? timestamp * 1000 : timestamp;
    let date = new Date(timestamp);
    let Y = date.getFullYear() + '-';
    let M = date.getMonth() + 1;
    M = (M < 10 ? '0' + M : M) + '-';
    let D = date.getDate();
    D = (D < 10 ? '0' + D : D) + ' ';
    let h = date.getHours();
    h = (h < 10 ? '0' + h : h) + ':';
    let m = date.getMinutes();
    m = (m < 10 ? '0' + m : m) + ':';
    let s = date.getSeconds();
    s = s < 10 ? '0' + s : s;
    return Y + M + D + h + m + s;
}

export function getSmallDate(timestamp) {
    timestamp = parseInt(timestamp, 10);
    timestamp = timestamp < 10000000000 ? timestamp * 1000 : timestamp;
    let date = new Date(timestamp);
    let h = date.getHours();
    h = (h < 10 ? '0' + h : h) + ':';
    let m = date.getMinutes();
    m = (m < 10 ? '0' + m : m) + ':';
    let s = date.getSeconds();
    s = s < 10 ? '0' + s : s;
    return h + m + s;
}

export function updateNodeDetail(caseList, currentNode) {
    for (let i = 0; i < caseList.length; i++) {
        let node = caseList[i];
        if (currentNode?.nodeId === node.nodeId) {
            caseList[i] = { ...currentNode };
            return caseList;
        }
        if (node.child && node.child.length > 0) {
            updateNodeDetail(node.child, currentNode);
        }
    }
    return caseList;
}
/**
 * 虚拟修改节点
 * @param {*} caseList 用例树
 * @param {*} node 需要修改的节点
 * @param {*} options 需要修改的内容
 */
export const updateNodeToTree = (caseList, node, options = {}) => {
    if (!caseList || !caseList.length) {
        return;
    }
    for (let i = 0; i < caseList.length; i++) {
        if (caseList[i].caseNodeId === node.caseNodeId) {
            caseList[i] = { ...caseList[i], ...options };
            return;
        }
        updateNodeToTree(caseList[i].children, node, options);
    }
    return caseList;
};

// 判断步骤是否需要提示
export function isErrorStep(step) {
    let _status = false;
    let _msg = '';
    // 智能定位
    if ([601].includes(step?.stepType) && step.stepInfo?.params?.findInfo?.findDesc === '') {
        _status = true;
        _msg = '未填写描述内容';
    }
    // 智能 ai
    if ([504, 602].includes(step?.stepType) && step.stepInfo?.params?.params?.inputAssert === '') {
        _status = true;
        _msg = '未填写校验内容';
    }
    // APP 相关
    if (step?.stepType === 1401) {
        if (step.stepChildren?.length === 0) {
            _status = true;
            _msg = '无步骤';
        }
    } else if (step?.stepInfo?.type === 1) {
        if (
            ['launchApp', 'authApp', 'clearApp', 'installApp', 'login'].includes(step.stepInfo.params?.type) &&
            step.stepInfo?.params?.params?.id === null
        ) {
            _status = true;
            _msg = '未选择 APP';
        }
        if (['installApp'].includes(step.stepInfo.params?.type) && step.stepInfo?.params?.params?.fileLink === '') {
            _status = true;
            _msg = '未填写包地址';
        }
        if (['scheme'].includes(step.stepInfo.params.type)) {
            const hasNoScheme = !step.stepInfo?.params?.params?.scheme;
            const hasNullScheme = step.stepInfo?.params?.params?.scheme === null;
            if (hasNoScheme || hasNullScheme) {
                _status = true;
                _msg = 'Scheme 内容为空';
            }
        }
        if (['clearPop'].includes(step.stepInfo.params?.type) && step.stepInfo?.params?.params?.popList?.length === 0) {
            _status = true;
            _msg = '未选择弹窗点除模版';
        }
        if (['runTemplate'].includes(step.stepInfo.params?.type) && step.stepInfo?.params?.params?.id === null) {
            _status = true;
            _msg = '未选择测试片段';
        }
    }
    // 控件相关
    if ([7, 8, 9].includes(step?.stepInfo?.type) && step.stepInfo?.params?.findInfo?.findNode?.length === 0) {
        _status = true;
        _msg = '未选择操作控件';
    }
    // 控件相关
    if ([10].includes(step?.stepInfo?.type) && step.stepInfo?.params?.findInfo?.widgetInfo?.findNode?.length === 0) {
        _status = true;
        _msg = '未选择操作控件';
    }

    return { status: _status, msg: _msg };
}
export const methodOptions = [
    { value: 1, label: 'GET', color: '#67ad5b' },
    { value: 2, label: 'POST', color: '#eb903a' },
    { value: 3, label: 'PUT', color: '#448ef7' },
    { value: 4, label: 'PATCH', color: '#d84292' },
    { value: 5, label: 'DELETE', color: '#e76033' },
    { value: 6, label: 'HEAD', color: '#377d3b' },
    { value: 7, label: 'OPTIONS', color: '#415be3' }
];
