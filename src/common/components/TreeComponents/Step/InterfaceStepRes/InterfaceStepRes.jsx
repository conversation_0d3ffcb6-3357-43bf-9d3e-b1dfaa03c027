import { useState, useEffect } from 'react';
import { Tabs, Spin, message } from 'antd';
import styles from './InterfaceStepRes.module.less';
import { getBoxLinkContent } from 'COMMON/utils/utils';
import InterfaceRequest from './InterfaceRequest';
import InterfaceResponse from './InterfaceResponse';
import InterfaceAssert from './InterfaceAssert';
const InterfaceStepRes = (props) => {
    const { currentStepDetail = {}, editType } = props;
    const [runResultData, setRunResultData] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    useEffect(() => {
        setRunResultData([]);
        if (currentStepDetail?.result?.runResult) {
            setRunResultData(currentStepDetail?.result);
        }
        // 放里面
        if (currentStepDetail?.result?.bosLink) {
            setIsLoading(true);
            getBoxLinkContent(currentStepDetail?.result?.bosLink)
                .then((runResult) => {
                    const result = runResult?.stepResult ?? {};
                    setRunResultData(result);
                })
                .finally(() => {
                    setIsLoading(false);
                });
        }
        // console.log(editType, currentStepDetail?.result.result, 'currentStepDetail?.result');
        // 执行结果 execute 执行场景， bos链接获取内容
        if (editType === 'execute') {
            setRunResultData(currentStepDetail?.result?.stepResult);
        }
    }, [currentStepDetail?.result, editType]);
    const onChange = () => {};
    const items = [
        {
            key: 'reqeust',
            label: '请求信息',
            children: <InterfaceRequest data={runResultData?.invocationResult?.[0] ?? null} />
        },
        {
            key: 'response',
            label: '响应信息',
            children: (
                <InterfaceResponse response={runResultData?.invocationResult?.[0]?.response} />
            )
        },
        {
            key: 'assertResult',
            label: '断言分析',
            children: <InterfaceAssert assertResult={runResultData?.assertResult} />
        }
    ];
    return (
        <div
            className={styles.interfaceStepRes}
            // key={currentStepDetail?.stepId}
            style={{
                height: window.innerHeight * 0.8
            }}
        >
            {contextHolder}
            <Spin spinning={isLoading}>
                <Tabs
                    defaultActiveKey="reqeust"
                    items={items}
                    onChange={onChange}
                    animated={false}
                />
            </Spin>
        </div>
    );
};

export default InterfaceStepRes;
