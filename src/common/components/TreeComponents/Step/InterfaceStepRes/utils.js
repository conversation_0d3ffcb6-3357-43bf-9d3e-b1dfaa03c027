import { methodOptions } from 'COMMON/components/TreeComponents/Step/utils.js';

// 根据 method 数字值获取对应的 label
export const getHttpMethodLabel = (method) => {
    const methodOption = methodOptions.find((option) => option.value === method);
    return methodOption ? methodOption.label : ''; // 如果没有匹配项，返回 '未知'
};
// 根据 method 数字值获取对应的 color
export const getHttpMethodColor = (method) => {
    const methodOption = methodOptions.find((option) => option.value === method);
    return methodOption ? methodOption.color : 'gray'; // 如果没有匹配项，返回 'gray'
};
