import JSONMonacoEditor from 'COMMON/components/TreeComponents/Step/StepInfo/InterfaceAction/Postman/JSONMonacoEditor.jsx';
import { safeJsonStringify } from 'COMMON/utils/utils';
import ReactJson from 'react-json-view';
import { Table, Tag } from 'antd';
const getQueryData = (queryList) => {
    const result = {};
    queryList.forEach((element) => {
        const key = element.name;
        if (result[element.name]) {
            if (Array.isArray(result[key])) {
                result[key].push(element.value);
            } else {
                result[key] = [result[key], element.value];
            }
        } else {
            result[key] = element.value;
        }
    });
    return result;
};
const TYPE_TEXT = {
    1: {
        name: 'Text',
        color: 'geekblue'
    },
    2: {
        name: 'File',
        color: 'volcano'
    }
};
const RequestBody = (props) => {
    const { body } = props;
    // console.log(123, body, safeJsonStringify(body.json || '{}'));
    if (body?.type === 4) {
        return (
            <JSONMonacoEditor
                readOnly
                language="json"
                viewValue={safeJsonStringify(body.json || '{}')}
                theme="vs-light"
                height="30vh"
                setEditor={() => {}}
            />
        );
    }
    if (body?.type === 5) {
        return (
            <JSONMonacoEditor
                readOnly
                language="json"
                viewValue={safeJsonStringify(body?.raw || '{}')}
                theme="vs-light"
                height="30vh"
                setEditor={() => {}}
            />
        );
    }
    if (body?.type === 3) {
        return (
            <ReactJson
                name={false}
                displayDataTypes={false}
                src={getQueryData(body?.fromUrlencode)}
            />
        );
    }
    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name'
        },
        {
            title: 'Type',
            dataIndex: 'type',
            key: 'type',
            render: (type) => <Tag color={TYPE_TEXT[type].color}>{TYPE_TEXT[type].name}</Tag>
        },
        {
            title: 'Value',
            key: 'value',
            render: (_, record) => <div>{record?.type === 2 ? record?.link : record?.value}</div>
        }
    ];
    if (body?.type === 2) {
        return <Table columns={columns} dataSource={body?.formData} />;
    }
    return <div>none</div>;
};

export default RequestBody;
