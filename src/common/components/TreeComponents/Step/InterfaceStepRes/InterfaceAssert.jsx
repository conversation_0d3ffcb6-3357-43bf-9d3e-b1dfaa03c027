import { useState, useEffect } from 'react';
import { Collapse, Select, Input, Form, Empty } from 'antd';
import { isEmpty } from 'lodash';
import ReactJson from 'react-json-view';
import {CardTitle} from 'COMMON/components/common/Card';
import { safeJsonParse } from 'COMMON/utils/utils';
import { v4 } from 'uuid';
import { operationList } from './const';

const { Panel } = Collapse;

const ResponseAssertItem = (props) => {
    const { onBlurUpdateStep, currentStep } = props;
    const [value, setValue] = useState({});

    useEffect(() => {
        if (!isEmpty(props.defaultValue)) {
            setValue(props.defaultValue);
        } else if (!isEmpty(props.value)) {
            setValue(props.value);
        } else {
            setValue({});
        }
    }, [props.defaultValue, props.value]);

    const onBlur = (field, fieldValue) => {
        props.onChange({
            ...value,
            [field]: fieldValue
        });
    };

    return (
        <>
            <div style={{ display: 'flex', marginTop: 8, alignItems: 'center' }}>
                <div
                    style={{
                        width: 120,
                        marginRight: 6,
                        flexShrink: 0,
                        textAlign: 'center',
                        display: 'inline-block',
                        background: '#f5f7fa',
                        padding: '4px 8px',
                        borderRadius: 2
                    }}
                >
                    JSONPath
                </div>
                <Input
                    disabled
                    defaultValue={props.value?.jsonPath}
                    placeholder="请输入JSONPath"
                    onBlur={(e) => {
                        onBlur('jsonPath', e.target.value);
                    }}
                />
            </div>
            <div style={{ display: 'flex', marginTop: 8 }}>
                <Select
                    style={{
                        marginRight: 6,
                        width: 120,
                        flexShrink: 0
                    }}
                    disabled
                    placeholder="请选择操作符"
                    options={operationList}
                    defaultValue={props.value?.type}
                    onChange={(selectvalue) => {
                        onBlur('type', selectvalue);
                    }}
                />
                <Input
                    defaultValue={props.value?.data}
                    onBlur={(e) => {
                        onBlur('data', e.target.value);
                    }}
                    placeholder="请输入断言值"
                    disabled
                />
            </div>
        </>
    );
};

const AssertionFormItem = ({ index, assertion }) => {
    return (
        <>
            <div style={{ display: 'flex', marginTop: 8 }}>
                <Select
                    style={{
                        marginRight: 6,
                        width: 120,
                        flexShrink: 0
                    }}
                    placeholder="请选择操作符"
                    options={[
                        { value: 1, label: '包含' },
                        { value: 2, label: '正则' }
                    ]}
                    defaultValue={assertion.type}
                    disabled
                />
                <Input
                    style={{ flex: 2 }}
                    placeholder="请输入断言条件"
                    defaultValue={assertion.data}
                    disabled
                />
            </div>
        </>
    );
};

const InterfaceAssert = ({ assertResult }) => {
    const renderAssertions = (assertions = [], type) => {
        if (!Array.isArray(assertions)) {
            // 如果 assertions 不是数组，直接处理单个对象
            assertions = [assertions];
        }
        return assertions.map((assertion, index) => {
            // isPass // 0-通过 1-不通过
            const title =
                type === 'assertText'
                    ? 'Response Text 断言'
                    : type === 'assertJsonSchema'
                    ? 'Response JSON Schema 断言'
                    : 'Response JSON 断言';
            return (
                <Panel
                    style={{
                        border: `solid 1px ${assertion.isPass ? '#ff4446' : '#48bc19'}`,
                        opacity: 0.8,
                        borderRadius: 5,
                        margin: 0.5
                    }}
                    key={v4()}
                    header={
                        <div style={{ color: assertion.isPass ? '#ff4446' : '#48bc19' }}>
                            {type} [{index + 1}]
                        </div>
                    }
                >
                    <CardTitle text={title} style={{ margin: 0 }} />
                    {type === 'assertText' && (
                        <AssertionFormItem index={index} assertion={assertion} />
                    )}
                    {type === 'assertJson' && (
                        <ResponseAssertItem
                            value={{
                                jsonPath: assertion.jsonPath,
                                type: assertion.type,
                                data: assertion.data
                            }}
                        />
                    )}
                    {type === 'assertJsonSchema' && (
                        <ReactJson
                            name={false}
                            displayDataTypes={false}
                            src={safeJsonParse(assertion.data)}
                        />
                    )}
                    <CardTitle text="请求的响应" style={{ margin: '10px 0' }} />
                    <ReactJson
                        name={false}
                        displayDataTypes={false}
                        src={safeJsonParse(assertion.assertedContent)}
                    />
                </Panel>
            );
        });
    };

    return isEmpty(assertResult?.assertText) && isEmpty(assertResult?.assertJson) && isEmpty(assertResult?.assertJsonSchema) ? (
        <Empty
            style={{ marginTop: 200 }}
            description="暂无断言结果"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
    ) : (
        <div style={{ height: '100%', overflowY: 'auto' }}>
            <div>
                <Collapse accordion bordered={false} size="small" ghost>
                    {renderAssertions(assertResult?.assertJson, 'assertJson')}
                </Collapse>
            </div>
            <div>
                <Collapse accordion bordered={false} size="small" ghost>
                    {renderAssertions(assertResult?.assertText, 'assertText')}
                </Collapse>
            </div>
            <div>
                <Collapse accordion bordered={false} size="small" ghost>
                    {renderAssertions(assertResult?.assertJsonSchema, 'assertJsonSchema')}
                </Collapse>
            </div>
        </div>
    );
};

export default InterfaceAssert;
