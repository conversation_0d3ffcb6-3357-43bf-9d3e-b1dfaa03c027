import { Collapse } from 'antd';
import {
    DeleteOutlined,
    PlusOutlined,
    CaretRightOutlined,
    RightOutlined,
    EditOutlined,
    FolderOutlined,
    FolderOpenOutlined,
    EllipsisOutlined
} from '@ant-design/icons';
import ReactJson from 'react-json-view';
import InterfaceHeader from './InterfaceHeader';
import { safeJsonParse } from 'COMMON/utils/utils';
import RequestBody from './RequestBody';
import { BodyType } from './const.js';
import { getHttpMethodLabel } from './utils';
const getHttpProtocol = (url) => {
    return url ? url.split('://')[0] : '';
};
const getQueryData = (queryList) => {
    const result = {};
    if (Array.isArray(queryList)) {
        queryList.forEach((element) => {
            const key = element?.name;
            if (result[element?.name]) {
                if (Array.isArray(result[key])) {
                    result[key].push(element?.value);
                } else {
                    result[key] = [result[key], element?.value];
                }
            } else {
                result[key] = element?.value;
            }
        });
    }
    return result;
};
const Title = (props) => {
    const { title } = props;
    return <div style={{ color: '#598fe9' }}>{title}</div>;
};
const InterfaceRequest = (props) => {
    const { data } = props;
    const items = [
        {
            key: 'method',
            label: <Title title="method" />,
            children: <p>{getHttpMethodLabel(data?.method)}</p>
        },
        {
            key: 'address',
            label: <Title title="address" />,
            children: <p>{data?.host + data?.path || ''}</p>
        },
        {
            key: 'requestIp',
            label: <Title title="requestIp" />,
            children: <p>{data?.remoteIp}</p>
        },
        {
            key: 'protocol',
            label: <Title title="protocol" />,
            children: <p>{data && data.host ? getHttpProtocol(data?.host) : ''}</p>
        },
        {
            key: 'request params',
            label: <Title title="request params" />,
            children: (
                <ReactJson
                    name={false}
                    displayDataTypes={false}
                    src={getQueryData(data?.request?.query)}
                />
            )
        },
        {
            key: 'request header',
            label: <Title title="request header" />,
            children: <InterfaceHeader list={data?.request?.headers || []} />
        },
        {
            key: 'request body',
            label: <Title title="request body" />,
            children: (
                <div>
                    <div
                        style={{
                            marginBottom: 10
                        }}
                    >
                        BodyType: {BodyType[data?.request?.body?.type]}
                    </div>
                    <RequestBody body={data?.request?.body} />
                </div>
            )
        }
    ];
    return (
        <Collapse
            defaultActiveKey={items.map((v) => v.key)}
            ghost
            items={items}
            expandIcon={<RightOutlined />}
        />
    );
};

export default InterfaceRequest;
