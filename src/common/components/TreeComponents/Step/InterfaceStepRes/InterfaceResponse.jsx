import React, { useMemo, useEffect } from 'react';
import React<PERSON><PERSON> from 'react-json-view';
import { Collapse } from 'antd';
import { safeJsonParse } from 'COMMON/utils/utils';
import {CardTitle} from 'COMMON/components/common/Card';
import InterfaceHeader from './InterfaceHeader';
import JSONMonacoEditor from '../StepInfo/InterfaceAction/Postman/JSONMonacoEditor';

const CONTENT_TYPE = {
    'application/json': 'json',
    'application/xml': 'xml',
    'text/html': 'html',
    'text/plain': 'txt',
    'application/x-www-form-urlencoded': 'json',
    'multipart/form-data': 'json',
    'application/graphql': 'txt',
    'application/graphql+json': 'txt',
    'text/javascript': 'javascript',
    'application/ld+json': 'txt',
    'text/css': 'css',
    'application/x-yaml': 'txt',
    'text/vnd.graphviz': 'txt'
};
const InterfaceResponse = (props) => {
    const { response } = props;
    const contentType = useMemo(() => {
        return response?.headers.find((item) => item.key === 'Content-Type');
    }, [response]);

    const launguage = useMemo(() => {
        const find = Object.keys(CONTENT_TYPE).find((item) => contentType?.value?.includes(item));
        if (find) {
            return CONTENT_TYPE[find];
        }
        return 'txt';
    }, [contentType]);

    const [editor, setEditor] = React.useState(null);

    const items = [
        {
            key: 'header',
            label: <a>response header</a>,
            children: <InterfaceHeader list={response?.headers} />
        },
        {
            key: 'body',
            label: <a>response body</a>,
            children:
                launguage === 'json' ? (
                    <ReactJson
                        name={false}
                        displayDataTypes={false}
                        src={safeJsonParse(response?.body?.data)}
                    />
                ) : (
                    <JSONMonacoEditor
                        viewValue={response?.body?.data}
                        readOnly
                        language={launguage}
                        height={300}
                        setEditor={setEditor}
                    />
                )
        }
    ];
    return <Collapse defaultActiveKey={items.map((item) => item.key)} ghost items={items} />;
};

export default InterfaceResponse;
