import styles from './InterfaceStepRes.module.less';
import { v4 } from 'uuid';
const InterfaceHeader = (props) => {
    // console.log(props, 'props');
    const { list } = props;
    return (
        <div className={styles.InterfaceHeader}>
            {Array.isArray(list) &&
                list.map((item, index) => {
                    return (
                        <div key={v4} className={styles.InterfaceHeaderList}>
                            <div
                                className={styles.InterfaceHeaderListItem}
                                style={{
                                    color: '#607d8b'
                                }}
                            >
                                {item.key}：
                            </div>
                            <div className={styles.InterfaceHeaderListItem}>{item.value}</div>
                        </div>
                    );
                })}
        </div>
    );
};

export default InterfaceHeader;
