import {useState, useEffect, forwardRef, useImperativeHandle} from 'react';
import {isEmpty} from 'lodash';
import {
    Modal, Tooltip, Table, Spin,
    Input, Collapse, message
} from 'antd';
import {
    ClearOutlined,
    PlayCircleOutlined,
    PauseCircleOutlined,
    SearchOutlined,
    ApiOutlined,
    EditOutlined,
} from '@ant-design/icons';
import electron from 'COMMON/utils/electron';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import {deepcopy, getUrl, getSmallDate, getQuery, changeKeyValue} from 'COMMON/components/TreeComponents/Step/utils';
import commonModel from 'COMMON/models/commonModel';
import NoContent from 'COMMON/components/common/NoContent';

const {Panel} = Collapse;
function RequestProxyModal(props, ref) {
    const {currentStep, currentDevice, setShowModal, handleUpdateStep,
        currentNode, curOsType} = props;
    const [open, setOpen] = useState();
    const [loading, setLoading] = useState(false);
    const [urlList, setUrlList] = useState([]);
    const [dataList, setDataList] = useState({});
    const [searchUrl, setSearchUrl] = useState('');
    const [isProxyStart, setIsProxyStart] = useState(true);
    const [searchUrlList, setSearchUrlList] = useState({});

    const showModal =
        () => {
            setShowModal(true);
            setOpen(true);
        };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(ref, () => {
        return {
            show: showModal
        };
    }, [showModal]);


    useEffect(() => {
        if (isElectron()) {
            electron.on('proxy.request', ({request}) => {
                let newUrlList = urlList;
                newUrlList.unshift(request);
                setUrlList(newUrlList);
                getDataList(request);
            });
            return () => electron.remove('proxy.request');
        }
    }, [open, urlList]);

    useEffect(() => {
        setSearchUrlList(getSearchUrlList(dataList));
    }, [dataList, searchUrl]);

    const getSearchUrlList = (dataList) => {
        let searchUrlList = [];
        for (let host in dataList) {
            let items = dataList[host];
            items.forEach((item) => {
                let realUrl = host + item.url;
                if (-1 !== realUrl.indexOf(searchUrl.trim()) || '' === searchUrl.trim()) {
                    if (-1 === Object.keys(searchUrlList).indexOf(host)) {
                        searchUrlList[host] = [];
                    }
                    searchUrlList[host].push(item);
                }
            });
        }
        return searchUrlList;
    };

    const getDataList = (url) => {
        let mainUrl = getUrl(url.requestDetail, false);
        if (-1 === Object.keys(dataList).indexOf(mainUrl)) {
            dataList[mainUrl] = [];
        }
        dataList[mainUrl].unshift(
            {
                id: url.id,
                protocol: url.requestDetail.protocol,
                hostname: url.requestDetail.hostname,
                key: url.requestDetail.hostname + '_' + dataList[mainUrl].length,
                time: getSmallDate(url.timestamp),
                port: url.requestDetail.port,
                url: url.requestDetail.path
            }
        );
        setDataList({...dataList});
    };

    const getJsx = (dataList) => {
        let jsx = [];
        let _data = 0;
        for (let index in dataList) {
            const columns = [
                {
                    title: '时间',
                    key: 'time' + String(index),
                    dataIndex: 'time',
                    width: 100,
                },
                Table.EXPAND_COLUMN,
                {
                    title: 'URL',
                    key: 'url' + String(index),
                    ellipsis: true,
                    onCell: (record) => {
                        return {rowSpan: record.rowSpan};
                    },
                    render: (record) => (
                        <span>
                            {record.url}
                        </span>
                    )
                }, {
                    title: '操作',
                    key: 'operator' + String(index),
                    dateIndex: 'operator',
                    onCell: (record) => {
                        return {rowSpan: record.rowSpan};
                    },
                    render: (record) => (
                        <span>
                            {
                                -1 !== ['mock', 'requestVerify', 'requestRedirect'
                                ].indexOf(currentStep?.stepInfo.params.type) ?
                                    <Tooltip title='选择填入'>
                                        <EditOperator
                                            {...props}
                                            urlList={urlList}
                                            currentNode={currentNode}
                                            record={record}
                                            handleUpdateStep={handleUpdateStep}
                                            setOpen={setOpen}
                                        />
                                    </Tooltip> : <>暂无</>
                            }
                            {/* <Tooltip title='预览'>
                            <EyeOutlined style={{marginLeft: 15}}
                                onClick={() => {
                                    setJsonOpen(true);
                                    setShowModal(true);
                                }} />
                            <Modal
                                title="预览"
                                open={JsonOpen}
                                className="inputEditor"
                                onCancel={() => {
                                    setJsonOpen(false);
                                    setShowModal(false);
                                }}
                                onOk={() => {
                                    setJsonOpen(false);
                                    setShowModal(false);
                                }}
                                width={window.innerWidth * 0.8}
                                footer={null}
                            >
                                <Editor
                                    value={''}
                                    // value={JSON.parse(record?.replace(/\r|\n/g, ''))}
                                    className="inputEditor"
                                    onError={(error) => {
                                        message.error('发生了报错: ', error);
                                    }}
                                />
                            </Modal>
                        </Tooltip> */}
                        </span>
                    ),
                }];
            jsx.push(
                <Panel header={index} key={`urlList_${String(_data) + 1}`}>
                    <Table
                        style={{
                            cursor: 'pointer',
                        }}
                        key={`table_${String(_data) + 1}`}
                        size='small'
                        columns={columns}
                        dataSource={[...dataList[index]]}
                        scroll={{
                            y: 340,
                        }}
                        expandable={{
                            expandedRowRender: (record) => (
                                <p
                                    style={{
                                        margin: 0,
                                    }}
                                >
                                    {getUrl(record)}
                                </p>
                            ),
                        }}
                    />
                </Panel>
            );
            _data++;
        }
        return jsx;
    };
    return (
        <>
            <Modal
                title={
                    <>
                        代理请求拦截
                        {isProxyStart ? (
                            <Tooltip title='停止请求'>
                                <PauseCircleOutlined
                                    style={{
                                        marginLeft: 5,
                                        color: '#777',
                                    }}
                                    onClick={async () => {
                                        try {
                                            if (null === currentDevice) {
                                                return false;
                                            }
                                            if (2 !== currentDevice.status) {
                                                message.error('设备状态异常');
                                                return false;
                                            }
                                            await electron.send('proxy.stop',
                                                {deviceType: curOsType, deviceId: currentDevice?.deviceId});
                                            setIsProxyStart(false);
                                        } catch (err) {
                                            message.error(err.message ? err.message : err);
                                        }
                                    }}
                                />
                            </Tooltip>
                        ) : (
                            <Tooltip title='开始请求'>
                                <PlayCircleOutlined
                                    style={{
                                        marginLeft: 5,
                                        color: '#777',
                                    }}
                                    onClick={async () => {
                                        try {
                                            if (null === currentDevice) {
                                                return false;
                                            }
                                            if (2 !== currentDevice.status) {
                                                message.error('设备状态异常');
                                                return false;
                                            }
                                            await electron.send('proxy.start',
                                                {deviceType: curOsType, deviceId: currentDevice.deviceId});
                                            setIsProxyStart(true);

                                        } catch (err) {
                                            message.error(err.message ? err.message : err);
                                        }
                                    }}
                                />
                            </Tooltip>
                        )}
                        <Tooltip title='清空'>
                            <ClearOutlined
                                style={{
                                    marginLeft: 5,
                                    color: '#777'
                                }}
                                onClick={async () => {
                                    try {
                                        if (null === currentDevice) {
                                            return false;
                                        }
                                        if (2 !== currentDevice.status) {
                                            message.error('设备状态异常');
                                            return false;
                                        }
                                        await electron.send('proxy.clear',
                                            {deviceType: curOsType, deviceId: currentDevice.deviceId});
                                        setUrlList([]);
                                        setDataList({});
                                        setSearchUrlList({});
                                        message.success('请求已清空');
                                    } catch (err) {
                                        message.error(err.message ? err.message : err);
                                    }
                                }}
                            />
                        </Tooltip>
                        <Tooltip title='获取前序请求'>
                            <ApiOutlined
                                style={{
                                    marginLeft: 5,
                                    color: '#777',
                                }}
                                onClick={async () => {
                                    try {
                                        if (null === currentDevice) {
                                            return false;
                                        }
                                        if (2 !== currentDevice.status) {
                                            message.error('设备状态异常');
                                            return false;
                                        }
                                        setLoading(true);
                                        let res = await electron.send('proxy.get',
                                            {deviceType: curOsType, deviceId: currentDevice?.deviceId});
                                        setUrlList(res);
                                        for (let item of res) {
                                            getDataList(item);
                                        }
                                        message.success('获取完成');
                                    } catch (err) {
                                        message.error(err.message ? err.message : err);
                                    } finally {
                                        setLoading(false);
                                    }
                                }}
                            />
                        </Tooltip>
                    </>
                }
                open={open}
                centered
                width={window.innerWidth * 0.8}
                onCancel={async () => {
                    try {
                        await electron.send('proxy.stop',
                            {deviceType: curOsType, deviceId: currentDevice?.deviceId});
                    } catch (err) {
                        message.error(err.message ? err.message : err);
                    } finally {
                        setOpen(false);
                        setShowModal(false);
                    }
                }}
                footer={null}
            >
                <Spin
                    spinning={loading}
                    style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        transform: 'translate(-50%, -50%)',
                    }}
                    tip='请求获取中，请稍候'
                >
                    <div
                        style={{
                            maxHeight: window.innerHeight - 200,
                            overflow: 'scroll',
                        }}
                    >
                        <div>
                            <div style={{marginBottom: 5}}>
                                <Input
                                    className="inputEditor"
                                    placeholder='输入搜索内容'
                                    value={searchUrl}
                                    addonBefore={<SearchOutlined />}
                                    onChange={(e) => {
                                        setSearchUrl(e.target.value);
                                    }}
                                />
                            </div>
                            {0 === urlList.length || ('' !== searchUrl && 0 === Object.keys(searchUrlList).length) ? (
                                <NoContent text='暂无相关请求' />
                            ) : <Collapse accordion>
                                {getJsx(searchUrlList)}
                            </Collapse>
                            }
                        </div>
                    </div>
                </Spin>
            </Modal>
        </>

    );
};

function EditOperator(props) {
    const {currentStep, setShowModal, urlList, record,
        handleUpdateStep, setOpen} = props;
    return (
        <EditOutlined
            onClick={async () => {
                for (let reqItem of urlList) {
                    let copyItem = deepcopy(reqItem);
                    if (copyItem.id === record.id) {
                        let newCurrentStep = deepcopy(currentStep);
                        if (
                            'requestRedirect' !==
                            newCurrentStep.stepInfo.params.type
                        ) {
                            newCurrentStep.stepInfo.params.params = {
                                timestamp: copyItem.timestamp,
                                mockRequest: {
                                    matchRequest: {
                                        protocol: copyItem.requestDetail.protocol,
                                        hostname: copyItem.requestDetail.hostname,
                                        port: copyItem.requestDetail.port,
                                        method: copyItem.requestDetail.method,
                                        path: copyItem.requestDetail.path,
                                        headers: copyItem.requestDetail.headers,
                                    },
                                    requestDetail: {},
                                    responseDetail: copyItem.responseDetail,
                                    verifyDetail: {
                                        verifyType: 1,
                                    },
                                }
                            };
                        } else {
                            newCurrentStep.stepInfo.params.params = {
                                timestamp: copyItem.timestamp,
                                mockRequest: {
                                    ...newCurrentStep.stepInfo.params.params.mockRequest,
                                    matchRequest: {
                                        protocol: copyItem.requestDetail.protocol,
                                        hostname: copyItem.requestDetail.hostname,
                                        port: copyItem.requestDetail.port,
                                        method: copyItem.requestDetail.method,
                                        path: copyItem.requestDetail.path,
                                        headers: copyItem.requestDetail.headers,
                                    },
                                },
                            };
                        }
                        newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.body =
                            copyItem.requestDetail.body;
                        let query = getQuery(
                            newCurrentStep.stepInfo.params.params.mockRequest
                                .matchRequest.path
                        );
                        newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.query =
                            changeKeyValue(query);
                        if ('requestVerify' === newCurrentStep.stepInfo?.params?.type &&
                            '' !== newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.hostname) {
                                newCurrentStep.stepInfo.params.params.mockRequest.verifyDetail = {
                                    verifyType: newCurrentStep.stepInfo.params.params.mockRequest.verifyDetail.verifyType ?
                                        newCurrentStep.stepInfo.params.params.mockRequest.verifyDetail.verifyType : 1,
                                    query: newCurrentStep.stepInfo.params.params.mockRequest.matchRequest ?
                                        newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.query : {},
                                    body: '' === newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.body ?
                                        {} : JSON.parse(newCurrentStep.stepInfo.params.params.mockRequest.matchRequest.body),
                                    response: '' === newCurrentStep.stepInfo.params.params.mockRequest.responseDetail.body ?
                                        {} : JSON.parse(newCurrentStep.stepInfo.params.
                                            params.mockRequest.responseDetail.body)
                            };
                        }
                        try {
                            await handleUpdateStep({...newCurrentStep, stepInfo: newCurrentStep.stepInfo});
                        } catch (err) {
                        } finally {
                            setOpen(false);
                            setShowModal(false);
                        }
                    }
                }
            }}
        />
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    deviceList: state.common.base.deviceList,
    showModal: state.common.base.showModal,
}))(forwardRef(RequestProxyModal));

