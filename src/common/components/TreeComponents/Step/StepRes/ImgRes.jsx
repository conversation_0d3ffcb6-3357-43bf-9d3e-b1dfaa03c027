import { useState, useEffect } from 'react';
import { isEmpty } from 'lodash';
import CanvasScreenshot from 'COMMON/components/TreeComponents/Step/StepInfo/Canvas';
import styles from './StepRes.module.less';
import classnames from 'classnames';

const getCanvasSize = (screenSize, innerHeight, innerWidth, curOsType) => {
    const imgScreen =
        Object.keys(screenSize).indexOf('rotation') !== -1 &&
        [90, 270].indexOf(screenSize.rotation) !== -1;
    let screenWidth = screenSize.width;
    let screenHeight = screenSize.height;
    let imgHeight = innerHeight - 350;
    let imageScale = imgHeight / screenHeight;
    let imgWidth = imageScale * screenWidth;
    if (imgScreen || screenWidth > screenHeight) {
        // ios 宽高需要互换
        if (+curOsType === 2) {
            screenWidth = screenSize.height;
            screenHeight = screenSize.width;
        }
        imageScale = (((innerWidth - 20) * 0.6 - 50) * 10) / 24 / screenWidth;
        imgWidth = (((innerWidth - 20) * 0.6 - 50) * 10) / 24;
        imgHeight = imageScale * screenHeight;
    }
    return { imgHeight, imgWidth, imageScale };
};

function StepRes(props) {
    const { screenshot, screenSize, matchNodeList, rect, rectList, currentStep, curOsType } = props;
    const [innerHeight, setInnerHeight] = useState(window.innerHeight);
    const [innerWidth, setInnerWidth] = useState(window.innerWidth);
    const [imgHeight, setImgHeight] = useState(0);
    const [imgWidth, setImgWidth] = useState(0);
    const [screenHeight, setScreenHeight] = useState(0);
    const [screenWidth, setScreenWidth] = useState(0);
    const [imageScale, setImageScale] = useState(1);
    const [scale, setScale] = useState(1);
    const [imgSrc, setImgSrc] = useState('');
    const [rotation, setRotation] = useState(null);

    // 页面大小变动后，图片resize
    useEffect(() => {
        window.onresize = () => {
            setInnerHeight(window.innerHeight);
            setInnerWidth(window.innerWidth);
        };
    }, []);

    useEffect(() => {
        let img = screenshot;
        setImgSrc(img);
        if (screenSize) {
            const {
                imgHeight: h,
                imgWidth: w,
                imageScale: s
            } = getCanvasSize(screenSize, innerHeight, innerWidth, curOsType);
            setImgHeight(h);
            setImgWidth(w);
            setImageScale(s);
            setScale(screenSize.scale);
            setRotation(screenSize.rotation);
            setScreenHeight(screenSize.height);
            setScreenWidth(screenSize.width);
        }
    }, [screenshot, screenSize]);

    const ratio = window.devicePixelRatio || 1;

    const drawArrow = (ctx, fromX, fromY, toX, toY, theta, headlen, width, color) => {
        let angle = (Math.atan2(fromY - toY, fromX - toX) * 180) / Math.PI;
        let angle1 = ((angle + theta) * Math.PI) / 180;
        let angle2 = ((angle - theta) * Math.PI) / 180;
        let topX = headlen * Math.cos(angle1);
        let topY = headlen * Math.sin(angle1);
        let botX = headlen * Math.cos(angle2);
        let botY = headlen * Math.sin(angle2);

        ctx.save();
        ctx.beginPath();

        let arrowX = toX + topX;
        let arrowY = toY + topY;
        ctx.moveTo(arrowX, arrowY);
        ctx.lineTo(toX, toY);
        arrowX = toX + botX;
        arrowY = toY + botY;
        ctx.lineTo(arrowX, arrowY);
        ctx.strokeStyle = color;
        ctx.lineWidth = width;
        ctx.stroke();
        ctx.restore();
    };

    const draw = (context) => {
        // 绘制红框 点击 element
        if (matchNodeList) {
            for (let item of matchNodeList) {
                if (item?.replay?.rect) {
                    let { x, y, height, width } = item?.replay?.rect;
                    x *= imageScale * ratio;
                    y *= imageScale * ratio;
                    height *= imageScale * ratio;
                    width *= imageScale * ratio;
                    context.beginPath();
                    context.setLineDash([10, 0]);
                    context.lineWidth = 2;
                    context.strokeStyle = 'red';
                    context.strokeRect(x, y, width, height);
                    context.closePath();
                }
                if (item?.rect) {
                    let { x, y, h: height, w: width } = item?.rect;
                    x *= imageScale * ratio;
                    y *= imageScale * ratio;
                    height *= imageScale * ratio;
                    width *= imageScale * ratio;
                    context.beginPath();
                    context.setLineDash([10, 0]);
                    context.lineWidth = 2;
                    context.strokeStyle = 'red';
                    context.strokeRect(x, y, width, height);
                    context.closePath();
                }
            }
        }

        // 绘制红框 点击 element
        // 如果宽高过小，则绘制十字线
        if (rect && rect?.width) {
            if (rect?.width > 3 || rect?.height > 3) {
                let { x, y, height, width } = rect;
                x *= (imageScale * ratio) / scale;
                y *= (imageScale * ratio) / scale;
                height *= (imageScale * ratio) / scale;
                width *= (imageScale * ratio) / scale;
                context.beginPath();
                context.setLineDash([10, 0]);
                context.lineWidth = 3;
                context.strokeStyle = 'orange';
                context.strokeRect(x, y, width, height);
                context.closePath();
            } else {
                let { x, y } = rect;
                x *= (imageScale * ratio) / scale;
                y *= (imageScale * ratio) / scale;
                let ratioWidth = imageScale * ratio * screenWidth;
                let ratioHeight = imageScale * ratio * screenHeight;
                context.lineWidth = 2;
                context.strokeStyle = 'red';
                context.beginPath();
                context.moveTo(x, 0);
                context.lineTo(x, ratioHeight);
                context.stroke();
                context.moveTo(0, y);
                context.lineTo(ratioWidth, y);
                context.stroke();
                context.closePath();
            }
        }

        // 屏幕点击
        if (rect && rect?.width && rect?.startX) {
            let { x, y } = rect;
            x /= scale;
            y /= scale;
            let ratioWidth = imageScale * ratio * screenWidth;
            let ratioHeight = imageScale * ratio * screenHeight;
            // ios 宽高需要互换
            // ios 点击位置需要镜面映射
            if (+curOsType === 2 && rotation) {
                if (-1 !== [90, 270].indexOf(rotation)) {
                    let _x = x;
                    x = y;
                    y = screenWidth - _x;
                    let _width = ratioWidth;
                    ratioWidth = ratioHeight;
                    ratioHeight = _width;
                }
            }
            x *= imageScale * ratio;
            y *= imageScale * ratio;
            if (+curOsType === 2 && 90 === rotation) {
                x = ratioWidth - x;
                y = ratioHeight - y;
            }
            context.lineWidth = 2;
            context.strokeStyle = 'red';
            context.beginPath();
            context.moveTo(x, y);
            context.lineTo(ratioWidth, y);
            context.stroke();
            context.moveTo(x, y);
            context.lineTo(0, y);
            context.stroke();
            context.moveTo(x, y);
            context.lineTo(x, ratioHeight);
            context.stroke();
            context.moveTo(x, y);
            context.lineTo(x, 0);
            context.stroke();
            context.closePath();
        }
        if (rect && rect.startX) {
            // 绘制滑动线段
            let { startX, startY, targetX, targetY } = rect;
            startX /= scale;
            startY /= scale;
            targetX /= scale;
            targetY /= scale;
            // ios 点击位置需要镜面映射
            if (+localStorage.getItem('regressionCase_curOsType') === 2 && rotation) {
                if (-1 !== [90, 270].indexOf(rotation)) {
                    let _x = startX;
                    startX = startY;
                    startY = screenWidth - _x;
                    let _x2 = targetX;
                    targetX = targetY;
                    targetY = screenWidth - _x2;
                }
            }
            startX *= imageScale * ratio;
            startY *= imageScale * ratio;
            targetX *= imageScale * ratio;
            targetY *= imageScale * ratio;
            context.lineWidth = 2;
            context.strokeStyle = 'red';
            context.beginPath();
            context.moveTo(startX, startY);
            context.lineTo(targetX, targetY);
            context.stroke();
            // 画箭头
            drawArrow(context, startX, startY, targetX, targetY, 30, 15, 2, 'red');
            context.closePath();
        }
        // 绘制选中元素
        if (!isEmpty(rectList)) {
            for (let item of rectList) {
                let { x, y, h: height, w: width } = item;
                x *= (imageScale * ratio) / scale;
                y *= (imageScale * ratio) / scale;
                height *= (imageScale * ratio) / scale;
                width *= (imageScale * ratio) / scale;
                context.beginPath();
                context.setLineDash([10, 0]);
                context.lineWidth = 3;
                context.strokeStyle = 'orange';
                context.strokeRect(x, y, width, height);
                context.closePath();
            }
        }
    };

    return (
        <div className={styles.stepResDetail}>
            {'' !== imgSrc ? (
                <div className={classnames(styles.canvasScreenshot)}>
                    <CanvasScreenshot
                        draw={draw}
                        needImgSize={currentStep?.stepType === 410}
                        src={
                            imgSrc?.startsWith('http') ||
                            imgSrc?.startsWith('data') ||
                            '' === imgSrc
                                ? imgSrc
                                : 'file://' + imgSrc
                        }
                        ratio={ratio}
                        width={imgWidth}
                        height={imgHeight}
                    />
                </div>
            ) : null}
        </div>
    );
}

export default StepRes;
