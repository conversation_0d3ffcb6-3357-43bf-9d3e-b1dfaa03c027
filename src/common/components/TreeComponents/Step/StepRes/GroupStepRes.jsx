import {useState, useEffect, useMemo} from 'react';
import {Row, Col, Tag, Tooltip, Badge} from 'antd';
import classnames from 'classnames';
import NoContent from 'COMMON/components/common/NoContent';
import {getName} from 'COMMON/components/TreeComponents/Step/utils';
import AIAssertRes from '../TestStep/AIAssertRes';
import ImgRes from './ImgRes';
import VideoRes from './VideoRes';
import styles from './StepRes.module.less';

function StepRes(props) {
    const {testRes, currentStep, curOsType} = props;
    const [selecteStep, setSelecteStep] = useState(null);
    const [executeRes, setExecuteRes] = useState([]);
    const withScreenRecord =
        currentStep?.stepInfo?.params?.params?.assert?.params?.withScreenRecord;
    const screenRecordFile = testRes?.data?.extra?.screenRecordFile;

    useEffect(() => {
        let stepChildren = currentStep?.stepChildren;
        let newStepChildren = stepChildren?.map(item => {
            return {
                ...item,
                result: testRes?.data?.extra?.stepResult?.find(res => res?.stepId === item?.stepId)?.result ?? null
            };
        });
        // 如果含智能校验步骤，则将智能校验步骤的截图放在步骤后面
        newStepChildren.push({
            ...currentStep,
            result: testRes,
            stepType: 'final',
            stepDesc: '最终截图'
        });
        // 视频校验
        withScreenRecord &&
            newStepChildren.push({
                ...currentStep,
                stepId: -currentStep?.stepId,
                result: testRes,
                stepType: 'screenRecord',
                stepDesc: '录屏'
            });
        setSelecteStep(newStepChildren?.[0] ?? null);
        setExecuteRes(newStepChildren);
    }, [currentStep?.stepId]);

    const imgDetail = useMemo(() => {
        if (selecteStep?.result?.status === 0) {
            return selecteStep?.result?.data;
        }
        if (selecteStep?.result?.status === -1) {
            return selecteStep?.result?.data?.extra;
        }
        return null;
    }, [selecteStep]);

    const resType = useMemo(() => {
        if (selecteStep?.stepType === 'screenRecord') {
            return 'video';
        }
        if (imgDetail?.screenshot) {
            return 'img';
        }
    }, [selecteStep, imgDetail]);
    return (
        <Row>
            <Col className={styles.stepNameList} flex='70px'>
                {executeRes?.map((item) => (
                    <div
                        className={classnames(styles.stepName, {
                            [styles.stepNameActived]: selecteStep?.stepId === item?.stepId
                        })}
                        onClick={() => setSelecteStep(item)}
                        key={item?.stepId}
                    >
                        {item?.result?.status === 0 && (
                            <Badge status="success" style={{ marginRight: 3 }} />
                        )}
                        {item?.result?.status === -1 && (
                            <Badge status="error" style={{ marginRight: 3 }} />
                        )}
                        {![0, -1]?.includes(item?.result?.status) && (
                            <Badge status="default" style={{ marginRight: 3 }} />
                        )}
                        {['final', 'screenRecord'].includes(item?.stepType)
                            ? item?.stepDesc
                            : getName(
                                  item?.stepInfo?.type,
                                  item?.stepInfo?.params || item?.stepInfo,
                                  item?.stepType
                              )}
                    </div>
                ))}
            </Col>
            <Col className={styles.stepContent} flex='auto'>
                {
                    currentStep?.stepInfo?.params?.params?.assert?.type === 1 &&
                    <AIAssertRes
                        testRes={testRes}
                        status={testRes?.status}
                        reason={testRes?.data?.extra?.reason}
                        msg={testRes?.msg}
                    />
                }
                <div className={styles.imgsRes}>
                    {resType === 'video' &&
                        (screenRecordFile ? (
                            <VideoRes videoSrc={screenRecordFile} />
                        ) : (
                            <NoContent text="视频校验失败" className={styles.noContent} />
                        ))}
                    {resType === 'img' && (
                        <ImgRes
                            screenshot={imgDetail?.screenshot ?? imgDetail?.extra?.screenshot}
                            screenSize={imgDetail?.screenSize ?? imgDetail?.extra?.screenSize}
                            matchNodeList={imgDetail?.extra?.matchNodeList}
                            rect={imgDetail?.rect}
                            rectList={imgDetail?.extra?.rectList}
                            currentStep={currentStep}
                            curOsType={curOsType}
                        />
                    )}
                    {!imgDetail?.screenshot && selecteStep?.result && <>
                        <Tag color={selecteStep?.result?.status === 0 ? 'green' : 'red'}>
                            步骤执行
                            {selecteStep?.result?.status === 0 ? '通过' : '失败'}
                        </Tag>
                        <p style={{marginTop: 5, fontSize: 12}}>原因：{selecteStep?.result?.msg ?? '--'}</p>
                    </>}
                    {!selecteStep?.result &&
                        <NoContent text='无执行信息' className={styles.noContent} />}
                </div>
            </Col>
        </Row>
    );
};

export default StepRes;
