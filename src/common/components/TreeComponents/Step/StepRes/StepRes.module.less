.noContent {
    margin-top: 50px;
}

.aiAssertRes {
    position: relative;
    margin: 15px 5px 5px 5px;
}

.imgsRes {
    padding-top: 55px;
}

.canvasScreenshot {
    margin-top: 15px;
    text-align: center;
}

.canvasScreenshotWithMargin {
    margin-top: 15px;
}

.stepNameList {
    width: 100%;
    border-right: 1px solid #eee;

    .stepName {
        padding: 5px 0;
        width: 100%;
        color: #777;
        font-size: 12px;
        cursor: pointer;

        &:hover {
            color: #777;
        }
    }

    .stepNameActived {
        font-weight: bold;
        color: var(--primary-color);
        border-right: 2px solid var(--primary-color);
    }
}

.stepContent {
    overflow: hidden;
    padding-left: 5px;
}

.videoWrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 14px;
}

.video {
    width: 280px;
    height: 500px;
}