import { Tag } from 'antd';
import { STEP_TYPE } from 'COMMON/components/TreeComponents/Step/const';
import AIAssertRes from '../TestStep/AIAssertRes';
import ImgRes from './ImgRes';
import styles from './StepRes.module.less';

function StepRes(props) {
    const { testRes, currentStep, curOsType } = props;
    const hasAIAssert = [...STEP_TYPE.AI, ...STEP_TYPE.GROUP]?.includes(currentStep?.stepType);

    return (
        <div className={styles.stepResDetail}>
            {hasAIAssert && testRes && (
                <div className={styles.aiAssertRes}>
                    <AIAssertRes
                        testRes={testRes}
                        status={testRes?.status}
                        reason={testRes?.data?.extra?.reason}
                        msg={testRes?.msg}
                        stepType={currentStep?.stepType}
                    />
                </div>
            )}
            <div className={styles.imgRes} style={{ paddingTop: hasAIAssert ? 50 : 0 }}>
                <ImgRes
                    screenshot={testRes?.data?.screenshot ?? testRes?.data?.extra?.screenshot}
                    screenSize={testRes?.data?.screenSize ?? testRes?.data?.extra?.screenSize}
                    matchNodeList={testRes?.data?.extra?.matchNodeList}
                    rect={testRes?.data?.rect}
                    rectList={testRes?.data?.extra?.rectList}
                    currentStep={currentStep}
                    curOsType={curOsType}
                />
            </div>
            {testRes?.data?.extra?.whiteRatio >= 0 ? (
                <Tag color="default" style={{ marginTop: 5 }}>
                    白屏率 {testRes?.data?.extra?.whiteRatio}%
                </Tag>
            ) : null}
        </div>
    );
}

export default StepRes;
