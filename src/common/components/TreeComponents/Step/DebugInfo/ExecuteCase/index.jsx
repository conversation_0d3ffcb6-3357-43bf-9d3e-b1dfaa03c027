import {useState, useEffect} from 'react';
import {isEmpty} from 'lodash';
import {RightOutlined, DownOutlined, LeftOutlined} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {convertOsTypeToType} from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import {HOOK_TYPE, HOOK_TYPE_TEXT} from 'COMMON/components/TreeComponents/StepDetail/StepList/const';
import StepListWithCommon from 'COMMON/components/TreeComponents/StepDetail/StepList/StepListWithCommon';
import styles from './ExecuteCase.module.less';

function ExecuteCase(props) {
    const {
        serverDebugData,
        curOsType, // 端类型
        currentStep,
        setCurrentStep,
    } = props;

    const [setupCaseList, setSetupCaseList] = useState([]);
    const [teardownCaseList, setTeardownCaseList] = useState([]);
    const [stepCaseList, setStepCaseList] = useState([]);

    useEffect(() => {
        let os = convertOsTypeToType(curOsType);
        let caseList = serverDebugData?.caseList ?? [];

        // 提取所有 setup / teardown id
        let setupCaseIdList = caseList?.map(item => {
            if (item?.extra?.setupInfo?.[os]) {
                return item?.extra?.setupInfo?.[os];
            }
        });
        let teardownCaseIdList = caseList?.map(item => {
            if (item?.extra?.teardownInfo?.[os]) {
                return item?.extra?.teardownInfo?.[os];
            }
        });
        // 拆分 case
        let setupCaseList = caseList?.filter(item => setupCaseIdList?.includes(item?.caseNodeId));
        let stepCaseList = caseList?.filter(item => !setupCaseIdList?.includes(item?.caseNodeId) &&
            !teardownCaseIdList?.includes(item?.caseNodeId));
        let teardownCaseList = caseList?.filter(item => teardownCaseIdList?.includes(item?.caseNodeId));
        setSetupCaseList(setupCaseList);
        setStepCaseList(stepCaseList);
        setTeardownCaseList(teardownCaseList);
    }, [serverDebugData]);

    // 在组件函数内部添加状态管理折叠状态
    const [collapsedGroups, setCollapsedGroups] = useState({
        setup: false,
        step: false,
        teardown: false
    });

    const [collapsedCases, setCollapsedCases] = useState({
        setup: {},
        step: {},
        teardown: {}
    });

    // 切换折叠状态函数
    const toggleCollapse = (groupType) => {
        setCollapsedGroups((prev) => ({
            ...prev,
            [groupType]: !prev[groupType]
        }));
    };

    const toggleCaseCollapse = (caseNodeId, groupType) => {
        setCollapsedCases((prev) => {
            return {
                ...prev,
                [groupType]: {
                    ...prev[groupType],
                    [caseNodeId]: !prev[groupType]?.[caseNodeId]
                }
            }
        });
    };

    const renderCaseNode = (caseNode, hookType) => {
        const groupType = HOOK_TYPE_TEXT[hookType];
        const isCaseCollapsed = collapsedCases?.[groupType]?.[caseNode?.caseNodeId]; // 节点是否折叠
        return (
            <>
                <div
                    className={styles.groupTitle}
                    onClick={() => toggleCaseCollapse(caseNode?.caseNodeId, groupType)}
                >
                    {
                        isCaseCollapsed ?
                            <RightOutlined className={styles.titleIcon} /> :
                            <DownOutlined className={styles.titleIcon} />
                    }
                    <span>{caseNode?.nodeName}</span>
                    <span className={styles.stepCount}>({caseNode?.step?.length})</span>
                </div>
                {
                    !isCaseCollapsed &&
                    <div className={styles.groupList}>
                        <StepListWithCommon
                            {...props}
                            editType='debug'
                            showAddGroup={false}
                            currenStep={currentStep}
                            setCurrentStep={setCurrentStep}
                            stepList={caseNode?.step}
                            moduleStepList={caseNode?.step}
                        />
                    </div>
                }
            </>
        );
    };


    // 修改渲染步骤列表的辅助函数
    const renderStepGroup = (caseList, hookType, showHeader) => {
        const groupType = HOOK_TYPE_TEXT[hookType];
        const isCollapsed = collapsedGroups[groupType]; // 分组是否折叠
        return (
            <div className={styles.stepGroup}>
                {showHeader && (
                    <div className={styles.stepGroupHeader}>
                        <div
                            className={styles.headerLeft}
                            onClick={() => toggleCollapse(groupType)}
                        >
                            <span className={styles.collapseIcon}>
                                {isCollapsed ? <RightOutlined /> : <DownOutlined />}
                            </span>
                            <span className={styles.stepGroupTitle}>{groupType.toUpperCase()}</span>
                        </div>
                    </div>
                )}
                {!isCollapsed && (
                    <div className={styles.stepGroupContent}>
                        {
                            caseList?.map(caseNode => (
                                renderCaseNode(caseNode, hookType)
                            ))
                        }
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className={styles.executeCase}>
            {
                renderStepGroup(
                    setupCaseList,
                    HOOK_TYPE.setup,
                    !isEmpty(setupCaseList)
                )
            }
            {
                renderStepGroup(
                    stepCaseList,
                    HOOK_TYPE.step,
                    !isEmpty(setupCaseList) || !isEmpty(teardownCaseList)
                )
            }
            {
                renderStepGroup(
                    teardownCaseList,
                    HOOK_TYPE.teardown,
                    !isEmpty(teardownCaseList)
                )
            }
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({

}))(ExecuteCase);
