import {useEffect, useState} from 'react';
import {Divider, Splitter, Tag, Tooltip} from 'antd';
import {connectModel} from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import NoContent from 'COMMON/components/common/NoContent';
import DebugData from './DebugData';
import {StatusCode, StatusCodeText} from './const';
import styles from './DebugInfo.module.less';
import {CloseOutlined} from '@ant-design/icons';
import ExecuteCase from './ExecuteCase';

function DebugInfo(props) {
    const {curOsType,
        serverDebugData,
        serverTestRes, setServerTestRes} = props;
    const [stepList, setStepList] = useState([]);
    const [currentTestStep, setCurrentTestStep] = useState(null);
    const [currentStep, setCurrentStep] = useState(null);
    const [currentTestStepInvocationResult, setCurrentTestStepInvocationResult] = useState(null);
    const [totalTime, setTotalTime] = useState(null);

    useEffect(() => {
        let _step = null;
        for (let _case of (serverDebugData?.caseList ?? [])) {
            if (_case?.step?.length) {
                _step = _case?.step?.[0];
                break;
            }
        }
        setCurrentStep(_step);
    }, [serverDebugData]);

    useEffect(() => {
        setStepList(serverTestRes?.stepList);
        let times = serverTestRes?.stepList?.map(item =>
            item?.result?.resultInfo?.stepResult?.invocationResult?.[0]?.duration ?? 0
        );
        setTotalTime(times?.reduce((a, b) => a + b, 0));
    }, [serverTestRes, stepList]);

    useEffect(() => {
        let testStep = stepList?.find(item => item.stepId === currentStep?.stepId);
        if (typeof testStep?.result?.resultInfo === 'string') {
            fetch(testStep?.result?.resultInfo)
                .then(response => response.json()).then(data => {
                    testStep.result.resultInfo = data;
                    let newStep = {
                        ...currentStep,
                        ...testStep
                    };
                    setCurrentTestStep(newStep);
                    setStepList(stepList?.map(item => {
                        if (item.stepId === newStep?.stepId) {
                            return newStep;
                        }
                        return item;
                    }));
                })
        } else {
            setCurrentTestStep({
                ...currentStep,
                ...testStep
            });
        }
    }, [currentStep?.stepId, stepList]);

    useEffect(() => {
        setCurrentTestStepInvocationResult(
            currentTestStep?.result?.resultInfo?.stepResult?.invocationResult?.[0]);
    }, [currentTestStep]);

    const RenderDebugRes = ({serverTestRes}) => {
        let text = '未知';
        let type = 'default';
        switch (serverTestRes?.status) {
            case 0:
                text = '执行中';
                type = 'processing';
                break;
            case 1:
                text = '执行失败';
                type = 'error';
                break;
            case 2:
                text = '执行成功';
                type = 'success';
                break;
            case 3:
                text = '执行异常';
                type = 'error';
                break;
            case 4:
                text = '已取消';
                type = 'processing';
                break;
            default:
                break;
        }
        let msg = '';
        if ([1, 3]?.includes(serverTestRes?.status)) {
            msg = JSON.parse(serverTestRes?.errorInfo)?.message;
        }
        return (
            <>
                <span className={styles.totalInfo}>
                    {totalTime ?? '--'} ms
                </span>
                <Divider type='vertical' />
                <Tooltip title={msg}>
                    <Tag color={type}>
                        {text}
                    </Tag>
                </Tooltip>
            </>
        );
    };

    return (
        <>
            <Splitter>
                {
                    serverTestRes?.actionType === 'case' &&
                    <Splitter.Panel min={200} defaultSize={'25%'} style={{borderRight: '1px solid #f0f0f0'}}>
                        <div className={styles.title}>
                            <div className={styles.titleLeft}>
                                调试结果
                            </div>
                            <div className={styles.titleRight}>
                                <RenderDebugRes serverTestRes={serverTestRes} />
                            </div>
                        </div>
                        <ExecuteCase
                            curOsType={curOsType}
                            serverDebugData={serverDebugData}
                            currentStep={currentStep}
                            setCurrentStep={setCurrentStep}
                            stepList={stepList}
                        />
                    </Splitter.Panel>
                }
                <Splitter.Panel className={styles.rightPanel}>
                    {
                        serverTestRes?.actionType === 'step' &&
                        <div className={styles.title}>
                            <div className={styles.titleLeft}>
                                调试结果
                            </div>
                        </div>
                    }

                    <div
                        className={styles.extraInfo}
                    >
                        {
                            (serverTestRes?.status === 0) ? null : (
                                <>
                                    {/* http */}
                                    {
                                        currentTestStep?.stepType === 1001 &&
                                        <>
                                            <Tooltip title={currentTestStep?.result?.resultInfo?.msg}>
                                                <Tag
                                                    className={styles.statusCode}
                                                    color={StatusCode(currentTestStepInvocationResult?.response?.statusCode, 'http', currentTestStep?.status)}
                                                >
                                                    {currentTestStepInvocationResult?.response?.statusCode ??
                                                        StatusCodeText(currentTestStep?.status,
                                                            'other')}
                                                </Tag>
                                            </Tooltip>
                                            <span className={styles.statusCodeInfo}>
                                                {currentTestStepInvocationResult?.response?.size ?? '--'} MB
                                                &nbsp;
                                                {currentTestStepInvocationResult?.duration ?? '--'} ms
                                            </span>

                                        </>
                                    }
                                    {/* sql / redis */}
                                    {
                                        currentTestStep?.stepType !== 1001 &&
                                        <>
                                            <Tooltip title={currentTestStep?.result?.resultInfo?.msg}>
                                                <Tag
                                                    className={styles.statusCode}
                                                    color={StatusCode(undefined, 'other', currentTestStep?.status,
                                                    )}
                                                >
                                                    {StatusCodeText(currentTestStep?.status,
                                                        'other')}
                                                </Tag>
                                            </Tooltip>
                                            <span className={styles.statusCodeInfo}>
                                                &nbsp;
                                                {currentTestStepInvocationResult?.duration ?? '--'} ms
                                            </span>
                                        </>
                                    }
                                    <Divider type="vertical" />
                                    <span
                                        className={styles.closeDebugInfo}
                                        onClick={() => {
                                            setServerTestRes(null);
                                        }}
                                    >
                                        <CloseOutlined />
                                    </span>
                                </>)
                        }
                    </div>
                    <div
                        className={styles.content}
                        style={{
                            height: serverTestRes?.actionType === 'step' ? 'calc(100% - 40px)' : '100%'
                        }}
                    >
                        {
                            (serverTestRes?.status === 0 && serverTestRes?.actionType === 'step') ?
                                <NoContent text='调试进行中...' className={styles.noContent} /> :
                                <DebugData
                                    {...props}
                                    serverTestStepRes={currentTestStep}
                                />
                        }
                    </div>
                </Splitter.Panel>
            </Splitter>
        </>
    );
};

export default connectModel([commonModel], (state) => ({
    serverTestRes: state.common.case.serverTestRes,
    serverDebugData: state.common.case.serverDebugData,
}))(DebugInfo);