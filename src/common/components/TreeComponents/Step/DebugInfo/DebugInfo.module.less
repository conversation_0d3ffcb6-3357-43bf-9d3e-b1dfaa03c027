@import "RESOURCES/css/resizableBox.less";

.noContent {
    margin-top: 50px;
}

.debugInfo {
    position: relative;
    bottom: 0;
}

.resizableBox {
    border-top: 1px solid var(--border-color);
}

.title {
    position: relative;
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding: 0 5px;
    cursor: pointer;

    .titleLeft {
        float: left;
        font-size: 14px;
        color: #404653;
        font-weight: bold;

        .titleLeftIcon {
            font-size: 10px;
            font-weight: bold;
            margin-right: 10px;
        }
    }

    .titleRight {
        position: absolute;
        right: 5px;
        overflow: hidden;
    }
}

.content {
    overflow: scroll;
}

.rightPanel {
    position: relative;

    .extraInfo {
        position: absolute;
        right: 10px;
        top: 5px;

        .closeDebugInfo {
            margin-left: 5px;
            padding: 3px;
            border-radius: 2px;
            cursor: pointer;

            &:hover {
                background-color: var(--base-hover-background-color);
            }
        }
    }
}

.totalInfo {
    float: left;
}

.statusCodeInfo,
.totalInfo {
    font-size: 12px;
    color: #777;
}