export const StatusCode = (statusCode, type, status) => {
    if (type === 'other' || !statusCode) {
        switch (status) {
            case 0:
                return 'success';
            case 1:
                return 'error';
            case 2:
                return 'error';
            case 3:
                return 'process';
            default:
                return 'default';
        }
    }
    switch (statusCode) {
        case 200:
            return 'success';
        case 301:
            return 'warning';
        case 302:
            return 'warning';
        default:
            return 'error';
    }
};


// 0:成功 1:失败 2:异常 3:运行中
export const StatusCodeText = (statusCode, type) => {
    if (type === 'other') {
        switch (statusCode) {
            case 0:
                return 'Success';
            case 1:
                return 'Error';
            case 2:
                return 'Warning';
            case 3:
                return 'Running';
            default:
                return '未知';
        }
    }
};
