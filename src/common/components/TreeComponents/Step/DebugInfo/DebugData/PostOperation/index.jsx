import {CheckCircleTwoTone, CloseCircleTwoTone} from '@ant-design/icons';
import {isEmpty} from 'lodash';
import NoContent from 'COMMON/components/common/NoContent';
import SqlResponseInfo from '../ResponseInfo/SQL';
import AssertInfo from './AssertInfo';
import styles from './PostOperation.module.less';

const PostOperation = (props) => {
    const {data} = props;

    return (
        <div className={styles.card}>
            {
                isEmpty(data) ?
                    <NoContent className={styles.noContent} text='暂无操作' /> :
                    <>
                        {
                            data?.map((item, index) => (
                                <div key={'PostOperation' + String(index)} className={styles.resultItem}>
                                    <div>
                                        {
                                            item?.result === 1 ?
                                                <CloseCircleTwoTone twoToneColor='#ff5052' />
                                                :
                                                <CheckCircleTwoTone twoToneColor='#52c41a' />
                                        }
                                        <span
                                            className={styles.assertContent}
                                            style={{
                                                color: item?.result === 1 ? '#ff5052' : '#52c41a',
                                            }}
                                        >
                                            {item?.result === 0 ? '执行成功' : '执行失败'}
                                        </span>
                                        <AssertInfo data={item?.assertResult ?? null} type='postOperation' />
                                        <SqlResponseInfo
                                            data={item?.invocationResult?.[0].response ?? null}
                                            height={80}
                                        />
                                    </div>
                                </div>
                            ))
                        }
                    </>
            }
        </div>
    );
};

export default PostOperation;