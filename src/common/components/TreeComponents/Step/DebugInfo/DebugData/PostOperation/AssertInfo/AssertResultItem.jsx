import {Tag, Typography} from 'antd';
import {CheckCircleTwoTone, CloseCircleTwoTone} from '@ant-design/icons';
import styles from './AssertInfo.module.less';

const {Paragraph, Text} = Typography;

// 获取操作符文本
const getOperatorText = (type) => {
    switch (type) {
        case 1:
            return '包含';
        case 2:
            return '正则';
        case 3:
            return '等于';
        case 4:
            return '大于';
        case 5:
            return '小于';
        case 6:
            return '大于等于';
        case 7:
            return '小于等于';
        case 8:
            return '不等于';
        case 9:
            return '存在jsonPath';
        default:
            return '未知操作';
    }
};

const AssertResultItem = ({data}) => {
    return (
        <div className={styles.resultItem}>
            <div>
                <span className={styles.assertTitle}>
                    断言结果:&nbsp;&nbsp;
                    {data?.assertResult?.assertedContent}
                </span>
            </div>
            <div>
                <span className={styles.assertTitle}>
                    预期内容:&nbsp;&nbsp;
                    {getOperatorText(data?.assertResult?.type)}
                    &nbsp;&nbsp;
                    {data?.assertResult?.data}
                </span>
            </div>
        </div>
    );
};

export default AssertResultItem;
