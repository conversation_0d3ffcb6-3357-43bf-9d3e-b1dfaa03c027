import {useMemo, useState} from 'react';
import classnames from 'classnames';
import {isEmpty} from 'lodash';
import NoContent from 'COMMON/components/common/NoContent';
import HttpRequestInfo from './RequestInfo/HTTP';
import SqlRequestInfo from './RequestInfo/SQL';
import RedisRequestInfo from './RequestInfo/Redis';
import ResponseHeader from './ResponseInfo/HTTP/ResponseHeader';
import ResponseBody from './ResponseInfo/HTTP/ResponseBody';
import SqlResponseInfo from './ResponseInfo/SQL';
import RedisResponseInfo from './ResponseInfo/Redis';
import AssertInfo from './AssertInfo';
import PostOperation from './PostOperation';
import {HTTP_TAB_OPTIONS, SQL_TAB_OPTIONS, REDIS_TAB_OPTIONS} from './const';
import styles from './DebugData.module.less';

function DebugData(props) {
    const {serverTestStepRes, currentStep, handleUpdateStep, style} = props;
    const [activeKey, setActiveKey] = useState('body');
    const stepResult = serverTestStepRes?.result?.resultInfo?.stepResult;
    
    // 数据展示
    const getExtraTitle = (step, key) => {
        if (key === 'assertResult' && step?.assertResult?.assertResult?.length) {
            return (
                <span className={styles.extraTitle}>
                    ({step?.assertResult?.assertResult?.length})
                </span>
            );
        }
        if (key === 'postOperation' && step?.postOperationResult?.length) {
            return (
                <span className={styles.extraTitle}>
                    ({step?.postOperationResult?.length})
                </span>
            );
        }
        return;
    };

const items = useMemo(() => {
    let options = [];
    switch (serverTestStepRes?.stepType) {
        case 1001:
            options = HTTP_TAB_OPTIONS;
            break;
        case 1101:
            options = SQL_TAB_OPTIONS;
            break;
        case 1201:
            options = REDIS_TAB_OPTIONS;
            break;
        default:
            options = HTTP_TAB_OPTIONS;
            break;
    }
    setActiveKey(options?.[0]?.key);
    return options;
}, [serverTestStepRes]);

if (isEmpty(serverTestStepRes)) {
    return (<NoContent text='执行结果为空' className={styles.noContent} />);
}

return (
    <div className={styles.debugData} style={style}>
        <div className={styles.tabs}>
            {items?.map(item => {
                return (
                    <div
                        key={item?.key}
                        className={classnames(styles.tab,
                            {[styles.activeTab]: activeKey === item.key})}
                        onClick={() => {
                            setActiveKey(item.key);
                        }}
                    >
                        {item.label}
                        {getExtraTitle(stepResult, item.key)}
                    </div>
                );
            })}
        </div>
        <div className={styles.content}>
            {/* http response */}
            {
                activeKey === 'http_body' &&
                <ResponseBody
                    data={stepResult?.invocationResult?.[0].response?.body ?? null}
                    currentStep={currentStep}
                    handleUpdateStep={handleUpdateStep}
                />
            }
            {
                activeKey === 'http_header' &&
                <ResponseHeader
                    data={stepResult?.invocationResult?.[0].response?.headers ?? []}
                />
            }
            {/* sql response */}
            {
                activeKey === 'sql_response' &&
                <SqlResponseInfo
                    data={stepResult?.invocationResult?.[0].response ?? null}
                    currentStep={currentStep}
                    handleUpdateStep={handleUpdateStep}
                />
            }
            {/* redis response */}
            {
                activeKey === 'redis_response' &&
                <RedisResponseInfo
                    data={stepResult?.invocationResult?.[0].response ?? null}
                    currentStep={currentStep}
                    handleUpdateStep={handleUpdateStep}
                />
            }
            {
                activeKey === 'assertResult' &&
                <AssertInfo data={stepResult?.assertResult ?? null} />
            }
            {
                activeKey === 'postOperation' &&
                <PostOperation data={stepResult?.postOperationResult ?? null} />
            }
            {
                activeKey === 'http_request' &&
                <HttpRequestInfo data={stepResult?.invocationResult?.[0].request ?? null} />
            }
            {
                activeKey === 'sql_request' &&
                <SqlRequestInfo data={stepResult?.invocationResult?.[0].request ?? null} />
            }
            {
                activeKey === 'redis_request' &&
                <RedisRequestInfo data={stepResult?.invocationResult?.[0].request ?? null} />
            }
        </div>
    </div>
);
};

export default DebugData;