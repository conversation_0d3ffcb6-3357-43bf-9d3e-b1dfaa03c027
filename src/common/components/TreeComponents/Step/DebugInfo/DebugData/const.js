export const BodyType = {
    1: 'none',
    2: 'form-data',
    3: 'x-www-form-urlencoded',
    4: 'JSON',
    5: 'raw'
};
export const operationList = [
    {
        value: 1,
        label: '包含'
    },
    {
        value: 2,
        label: '正则'
    },
    {
        value: 3,
        label: '等于'
    },
    {
        value: 4,
        label: '大于'
    },
    {
        value: 5,
        label: '小于'
    },
    {
        value: 6,
        label: '大于等于'
    },
    {
        value: 7,
        label: '小于等于'
    },
    {
        value: 8,
        label: '不等于'
    },
    {
        value: 9,
        label: '存在JsonPath'
    }
];

export const HTTP_TAB_OPTIONS = [
    {
        key: 'http_body',
        label: 'Body'
    },
    {
        key: 'http_header',
        label: 'Header',
    },
    {
        key: 'assertResult',
        label: '断言分析',
    },
    {
        key: 'postOperation',
        label: '后置操作',
    },
    {
        key: 'http_request',
        label: '请求信息',
    },
];

export const SQL_TAB_OPTIONS = [
    {
        key: 'sql_response',
        label: 'Response'
    },
    {
        key: 'assertResult',
        label: '断言分析',
    },
    {
        key: 'postOperation',
        label: '后置操作',
    },
    {
        key: 'sql_request',
        label: '请求信息',
    },
];

export const REDIS_TAB_OPTIONS = [
    {
        key: 'redis_response',
        label: 'Response'
    },
    {
        key: 'assertResult',
        label: '断言分析',
    },
    {
        key: 'postOperation',
        label: '后置操作',
    },
    {
        key: 'redis_request',
        label: '请求信息',
    },
];