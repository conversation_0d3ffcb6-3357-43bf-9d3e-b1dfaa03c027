import {forwardRef, useState, useRef, useCallback, useImperativeHandle} from 'react';
import {Modal, Form, Button, Space, Input, Select, message} from 'antd';
import {useNavigate} from 'umi';
import {add, isEmpty} from 'lodash';
import {stringifyUrl} from 'query-string';
import EventBus from 'COMMON/utils/eventBus';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import styles from './common.module.less';

function VariableModal(props, ref) {
    const {setShowModal, onChange} = props;
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [addForm] = Form.useForm();

    const showModal = useCallback(
        ({jsonPath, value}) => {
            setShowModal(true);
            setOpen(true);
            if (addForm) {
                addForm?.setFieldValue('jsonPath', jsonPath);
            }
        }, []);

    const hideModal = useCallback(
        () => {
            setShowModal(false);
            setOpen(false);
        }, []);

    const onClick = () => {
        addForm.validateFields().then((values) => {
            onChange && onChange(values?.jsonPath, values?.extractName);
            hideModal();
        }).catch(() => {
            message.error('请输入变量名称');
        });
    };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(ref, () => {
        return {
            show: showModal
        };
    }, [showModal]);

    return (
        <Modal
            title="设为变量"
            centered
            autoComplete="off"
            open={open}
            destroyOnClose
            transitionName=""
            onCancel={hideModal}
            footer={
                <div>
                    <Space>
                        <Button onClick={hideModal}>
                            取消
                        </Button>
                        <Button
                            type="primary"
                            loading={loading}
                            onClick={onClick}
                        >
                            确定
                        </Button>
                    </Space>
                </div>
            }
            mask="true"
            maskClosable="false"
            width={500}
        >
            <Form form={addForm} colon={false} labelCol={{span: 8}}>
                <Form.Item label="变量名称" name="extractName">
                    <Input placeholder="请输入变量名称" />
                </Form.Item>
                <Form.Item
                    label="JSONPath 表达式"
                    name="jsonPath"
                    rules={[
                        {
                            required: true,
                            message: '请输入 JSONPath 表达式'
                        }
                    ]}
                >
                    <Input placeholder="例: $.store.book[0].title" />
                </Form.Item>
            </Form>
        </Modal>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    showModal: state.common.base.showModal,
}))(forwardRef(VariableModal));