import {LinkedList} from 'monaco-editor/esm/vs/base/common/linkedList';
import {MenuId, MenuRegistry} from 'monaco-editor/esm/vs/platform/actions/common/actions';
import {jsonToAST} from './jsonToAST';

const getChineseTitle = (title) => {
    switch (title) {
        case 'Cut':
            return '剪切';
        case 'Copy':
            return '复制';
        case 'Paste':
            return '粘贴';
        case 'Format Document':
            return '格式化';
        default:
            return title;
    }
};

const getJsonPath = (tree, selection, path = [], jsonPath = {}) => {
    tree.forEach(node => {
        path.push(node.key?.value);
        // 行匹配
        if (node.loc.end.line === selection.positionLineNumber) {
            jsonPath.path = '$.' + path.join('.');
            jsonPath.data = node.value?.value ?? '';
        }
        if (node?.value?.children) {
            jsonPath = getJsonPath(node.value.children, selection, path, jsonPath);
        }
        path.pop();
    });
    return jsonPath;
};

export const setupContextMenuFeature = (editor, extraMenuOptions) => {
    try {
        // filterMenus(filterMenusOptions);
        // if (extraMenuOptions?.AddVariable) {
        //     editor.addAction({
        //         id: 'AddVariable',
        //         label: '设为变量',
        //         contextMenuOrder: 1,
        //         contextMenuGroupId: 'edit',
        //         run: () => {
        //             let selection = editor.getSelection();
        //             let astTree = jsonToAST(editor?.getValue());
        //             if (astTree?.children) {
        //                 let {path} = getJsonPath(astTree.children, selection);
        //                 // 更新步骤
        //                 extraMenuOptions?.AddVariable?.onChange(path ?? '');
        //             }
        //         }
        //     });
        // }
        // if (extraMenuOptions?.AddAssertion) {
        editor.addAction({
            id: 'AddAssertion',
            label: '添加断言',
            contextMenuOrder: 1,
            contextMenuGroupId: 'edit',
            run: () => {
                // let selection = editor.getSelection();
                // let astTree = jsonToAST(editor?.getValue());
                // if (astTree?.children) {
                //     let {path, data} = getJsonPath(astTree.children, selection);
                //     // 更新步骤
                //     extraMenuOptions?.AddAssertion?.onChange(path ?? '', data ?? '');
                // }
            }
        });
        // }
    } catch (err) {
        console.log(err);

    }
};

// 过滤菜单项
const filterMenus = (filterMenusOptions = ['Cut', 'Copy', 'Paste', 'Format Document']) => {
    const contextMenuEntry = MenuRegistry._menuItems.get(MenuId.EditorContext);
    let node = contextMenuEntry._first;
    do {
        if (node.element) {
            let title = node.element?.command?.title;
            if (!filterMenusOptions?.includes(title)) {
                contextMenuEntry._remove(node);
            } else {
                // 中文标题
                node.element.command.title = getChineseTitle(title);
            }
        }
        node = node.next;
    } while (node !== undefined);
};

// 添加新菜单（含子菜单）
const addActionWithSubmenus = (
    editor, descriptor) => {
    const submenu = new MenuId(descriptor.context);
    const list = new LinkedList();
    MenuRegistry._menuItems.set(submenu, list);

    for (let i = 0; i < descriptor.actions.length; i++) {
        const action = descriptor.actions[i];
        editor.addAction({
            id: action.id,
            label: action.label,
            run: action.run,
            contextMenuOrder: i,
            contextMenuGroupId: descriptor.context,
        });
        const actionId = editor
            .getSupportedActions()
            .find(a => a.label === action.label && a.id.endsWith(action.id))?.id;

        const items = MenuRegistry._menuItems.get(MenuId.EditorContext);
        const item = popItem(items, actionId);
        if (item) {
            list.push(item);
        }
    }

    MenuRegistry._menuItems.get(MenuId.EditorContext).push({
        group: descriptor.group,
        order: descriptor.order,
        submenu: submenu,
        title: descriptor.title,
    });
};

const popItem = (items, id) => {
    let node = items._first;
    do {
        if (node.element?.command?.id === id) {
            items._remove(node);
            return node.element;
        }
        node = node.next;
    } while (node !== undefined);
};