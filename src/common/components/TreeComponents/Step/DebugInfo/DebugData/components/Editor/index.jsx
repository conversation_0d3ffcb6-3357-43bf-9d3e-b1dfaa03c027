import {useState, useEffect, useRef} from 'react';
import * as monaco from 'monaco-editor';
import {CONFIG} from './config';

import {jsonToJsonPath} from './utils/common';

import VariableModal from './Modal/VariableModal';
import AssertionModal from './Modal/AssertionModal';

import styles from './Editor.module.less';

export function checkJsonCode(strJsonCode) {
    let res = '';
    try {
        for (let i = 0, j = 0, k = 0, ii, ele; i < strJsonCode.length; i++) {
            ele = strJsonCode.charAt(i);
            if (j % 2 === 0 && ele === '}') {
                // eslint-disable-next-line no-plusplus
                k--;
                for (ii = 0; ii < k; ii++) ele = `    ${ele}`;
                ele = `\n${ele}`;
            } else if (j % 2 === 0 && ele === '{') {
                ele += '\n';
                // eslint-disable-next-line no-plusplus
                k++;
                for (ii = 0; ii < k; ii++) ele += '    ';
            } else if (j % 2 === 0 && ele === ',') {
                ele += '\n';
                for (ii = 0; ii < k; ii++) ele += '    ';
                // eslint-disable-next-line no-plusplus
            } else if (ele === '"') j++;
            res += ele;
        }
    } catch (error) {
        res = strJsonCode;
    }
    return res;
};

const Editor = (props) => {
    const {
        height,
        options = {},
        contextmenuOptions = [],
        viewValue,
        onBlur,
        editType,
        language,
        pretty = true
    } = props;
    const [editor, setEditor] = useState(null);
    const editorRef = useRef(null);
    const variableModalRef = useRef(null); // 变量弹窗
    const assertionModalRef = useRef(null); // 断言弹窗

    // 初始化编辑器
    useEffect(() => {
        // 创建编辑器实例
        const editor = monaco.editor.create(document.getElementById('containerDebug'), {
            value: viewValue ?? '',
            language: language ?? 'plaintext',
            readOnly: ['readonly', 'debug', 'execute'].includes(editType) || false,
            ...CONFIG,
            editorClassName: 'monaco-editor',
            ...options,
        });

        // 添加右键功能
        for (let option of (contextmenuOptions || [])) {
            editor.addAction({
                id: option?.value, // 唯一 ID
                label: option?.label,
                contextMenuOrder: 1, // 排序
                contextMenuGroupId: 'edit', // 分组 id
                run: () => {
                    let selection = editor.getSelection();
                    // 获取 JSONPath
                    let editorValue = editorRef.current?.getValue();
                    let {path, data} = jsonToJsonPath(editorValue, selection);
                    if (option?.value === 'AddVariable') {
                        variableModalRef.current?.show({
                            jsonPath: path
                        });
                    }
                    if (option?.value === 'AddAssertion') {
                        assertionModalRef.current?.show({
                            jsonPath: path,
                            value: data
                        });
                    }
                }
            });
        }
        editorRef.current = editor;
        setEditor(editor);
    }, []);

    // 编辑器内容展示更新
    useEffect(() => {
        if (editor && editorRef?.current) {
            editorRef?.current?.setValue(pretty ? checkJsonCode(viewValue ?? '') : viewValue);
        }
    }, [editor, editorRef?.current, viewValue]);

    // 编辑器更新 options
    useEffect(() => {
        if (editor && editorRef?.current) {
            editorRef?.current?.updateOptions({
                language: language
            });
        }
    }, [editor, editorRef?.current, language]);

    // 编辑器事件
    useEffect(() => {
        // 监听失焦保存
        editorRef.current?.onDidBlurEditorText(() => {
            let val = editorRef.current?.getValue();
            onBlur && onBlur(val);
        });
    }, [editorRef?.current]);

    return (
        <>
            <div
                id='containerDebug'
                style={{
                    border: '1px solid #eee',
                    height: height || 300,
                    overflow: 'hidden',
                }}
            />
            {/* 变量弹窗 */}
            <VariableModal
                ref={variableModalRef}
                onChange={contextmenuOptions?.find(item => item?.value === 'AddVariable')?.onChange}
            />
            {/* 断言弹窗 */}
            <AssertionModal
                ref={assertionModalRef}
                onChange={contextmenuOptions?.find(item => item?.value === 'AddAssertion')?.onChange}
            />
        </>
    );
};

export default Editor;