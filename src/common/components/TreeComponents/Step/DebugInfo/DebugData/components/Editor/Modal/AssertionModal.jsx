import {forwardRef, useState, useCallback, useImperativeHandle} from 'react';
import {Modal, Form, Button, Space, Input, Select, message} from 'antd';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {ASSERT_OPTIONS} from '../../../../../StepInfo/ApiAction/components/Assertion/const';
import styles from './common.module.less';

function AssertionModal(props, ref) {
    const {setShowModal, onChange} = props;
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [addForm] = Form.useForm();

    const showModal = ({jsonPath, value}) => {
        setShowModal(true);
        setOpen(true);
        if (addForm) {
            addForm?.setFieldValue('jsonPath', jsonPath);
        }
    };

    const hideModal = useCallback(
        () => {
            setShowModal(false);
            setOpen(false);
        }, []);

    const onClick = () => {
        addForm.validateFields().then((values) => {
            setLoading(true);
            onChange && onChange(values?.jsonPath, values?.assert?.type, values?.assert?.data);
            hideModal();
            setLoading(false);
        }).catch(() => {
            setLoading(false);
            message.error('请检查表单输入是否正确');
        });
    };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(ref, () => {
        return {
            show: showModal
        };
    }, [showModal]);

    return (
        <Modal
            title="添加断言"
            centered
            autoComplete="off"
            open={open}
            destroyOnClose
            transitionName=""
            onCancel={hideModal}
            footer={
                <div>
                    <Space>
                        <Button onClick={hideModal}>
                            取消
                        </Button>
                        <Button
                            type="primary"
                            loading={loading}
                            onClick={onClick}
                        >
                            确定
                        </Button>
                    </Space>
                </div>
            }
            mask="true"
            maskClosable="false"
            width={500}
        >
            <Form form={addForm} colon={false} labelCol={{span: 8}}>
                <Form.Item
                    label="JSONPath 表达式"
                    name="jsonPath"
                    rules={[
                        {
                            required: true,
                            message: '请输入 JSONPath 表达式'
                        }
                    ]}
                >
                    <Input placeholder="例: $.store.book[0].title" />
                </Form.Item>
                <Form.Item label="断言">
                    <Space.Compact style={{width: '100%'}}>
                        <Form.Item
                            name={['assert', 'type']}
                            noStyle
                            rules={[{required: true, message: '请选择断言类型'}]}
                            initialValue={3}
                        >
                            <Select
                                options={ASSERT_OPTIONS}
                                popupMatchSelectWidth={false}
                                style={{display: 'inline-block', width: '100px'}}
                            />
                        </Form.Item>
                        <Form.Item
                            name={['assert', 'data']}
                            noStylea
                            rules={[{required: true, message: '请输入断言内容'}]}
                            style={{display: 'inline-block', width: 'calc(100% - 30px)'}}
                        >
                            <Input placeholder="请输入断言内容" />
                        </Form.Item>
                    </Space.Compact>
                </Form.Item>
            </Form>
        </Modal>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    showModal: state.common.base.showModal,
}))(forwardRef(AssertionModal));