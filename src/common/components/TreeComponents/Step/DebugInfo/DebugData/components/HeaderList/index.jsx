import {isEmpty} from 'lodash';
import NoContent from 'COMMON/components/common/NoContent';
import styles from './HeaderList.module.less';

const HeaderList = (props) => {
    const {data} = props;
    return (
        <div className={styles.table}>
            {
                isEmpty(data) ?
                    <NoContent text='暂无数据' className={styles.noContent} /> :
                    <table>
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>值</th>
                            </tr>
                        </thead>
                        <tbody>
                            {data.map((item, index) => (
                                <tr key={`tabel_${String(index)}`}>
                                    <td>
                                        {item.name || '--'}
                                    </td>
                                    <td>
                                        {item.value || '--'}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
            }
        </div>
    );
};

export default HeaderList;