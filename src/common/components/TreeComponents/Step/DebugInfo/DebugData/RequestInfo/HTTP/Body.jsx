import NoContent from 'COMMON/components/common/NoContent';
import FormDataTable from
    'COMMON/components/TreeComponents/Step/StepInfo/ApiAction/components/common/Table/FormDataTable';
import EditableTable from
    'COMMON/components/TreeComponents/Step/StepInfo/ApiAction/components/common/Table/EditableTable';
import Editor from
    '../../components/Editor';
import styles from '../RequestInfo.module.less';

const Body = ({data}) => {
    // 根据 Body 类型生成不同的显示内容
    const getBody = bd => {
        // none
        if (bd === 1) {
            return (
                <NoContent text='该请求体为空' className={styles.noContent} />
            );
        }
        // form-data
        if (bd === 2) {
            return (
                <FormDataTable
                    disabled
                    data={data?.requestData ?? []}
                />
            );
        }
        // x-www-form-urlencoded
        if (bd === 3) {
            return (
                <EditableTable
                    disabled
                    data={data?.requestData ?? []}
                />
            );
        }
        // json 和 raw
        if ([4, 5]?.includes(bd)) {
            return (
                <Editor
                    editType='readonly'
                    height={200}
                    language={bd === 4 ? 'json' : 'text'}
                    type={bd}
                    pretty={false}
                    viewValue={data?.requestData}
                />
            );
        }
    };

    return (
        <div className={styles.body}>
            {getBody(data?.type)}
        </div>
    );
};

export default Body;