import {useMemo} from 'react';
import HeaderList from '../../components/HeaderList';
import styles from '../RequestInfo.module.less';

const RedisInterfaceRequest = (props) => {
    const {data} = props;

    const dataItems = useMemo(() => {
        let items = [];
        for (let item in data) {
            let name = item;
            if (item === 'redisStatement') {
                name = 'Redis 语句';
            }
            if (item === 'username') {
                name = '用户名';
            }
            if (item === 'host') {
                name = 'Redis 服务地址';
            }
            if (item === 'port') {
                name = 'Redis 端口';
            }
            items.push(
                {
                    name: name,
                    value: data[item]
                }
            );
        }
        return items;
    }, [data]);

    return (
        <div className={styles.card}>
            <HeaderList
                data={dataItems}
            />
        </div>
    );
};

export default RedisInterfaceRequest;