import {isEmpty} from 'lodash';
import {METHID_OPTIONS} from 'COMMON/components/TreeComponents/Step/StepInfo/ApiAction/const';
import HeaderList from '../../components/HeaderList';
import Body from './Body';
import styles from '../RequestInfo.module.less';

const InterfaceRequest = (props) => {
    const {data} = props;

    const getParams = (params) => {
        if (isEmpty(params)) {
            return '';
        }
        return `?${params.map((item) => `${item?.name}=${item?.value}`)
            .join('&')}`;
    };

    const methodOption = METHID_OPTIONS?.find((item) => item.value === data?.method);
    return (
        <>
            <div className={styles.card}>
                <div className={styles.title}>请求 URL</div>
                <div>
                    <span
                        className={styles.selectMethod}
                        style={{
                            color: methodOption?.color
                        }}
                    >
                        {methodOption?.label}
                    </span>
                    <span className={styles.url}>
                        {data?.host + data?.path || ''}{getParams(data?.query)}
                    </span>
                </div>
            </div>
            <div className={styles.card}>
                <div className={styles.title}>Header</div>
                <HeaderList data={data?.headers ?? []} />
            </div>
            <div className={styles.card}>
                <div className={styles.title}>Body</div>
                <Body data={data?.body} height={200} />
            </div>
        </>
    );
};

export default InterfaceRequest;