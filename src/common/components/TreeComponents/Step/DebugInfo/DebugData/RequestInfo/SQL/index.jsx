import {useMemo} from 'react';
import HeaderList from '../../components/HeaderList';
import styles from '../RequestInfo.module.less';

const SqlInterfaceRequest = (props) => {
    const {data} = props;

    const dataItems = useMemo(() => {
        let items = [];
        for (let item in data) {
            let name = item;
            let value = data[item];
            if (item === 'type') {
                name = '数据库类型';
                value = data[item] === 1 ? 'MySQL' : 'BaikaiDB';
            }
            if (item === 'version') {
                name = '数据库版本';
                value = data[item] === 1 ? 'MySQL 5' : 'MySQL 8';
            }
            if (item === 'host') {
                name = '数据库服务地址';
            }
            if (item === 'port') {
                name = '数据库端口';
            }
            if (item === 'username') {
                name = '用户名';
            }
            if (item === 'dataBase') {
                name = '数据库名称';
            }
            if (item === 'sqlStatement') {
                name = '数据库语句';
            }
            if (item === 'encoding') {
                name = '编码';
                switch (data[item]) {
                    case 1:
                        value = 'ASCII';
                        break;
                    case 2:
                        value = 'ISO-8859';
                        break;
                    case 3:
                        value = 'UTF';
                        break;
                    case 4:
                        value = 'GBK';
                        break;
                    default:
                        break;
                };
            }
            items.push(
                {
                    name: name,
                    value: value
                }
            );
        }
        return items;
    }, [data]);

    return (
        <div className={styles.card}>
            <HeaderList
                data={dataItems}
            />

        </div>
    );
};

export default SqlInterfaceRequest;