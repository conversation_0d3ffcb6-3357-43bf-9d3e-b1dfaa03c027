import Editor from '../../components/Editor';

const ResponseBody = (props) => {
    const {data, language, height, currentStep, handleUpdateStep} = props;

    return (
        <Editor
            language={language ?? 'json'}
            height={height ?? window.innerHeight - 200}
            editType='readonly'
            viewValue={data}
            contextmenuOptions={[
                {
                    value: 'AddVariable',
                    label: '设为变量',
                    onChange: (jsonPath, extractName) => {
                        let newCurrentStep = {...currentStep};
                        newCurrentStep.stepInfo.variableExtract = [
                            ...(newCurrentStep.stepInfo.variableExtract || []),
                            {
                                extractName: extractName ?? '',
                                extractTarget: 2,
                                extractScope: 2,
                                jsonPath: jsonPath ?? '',
                                checked: true
                            }
                        ];
                        handleUpdateStep(newCurrentStep);
                    }
                },
                {
                    value: 'AddAssertion',
                    label: '添加断言',
                    onChange: (jsonPath, type, selectText) => {
                        let newCurrentStep = {...currentStep};
                        newCurrentStep.stepInfo.responseAssert = [
                            ...(newCurrentStep.stepInfo.responseAssert || []),
                            {
                                assertType: 1,
                                assertTarget: 2,
                                assertContent: {
                                    jsonPath: jsonPath ?? '',
                                    type: type ?? 3,
                                    data: selectText ?? ''
                                },
                                checked: true
                            }
                        ];
                        handleUpdateStep(newCurrentStep);
                    }
                }]
            }
        />
    );
};

export default ResponseBody;