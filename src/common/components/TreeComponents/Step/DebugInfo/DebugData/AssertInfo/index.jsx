import {isEmpty} from 'lodash';
import {Tag} from 'antd';
import NoContent from 'COMMON/components/common/NoContent';
import AssertResultItem from './AssertResultItem';
import styles from './AssertInfo.module.less';

const getResultText = (result) => {
    switch (result) {
        case 0:
            return {
                txt: '断言通过',
                color: 'success'
            };
        case 1:
            return {
                txt: '断言不通过',
                color: 'error'
            };
        case 2:
            return {
                txt: '断言异常',
                color: 'warning'
            };
        default:
            return {
                txt: '未知状态',
                color: 'default'
            };
    }
};

const Assertion = ({data}) => {
    const resultText = getResultText(data?.result);

    return (
        <div className={styles.card}>
            {
                isEmpty(data?.assertResult) ?
                    <NoContent className={styles.noContent} text='暂无断言' /> :
                    <>
                        <Tag
                            className={styles.result}
                            color={resultText?.color}
                        >
                            {resultText?.txt}
                        </Tag>
                        <span style={{fontSize: 12, color: '#777'}}>
                            ({data?.assertResult?.filter(item => item?.assertResult?.isPass === 0).length} /
                            {data?.assertResult?.length})
                        </span>
                        <div className={styles.content}>
                            {
                                data?.assertResult?.map((item, index) => (
                                    <AssertResultItem key={String(index)} data={item} />
                                ))
                            }
                        </div>
                    </>
            }

        </div>
    );
};

export default Assertion;
