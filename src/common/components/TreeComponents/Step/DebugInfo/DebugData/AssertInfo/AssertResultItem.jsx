import {Tag} from 'antd';
import {useState} from 'react';
import ResponseBody from '../ResponseInfo/components/body';
import styles from './AssertInfo.module.less';

// 获取断言类型文本
const getAssertionTypeText = (assertType) => {
    switch (assertType) {
        case 1:
            return 'Response JSON';
        case 2:
            return 'Response Text';
        case 3:
            return 'Response Schema';
        default:
            return '未知断言类型';
    }
};

// 获取操作符文本
const getOperatorText = (type) => {
    switch (type) {
        case 1:
            return '包含';
        case 2:
            return '正则';
        case 3:
            return '等于';
        case 4:
            return '大于';
        case 5:
            return '小于';
        case 6:
            return '大于等于';
        case 7:
            return '小于等于';
        case 8:
            return '不等于';
        case 9:
            return '存在jsonPath';
        default:
            return '未知操作';
    }
};

// 获取jsonPath数据
const getJSONPathData = (jsonPath, assertedContent) => {
    let content = JSON.parse(assertedContent ?? '');
    let paths = jsonPath.split('.');
    // 移除第一个元素，因为第一个元素是根节点
    paths.shift();
    let data = content;
    for (let path of paths) {
        data = data?.[path];
    }
    return data ?? '未知';
};

const AssertResultItem = ({data}) => {
    const [showDetail, setShowDetail] = useState(false);
    return (
        <div className={styles.resultItem}>
            <div className={styles.assertTag}>
                <Tag className={styles.assertType}>
                    {getAssertionTypeText(data?.assertType)}
                </Tag>
                <Tag color={data?.assertResult?.isPass === 1 ? 'error' : 'success'}>
                    {data?.assertResult?.isPass === 1 ? '失败' : '成功'}
                </Tag>
            </div>
            <div className={styles.assertContent}>
                <div className={styles.assertTitle}>
                    预期内容:&nbsp;&nbsp;
                    {data?.assertResult?.jsonPath}
                    &nbsp;
                    {getOperatorText(data?.assertResult?.type)}
                    &nbsp;
                    {data?.assertResult?.data}
                </div>
                <div className={styles.assertRes}>
                    断言结果:&nbsp;&nbsp;
                    {getJSONPathData(data?.assertResult?.jsonPath, data?.assertResult?.assertedContent)}
                </div>
                <div className={styles.assertDetail}>
                    结果详情:&nbsp;&nbsp;
                    <span className={styles.assertDetailIcon} onClick={() => setShowDetail(!showDetail)}>
                        {!showDetail ? '展开' : '收起'}
                    </span>
                    {
                        showDetail &&
                        <ResponseBody
                            height={200}
                            data={data?.assertResult?.assertedContent ?? ''}
                        />
                    }
                </div>
            </div>
        </div>
    );
};

export default AssertResultItem;
