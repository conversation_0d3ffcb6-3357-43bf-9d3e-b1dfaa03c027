@import '../common.module.less';

.content {
    margin-top: 10px;
}

.resultItem {
    position: relative;
    width: 100%;
    padding: 5px 10px;
    margin-bottom: 5px;
    background-color: #f9fafb;
    border-radius: 5px;
    overflow: hidden;
}

.assertTitle,
.assertType,
.assertContent {
    font-size: 12px;
}

.assertTag {
    position: absolute;
    right: 0;
    top: 5px;
}

.assertType {
    width: 110px;
    text-align: center;
}

.assertInfo {
    float: left;
    width: 50px;
}

.assertContent {
    float: left;
    width: 100%;
}

.assertRes {
    font-weight: bold;
}

.assertDetailIcon {
    cursor: pointer;
    color: var(--primary-color);
    text-decoration: underline;
}