import React, { useState } from 'react';
import { Collapse, Checkbox, Space, Dropdown, Menu } from 'antd';
import {
    HolderOutlined,
    EllipsisOutlined,
    RightOutlined,
    DownOutlined,
    CopyOutlined,
    DeleteOutlined
} from '@ant-design/icons';
import styles from './CollapsePanelItem.module.less';

const { Panel } = Collapse;

const CollapsePanelItem = ({
    item,
    title,
    checked = false,
    onCheckChange,
    onCopy,
    onRemove,
    editType = 'edit',
    children,
    className
}) => {
    const [isActive, setIsActive] = useState(false);

    const dropdownMenu = (
        <Menu>
            <Menu.Item
                key="copy"
                icon={<CopyOutlined />}
                onClick={(e) => {
                    e.domEvent.stopPropagation();
                    onCopy && onCopy(item.id);
                }}
            >
                复制
            </Menu.Item>
            <Menu.Item
                key="delete"
                icon={<DeleteOutlined />}
                danger
                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                onClick={(e) => {
                    e.domEvent.stopPropagation();
                    onRemove && onRemove(item.id);
                }}
            >
                删除
            </Menu.Item>
        </Menu>
    );

    return (
        <div className={`${styles.panelItem} ${className || ''}`}>
            <Collapse
                style={{ borderRadius: '4px' }}
                bordered={false}
                collapsible="header"
                size="small"
                className={styles.customCollapse}
                ghost
                expandIcon={({ isActive }) => {
                    setIsActive(isActive);
                    return <HolderOutlined />;
                }}
            >
                <Panel
                    header={
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                            <Checkbox
                                checked={checked}
                                onChange={(e) => onCheckChange && onCheckChange(e.target.checked)}
                                onClick={(e) => e.stopPropagation()}
                                disabled={['readonly', 'debug', 'execute'].includes(editType)}
                                style={{ marginRight: '8px' }}
                            />
                            <span>{title}</span>
                        </div>
                    }
                    className={styles.customPanel}
                    key={item.id}
                    extra={
                        <Space>
                            <Dropdown
                                overlay={dropdownMenu}
                                trigger={['hover']}
                                placement="bottomRight"
                            >
                                <EllipsisOutlined
                                    style={{ cursor: 'pointer' }}
                                    onClick={(e) => e.stopPropagation()}
                                />
                            </Dropdown>
                            {isActive ? <DownOutlined /> : <RightOutlined />}
                        </Space>
                    }
                >
                    {children}
                </Panel>
            </Collapse>
        </div>
    );
};

export default CollapsePanelItem;
