import KuIcon from './ku.png';
import styles from './IcafeIcon.module.less';

function IcafeIcon({node}) {
    const {nodeType, icafeCardType} = node;
    
    let urlType = 'Story';
    if (nodeType === 2) {
        urlType = 'Ku';
    } else if (icafeCardType === 'Feature') {
        urlType = 'Feature';
    } else if (icafeCardType === 'Ku') {
        urlType = 'Ku';
    }
    switch (urlType) {
        case 'Feature':
            return <span className={styles.featureIcon}># Feature</span>
        case 'Story':
            return <span className={styles.storyIcon}># Story</span>
        case 'Ku':
            return <img src={KuIcon} className={styles.kuIcon} width={14} height={14} />
        default:
            break;
    }
};

export default IcafeIcon;