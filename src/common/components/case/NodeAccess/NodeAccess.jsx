import classnames from 'classnames';
import styles from './NodeAccess.module.less';

function NodeAccess(props) {
    const {node} = props;
    const getParams = (isAccess) => {
        let type = '';
        let className = '';
        switch (isAccess) {
            case true:
                type = '准';
                className = styles.accessNode;
                break;
            default:
                break;
        }
        return {type, className};
    };

    const {type, className} = getParams(node?.isAccess ?? node?.extra?.isAccess);

    return (
        <>
            {
                type !== '' &&
                <div className={classnames(styles.defaultNode, className)}>{type}</div>
            }
        </>
    );
}

export default NodeAccess;
