import { Tooltip } from 'antd';
import android from 'RESOURCES/img/android.png';
import apple from 'RESOURCES/img/apple.png';
import server from 'RESOURCES/img/server.png';
import web from 'RESOURCES/img/web.png';
import harmonyos from 'RESOURCES/img/harmonyos.png';
import styles from './NodeOsTag.module.less';

function OsIcon({ icon, alt }) {
    return (
        <div className={styles.osIconWrapper}>
            <img src={icon} alt={alt} className={styles.osIcon} />
        </div>
    );
}

function NodeOsTag(props) {
    // 只负责渲染端类型icon，不涉及数据逻辑
    const { node } = props;

    const getOsTag = (osTag) => {
        switch (osTag) {
            case 1:
                return { title: 'Android', icon: <OsIcon icon={android} alt="Android" /> };
            case 2:
                return { title: 'iOS', icon: <OsIcon icon={apple} alt="iOS" /> };
            case 4:
                return { title: '服务端', icon: <OsIcon icon={server} alt="服务端" /> };
            case 5:
                return { title: 'Web 端', icon: <OsIcon icon={web} alt="Web 端" /> };
            case 6:
                return {
                    title: 'HarmonyOS 端',
                    icon: <OsIcon icon={harmonyos} alt="HarmonyOS 端" />
                };
        }
    };

    return (
        <>
            {/* 端类型标签图标 */}
            {node?.extra?.osTypeWhiteList &&
                node?.extra?.osTypeWhiteList.map((osTagItem, index) => (
                    <Tooltip key={index} title={getOsTag(osTagItem)?.title}>
                        <div className={styles.disableIcon}>{getOsTag(osTagItem)?.icon}</div>
                    </Tooltip>
                ))}
        </>
    );
}

export default NodeOsTag;