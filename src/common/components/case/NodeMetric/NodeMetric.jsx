import {useEffect, useState} from 'react';
import {Tooltip} from 'antd';
import {FolderOutlined, StopOutlined} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import styles from './NodeMetric.module.less';

function NodeMetric(props) {
    const {node} = props;
    return (
        <Tooltip title={`用例数 ${node?.childMetric?.leaveNum}`}>
            <span className={styles.nodeMetric}>({node?.childMetric?.leaveNum})</span>
        </Tooltip>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    showModal: state.common.base.showModal,
    caseConfig: state.common.case.caseConfig,
    treeData: state.common.case.treeData,
    currentSteps: state.common.case.currentSteps
}))(NodeMetric);
