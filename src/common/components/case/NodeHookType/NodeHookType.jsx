import { useSelector } from 'react-redux';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import styles from './NodeHookType.module.less';
import { Tag } from 'antd';
const getHookId = (node, hookType, curOsType) => {
    return node?.extra?.[hookType]?.[convertOsTypeToType(curOsType)];
};
const NodeHookType = (props) => {
    const { node, curOsType } = props;
    return (
        <>
            {getHookId(node, 'setupInfo', curOsType) && <div className={styles.setup}>S</div>}
            {getHookId(node, 'teardownInfo', curOsType) && <div className={styles.teardown}>T</div>}
        </>
    );
};

export default NodeHookType;
