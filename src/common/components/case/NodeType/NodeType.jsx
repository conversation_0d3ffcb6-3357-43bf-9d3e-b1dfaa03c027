import {useEffect, useState} from 'react';
import {Tooltip} from 'antd';
import {FolderOutlined, StopOutlined} from '@ant-design/icons';
import {connectModel} from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import classnames from 'classnames';
import {convertOsTypeToType} from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import baseModel from 'COMMON/models/baseModel';
import {getQueryParams} from 'COMMON/utils/utils';
import styles from './NodeType.module.less';

function NodeType(props) {
    const {node, curOsType, treeData, setTreeData} = props;
    const query = getQueryParams();
    const getParams = (nodeType) => {
        let type = '';
        let className = '';
        switch (nodeType) {
            case 1:
                type = '人工';
                className = styles.manualNode;
                break;
            case 2:
                type = '自动';
                className = styles.autoNode;
                break;
            // case 3:
            //     type = '生成';
            //     className = styles.generateNode;
            //     break;
            case 4:
                type = '复验';
                className = styles.reviewNode;
                break;
            default:
                break;
        }
        return {type, className};
    };

    const {type, className} = getParams(
        node?.executionType ?? node?.extra?.executionType[convertOsTypeToType(curOsType)]
    );
    const stepList = node?.extra?.stepInfo?.[convertOsTypeToType(curOsType)] ?? [];

    return (
        <>
            {type !== '' && curOsType !== 4 &&
                <div className={classnames(styles.defaultNode, className)}>{type}</div>}
            {curOsType === 4 && stepList?.length > 0 &&
                <span className={styles.stepCount}>({stepList?.length})</span>}
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    showModal: state.common.base.showModal,
    caseConfig: state.common.case.caseConfig,
    treeData: state.common.case.treeData,
    currentSteps: state.common.case.currentSteps
}))(NodeType);
