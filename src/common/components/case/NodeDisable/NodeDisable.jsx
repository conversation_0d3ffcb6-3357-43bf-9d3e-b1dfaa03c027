import { useEffect, useState } from 'react';
import { Tooltip } from 'antd';
import { FolderOutlined, StopOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import classnames from 'classnames';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import baseModel from 'COMMON/models/baseModel';
import { getQueryParams } from 'COMMON/utils/utils';
import styles from './NodeDisable.module.less';

function NodeDisable(props) {
    const { node, curOsType, treeData, setTreeData } = props;

    const getDisableInfo = (disabledInfo) => {
        switch (disabledInfo?.[convertOsTypeToType(curOsType)]?.type ?? disabledInfo?.type) {
            case 2:
                return '该用例仅巡检禁用，无法被执行';
            default:
                return '该用例已禁用，无法被执行';
        }
    };

    return (
        <>
            {/* 禁用图标 */}
            {(node?.extra?.disabledInfo?.[convertOsTypeToType(curOsType)]?.status ??
                node?.extra?.disabled) && (
                <Tooltip title={getDisableInfo(node?.extra?.disabledInfo)}>
                    <div className={styles.disableIcon}>
                        <StopOutlined />
                    </div>
                </Tooltip>
            )}
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    showModal: state.common.base.showModal,
    caseConfig: state.common.case.caseConfig,
    treeData: state.common.case.treeData,
    currentSteps: state.common.case.currentSteps
}))(NodeDisable);
