import classnames from 'classnames';
import styles from './NodePriority.module.less';

function NodePriority(props) {
    const {node} = props;
    const getParams = (priority) => {
        let type = '';
        let className = '';
        switch (priority) {
            case 0:
                type = 'P0';
                className = styles.p0Node;
                break;
            case 1:
                type = 'P1';
                className = styles.p1Node;
                break;
            case 2:
                type = 'P2';
                className = styles.p2Node;
                break;
            default:
                break;
        }
        return {type, className};
    };

    const {type, className} = getParams(node?.priority ?? node?.extra?.priority);

    return (
        <>
            {
                type !== '' &&
                <div className={classnames(styles.defaultNode, className)}>{type}</div>
            }
        </>
    );
}

export default NodePriority;
