import {Form, Tag, Select} from 'antd';

function CloudDevice({cloudDeviceList, form, disabled = false, name = "cloudDevice", mode = 'multiple'}) {
    return (
        <Form.Item
            label='执行设备池'
            name={name}
            rules={
                [
                    {
                        required: true,
                        message: '请选择设备池',
                    },
                ]}
        >
            <Select
                mode={mode}
                style={{width: '100%'}}
                disabled={disabled}
                optionLabelProp="label"
                placeholder='请选择设备池'
                allowClear
            >
                {
                    cloudDeviceList.map((item) => {
                        return (
                            <Select.Option
                                key={`poolId_${item.poolId}`}
                                value={+item.poolId}
                                label={item.poolName ?? '未知'}
                            >
                                <span>
                                    <Tag>ID: {item.poolId}</Tag>
                                    {item.poolName ?? '未知'}
                                </span>
                            </Select.Option>
                        );
                    })
                }
            </Select>
        </Form.Item>
    );
};

export default CloudDevice;
