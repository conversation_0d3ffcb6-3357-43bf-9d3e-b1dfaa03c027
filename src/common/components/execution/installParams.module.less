
// 垂直布局样式
.verticalContainer {
    width: 100%;
    gap: 8px;
    margin-bottom: 0px;

    .removeButton {
        position: absolute;
        top: 9px;
        right: 0px;
    }
    
    .installParamsWrapper {
        margin-bottom: 60px;
    }
    .packageName {
        width: 50%;
        margin-bottom: 0;
    }

    .fileLink {
        width: 50%;
        margin-bottom: 0;
    }

    .name {
        width: 50%;
        margin-bottom: 0;
    }

    .password {
        width: 50%;
        margin-bottom: 0;
    }
}

// 紧凑布局样式
.compactContainer {
    width: 100%;

    // gap: 8px;
    // margin-bottom: 4px;
    .removeButton {
        position: absolute;
        top: 9px;
        right: -20px;
    }
    
    .installParamsWrapper {
        margin-bottom: 60px;
    }
    .packageName {
        width: 40%;
    }

    .fileLink {
        width: 60%;
    }

    .name {
        width: 40%;
    }

    .password {
        width: 60%;
    }
}

// 通用样式

.formItemContainer {
    position: relative;
    row-gap: 4px;
    .removeButton {
        position: absolute;
        top: 9px;
        right: -20px;
    }

}

.installParamsWrapper {
    margin-bottom: 60px;
}