import { Form, Flex, Button, Input } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import styles from './installParams.module.less';

export default ({
    form,
    disabled = false,
    maxCount,
    fieldName = 'installParams', // 表单字段名称，默认为 'installParams'
    style,
    layout = 'compact' // 布局类型，'vertical' 表示垂直布局（每行显示两个字段），'compact' 表示紧凑模式，默认为水平布局
}) => {


    const packageNameValidator = async (_, value, name, fieldName) => {
        if (!value) {
            return Promise.reject('填写包名');
        }
        let data = form.getFieldValue(fieldName);
        let sameIndex = data.findIndex((item) => item.packageName === value);
        if (sameIndex !== -1 && sameIndex !== name) {
            return Promise.reject('已存在相同包名');
        }
    };

    // 渲染表单项
    const renderFormItems = (name, restField, fieldName) => {

            return (
                <>
                    <Flex
                        className={`container, ${styles[`${layout}Container`]}`}
                    >
                        <Form.Item
                            {...restField}
                            className={styles.packageName}
                            name={[name, 'packageName']}
                            validateTrigger={['onBlur']}
                            rules={[
                                {
                                    validator: (_, value) =>
                                        packageNameValidator(_, value, name, fieldName)
                                }
                            ]}
                        >
                            <Input addonBefore="包名" placeholder="填写包名，com.xxx.xxx" />
                        </Form.Item>
                        <Form.Item
                            {...restField}
                            className={styles.fileLink}
                            name={[name, 'fileLink']}
                            rules={[
                                {
                                    required: true,
                                    message: '填写下载链接'
                                }
                            ]}
                        >
                            <Input
                                addonBefore={layout === 'vertical' ? '链接' : ''}
                                placeholder="填写下载链接"
                            />
                        </Form.Item>
                    </Flex>
                    <Flex className={styles[`${layout}Container`]}>
                        <Form.Item
                            {...restField}
                            className={styles.name}
                            name={[name, 'name']}
                        >
                            <Input
                                addonBefore="账号"
                                placeholder="账号选填，必须要 passport 风控豁免，不支持电话号码"
                            />
                        </Form.Item>
                        <Form.Item
                            {...restField}
                            className={styles.password}
                            name={[name, 'password']}
                        >
                            <Input addonBefore="密码" placeholder="密码选填" />
                        </Form.Item>
                    </Flex>
                </>
            );
    };

    return (
        <Form.Item label="APP 安装" style={style} className={styles.installParamsWrapper}>
            <Form.List name={fieldName}>
                {(fields, { add, remove }) => (
                    <>
                        {fields.map(({ key, name, ...restField }) => (
                            <Flex
                                key={key}
                                vertical={layout === 'vertical'}
                                justify="space-between"
                                align="center"
                                className={styles.formItemContainer}
                                gap={layout === 'vertical' ? 4 : 0}
                            >
                                {renderFormItems(name, restField, fieldName)}
                                {!disabled && (
                                    <MinusCircleOutlined
                                        onClick={() => remove(name)}
                                        className={styles.removeButton}
                                    />
                                )}
                            </Flex>
                        ))}
                        {!disabled && (!maxCount || fields.length < maxCount) && (
                            <Form.Item>
                                <Button
                                    type="dashed"
                                    onClick={() => add()}
                                    block
                                    icon={<PlusOutlined />}
                                >
                                    新增
                                </Button>
                            </Form.Item>
                        )}
                    </>
                )}
            </Form.List>
        </Form.Item>
    );
};
