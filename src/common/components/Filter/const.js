export const FILTER_INFO = {
    // 优先级: P0 / P1 / P2
    priority: {
        name: '等级',
        element: 'Select',
        type: 'priority',
        option: [
            { key: 'level0', value: 0, label: 'P0' },
            { key: 'level1', value: 1, label: 'P1' },
            { key: 'level2', value: 2, label: 'P2' }
        ]
    },
    // 是否准入
    access: {
        name: '准入',
        element: 'Select',
        type: 'access',
        option: [
            { key: 'access', value: 1, label: '准入' },
            { key: 'noAccess', value: 0, label: '非准入' }
        ]
    },
    // 用例类型（根据步骤列表判断）: 人工 / 半自动 / 自动
    executeType: {
        name: '类型',
        element: 'Select',
        type: 'executeType',
        option: [
            { key: 'executeType_1', value: 1, label: '人工' },
            { key: 'executeType_2', value: 2, label: '自动' },
            { key: 'executeType_4', value: 4, label: '半自动' }
        ]
    },
    // 自动化执行状态: 执行中 / 测试通过 / 测试不通过 / 执行异常 / 待复验 / 已取消
    autoStatus: {
        name: '执行',
        element: 'Select',
        type: 'autoStatus',
        option: [
            { key: 'auto_status_1', value: 1, label: '执行中' },
            { key: 'auto_status_2', value: 2, label: '测试通过' },
            { key: 'auto_status_3', value: 3, label: '测试不通过' },
            { key: 'auto_status_4', value: 4, label: '执行异常' },
            { key: 'auto_status_5', value: 5, label: '待复验' },
            { key: 'auto_status_6', value: 6, label: '已取消' }
        ]
    },
    // 签章结果: 成功 / 失败 / 阻塞 / 跳过 / 待测 / 禁用
    stampResult: {
        name: '签章',
        element: 'Select',
        type: 'stampResult',
        option: [
            { key: 'stamp_result_1', value: 1, label: '成功' },
            { key: 'stamp_result_2', value: 2, label: '失败' },
            { key: 'stamp_result_3', value: 3, label: '阻塞' },
            { key: 'stamp_result_4', value: 4, label: '跳过' },
            { key: 'stamp_result_0', value: 0, label: '待测' },
            { key: 'stamp_result_6', value: 6, label: '禁用' }
        ]
    },
    // 是否录制精准函数
    record: {
        name: '录制',
        element: 'Select',
        type: 'record',
        option: [
            { key: 'record_1', value: 1, label: '已录制' },
            { key: 'record_0', value: 0, label: '未录制' }
        ]
    },
    // 是否绑定链接
    link: {
        name: '链接',
        element: 'Select',
        type: 'link',
        option: [
            { key: 'link_1', value: 1, label: '已绑定' },
            { key: 'link_0', value: 0, label: '未绑定' }
        ]
    },
    // 标签
    tag: {
        name: '标签',
        element: 'Select',
        type: 'tab',
        option: [] // 选项（动态变更）
    },
    // 当前轮次
    curRound: {
        name: '当前轮次',
        element: 'CheckBox',
        type: 'curRound'
    },
    // 关联平台
    relation: {
        name: '平台',
        element: 'Select',
        type: 'relation',
        option: [] // 选项（动态变更）
    },
    // 禁用
    disabled: {
        name: '禁用',
        element: 'Select',
        type: 'disabled',
        option: [
            { key: 'disabled_1', value: 1, label: '已禁用' },
            { key: 'disabled_0', value: 0, label: '未禁用' }
        ]
    },
    creator: {
        name: '节点更新人',
        element: 'UserSelect',
        type: 'creator',
        placeholder: '请输入, 如：lishuang30',
        option: [] // 选项（动态变更）
    }
};
