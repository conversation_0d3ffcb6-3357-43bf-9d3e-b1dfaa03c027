@import "RESOURCES/css/common.less";

.header {
    height: 20px;
    line-height: 20px;
   
    .title {
        font-weight: bold;
        color: #383838;
        font-size: 14px;
    }

    .group {
        float: right;
        font-size: 12px;
    }

    .query,
    .clear {
        margin-left: 10px;
        color: #777;
    }

    .clear:hover {
        color: var(--primary-color);
    }

    .query {
        background-color: var(--primary-color);
        padding: 2px 5px;
        border-radius: 3px;
        color: #fff;
        border: 1px solid var(--primary-color);
    }
}

.container {
    width: 400px;
    padding: 5px 0;
    margin-top: 5px;
    overflow: hidden;
    border-top: 1px solid var(--border-color);

    .filterItem {
        float: left;
        // margin: 3px 0;
        padding: 0 10px;
        width: 50%;
    }

    .filterTitle {
        display: inline-block;
        padding: 5px 0 2px 0;
        font-size: 13px;
    }

    .filterSelect,
    .filterInput,
    .filterCheckbox {
        width: 100%;
    }
}


// tag
.tagIcon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 10px;
}

.tagName {
    margin-left: 15px;
}

.tagCreator {
    margin-left: 5px;
    font-size: 10px;
    color: #bfbfbf;
}