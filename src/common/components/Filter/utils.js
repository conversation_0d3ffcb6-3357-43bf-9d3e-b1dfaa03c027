import { isEmpty } from 'lodash';
import { stringify } from 'query-string';

/**
 *  从url中获取query参数，更新filterData
 * @param {*} filterData
 * @param {*} urlQuery
 * @returns
 */
export const updateFilterDataFromUrl = (filterData, urlQuery) => {
    const newFilterData = { ...filterData };
    Object.keys(urlQuery).forEach((key) => {
        // 用来匹配filters[key]或filters[key]!!
        const filterMatch = key.match(/^filters\[([^\]]+)\](!!)?$/);
        if (filterMatch) {
            const filterKey = filterMatch[1]; // 键名
            const isNoBelong = !!filterMatch[2]; // 是否有!!
            // filterData是否存在该key
            if (newFilterData[filterKey]) {
                // 把字符串拆分为数组
                const value = urlQuery[key];
                const data = value
                    ? value.split(',').map((num) => {
                          return isNaN(+num) ? num : +num;
                      })
                    : [];
                newFilterData[filterKey] = {
                    activeKey: isNoBelong ? 'noBelong' : 'belong',
                    data
                };
            }
        }
    });
    return newFilterData;
};

/**
 * 根据筛选条件获取query参数
 * @param {*} filterData
 * @param {*} query
 * @returns
 */

export const getQueryStringFromFilters = (filterData, options = {}) => {
    let queryParams = {};

    Object.keys(filterData).forEach((key) => {
        const { activeKey, data } = filterData[key];
        if (isEmpty(data)) {
            return;
        }
        const queryKey = `filters[${key}]${activeKey === 'noBelong' ? '!!' : ''}`;
        queryParams[queryKey] = data.join(',');
    });

    queryParams = {
        ...queryParams,
        ...options
    };

    return stringify(queryParams, { encode: false });
};
