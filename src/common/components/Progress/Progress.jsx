import {Tooltip} from 'antd';
import styles from './Progress.module.less';

function Progress({caseProgress = {
    totalNum: 0,
    skipNum: 0,
    blockNum: 0,
    passNum: 0,
    failNum: 0,
    errorNum: 0,
    checkNum: 0,
    disabledNum: 0,
    cancelNum: 0,
}}) {
    const totalNum = caseProgress?.totalNum ?? 0;
    const skipNum = caseProgress?.skipNum ?? 0; // 跳过 -> 成功
    const blockNum = caseProgress?.blockNum ?? 0; // 阻塞 -> 失败
    const passNum = caseProgress?.passNum ?? 0; // 成功
    const failNum = caseProgress?.failNum ?? 0; // 失败
    const errorNum = caseProgress?.errorNum ?? 0; // 执行异常 -> 失败
    const checkNum = caseProgress?.checkNum ?? 0; // 待复验 -> 待测
    const disabledNum = caseProgress?.disabledNum ?? 0; // 禁用
    // const cancelNum = caseProgress?.cancelNum ?? 0; // 取消 -> 待测
    const waitNum = totalNum - skipNum - blockNum - passNum - failNum - disabledNum; // 待测

    const realWaitNum = waitNum;
    const realBlockNum = blockNum;
    const realCheckNum = checkNum;
    const realSkipNum = skipNum;
    const realErrorNum = errorNum;
    const realPassNum = passNum;
    const realFailNum = failNum;
    const realDisabledNum = disabledNum;
    const waitPer = totalNum === 0 ? 0 : realWaitNum / totalNum;
    const passPer = totalNum === 0 ? 0 : realPassNum / totalNum;
    const failPer = totalNum === 0 ? 0 : realFailNum / totalNum;
    const skipPer = totalNum === 0 ? 0 : realSkipNum / totalNum;
    const blockPer = totalNum === 0 ? 0 : realBlockNum / totalNum;
    const disabledPer = totalNum === 0 ? 0 : realDisabledNum / totalNum;
    return (
        <div className={styles.progressModule}>
            {
                totalNum === 0 ?
                    <div className={styles.progress}>
                        <Tooltip title={'待测 0'}>
                            <div style={{width: `${(1 - passPer) * 100}%`, background: '#48bc19'}}></div>
                        </Tooltip>
                    </div> :
                    <div className={styles.progress}>
                        {0 !== realPassNum && <Tooltip title={`成功 ${realPassNum}/${totalNum}`}>
                            <div style={{width: `${passPer * 100}%`, background: '#48bc19'}}></div>
                        </Tooltip>}
                        {0 !== realSkipNum && <Tooltip title={`跳过 ${realSkipNum}/${totalNum}`}>
                            <div style={{width: `${skipPer * 100}%`, background: '#f1c40f'}}></div>
                        </Tooltip>}
                        {0 !== realFailNum && <Tooltip title={`失败 ${realFailNum}/${totalNum}`}>
                            <div style={{width: `${failPer * 100}%`, background: '#f41f28'}}></div>
                        </Tooltip>}
                        {0 !== realBlockNum && <Tooltip title={`阻塞 ${realBlockNum}/${totalNum}`}>
                            <div style={{width: `${blockPer * 100}%`, background: '#BC6B31'}}></div>
                        </Tooltip>}
                        {0 !== realDisabledNum && <Tooltip title={`禁用 ${realDisabledNum}/${totalNum}`}>
                            <div style={{width: `${disabledPer * 100}%`, background: '#333333'}}></div>
                        </Tooltip>}
                        {(0 !== realWaitNum) && <Tooltip
                            title={<span>
                                <div>待测 {realWaitNum}/{totalNum}</div>
                                <div>其中：待复验 {realCheckNum}/{totalNum}&nbsp;执行异常 {realErrorNum}/{totalNum}</div>
                            </span>}
                        >
                            <div style={{width: `${waitPer * 100}%`, background: '#eee'}}></div>
                        </Tooltip>}
                    </div>
            }
            <Tooltip
                title={
                    totalNum === 0 || waitNum === totalNum ? '' :
                        `${0 !== realPassNum ? `成功 ${realPassNum}/${totalNum}` : ''}
                    ${0 !== realSkipNum ? `跳过 ${realSkipNum}/${totalNum}` : ''}
                    ${0 !== realFailNum ? `失败 ${realFailNum}/${totalNum}` : ''}
                    ${0 !== realBlockNum ? `阻塞 ${realBlockNum}/${totalNum}` : ''}
                    ${0 !== realDisabledNum ? `禁用 ${realDisabledNum}/${totalNum}` : ''}
                `
                }
            >
                <div className={styles.progressRes}>
                    {
                        totalNum === 0 ? '100%' :
                            `${(((totalNum -
                                waitNum) / totalNum) * 100).toFixed(1)}%`
                    }
                </div>
            </Tooltip>
        </div>
    );
}

export default Progress;