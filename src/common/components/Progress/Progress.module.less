// progress
.progressModule {
    position: relative;
    display: flex;
    padding-left: 5px;
    cursor: pointer;
}

.progress {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 120px;
    display: flex;

    &>div {
        flex-shrink: 0;
        height: 10px;
        transition: all .3s ease-in-out;
    }
}

.progress div:first-child {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.progress div:last-child {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

.progressRes {
    position: absolute;
    left: 130px;
    color: #777;
    top: 50%;
    transform: translateY(-50%);
    font-size: 8px;
}