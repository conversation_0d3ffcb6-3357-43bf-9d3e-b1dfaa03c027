import { useRef, useEffect, useState } from 'react';
import { Tooltip } from 'antd';
import styles from './EllipsisTooltip.module.less';

// 如果文本长度超过maxWidth，则显示省略号，并添加Tooltip提示
const EllipsisTooltip = ({ text, maxWidth = 150, deufaultText = '未知', ...props }) => {
    const spanRef = useRef(null);
    const [isOverflow, setIsOverflow] = useState(false);
    // 监听text的变化，当text变化时，重新判断文本是否溢出
    useEffect(() => {
        const el = spanRef.current;
        if (el) {
            setIsOverflow(el.scrollWidth > el.clientWidth);
        }
    }, [text]);

    const content = (
        <span ref={spanRef} className={styles.ellipsisText} {...props} > 
            {text ?? deufaultText}
        </span>
    );

    // 文本溢出，则显示Tooltip提示
    return isOverflow ? <Tooltip title={text}>{content}</Tooltip> : content;
};

export default EllipsisTooltip;
