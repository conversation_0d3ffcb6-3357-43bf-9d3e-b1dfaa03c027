import { useEffect, useState } from 'react';
import { Checkbox, Divider, Select, Form, Input, message, Modal, Switch, Dropdown } from 'antd';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import { updateTreeDataHook } from 'FEATURES/front_qe_tools/case/edit/EditPage/CaseDetailView/LayoutSider/utils';
import { createSnippet, getSnippetList } from 'COMMON/api/front_qe_tools/config';
import {
    batchCopyStepList,
    createNodeHookStep,
    deleteSetupTeardownNode
} from 'COMMON/api/front_qe_tools/step';
import {
    createSystemStepInitConfig,
    handleCreateStep
} from 'COMMON/components/TreeComponents/Step/StepInit/utils';
import styles from './AddOperation.module.less';

const HOOK_TYPE = {
    1: 'setupInfo',
    2: 'teardownInfo'
};

function AddOperation(props) {
    const {
        showBatchStepSwitch,
        className,
        nodeId,
        curOsType,
        currentSpace,
        stepList,
        checkStepList,
        setCheckStepList,
        snippetList,
        setSnippetList,
        handleDeleteStep,
        handleUpdateStepList,
        updateTreeDataHook: updateTreeDataHookChange,
        onChange,
        treeData,
        setTreeData,
        currentNode,
        setCurrentNode
    } = props;
    const [saveStepListToTemplateModalOpen, setSaveStepListToTemplateModalOpen] = useState(false); // 保存步骤为测试片段弹窗是否打开
    const [checkAll, setCheckAll] = useState(false); // 是否全选步骤
    const [keyList, setKeyList] = useState([]); // 测试片段下拉列表
    const [createLoading, setCreateLoading] = useState(false); // 创建测试片段加载状态
    const [messageApi, contextHolder] = message.useMessage();
    const [addForm] = Form.useForm();
    // 处理添加 SETUP 或 TEARDOWN 节点
    const handleAddHook = async (hookType) => {
        try {
            const params = {
                caseNodeId: nodeId,
                osType: curOsType,
                hookType: hookType // 1-setup 2-teardown
            };

            const res = await createNodeHookStep(params);
            messageApi.success(`${hookType === 1 ? 'SETUP' : 'TEARDOWN'} 节点创建成功`);
            // 如果有更新树结构的回调，调用它
            if (updateTreeDataHookChange && typeof updateTreeDataHookChange === 'function') {
                updateTreeDataHookChange(hookType, res.caseNodeId);
            } else {
                if (setTreeData && typeof setTreeData === 'function') {
                    // 更新树结构
                    // 重新获取树数据或更新当前节点
                    updateTreeDataHook(
                        treeData,
                        hookType,
                        res.caseNodeId,
                        setTreeData,
                        nodeId,
                        curOsType,
                        setCurrentNode
                    );
                }
            }
        } catch (error) {}
    };
    const items = [
        {
            key: '1',
            disabled: (currentNode?.data || currentNode)?.extra?.setupInfo?.[
                convertOsTypeToType(curOsType)
            ],
            label: <span className={styles.downItem}>添加 SETUP </span>,
            onClick: () => handleAddHook(1)
        },
        {
            key: '2',
            disabled: (currentNode?.data || currentNode)?.extra?.teardownInfo?.[
                convertOsTypeToType(curOsType)
            ],
            label: <span className={styles.downItem}>添加 TEARDOWN</span>,
            onClick: () => handleAddHook(2)
        }
    ];
    useEffect(() => {
        setCheckAll(checkStepList.length === stepList.length);
    }, [checkStepList, stepList]);
    return (
        <>
            <div className={classnames(styles.stepListSwitch, className)}>
                {contextHolder}
                <Dropdown menu={{ items }} placement="bottomLeft" trigger={['click']}>
                    <span className={styles.operaTitle}>添加操作</span>
                </Dropdown>

                {/* <Switch
                    size="small"
                    checked={showBatchStepSwitch}
                    onChange={onChange}
                    tabIndex={-1}
                /> */}
            </div>
            {/* <div className={classnames(styles.stepListSwitch, className)}>
                {showBatchStepSwitch && <>
                    <span className={styles.operaCheckAllBtn}>
                        <Checkbox
                            checked={checkAll}
                            onChange={(e) => {
                                setCheckAll(e.target.checked);
                                onCheckAllStepList(e.target.checked);
                            }}
                        >
                            <span className={styles.operaCheckAllTitle}>全选</span>
                        </Checkbox>
                    </span>
                </>}
            </div> */}
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    snippetList: state.common.case.snippetList,
    treeData: state.common.case.treeData,
    currentNode: state.common.case.currentNode
}))(AddOperation);
