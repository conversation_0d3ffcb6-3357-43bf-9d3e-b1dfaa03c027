import React, {Component} from 'react';
import {Button} from 'antd';
import qamateLogo from 'RESOURCES/img/newIcon.png';
import styles from './ErrorBoundary.module.less';

// 定义一个错误边界组件，继承自 React.Component
class ErrorBoundary extends Component {
    // 构造函数，初始化状态
    constructor(props) {
        super(props);
        // 定义一个 state 变量 hasError，用于标记是否发生错误
        this.state = {hasError: false};
    }

    // 静态方法，当子组件抛出错误时会被调用
    static getDerivedStateFromError(error) {
        // 更新 state 中的 hasError 为 true，表示发生了错误
        return {hasError: true};
    }

    // 当错误发生时会调用这个方法，可以在这里进行错误日志记录等操作
    componentDidCatch(error, errorInfo) {
        // 这里可以添加代码将错误信息发送到服务器进行日志记录
        console.log('错误信息:', error);
        console.log('错误详情:', errorInfo);
    }

    // 渲染方法
    render() {
        // 如果 hasError 为 true，说明发生了错误，渲染备用的 UI
        if (this.state.hasError) {
            return (
                <div className={styles.container}>
                    <div className={styles.info}>
                        <img src={qamateLogo} width={150} alt="QAMate" />
                        <div className={styles.title}>
                            <span className={styles.desc}>
                                哎呀，这里好像出了点问题，请稍后再试！
                            </span>
                            <br />
                            <span>从前有座山，山里有座庙， 庙里有个问题，现在找不到.....</span>
                        </div>
                        <Button
                            className={styles.btn}
                            onClick={() => {
                                window.location.reload();
                            }}
                        >
                            返回
                        </Button>
                    </div>
                </div>
            );
        }
        // 如果没有错误，正常渲染子组件
        return this.props.children;
    }
}

export default ErrorBoundary;