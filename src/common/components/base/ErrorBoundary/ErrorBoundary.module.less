.container {
    width: 100vw;
    height: 100vh;
    background-color: #f6f6f6;
}

.info {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.title {
    margin: 30px 0 10px 0;
    text-align: center;
    color: #777;

    span {
        margin-left: 10px;
        font-size: 12px;
    }

    .desc {
        font-size: 16px;
        font-weight: bold;
    }
}

.btn {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}