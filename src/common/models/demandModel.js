import {get} from 'lodash';
import {createModel, presetReducer} from 'COMMON/middleware';

export default createModel({
    project: 'demand',
    namespace: 'doc',
    docList: {
        initialValue: [],
        asyncOptions: [
            {
                getDocList: (params, success, fail) => {
                    return {
                        url: '/core/doc/tree/query',
                        method: 'post',
                        params,
                        success,
                        fail,
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const docList = get(action, 'data.tree', []);
                        state.docList = docList;
                        return {...state};
                    },
                    presetReducer[2],
                ],
            },
        ],
        syncOptions: [
            {
                setDocList: (docList) => ({docList}),
                reducer: (state, action) => {
                    state.docList = action.params.docList;
                    return {...state};
                }
            }
        ]
    },
    currentDoc: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentDoc: (currentDoc) => ({currentDoc}),
                reducer: (state, action) => {
                    state.currentDoc = action.params.currentDoc;
                    return {...state};
                }
            }
        ]
    }
});