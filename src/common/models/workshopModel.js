import {get} from 'lodash';
import {createModel, presetReducer} from 'COMMON/middleware';

// const [startReducer, failReducer] = presetReducer;
export default createModel({
    project: 'workshop',
    namespace: 'common',
    currentWorkshopGroup: {
        initialValue: '1',
        syncOptions: [
            {
                setCurrentWorkshopGroup: (currentWorkshopGroup) => ({currentWorkshopGroup}),
                reducer: (state, action) => {
                    state.currentWorkshopGroup = action.params.currentWorkshopGroup;
                    return {...state};
                }
            }
        ]
    },
    // Lab 实验室
    promptList: {
        initialValue: [],
        syncOptions: [
            {
                setPromptList: (promptList) => ({promptList}),
                reducer: (state, action) => {
                    state.promptList = action.params.promptList;
                    return {...state};
                }
            }
        ]
    },
    currentPrompt: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentPrompt: (currentPrompt) => ({currentPrompt}),
                reducer: (state, action) => {
                    state.currentPrompt = action.params.currentPrompt;
                    return {...state};
                }
            }
        ]
    },
    promptInfo: {
        initialValue: {},
        syncOptions: [
            {
                setPromptInfo: (promptInfo) => ({promptInfo}),
                reducer: (state, action) => {
                    state.promptInfo = action.params.promptInfo;
                    return {...state};
                }
            }
        ]
    },
    // 业务知识
    knowledgeList: {
        initialValue: [],
        syncOptions: [
            {
                setKnowledgeList: (knowledgeList) => ({knowledgeList}),
                reducer: (state, action) => {
                    state.knowledgeList = action.params.knowledgeList;
                    return {...state};
                }
            }
        ]
    },
    currentKnowledge: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentKnowledge: (currentKnowledge) => ({currentKnowledge}),
                reducer: (state, action) => {
                    state.currentKnowledge = action.params.currentKnowledge;
                    return {...state};
                }
            }
        ]
    },
    curTrafficCaseInfo: {
        initialValue: {},
        syncOptions: [
            {
                setCurTrafficCaseInfo: (curTrafficCaseInfo) => ({curTrafficCaseInfo}),
                reducer: (state, action) => {
                    state.curTrafficCaseInfo = action.params.curTrafficCaseInfo;
                    return {...state};
                }
            }
        ]
    },
    curTrafficExeDetail: {
        initialValue: {},
        syncOptions: [
            {
                setCurTrafficExeDetail: (curTrafficExeDetail) => ({curTrafficExeDetail}),
                reducer: (state, action) => {
                    state.curTrafficExeDetail = action.params.curTrafficExeDetail;
                    return {...state};
                }
            }
        ]
    },
    curExeDetail: {
        initialValue: {},
        syncOptions: [
            {
                setCurExeDetail: (curExeDetail) => ({curExeDetail}),
                reducer: (state, action) => {
                    state.curExeDetail = action.params.curExeDetail;
                    return {...state};
                }
            }
        ]
    },
    trafficTaskTree: {
        initialValue: [],
        syncOptions: [
            {
                setTrafficTaskTree: (trafficTaskTree) => ({trafficTaskTree}),
                reducer: (state, action) => {
                    state.trafficTaskTree = action.params.trafficTaskTree;
                    return {...state};
                }
            }
        ]
    },
    
});
