import {get} from 'lodash';
import {createModel, presetReducer} from 'COMMON/middleware';

export default createModel({
    project: 'common',
    namespace: 'device',
    locationList: {
        initialValue: [],
        asyncOptions: [
            {
                getLocationList: (params, success, fail) => {
                    return {
                        url: '/core/device/location/list',
                        method: 'post',
                        params,
                        success,
                        fail,
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const locationList = get(action, 'data.locationList', []);
                        state.locationList = locationList;
                        return {...state};
                    },
                    presetReducer[2],
                ],
            },
        ],
        syncOptions: [
            {
                setLocationList: (locationList) => ({locationList}),
                reducer: (state, action) => {
                    state.locationList = action.params.locationList;
                    return {...state};
                }
            }
        ]
    },
    brandList: {
        initialValue: [],
        asyncOptions: [
            {
                getBrandList: (params, success, fail) => {
                    return {
                        url: '/core/device/model/list',
                        method: 'post',
                        params,
                        success,
                        fail,
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const brandList = get(action, 'data.brandList', []);
                        state.brandList = brandList;
                        return {...state};
                    },
                    presetReducer[2],
                ],
            },
        ],
        syncOptions: [
            {
                setDocList: (docList) => ({docList}),
                reducer: (state, action) => {
                    state.docList = action.params.docList;
                    return {...state};
                }
            }
        ]
    },
    cabinetList: {
        initialValue: [],
        asyncOptions: [
            {
                getCabinetList: (params, success, fail) => {
                    return {
                        url: '/core/device/cabinet/list',
                        method: 'post',
                        params,
                        success,
                        fail,
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const cabinetList = get(action, 'data.cabinetList', []);
                        state.cabinetList = [
                            {
                                cabinetId: 0,
                                cabinetName: '未分配机柜',
                            }, ...cabinetList];
                        return {...state};
                    },
                    presetReducer[2],
                ],
            },
        ],
        syncOptions: [
            {
                setCabinetList: (cabinetList) => ({cabinetList}),
                reducer: (state, action) => {
                    state.cabinetList = action.params.cabinetList;
                    return {...state};
                }
            }
        ]
    },
    poolList: {
        initialValue: [],
        asyncOptions: [
            {
                getPoolList: (params, success, fail) => {
                    return {
                        url: '/core/device/pool/list',
                        method: 'post',
                        params,
                        success,
                        fail,
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const poolList = get(action, 'data.poolList', []);
                        state.poolList = [
                            {
                                poolId: 0,
                                poolName: '待分配设备池',
                            },
                            ...poolList
                        ];
                        return {...state};
                    },
                    presetReducer[2],
                ],
            },
        ],
        syncOptions: [
            {
                setPoolList: (poolList) => ({poolList}),
                reducer: (state, action) => {
                    state.poolList = action.params.poolList;
                    return {...state};
                }
            }
        ]
    },
});