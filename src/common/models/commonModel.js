import { createModel, presetReducer } from 'COMMON/middleware';
import { get } from 'lodash';

// const BUSINESS_TYPE = {
//     1: [], // android
//     2: [], // ios
//     3: [], // common
//     4: [], // server
//     5: [], // web
//     6: [] // harmony
// }

export default createModel({
    project: 'common',
    namespace: 'case',
    // 单步调试
    testStep: {
        initialValue: null,
        syncOptions: [
            {
                setTestStep: (testStep) => ({ testStep }),
                reducer: (state, action) => {
                    state.testStep = action.params.testStep;
                    return { ...state };
                }
            }
        ]
    },
    // 服务端 debugId
    serverDebugId: {
        initialValue: '123123',
        syncOptions: [
            {
                setServerDebugId: (serverDebugId) => ({ serverDebugId }),
                reducer: (state, action) => {
                    state.serverDebugId = action.params.serverDebugId;
                    return { ...state };
                }
            }
        ]
    },
    // 服务端调试步骤
    serverDebugData: {
        initialValue: null,
        syncOptions: [
            {
                setServerDebugData: (serverDebugData) => ({ serverDebugData }),
                reducer: (state, action) => {
                    state.serverDebugData = action.params.serverDebugData;
                    return { ...state };
                }
            }
        ]
    },
    // 服务端测试结果
    serverTestRes: {
        initialValue: null,
        syncOptions: [
            {
                setServerTestRes: (serverTestRes) => ({ serverTestRes }),
                reducer: (state, action) => {
                    state.serverTestRes = action.params.serverTestRes;
                    return { ...state };
                }
            }
        ]
    },
    testRes: {
        initialValue: null,
        syncOptions: [
            {
                setTestRes: (testRes) => ({ testRes }),
                reducer: (state, action) => {
                    state.testRes = action.params.testRes;
                    return { ...state };
                }
            }
        ]
    },
    mockList: {
        initialValue: [],
        syncOptions: [
            {
                setMockList: (mockList) => ({ mockList }),
                reducer: (state, action) => {
                    state.mockList = action.params.mockList;
                    return { ...state };
                }
            }
        ]
    },
    //  当前生成任务状态
    isGenerateTask: {
        initialValue: null,
        syncOptions: [
            {
                setIsGenerateTask: (isGenerateTask) => ({ isGenerateTask }),
                reducer: (state, action) => {
                    state.isGenerateTask = action.params.isGenerateTask;
                    return { ...state };
                }
            }
        ]
    },
    //  控件建模当前记录ID
    recordId: {
        initialValue: null,
        syncOptions: [
            {
                setRecordId: (recordId) => ({ recordId }),
                reducer: (state, action) => {
                    state.recordId = action.params.recordId;
                    return { ...state };
                }
            }
        ]
    },
    // 控件是否系统建模开关
    pageSourceSwitch: {
        initialValue:
            localStorage.getItem('pageSourceSwitch') === null || localStorage.getItem('pageSourceSwitch') === 'true',
        syncOptions: [
            {
                setPageSourceSwitch: (pageSourceSwitch) => ({ pageSourceSwitch }),
                reducer: (state, action) => {
                    state.pageSourceSwitch = action.params.pageSourceSwitch;
                    return { ...state };
                }
            }
        ]
    },
    // 用例可读/可编辑等状态
    caseStatus: {
        initialValue: 'edit',
        syncOptions: [
            {
                setCaseStatus: (caseStatus) => ({ caseStatus }),
                reducer: (state, action) => {
                    state.caseStatus = action.params.caseStatus;
                    return { ...state };
                }
            }
        ]
    },
    // 用例集
    caseNode: {
        initialValue: null,
        syncOptions: [
            {
                setCaseNode: (caseNode) => ({ caseNode }),
                reducer: (state, action) => {
                    state.caseNode = action.params.caseNode;
                    return { ...state };
                }
            }
        ]
    },
    // 签章进度
    caseSignProgress: {
        initialValue: {},
        syncOptions: [
            {
                setCaseSignProgress: (caseSignProgress) => ({ caseSignProgress }),
                reducer: (state, action) => {
                    state.caseSignProgress = action.params.caseSignProgress;
                    return { ...state };
                }
            }
        ]
    },
    // 用例成员
    memberInfo: {
        initialValue: {},
        asyncOptions: [
            {
                getMemberInfo: (params, success, fail) => ({
                    url: '/core/case/member/info',
                    method: 'post',
                    params: params,
                    success,
                    fail
                }),
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const defaultConfig = get(action, 'data', []);
                        state.memberInfo = defaultConfig;
                        return { ...state };
                    },
                    presetReducer[2]
                ]
            }
        ],
        syncOptions: [
            {
                setMemberInfo: (memberInfo) => ({ memberInfo }),
                reducer: (state, action) => {
                    state.memberInfo = action.params.memberInfo;
                    return { ...state };
                }
            }
        ]
    },
    // 当前case的配置
    caseConfig: {
        initialValue: {},
        syncOptions: [
            {
                setCaseConfig: (caseConfig) => ({ caseConfig }),
                reducer: (state, action) => {
                    state.caseConfig = action.params.caseConfig;
                    return { ...state };
                }
            }
        ]
    },
    // 签章信息
    signStage: {
        initialValue: {},
        syncOptions: [
            {
                setSignStage: (signStage) => ({ signStage }),
                reducer: (state, action) => {
                    state.signStage = action.params.signStage;
                    return { ...state };
                }
            }
        ]
    },
    curOsType: {
        initialValue: null,
        syncOptions: [
            {
                setCurOsType: (curOsType) => ({ curOsType }),
                reducer: (state, action) => {
                    state.curOsType = action.params.curOsType;
                    return { ...state };
                }
            }
        ]
    },
    envList: {
        initialValue: {},
        syncOptions: [
            {
                setEnvList: (envList) => ({ envList }),
                reducer: (state, action) => {
                    state.envList = action.params.envList;
                    return { ...state };
                }
            }
        ]
    },
    personalServices: {
        initialValue: false,
        syncOptions: [
            {
                setPersonalServices: (personalServices) => ({ personalServices }),
                reducer: (state, action) => {
                    state.personalServices = action.params.personalServices;
                    return { ...state };
                }
            }
        ]
    },
    snippetList: {
        initialValue: {},
        syncOptions: [
            {
                setSnippetList: (snippetList) => ({ snippetList }),
                reducer: (state, action) => {
                    state.snippetList = action.params.snippetList;
                    return { ...state };
                }
            }
        ]
    },
    appList: {
        initialValue: {},
        syncOptions: [
            {
                setAppList: (appList) => ({ appList }),
                reducer: (state, action) => {
                    state.appList = action.params.appList;
                    return { ...state };
                }
            }
        ]
    },
    dbList: {
        initialValue: [],
        asyncOptions: [
            {
                getDbList: (params, success, fail) => {
                    return {
                        url: '/core/module/db/list',
                        method: 'post',
                        params,
                        success,
                        fail
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const dbList = get(action, 'data', []);
                        state.dbList = dbList;
                        return { ...state };
                    },
                    presetReducer[2]
                ]
            }
        ],
        syncOptions: [
            {
                setDbList: (dbList) => ({ dbList }),
                reducer: (state, action) => {
                    state.dbList = action.params.dbList;
                    return { ...state };
                }
            }
        ]
    },
    redisList: {
        initialValue: [],
        asyncOptions: [
            {
                getRedisList: (params, success, fail) => {
                    return {
                        url: '/core/module/redis/list',
                        method: 'post',
                        params,
                        success,
                        fail
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const redisList = get(action, 'data', []);
                        state.redisList = redisList;
                        return { ...state };
                    },
                    presetReducer[2]
                ]
            }
        ],
        syncOptions: [
            {
                setRedisList: (redisList) => ({ redisList }),
                reducer: (state, action) => {
                    state.redisList = action.params.redisList;
                    return { ...state };
                }
            }
        ]
    },
    schemeList: {
        initialValue: {},
        syncOptions: [
            {
                setSchemeList: (schemeList) => ({ schemeList }),
                reducer: (state, action) => {
                    state.schemeList = action.params.schemeList;
                    return { ...state };
                }
            }
        ]
    },
    serverList: {
        initialValue: [],
        asyncOptions: [
            {
                getServerList: (params, success, fail) => {
                    return {
                        url: '/core/module/server/list',
                        method: 'post',
                        params,
                        success,
                        fail
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const serverList = get(action, 'data.serverList', []);
                        state.serverList = serverList;
                        return { ...state };
                    },
                    presetReducer[2]
                ]
            }
        ],
        syncOptions: [
            {
                setServerList: (serverList) => ({ serverList }),
                reducer: (state, action) => {
                    state.serverList = action.params.serverList;
                    return { ...state };
                }
            }
        ]
    },
    paramList: {
        initialValue: [],
        asyncOptions: [
            {
                getParamList: (params, success, fail) => {
                    return {
                        url: '/core/module/params/list',
                        method: 'post',
                        params,
                        success,
                        fail
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const paramList = get(action, 'data.paramList', []);
                        state.paramList = paramList;
                        return { ...state };
                    },
                    presetReducer[2]
                ]
            }
        ],
        syncOptions: [
            {
                setParamList: (paramList) => ({ paramList }),
                reducer: (state, action) => {
                    state.paramList = action.params.paramList;
                    return { ...state };
                }
            }
        ]
    },
    requestDataList: {
        initialValue: [],
        syncOptions: [
            {
                setRequestDataList: (requestDataList) => ({ requestDataList }),
                reducer: (state, action) => {
                    state.requestDataList = action.params.requestDataList;
                    return { ...state };
                }
            }
        ]
    },
    currentRequestData: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentRequestData: (currentRequestData) => ({ currentRequestData }),
                reducer: (state, action) => {
                    state.currentRequestData = action.params.currentRequestData;
                    return { ...state };
                }
            }
        ]
    },
    requestDataRelationList: {
        initialValue: {
            status: 0, // 0 未获取 1 获取中 2 已获取
            data: {}
        },
        syncOptions: [
            {
                setRequestDataRelationList: (requestDataRelationList) => ({ requestDataRelationList }),
                reducer: (state, action) => {
                    state.requestDataRelationList = action.params.requestDataRelationList;
                    return { ...state };
                }
            }
        ]
    },
    caseTemplateList: {
        initialValue: [],
        asyncOptions: [
            {
                getCaseTemplateList: (params, success, fail) => {
                    return {
                        url: '/core/module/template/list',
                        method: 'post',
                        params: {
                            ...params
                        },
                        success,
                        fail
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const caseTemplateList = get(action, 'data.templateList', []);
                        state.caseTemplateList = caseTemplateList;
                        return { ...state };
                    },
                    presetReducer[2]
                ]
            }
        ],
        syncOptions: [
            {
                setCaseTemplateList: (caseTemplateList) => ({ caseTemplateList }),
                reducer: (state, action) => {
                    state.caseTemplateList = action.params.caseTemplateList;
                    return { ...state };
                }
            }
        ]
    },
    treeData: {
        initialValue: [],
        syncOptions: [
            {
                setTreeData: (treeData) => ({ treeData }),
                reducer: (state, action) => {
                    state.treeData = action.params.treeData;
                    return { ...state };
                }
            }
        ]
    },
    copyStep: {
        initialValue: null,
        syncOptions: [
            {
                setCopyStep: (copyStep) => ({ copyStep }),
                reducer: (state, action) => {
                    state.copyStep = action.params.copyStep;
                    return { ...state };
                }
            }
        ]
    },
    groupList: {
        initialValue: [],
        syncOptions: [
            {
                setGroupList: (groupList) => ({ groupList }),
                reducer: (state, action) => {
                    state.groupList = action.params.groupList;
                    return { ...state };
                }
            }
        ]
    },
    currentGroup: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentGroup: (plan) => ({ plan }),
                reducer: (state, action) => {
                    state.currentGroup = action.params.plan;
                    return { ...state };
                }
            }
        ]
    },
    treeNodeList: {
        initialValue: [],
        syncOptions: [
            {
                setTreeNodeList: (treeNodeList) => ({ treeNodeList }),
                reducer: (state, action) => {
                    state.treeNodeList = action.params.treeNodeList;
                    return { ...state };
                }
            }
        ]
    },
    // 当前case
    currentCase: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentCase: (currentCase) => ({ currentCase }),
                reducer: (state, action) => {
                    state.currentCase = action.params.currentCase;
                    return { ...state };
                }
            }
        ]
    },
    // 当前case 选中的step
    currentSteps: {
        initialValue: [],
        syncOptions: [
            {
                setCurrentSteps: (currentSteps) => ({ currentSteps }),
                reducer: (state, action) => {
                    state.currentSteps = action.params.currentSteps;
                    return { ...state };
                }
            }
        ]
    },
    // 当前case 复制的step
    selectSteps: {
        initialValue: null,
        syncOptions: [
            {
                setSelectSteps: (selectSteps) => ({ selectSteps }),
                reducer: (state, action) => {
                    state.selectSteps = action.params.selectSteps;
                    return { ...state };
                }
            }
        ]
    },
    /**
     * 当前case的精准记录状态
     */
    currentAccurateRecordStatus: {
        initialValue: false,
        syncOptions: [
            {
                setCurrentAccurateRecordStatus: (currentAccurateRecordStatus) => ({ currentAccurateRecordStatus }),
                reducer: (state, action) => {
                    state.currentAccurateRecordStatus = action.params.currentAccurateRecordStatus;
                    return { ...state };
                }
            }
        ]
    },
    /**
     * 录制中case
     */
    isRecordTask: {
        initialValue: false,
        syncOptions: [
            {
                setIsRecordTask: (isRecordTask) => ({ isRecordTask }),
                reducer: (state, action) => {
                    state.isRecordTask = action.params.isRecordTask;
                    return { ...state };
                }
            }
        ]
    },
    recordNodeList: {
        initialValue: [],
        syncOptions: [
            {
                setRecordNodeList: (recordNodeList) => ({ recordNodeList }),
                reducer: (state, action) => {
                    state.recordNodeList = action.params.recordNodeList;
                    return { ...state };
                }
            }
        ]
    },
    cuid: {
        initialValue: '',
        syncOptions: [
            {
                setCuid: (cuid) => ({ cuid }),
                reducer: (state, action) => {
                    state.cuid = action.params.cuid;
                    return { ...state };
                }
            }
        ]
    },
    packageId: {
        initialValue: '',
        syncOptions: [
            {
                setPackageId: (packageId) => ({ packageId }),
                reducer: (state, action) => {
                    state.packageId = action.params.packageId;
                    return { ...state };
                }
            }
        ]
    },
    videoPrecisionRecord: {
        initialValue: '',
        syncOptions: [
            {
                setVideoPrecisionRecord: (videoPrecisionRecord) => ({ videoPrecisionRecord }),
                reducer: (state, action) => {
                    state.videoPrecisionRecord = action.params.videoPrecisionRecord;
                    return { ...state };
                }
            }
        ]
    },
    currentNode: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentNode: (currentNode) => ({ currentNode }),
                reducer: (state, action) => {
                    state.currentNode = action.params.currentNode;
                    return { ...state };
                }
            }
        ]
    },
    executePlanDetail: {
        initialValue: null,
        syncOptions: [
            {
                setExecutePlanDetail: (executePlanDetail) => ({ executePlanDetail }),
                reducer: (state, action) => {
                    state.executePlanDetail = action.params.executePlanDetail;
                    return { ...state };
                }
            }
        ]
    }
});
