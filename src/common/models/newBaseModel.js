import { createModel } from 'COMMON/middleware';

export default createModel({
    project: 'common',
    namespace: 'new_base',
    statusCode: {
        initialValue: 0, // 工具状态，0:初始化；1:准备完成；4:需要进行热更新
        syncOptions: [
            {
                setStatusCode: (statusCode) => ({ statusCode }),
                reducer: (state, action) => {
                    state.statusCode = action.params.statusCode;
                    return { ...state };
                }
            }
        ]
    }
});
