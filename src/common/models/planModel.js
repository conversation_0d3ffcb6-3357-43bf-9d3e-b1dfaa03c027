import {createModel, presetReducer} from 'COMMON/middleware';
import {get} from 'lodash';

export default createModel({
    project: 'common',
    namespace: 'plan',
    planGroupList: {
        initialValue: [],
        syncOptions: [
            {
                setPlanGroupList: (planGroupList) => ({planGroupList}),
                reducer: (state, action) => {
                    state.planGroupList = action.params.planGroupList;
                    return {...state};
                }
            }
        ]
    },
    currentPlanGroup: {
        initialValue: [],
        syncOptions: [
            {
                setCurrentPlanGroup: (currentPlanGroup) => ({currentPlanGroup}),
                reducer: (state, action) => {
                    state.currentPlanGroup = action.params.currentPlanGroup;
                    return {...state};
                }
            }
        ]
    },
    // 计划所选用例对应的用例组
    currentPlanMapGroup: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentPlanMapGroup: (currentPlanMapGroup) => ({currentPlanMapGroup}),
                reducer: (state, action) => {
                    state.currentPlanMapGroup = action.params.currentPlanMapGroup;
                    return {...state};
                }
            }
        ]
    },
    currentPlanTree: {
        initialValue: [],
        syncOptions: [
            {
                setCurrentPlanTree: (currentPlanTree) => ({currentPlanTree}),
                reducer: (state, action) => {
                    state.currentPlanTree = action.params.currentPlanTree;
                    return {...state};
                }
            }
        ]
    },
    curCaseProgress: {
        initialValue: {
            lastCaseProgress: null,
            caseProgress: null,
            lastRound: false
        },
        syncOptions: [
            {
                setCurCaseProgress: (curCaseProgress) => ({curCaseProgress}),
                reducer: (state, action) => {
                    state.curCaseProgress = action.params.curCaseProgress;
                    return {...state};
                }
            }
        ]
    },
    planList: {
        initialValue: [],
        syncOptions: [
            {
                setPlanList: (planList) => ({planList}),
                reducer: (state, action) => {
                    state.planList = action.params.planList;
                    return {...state};
                }
            }
        ]
    },
    planTemplateList: {
        initialValue: [],
        syncOptions: [
            {
                setPlanTemplateList: (planTemplateList) => ({planTemplateList}),
                reducer: (state, action) => {
                    state.planTemplateList = action.params.planTemplateList;
                    return {...state};
                }
            }
        ]
    },
    currentPlanTemplate: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentPlanTemplate: (currentPlanTemplate) => ({currentPlanTemplate}),
                reducer: (state, action) => {
                    state.currentPlanTemplate = action.params.currentPlanTemplate;
                    return {...state};
                }
            }
        ]
    },
    currentPageForPlanList: {
        initialValue: null,
        syncOptions: [
            {
                setCurrentPageForPlanList: (currentPageForPlanList) => ({currentPageForPlanList}),
                reducer: (state, action) => {
                    state.currentPageForPlanList = action.params.currentPageForPlanList;
                    return {...state};
                }
            }
        ]
    },
    totalPageForPlanList: {
        initialValue: 1,
        syncOptions: [
            {
                setTotalPageForPlanList: (totalPageForPlanList) => ({totalPageForPlanList}),
                reducer: (state, action) => {
                    state.totalPageForPlanList = action.params.totalPageForPlanList;
                    return {...state};
                }
            }
        ]
    },
    totalCountForPlanList: {
        initialValue: 0,
        syncOptions: [
            {
                setTotalCountForPlanList: (totalCountForPlanList) => ({totalCountForPlanList}),
                reducer: (state, action) => {
                    state.totalCountForPlanList = action.params.totalCountForPlanList;
                    return {...state};
                }
            }
        ]
    },
    currentPlan: {
        initialValue: {},
        syncOptions: [
            {
                setCurrentPlan: (currentPlan) => ({currentPlan}),
                reducer: (state, action) => {
                    state.currentPlan = action.params.currentPlan;
                    return {...state};
                }
            }
        ]
    },
    cloudTaskList: {
        initialValue: [],
        syncOptions: [
            {
                setCloudTaskList: (cloudTaskList) => ({cloudTaskList}),
                reducer: (state, action) => {
                    state.cloudTaskList = action.params.cloudTaskList;
                    return {...state};
                },
            }
        ]
    },
    caseNode: {
        initialValue: null,
        syncOptions: [
            {
                setCaseNode: (caseNode) => ({caseNode}),
                reducer: (state, action) => {
                    state.caseNode = action.params.caseNode;
                    return {...state};
                },
            }
        ]
    },
    // 巡检轮次列表
    inspectionRoundList: {
        initialValue: [],
        syncOptions: [
            {
                setInspectionRoundList: (inspectionRoundList) => ({inspectionRoundList}),
                reducer: (state, action) => {
                    state.inspectionRoundList = action.params.inspectionRoundList;
                    return {...state};
                },
            }
        ]
    },
});
