import {get} from 'lodash';
import {createModel, presetReducer} from 'COMMON/middleware';

// const [startReducer, failReducer] = presetReducer;
export default createModel({
    project: 'laboratory',
    namespace: 'common',
    currentPrompt: {
        initialValue: {},
        syncOptions: [
            {
                setCurrentPrompt: (currentPrompt) => ({currentPrompt}),
                reducer: (state, action) => {
                    state.currentPrompt = action.params.currentPrompt;
                    return {...state};
                }
            }
        ]
    },
    promptTypeList: {
        initialValue: [],
        syncOptions: [
            {
                setPromptTypeList: (promptTypeList) => ({promptTypeList}),
                reducer: (state, action) => {
                    state.promptTypeList = action.params.promptTypeList;
                    return {...state};
                }
            }
        ]
    },
    allPromptList: {
        initialValue: [],
        syncOptions: [
            {
                setAllPromptList: (allPromptList) => {
                    return {allPromptList};
                },
                reducer: (state, action) => {
                    state.allPromptList = action.params.allPromptList;
                    return {...state};
                }
            }
        ],
        asyncOptions: [
            {
                getAllPromptList: (params, success, fail) => {
                    return {
                        url: '/lazyailab/prompt/queryList',
                        method: 'post',
                        params: {
                            ...params
                        },
                        success,
                        fail
                    };
                },
                reducers: [
                    presetReducer[0],
                    (state, action) => {
                        const allPromptList = get(action, 'data', []);
                        state.allPromptList = allPromptList;
                        // console.log(allPromptList, 'promptList'); //测试用
                        return {...state};
                    },
                    presetReducer[2]
                ]
            }
        ]
    }
});
