import { createModel, presetReducer } from 'COMMON/middleware';

export default createModel({
    project: 'common',
    namespace: 'trd',
    mrdTree: {
        initialValue: [],
        syncOptions: [
            {
                setMrdTree: (mrdTree) => ({ mrdTree }),
                reducer: (state, action) => {
                    state.mrdTree = action.params.mrdTree;
                    return { ...state };
                }
            }
        ]
    },
    curTreeNodeId: {
        initialValue: null,
        syncOptions: [
            {
                setCurTreeNodeId: (curTreeNodeId) => ({ curTreeNodeId }),
                reducer: (state, action) => {
                    state.curTreeNodeId = action.params.curTreeNodeId;
                    return { ...state };
                }
            }
        ]
    }
});
