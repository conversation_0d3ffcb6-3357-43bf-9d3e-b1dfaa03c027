import { createModel, presetReducer } from 'COMMON/middleware';


export default createModel({
    project: 'common',
    namespace: 'rag',
    allWorkGroupList: {
        initialValue: [],
        syncOptions: [
            {
                setAllWorkGroupList: (allWorkGroupList) => ({ allWorkGroupList }),
                reducer: (state, action) => {
                    state.allWorkGroupList = action.params.allWorkGroupList;
                    return { ...state };
                }
            }
        ]
    },
    joinedWorkGroupList: {
        initialValue: [],
        syncOptions: [
            {
                setJoinedWorkGroupList: (joinedWorkGroupList) => ({ joinedWorkGroupList }),
                reducer: (state, action) => {
                    state.joinedWorkGroupList = action.params.joinedWorkGroupList;
                    return { ...state };
                }
            }
        ]
    },
    embeddingModelList: {
        initialValue: null,
        syncOptions: [
            {
                setEmbeddingModelList: (embeddingModelList) => ({ embeddingModelList }),
                reducer: (state, action) => {
                    state.embeddingModelList = action.params.embeddingModelList;
                    return { ...state };
                }
            }
        ]
    }
});
