function startReducer(state, action) {
    state[action.name] = state[action.name] || action.data;
    return {...state};
}

function successReducer(state, action) {
    state[action.name] = action.data || undefined;
    return {...state};
}

function failReducer(state, action) {
    state[action.name] = state[action.name] || action.data;
    return {...state};
}

export default [startReducer, successReducer, failReducer];
