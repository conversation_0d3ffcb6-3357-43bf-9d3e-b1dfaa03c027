import {createStore, applyMiddleware, combineReducers} from 'redux';
import thunk from 'redux-thunk';

function initialReducer(state = {}) {
    return {...state};
}

const reducerIndex = {
    start: 0,
    success: 1,
    fail: 2,
};


function configureStore() {
    const store = createStore(createReducer({}), {}, applyMiddleware(thunk));

    const reducers = {};
    // 添加一个对象以跟踪已注册的异步 Reducer
    store.asyncReducers = {};

    // 创建注入 reducer 函数
    // 此函数添加 async reducer，并创建一个新的组合 reducer
    store.injectReducer = (key, options) => {
        if (!reducers[key]) {
            reducers[key] = {};
        }
        reducers[key] = {
            ...reducers[key],
            ...options,
        };
        store.asyncReducers[key] = (state, action) => {
            if (!action.type.startsWith('@@redux')) {
                const split = action.type.split('.');
                const namespace = split[1];
                const name = split[2];
                if (action.type in reducers[key]) {
                    if (action.type.endsWith('.init')) {
                        if (action.type === `${key}.${namespace}.${name}.init`) {
                            if (!state[namespace]) {
                                state[namespace] = {};
                            }
                            if (!state[namespace][name] && action.initialValue !== undefined) {
                                state[namespace][name] = action.initialValue;
                            }
                        }
                        return {...state};
                    } else {
                        const _reducers = reducers[key][action.type];
                        if (Array.isArray(_reducers)) {
                            const res = _reducers[reducerIndex[action.status]](state[namespace], action);
                            return {
                                ...state,
                                [namespace]: res,
                            };
                        } else {
                            const res = _reducers(state[namespace], action);
                            return {
                                ...state,
                                [namespace]: res,
                            };
                        }
                    }
                }
            }
            return {...state};
        };
        store.replaceReducer(createReducer(store.asyncReducers));
    };

    return store;
}

export default configureStore();

function createReducer(asyncReducers) {
    return combineReducers({
        initialReducer,
        ...asyncReducers
    });
}
