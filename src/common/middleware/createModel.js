import {get, post} from 'COMMON/utils/requestUtils';
import store from './store';

function createModel(model) {
    const project = model.project;
    if (!project) {
        throw new Error('请设置project!');
    }
    delete model.project;

    const namespace = model.namespace;
    if (!namespace) {
        throw new Error('请设置namespace!');
    }
    delete model.namespace;
    let actions = [];
    let reducers = {
        asyncReducers: {},
        syncReducers: {},
    };
    Object.keys(model).forEach(name => {
        const {asyncOptions, syncOptions, initialValue} = model[name];
        store.injectReducer(project, {
            [`${project}.${namespace}.${name}.init`]: (state, action) => {
                if (!state[namespace]) {
                    state[namespace] = {};
                }
                if (!state[namespace][name] && action.initialValue !== undefined) {
                    state[namespace][name] = action.initialValue;
                }
                return {...state};
            }
        });
        // 设置初始值
        store.dispatch({type: `${project}.${namespace}.${name}.init`, initialValue});
        if (Array.isArray(asyncOptions)) {
            const {_actions, _reducers} = resolveAsyncOptions(project, namespace, name, asyncOptions, initialValue);
            actions = [...actions, ..._actions];
            reducers.asyncReducers = {
                ...reducers.asyncReducers,
                ..._reducers,
            };
        }
        if (Array.isArray(syncOptions)) {
            const {_actions, _reducers} = resolveSyncOptions(project, namespace, name, syncOptions);
            actions = [...actions, ..._actions];
            reducers.syncReducers = {
                ...reducers.syncReducers,
                ..._reducers,
            };
        }
    });

    store.injectReducer(project, {
        ...reducers.asyncReducers,
        ...reducers.syncReducers,
    });
    

    return actions;
}

function resolveRequest(request) {
    if (request.method && request.method.toLowerCase() === 'post') {
        return post(request.url, request.params);
    }
    return get(request.url, request.params, request?.options);
}

/**
 * 解析异步事件
 * @param project
 * @param namespace
 * @param name
 * @param asyncOptions
 * @param initialValue
 * @returns {{_actions: [], _reducers: {}}}
 */
function resolveAsyncOptions(project, namespace, name, asyncOptions, initialValue) {
    const _actions = [];
    const _reducers = {};
    asyncOptions.forEach(asyncOption => {
        const {reducers} = asyncOption;
        delete asyncOption.reducers;
        const actionName = Object.keys(asyncOption)[0];
        if (!Array.isArray(reducers) || reducers.length !== 3) {
            throw new Error(`请正确填写reducers，${project}.${namespace}.${name}`);
        } else {
            _reducers[`${project}.${namespace}.${name}.${actionName}`] = reducers;
        }
        // 处理action
        _actions.push({
            [actionName]: (...rest) => (dispatch) => {
                return new Promise((resolve, reject) => {
                    const result = asyncOption[actionName](...rest);
                    dispatch({
                        type: `${project}.${namespace}.${name}.${actionName}`,
                        name,
                        data: initialValue,
                        requestConfig: result,
                        status: 'start'
                    });
                    resolveRequest(result)
                        .then((data) => {
                            dispatch({
                                type: `${project}.${namespace}.${name}.${actionName}`,
                                name,
                                data,
                                requestConfig: result,
                                status: 'success'
                            });
                            result.success && result.success(data);
                            resolve(data);
                        })
                        .catch(err => {
                            dispatch({
                                type: `${project}.${namespace}.${name}.${actionName}`,
                                name,
                                data: initialValue,
                                requestConfig: result,
                                status: 'fail'
                            });
                            result.fail && result.fail(err);
                            reject(err);
                        });
                });
            },
        });
    });
    return {
        _actions,
        _reducers,
    };
}

/**
 * 解析同步事件
 * @param project
 * @param namespace
 * @param name
 * @param syncOptions
 * @returns {{_actions: [], _reducers: {}}}
 */
function resolveSyncOptions(project, namespace, name, syncOptions) {
    const _actions = [];
    const _reducers = {};
    syncOptions.forEach(syncOption => {
        const {reducer} = syncOption;
        delete syncOption.reducer;
        const actionName = Object.keys(syncOption)[0];
        if (!reducer) {
            throw new Error(`请正确填写reducer，${project}.${namespace}.${name}`);
        } else {
            _reducers[`${project}.${namespace}.${name}.${actionName}`] = reducer;
        }
        _actions.push({
            [actionName]: (...rest) => (dispatch) => {
                const params = syncOption[actionName](...rest);
                dispatch({type: `${project}.${namespace}.${name}.${actionName}`, params});
            },
        });
    });

    return {
        _actions,
        _reducers,
    };
}

export default createModel;
