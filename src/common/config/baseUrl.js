// 精准地址
export const PRECISION_BASICURL = window.location.protocol.includes('https') ?
    'https://testone.baidu-int.com/colorcode' : 'http://naprecision.baidu-int.com:8091';


// testone 地址
export const TESTONE_BASICURL = window.location.protocol.includes('https') ?
    'https://testone.baidu-int.com' :
    'http://*************:8088';


// websocket 调试地址
export const WS_DEBUG_BASICURL = window.location.protocol.includes('https') ?
    (window.location.host?.includes('qamate') ?
        'wss://qamate.baidu-int.com' : 'wss://pioneer.baidu-int.com') :
    'ws://rmtc-offline.bcc-bdbl.baidu.com';