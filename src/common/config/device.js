import {message} from 'antd';

export function checkDevice(deviceList, currentDevice, osType) {
    try {
        // 确定有设备连接
        let gotDevice = false;
        for (let device of deviceList[2 === +osType ? 'iOS' : 'android']) {
            if (device.deviceId === currentDevice?.deviceId) {
                gotDevice = true;
                if (![2].includes(device?.status)) {
                    message.error({content: '请确保设备状态正常'});
                    return false;
                }
                break;
            }
        }
        if (!gotDevice) {
            message.error('请确保有设备连接');
            return false;
        }
        return true;
    } catch (err) {
        console.log(err?.message ?? err);
    }
}