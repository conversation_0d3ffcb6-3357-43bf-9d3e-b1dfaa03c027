export const layout = {
    labelCol: {
        span: 4,
    },
    wrapperCol: {
        span: 20,
    },
};

export const layout2 = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 18,
    },
};
export const layout3 = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 16,
    },
};

export const formButtonLayout = {
    wrapperCol: {
        offset: 6,
        span: 18,
    }
};

export const filterOption = (input, option) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
