let events = {};

/**
 * key： modelName_eventName，eg：iterationCase_refreshVersionList
 */
class EventBus {
    static on(key, callback) {
        if (events[key]) {
            const index = events[key].findIndex(e => e === callback);
            if (index === -1) {
                events[key].push(callback);
            }
        } else {
            events[key] = [callback];
        }
    }

    static emit(key, ...rest) {
        if (!events[key]) {
            return;
        }
        events[key].forEach(fun => {
            if (typeof fun !== 'function') {
                return;
            }
            fun(...rest);
        });
    }

    static off(key, callback) {
        if (callback) {
            const index = events[key].findIndex(item => item === callback);
            if (index !== -1) {
                events[key].splice(index, 1);
            }
        } else {
            events[key] = undefined;
        }
    }

    static offAll() {
        events = {};
    }
}

export default EventBus;
