import {isEmpty} from 'lodash';
import {convertOsTypeToType, convertSignStageToType} from 'PACKAGES/react-kityminder-editor-v2/src/utils';

export const FILTER_DATA = {
    access: {activeKey: 'belong', data: []},
    priority: {activeKey: 'belong', data: []},
    tag: {activeKey: 'belong', data: []},
    stampResult: {activeKey: 'belong', data: []},
    autoStatus: {activeKey: 'belong', data: []},
    executeType: {activeKey: 'belong', data: []},
    record: {activeKey: 'belong', data: []},
    link: {activeKey: 'belong', data: []},
    relation: {activeKey: 'belong', data: []},
    disabled: {activeKey: 'belong', data: []},
    creator: {activeKey: 'belong', data: []}
};

export const filterObject = (obj, keys) =>
    Object.keys(obj)
        .filter((key) => !keys?.includes(key))
        .reduce((res, key) => {
            res[key] = obj[key];
            return res;
        }, {});

const reverseSelectValue = (value) => {
    switch (value) {
        case 1:
            return -1;
        case -1:
            return 1;
        default:
            return 0;
    }
};

// 找boolean属性
const findBooleanAttrNode = (data, value, children, parentStatus) => {
    let hasAccess = data?.includes(1);
    let notHasAccess = data?.includes(0);
    if (parentStatus === 1) {
        return 1;
    }
    if ((!hasAccess && !notHasAccess) || (hasAccess && notHasAccess)) {
        return 1;
    }
    // 仅选【准入】
    if (hasAccess && !notHasAccess) {
        // 节点准入 仅选【准入】且 不管是否有孩子，采纳
        if (value === 1) {
            return 1;
        }
        // 节点非准入 仅选【准入】 且 有孩子，继续向后看
        if (value === 0 && !isEmpty(children)) {
            return 0;
        }
        // 节点非准入 仅选【准入】 且 无孩子，排除
        if (value === 0 && isEmpty(children)) {
            return -1;
        }
    }
    if (!hasAccess && notHasAccess) {
        // 节点准入
        if (value === 1) {
            return -1;
        }
        // 节点非准入 仅选【非准入】 且 有孩子，继续向后看
        if (value === 0 && !isEmpty(children)) {
            return 0;
        }
        // 节点非准入 仅选【非准入】 且 有孩子，继续向后看
        if (value === 0 && isEmpty(children)) {
            return 1;
        }
    }
};

// 找该单一属性
const findAttrSingleNode = (data, value, children, parentStatus) => {
    if (isEmpty(data)) {
        return 1;
    }
    if (parentStatus === 1) {
        return 1;
    }
    // 节点存在该属性 且 不管是否有孩子，采纳
    if (data?.includes(value)) {
        return 1;
    }
    // 节点不存在该属性 且 有孩子，继续向后看
    if (!data?.includes(value) && !isEmpty(children)) {
        return 0;
    }
    // 节点不存在该属性 且 无孩子，排除
    if (!data?.includes(value) && isEmpty(children)) {
        return -1;
    }
};

// 找该多属性
const findAttrComplexNode = (data, value, children, parentStatus) => {
    let intersection = data.filter((obj1) => {
        return value.some((obj2) => obj2 === obj1);
    });
    if (isEmpty(data)) {
        return 1;
    }
    if (parentStatus === 1) {
        return 1;
    }
    // 节点存在该属性 且 不管是否有孩子，采纳
    if (!isEmpty(intersection)) {
        return 1;
    }
    // 节点不存在该属性 且 有孩子，继续向后看
    if (isEmpty(intersection) && !isEmpty(children)) {
        return 0;
    }
    // 节点不存在该属性 且 无孩子，排除
    if (isEmpty(intersection) && isEmpty(children)) {
        return -1;
    }
};

// 找属性 仅看叶子结点
const findAttrLeafNode = (data, value, children, type, parentStatus) => {
    if (!isEmpty(children)) {
        return 0;
    }
    if (parentStatus === 1) {
        return 1;
    }
    let dataList = data;
    if (type === 'stampResult' && data?.includes(0)) {
        dataList = [...data, 5];
    }
    // 节点存在该属性，采纳
    if (dataList?.includes(value)) {
        return 1;
    }
    // 节点不存在该属性，排除
    if (!dataList?.includes(value)) {
        return -1;
    }
};

// boolean属性 节点是否筛选
// data 0 / 1
const filterNodeWithBooleanAttr = (filterData, type, item, data, parentStatus) => {
    let filterAttrKey = filterData?.[type]?.activeKey; // activeKey
    let filterAttr = filterData?.[type]?.data; // data
    let selectWithAttr = 1;
    if (!isEmpty(filterAttr)) {
        if (parentStatus === 1) {
            selectWithAttr = 1;
        } else {
            selectWithAttr = findBooleanAttrNode(filterAttr, data, item?.children);
        }
        // 正向
        if (filterAttrKey === 'belong') {
            selectWithAttr = selectWithAttr;
        }
        // 反向
        if (filterAttrKey === 'noBelong') {
            selectWithAttr = reverseSelectValue(selectWithAttr);
        }
    }
    return selectWithAttr;
};

// 单一属性 节点是否筛选
const filterNodeWithAttrSingle = (filterData, type, item, data, parentStatus) => {
    let filterAttrKey = filterData?.[type]?.activeKey; // activeKey
    let filterAttr = filterData?.[type]?.data; // data
    let selectWithAttr = 1;
    if (!isEmpty(filterAttr)) {
        if (parentStatus === 1) {
            selectWithAttr = 1;
        } else {
            selectWithAttr = findAttrSingleNode(filterAttr, data, item?.children);
        }
        // 正向
        if (filterAttrKey === 'belong') {
            selectWithAttr = selectWithAttr;
        }
        // 反向
        if (filterAttrKey === 'noBelong') {
            selectWithAttr = reverseSelectValue(selectWithAttr);
        }
    }
    return selectWithAttr;
};

// 多属性 节点是否筛选
const filterNodeWithAttrComplex = (filterData, type, item, data, parentStatus) => {
    let filterAttrKey = filterData?.[type]?.activeKey; // activeKey
    let filterAttr = filterData?.[type]?.data; // data
    let selectWithAttr = 1;
    if (!isEmpty(filterAttr)) {
        if (parentStatus === 1) {
            selectWithAttr = 1;
        } else {
            selectWithAttr = findAttrComplexNode(filterAttr, data, item?.children);
        }
        // 正向
        if (filterAttrKey === 'belong') {
            selectWithAttr = selectWithAttr;
        }
        // 反向
        if (filterAttrKey === 'noBelong') {
            selectWithAttr = reverseSelectValue(selectWithAttr);
        }
    }
    return selectWithAttr;
};

// 单一属性仅看叶子结点 节点是否筛选
const filterNodeWithAttrLeaf = (filterData, type, item, data, parentStatus) => {
    let filterAttrKey = filterData?.[type]?.activeKey; // activeKey
    let filterAttr = filterData?.[type]?.data; // data
    let selectWithAttr = 1;

    if (!isEmpty(filterAttr)) {
        if (parentStatus === 1) {
            selectWithAttr = 1;
        } else {
            selectWithAttr = findAttrLeafNode(filterAttr, data, item?.children, type);
        }
        // 正向
        if (filterAttrKey === 'belong') {
            selectWithAttr = selectWithAttr;
        }
        // 反向
        if (filterAttrKey === 'noBelong') {
            selectWithAttr = reverseSelectValue(selectWithAttr);
        }
    }
    return selectWithAttr;
};

// 文本 节点是否筛选
const filterNodeWithName = (search, item, data, parentStatus) => {
    let selectWithName = 1;
    if (parentStatus === 1 || item?.nodeName?.includes(search)) {
        selectWithName = 1;
    } else {
        if (!isEmpty(item?.children) && !item?.nodeName?.includes(search)) {
            selectWithName = 0;
        }
        if (isEmpty(item?.children) && !item?.nodeName?.includes(search)) {
            selectWithName = 1;
        }
    }
    return selectWithName;
};

const VALUE_TYPE = {
    2: 'ITP',
    3: 'FastEeb',
    4: 'Monster',
    5: 'ICode',
    6: 'UBC',
    7: 'ICafe'
};

// 过滤
export function filterConditionCaseNodeRecursive(
    tree,
    filterData,
    newData = [],
    executeTypeList = [],
    parentStatus = {}
) {
    // 添加对tree的空值检查
    if (!tree || !Array.isArray(tree)) {
        return newData;
    }
    for (const item of tree) {
        let node = {
            ...item,
            children: []
        };
        // 端类型
        let osType = convertOsTypeToType(filterData?.stampInfo?.osType);

        let selectAll = [];
        // 筛选关键词
        let selectWithSearchValue = isEmpty(filterData?.search)
            ? 1
            : filterNodeWithName(filterData?.search, item, item?.nodeName, parentStatus?.search);
        selectAll.push(selectWithSearchValue);

        // 筛选准入
        let selectWithAccess = isEmpty(filterData?.access?.data)
            ? 1
            : filterNodeWithBooleanAttr(
                filterData,
                'access',
                item,
                item?.extra?.isAccess === true ? 1 : 0,
                parentStatus?.access
            );
        selectAll.push(selectWithAccess);

        // 筛选优先级
        let selectWithPriority = isEmpty(filterData?.priority?.data)
            ? 1
            : filterNodeWithAttrSingle(filterData, 'priority', item, item?.extra?.priority, parentStatus?.priority);
        selectAll.push(selectWithPriority);

        // 筛选标签
        let selectWithTag = isEmpty(filterData?.tag?.data)
            ? 1
            : filterNodeWithAttrComplex(
                filterData,
                'tag',
                item,
                [...item?.extra?.tagInfo?.caseTagList, ...item?.extra?.tagInfo?.moduleTagList],
                parentStatus?.tag
            );
        selectAll.push(selectWithTag);

        // 筛选链接
        let selectWithLink = isEmpty(filterData?.link?.data)
            ? 1
            : filterNodeWithAttrComplex(
                filterData,
                'link',
                item,
                item?.extra?.linkList?.map((_ele) => _ele.name),
                parentStatus?.link
            );
        selectAll.push(selectWithLink);

        // 筛选平台
        let selectWithRelation = isEmpty(filterData?.relation?.data)
            ? 1
            : filterNodeWithAttrComplex(
                filterData,
                'relation',
                item,
                item?.extra?.relationInfo?.[osType]?.map((_ele) => VALUE_TYPE[_ele.platform] + '.' + _ele.address),
                parentStatus?.relation
            );
        selectAll.push(selectWithRelation);

        // 筛选录制
        let selectWithRecord = isEmpty(filterData?.record?.data)
            ? 1
            : filterNodeWithBooleanAttr(
                filterData,
                'record',
                item,
                item?.extra?.recordTaskInfo?.[osType]?.recordIdList?.length > 0 ? 1 : 0,
                parentStatus?.record
            );
        selectAll.push(selectWithRecord);

        // 筛选禁用
        let selectWithDisabled = isEmpty(filterData?.disabled?.data)
            ? 1
            : filterNodeWithBooleanAttr(
                filterData,
                'disabled',
                item,
                item?.extra?.disabledInfo?.[osType]?.status === true ? 1 : 0,
                parentStatus?.disabled
            );
        selectAll.push(selectWithDisabled);

        // 筛选创建人
        let selectWithCreator = isEmpty(filterData?.creator?.data)
            ? 1
            : filterNodeWithAttrSingle(
                filterData,
                'creator',
                item,
                item?.updateUser,
                parentStatus?.creator
            );
        selectAll.push(selectWithCreator);

        // 签章端
        let stampOsType =
            filterData?.stampInfo?.signType === 1 ? 'common' : convertOsTypeToType(filterData?.stampInfo?.osType);
        // 签章阶段
        let stampStage = convertSignStageToType(filterData?.stampInfo?.querySignStage);
        // 签章结果列表
        let signInfoListWithOsType = item?.extra?.signInfo?.[stampOsType]?.[stampStage];
        // 最新签章结果
        let curSignInfo = signInfoListWithOsType?.[0]?.status ?? 0;
        // 根据最新签章结果，筛选签章结果
        let selectWithStampResult = 1;
        if (isEmpty(filterData?.stampResult?.data)) {
            selectWithStampResult = 1;
        } else if (!isEmpty(item?.children)) {
            selectWithStampResult = 0;
        } else {
            selectWithStampResult = filterNodeWithAttrLeaf(
                filterData,
                'stampResult',
                item,
                curSignInfo,
                parentStatus?.stampResult
            );
        }
        selectAll.push(selectWithStampResult);

        // 自动化执行结果列表
        let cloudInfoListWithOsType = item?.cloudInfo?.[osType];
        // 最新自动化执行结果
        let autoStatus = cloudInfoListWithOsType?.[0]?.status;
        let selectWithAutoStatus = 1;
        // 筛选自动化执行结果
        if (isEmpty(filterData?.autoStatus?.data)) {
            selectWithAutoStatus = 1;
        } else if (!isEmpty(item?.children)) {
            selectWithAutoStatus = 0;
        } else {
            // 若不存在，则看 签章结果列表中最新的自动化签章数据
            if (!autoStatus) {
                let signStatus = signInfoListWithOsType?.find((_ele) => _ele.type === 2)?.status;
                if (signStatus === 1) {
                    autoStatus = 2;
                }
                if (signStatus === 2) {
                    autoStatus = 3;
                }
            }
            // 筛选自动化执行结果
            selectWithAutoStatus = filterNodeWithAttrLeaf(
                filterData,
                'autoStatus',
                item,
                autoStatus,
                parentStatus?.autoStatus
            );
        }
        selectAll.push(selectWithAutoStatus);

        // 节点步骤类型
        let selectWithExecuteType = 1;
        executeTypeList.push(item?.extra?.executionType[osType]);
        if (isEmpty(filterData?.executeType?.data)) {
            selectWithExecuteType = 1;
        } else if (!isEmpty(item?.children)) {
            selectWithExecuteType = 0;
        } else {
            let nodeType = 1;
            if (executeTypeList[executeTypeList.length - 1] === 0 || executeTypeList.includes(1)) {
                nodeType = 1;
            } else if (executeTypeList.includes(4)) {
                nodeType = 4;
            } else {
                nodeType = 2;
            }
            selectWithExecuteType = filterNodeWithAttrLeaf(
                filterData,
                'executeType',
                item,
                nodeType,
                parentStatus?.executeType
            );
        }
        selectAll.push(selectWithExecuteType);
        if ([1, 2]?.includes(item.extra.osTag) && item.extra.osTag !== filterData?.stampInfo?.osType) {
        } else {
            if (selectAll?.every((item) => item === 1)) {
                newData.push(item);
            }
            if (
                selectAll?.some((item) => item === 0) &&
                !selectAll?.some((item) => item === -1) &&
                !isEmpty(item?.children)
            ) {
                let _child = filterConditionCaseNodeRecursive(
                    item.children,
                    filterData,
                    newData.children,
                    executeTypeList,
                    {
                        search: selectWithSearchValue,
                        access: selectWithAccess,
                        priority: selectWithPriority,
                        tag: selectWithTag,
                        link: selectWithLink,
                        record: selectWithRecord,
                        stampResult: selectWithStampResult,
                        autoStatus: selectWithAutoStatus,
                        relation: selectWithRelation,
                        disabled: selectWithDisabled,
                        creator: selectWithCreator
                    }
                );
                if (!isEmpty(_child)) {
                    node.children = _child;
                    newData.push(node);
                }
            }
        }
        executeTypeList.pop();
    }
    return newData;
}
