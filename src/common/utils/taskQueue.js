import { sleep } from './utils';

class TaskQueue {
    static instance;

    constructor() {
        if (TaskQueue.instance) {
            return TaskQueue.instance; // 保证单例
        }

        this.queue = []; // 待执行任务列表 [{ taskFn, callback }]
        this.processing = false; // 是否正在处理队列
        this.currentRun = null; // 当前队列处理 Promise
        this.running = false; // 是否有 start 正在执行
        this.currentStartToken = null; // 用于标识正在执行的 start 调用
        this.addCounter = 0; // addTask 次数计数器，用于检测 startFn 执行期间是否产生新任务
        this.taskId = null;
        this.lastTaskCompletedTime = null; // 最后一次任务完成的时间戳
        TaskQueue.instance = this;
    }

    setTaskId() {
        this.taskId++;
    }

    // 获取最后一次任务完成的时间
    getLastTaskCompletedTime() {
        return this.lastTaskCompletedTime;
    }

    // 获取距离最后一次任务完成的时间间隔（毫秒）
    getTimeSinceLastTaskCompleted() {
        if (this.lastTaskCompletedTime === null) {
            return null; // 还没有任务完成过
        }
        return Date.now() - this.lastTaskCompletedTime;
    }

    // 添加任务到队列
    addTask(taskFn, callback) {
        if (typeof taskFn !== 'function') {
            throw new TypeError('当前参数必须是 promise');
        }
        this.queue.push({ taskFn, callback });

        this.addCounter += 1;
        // 立即执行队列（若尚未在处理）
        this._processQueue();
        // console.log('addTask', this.addCounter, this.queue);
    }

    //  顺序执行当前队列中的全部任务
    async _processQueue() {
        if (this.processing) {
            return this.currentRun; // 已有处理在进行
        }
        this.processing = true;
        this.currentRun = (async () => {
            while (this.queue.length) {
                const { taskFn, callback } = this.queue.shift();
                try {
                    const result = await taskFn();
                    callback?.(null, result);
                    // 记录任务完成时间
                    this.lastTaskCompletedTime = Date.now();
                } catch (err) {
                    callback?.(err);
                    // 即使任务失败也记录完成时间
                    this.lastTaskCompletedTime = Date.now();
                }
            }
        })();
        try {
            await this.currentRun;
        } finally {
            this.processing = false;
            this.currentRun = null;
        }
    }

    // 等待队列与当前处理全部完成
    async _waitForDrain() {
        // eslint-disable-next-line no-constant-condition
        while (true) {
            if (!this.processing && this.queue.length === 0) {
                return;
            }
            if (this.processing && this.currentRun) {
                await this.currentRun;
            } else {
                // 队列非空但尚未开始处理，触发处理
                this._processQueue();
                await new Promise((r) => setTimeout(r, 0));
            }
        }
    }

    // 执行 startTaskFn，并确保在其执行期间若有新任务插入，会重跑。
    async start(startTaskFn) {
        const taskId = this.taskId;
        if (typeof startTaskFn !== 'function') {
            throw new TypeError('startTaskFn 必须是 Promise');
        }

        // 为本次 start 创建唯一 token，并替换旧 token 以取消之前的 start
        const token = {};
        this.currentStartToken = token;
        // 若之前没有 start 在跑，需要标记 running；若已有则表示正在取消旧的
        if (!this.running) {
            this.running = true;
        }
        try {
            this.startFn = startTaskFn;
            if (!this.startFn) {
                return;
            }
            // eslint-disable-next-line no-constant-condition
            while (true) {
                // 若已有新的 start 触发，则终止本轮
                if (this.currentStartToken !== token) {
                    return;
                }
                // (1) 等待所有已添加任务执行完成
                await this._waitForDrain();

                if (this.currentStartToken !== token) {
                    return;
                }

                // (2) 记录 addCounter 快照（此刻队列为空且闲置）

                const snapshot = this.addCounter;
                if (this.currentStartToken !== token) {
                    return;
                }
                await sleep(1000);
                // (3) 执行 startTaskFn，若抛错则由调用方处理
                const result = await this.startFn();

                if (this.currentStartToken !== token || this.taskId !== taskId) {
                    return;
                }

                // (4) 若执行期间没有新任务 => 返回结果；否则继续循环
                if (this.addCounter === snapshot) {
                    this.lastTaskCompletedTime = Date.now();
                    return result;
                }
                // 有新任务 → 循环继续：先跑新任务，再重试 startTaskFn
            }
        } finally {
            // 仅当本轮仍为最新 token 时，才重置 running 状态
            if (this.currentStartToken === token) {
                this.running = false;
            }
        }
    }

    reset() {
        // 任务全部执行完成
        this.queue = [];
        this.addCounter = 0;
        this.processing = false;
        this.currentRun = null;
        this.running = false;
        this.currentStartToken = null;
        // 取消startFn 的任务
        this.startFn = null;
        // this.lastTaskCompletedTime = null;
    }
}

// 导出单例实例
const taskQueue = new TaskQueue();

export default taskQueue;

export { TaskQueue };
