import EventBus from 'COMMON/utils/eventBus';
import taskQueue from './taskQueue';
export const getCaseNodeType = (nodeType, step) => {
    let newNodeType = nodeType;
    if (-1 !== [0, 2, 3, 4].indexOf(newNodeType) && 3 === step.stepInfo.type &&
        'operator' === step.stepInfo.params.type) {
        newNodeType = 1;
    }
    if (-1 !== [0, 2, 3].indexOf(newNodeType) && 3 === step.stepInfo.type &&
        'assest' === step.stepInfo.params.type) {
        newNodeType = 4;
    }
    if (-1 !== [0, 3].indexOf(newNodeType) && 3 !== step.stepInfo.type) {
        newNodeType = 2;
    }
    return newNodeType;
};
/**
 * 根据节点ID列表，找出所有叶子节点ID
 * @param {Array} caseNodeIds - 节点ID列表
 * @param {Array} dataSource - 节点数据源
 * @returns {Array} - 所有叶子节点ID列表
 */
export const findLeafNodesByIds = (caseNodeIds, dataSource) => {
    // 如果传入的参数无效，直接返回原始caseNodeIds
    if (!dataSource || dataSource.length === 0) {
        return caseNodeIds || [];
    }

    const nodeMap = new Map();
    // 递归构建节点映射
    const buildNodeMap = (node) => {
        if (!node) {
            return;
        }

        // 处理包装在data属性中的节点
        const nodeData = node.data || node;
        const nodeId = nodeData.caseNodeId || nodeData.nodeId || nodeData.id;

        if (!nodeId) {
            return;
        }

        nodeMap.set(nodeId, node);
        const children = node.children || nodeData.children;
        if (children && children.length > 0) {
            children.forEach((child) => {
                buildNodeMap(child);
            });
        }
    };

    // 构建节点映射
    dataSource.forEach((root, index) => {
        buildNodeMap(root);
    });

    // 如果没有提供节点ID，查找所有叶子节点
    if (!caseNodeIds || caseNodeIds.length === 0) {
        const allLeaves = [];
        dataSource.forEach((root) => {
            const leaves = findLeavesUnderNode(root);
            allLeaves.push(...leaves);
        });
        return [...new Set(allLeaves)];
    }

    // 查找指定节点下的所有叶子节点
    const findLeavesUnderNode = (node, depth = 0) => {
        const leaves = [];
        if (!node) {
            return leaves;
        }

        // 处理包装在data属性中的节点
        const nodeData = node.data || node;
        const nodeId = nodeData.caseNodeId || nodeData.nodeId || nodeData.id;
        const children = node.children || nodeData.children;

        if (!children || children.length === 0) {
            if (nodeId) {
                leaves.push(nodeId);
            }
        } else {
            // 递归遍历所有子节点
            children.forEach((child, index) => {
                const childLeaves = findLeavesUnderNode(child, depth + 1);
                leaves.push(...childLeaves);
            });
        }
        return leaves;
    };

    // 对每个指定的节点ID进行处理
    const result = [];
    caseNodeIds.forEach((id) => {
        const node = nodeMap.get(id);
        if (node) {
            const leaves = findLeavesUnderNode(node);
            result.push(...leaves);
        } else {
            result.push(id);
        }
    });

    // 去重并返回
    return [...new Set(result)];
};
// 接口调用完成 触发treeMap的更新事件
export const withTreeMapUpdate = (apiCall, operationType) => {
    return (...args) => {
        return new Promise((resolve, reject) => {
            taskQueue.addTask(async ()=>{
                const result =  await apiCall(...args);
                return result;
            }, (err, result) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(result);
                    if (operationType && result) {
                        EventBus.emit('updateTreeMap', {
                            type: operationType,
                            result,
                            params: args[0],
                        });
                    }
                }
            });
        });
    };
};

// 获取节点
export const findNodeDetail = (caseList, caseNodeId) => {
    for (let i = 0; i < caseList.length; i++) {
        let node = caseList[i];
        if (node.caseNodeId === Number(caseNodeId)) {
            return node;
        }
        if (node.children && node.children.length > 0) {
            let foundNode = findNodeDetail(node.children, caseNodeId);
            if (foundNode) {
                return foundNode;
            }
        }
    }
    return null;
};
