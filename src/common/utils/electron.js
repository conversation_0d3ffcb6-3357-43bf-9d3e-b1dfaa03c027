import store from '../middleware/store';

const handleFetchError = (error) => {
    store.dispatch({
        type: 'common.base.throwMsg.setThrowMsg',
        params: {
            throwMsg: {...error}
        }
    });
};
/**
 * 获取通信句柄
 */
const getIpcRenderer = () => window.ipcRenderer;

/**
 * 发送异步事件
 *
 * @param {string} eventName 要触发的事件名称
 * @param {Object} args 事件参数
 */
const send = async (eventName, args) => {
    return new Promise((resolve, reject) => {
        getIpcRenderer()?.invoke(eventName, args).then(res => {
            if (res && Object.prototype.hasOwnProperty.call(res, 'code')) {
                if (0 === res.code) {
                    resolve(res.data);
                } else {
                    handleFetchError(res, eventName);
                    reject(res.msg);
                }
            }
            else {
                console.log('系统异常', '请求 native 返回异常', res);
                reject('系统异常');
            }
        }).catch((err) => {
            reject(err);
        });
    });
};

/**
 * 监听异步事件
 *
 * @param {string} eventName 要监听的事件名称
 * @param {string} handler 事件发生后的处理函数
 */
const on = async (eventName, handler) => {
    return getIpcRenderer()?.on(eventName, (e, payload) => handler(payload));
};

/**
 * 取消监听异步事件
 *
 * @param {string} eventName 要监听的事件名称
 * @param {string} handler 事件发生后的处理函数
 */
const remove = async (eventName, handler) => {
    return getIpcRenderer()?.removeAllListeners(eventName, (e, payload) => handler(payload));
};

export default {
    send,
    on,
    remove
};