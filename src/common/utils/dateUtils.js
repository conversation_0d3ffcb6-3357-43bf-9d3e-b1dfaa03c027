import moment from 'moment';


/**
 * 根据时间获取欢迎语
 * @returns {string}
 */


export const getTimeState = () => {
    let timeNow = new Date();
    let hours = timeNow.getHours();
    let text = '';
    if (hours === 0) {
        text = '新的一天开始啦';
    } else if (hours >= 1 && hours <= 6) {
        text = '工作很重要，但也要注意身体哟';
    } else if (hours > 6 && hours < 11) {
        text = '早上好';
    } else if (hours >= 11 && hours < 13) {
        text = '中午好';
    } else if (hours >= 13 && hours <= 16) {
        text = '下午好';
    } else if (hours > 16 && hours <= 18) {
        text = '傍晚好';
    } else if (hours > 18 && hours <= 24) {
        text = '晚上好';
    }
    return text;
};

/**
 * 格式化时间
 * @param t 时间
 * @param f 格式
 * @returns {string}
 */
export const getFormatTime = (t, f) => {
    if (!t) {
        return '';
    }
    if (t.toString().length === 10) {
        t *= 1000;
    }
    let newT = new Date(t);
    let format = f || 'yyyy-MM-dd HH:mm:ss';
    // let newDt = new Date(new Date() - 0 + newD * 86400000);
    let month = newT.getMonth() + 1;
    month = (month < 10) ? ('0' + month) : month;
    let day = (newT.getDate() < 10) ? ('0' + newT.getDate()) : newT.getDate();
    let hour = (newT.getHours() < 10) ? ('0' + newT.getHours()) : newT.getHours();
    let min = (newT.getMinutes() < 10) ? ('0' + newT.getMinutes()) : newT.getMinutes();
    let sec = (newT.getSeconds() < 10) ? ('0' + newT.getSeconds()) : newT.getSeconds();
    if (format === 'yyyy-MM-dd HH:mm:ss') {
        newT = newT.getFullYear() + '-' + month  + '-' + day + ' ' + hour + ':' + min + ':' + sec;
    } else if (format === 'yyyy-MM-dd') {
        newT = newT.getFullYear() + '-' + month  + '-' + day;
    } else if (format === 'MM-dd HH:mm') {
        newT = month  + '-' + day + ' ' + hour + ':' + min;
    }

    return newT + '';
};

export const formatData1 = (completeTime) => {
    let time = '';
    const days = Math.floor(completeTime / (24 * 3600 * 1000));
    if (days > 0) {
        time += `${days}天`;
    }
    const leave1 = completeTime % (24 * 3600 * 1000);
    const hours = Math.floor(leave1 / (3600 * 1000));
    if (hours > 0) {
        time += `${hours}时`;
    }
    const leave2 = leave1 % (3600 * 1000);
    const minutes = Math.floor(leave2 / (60 * 1000));
    if (minutes > 0) {
        time += `${minutes}分`;
    }
    const leave3 = leave2 % (60 * 1000);
    const seconds = Math.floor(leave3 / 1000);
    if (seconds > 0) {
        time += `${seconds}秒`;
    }
    return time || '0秒';
};

/**
 * 根据粒度获取日期
 * @param m moment对象
 * @param granularity 聚合类型
 * @returns {string}
 */
export const getDateByType = (m, granularity) => {
    const year = moment(m).year();
    const quarter = moment(m).quarter();
    let month = moment(m).month() + 1;
    const date = moment(m).date();
    if (granularity === 'year') {
        return String(year);
    }
    if (granularity === 'quarter') {
        return `${year}-Q${quarter}`;
    }
    if (granularity === 'month') {
        return `${year}-${month}`;
    }
    return `${year}-${month}-${date}`;
};

/**
 * 将时间消耗（秒）格式化为小时、分钟和秒的字符串
 *
 * @param seconds 时间消耗（秒）
 * @returns 格式化后的时间字符串，例如 "1h 23m 45s"
 */
export const formatTimeConsume = (seconds) => {
    if (typeof seconds !== 'number' || seconds < 0) {
        return '-'; // 非法值返回占位符
    }
    const hours = Math.floor(seconds / 3600); // 计算小时
    const minutes = Math.floor((seconds % 3600) / 60); // 计算分钟
    const secs = seconds % 60; // 计算秒

    // 根据需要动态拼接结果
    const parts = [];
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}min`);
    if (secs > 0 || parts.length === 0) parts.push(`${secs}s`); // 确保至少显示秒

    return parts.join(' '); // 用空格拼接
};