// 映射步骤名称
export function getName(type) {
    let desc = '操作';
    switch (type) {
        // 人工
        case 101:
            desc = '人工操作';
            break;
        case 102:
            desc = '人工复验';
            break;
        case 201:
            desc = '控件操作';
            break;
        case 1001:
            desc = '接口调用';
            break;
        // 系统
        case 401:
            desc = '开启 APP';
            break;
        case 402:
            desc = 'Scheme';
            break;
        case 403:
            desc = '返回主屏幕';
            break;
        case 404:
            desc = '清理 APP';
            break;
        case 405:
            desc = '授权 APP';
            break;
        case 406:
            desc = '推送文件';
            break;
        case 407:
            desc = '网络模拟';
            break;
        case 408:
            desc = '数据模拟';
            break;
        case 409:
            desc = '数据转发';
            break;
        case 410:
            desc = 'Swipe';
            break;
        case 411:
            desc = '弹窗点除';
            break;
        case 412:
            desc = '测试片段';
            break;
        case 415:
            desc = 'Mock清空';
            break;
        case 416:
            desc = '请求清空';
            break;
        case 417:
            desc = '休眠等待';
            break;
        case 419:
            desc = '请求调用';
            break;
        case 420:
            desc = '屏幕滑动';
            break;
        case 421:
            desc = '屏幕点击';
            break;
        case 422:
            desc = 'Shell';
            break;
        case 423:
            desc = '返回上一页';
            break;
        // 校验
        case 501:
            desc = '白屏检测';
            break;
        case 502:
            desc = '点位校验';
            break;
        case 503:
            desc = '请求校验';
            break;
        case 504:
            desc = '智能断言'; // old
            break;
        case 601:
            desc = '智能定位';
            break;
        case 602:
            desc = '智能断言';
            break;
        case 1101:
            desc = 'SQL';
            break;
        case 1201:
            desc = 'Redis';
            break;
        case 1401:
            desc = '步骤组';
            break;
        default:
            break;
    }
    return desc;
}
