import { isEmpty } from 'lodash';
import { message } from 'antd';
import { mdSerialize, htmlDeserialize } from '@baidu/morpho-data-transform';
// utils
import { escape2Html, uploadContentToBos } from 'COMMON/utils/utils';
import { getDocInfoFromUrl } from 'FEATURES/front_qe_tools/case/demand/utils';
// api
import { getIcafeRelationDetail } from 'COMMON/api/front_qe_tools/demand';

// 插入icafe链接
export const insertNodeLink = (currentTree, newNodes, key, nodeLink) => {
    const resultTree = [...currentTree];
    // 如果当前树为空，直接返回新节点
    if (isEmpty(currentTree)) {
        return newNodes;
    }
    // 同级卡片列表
    let parentNodes = null;
    const insertRecursively = (nodes, parentNode = null) => {
        const resultNodes = [];
        for (let node of nodes) {
            // 如果找到了目标节点
            if (node.icafeLink === nodeLink) {
                // 子级
                if (key === 1) {
                    // 以前获取过的节点处理
                    node = newNodes[0];
                }
                // 父级
                if (key === 2) {
                    if (parentNode) {
                        // 已经获取过父节点， 处理之前的节点数据
                        newNodes[0].children = [
                            node,
                            ...(parentNode?.children || [])?.filter((v) => v.icafeLink !== nodeLink)
                        ];
                    } else if (newNodes[0].icafeLink !== nodeLink) {
                        // 当前请求节点有父节点
                        newNodes[0].children = [node, ...(currentTree || [])?.filter((v) => v.icafeLink !== nodeLink)];
                    } else {
                        // 当前请求节点没有父节点
                        newNodes = currentTree;
                    }
                    parentNodes = newNodes;
                }
                // 同级节点
                if (key === 3) {
                    // 更新同级节点
                    return [node, ...(newNodes || []).filter((v) => v.icafeLink !== nodeLink)];
                }
            } else if (node.children) {
                node.children = insertRecursively(node.children, node);
            }
            resultNodes.push(node);
        }
        return resultNodes;
    };
    // 执行递归插入操作
    const nodes = insertRecursively(resultTree);
    return parentNodes || nodes;
};

// 获取 iCafe 列表
export const getIcafeLinkList = (tree = []) => {
    let list = [];
    for (let item of tree) {
        list.push(item.icafeLink);
        list = [...list, ...getIcafeLinkList(item?.children ?? [])];
    }
    return list;
};

// 获取当前层级的的link
export const getIcafeLinkInfo = (tree, key, parent = null, nodeLink) => {
    // 找到当前节点
    for (let item of tree) {
        if (item?.icafeLink === nodeLink) {
            if (key === 1) {
                return getIcafeLinkList(item?.children ?? []);
            } else if (key === 2) {
                if (!parent) {
                    return [];
                }
                return [parent?.icafeLink];
            } else if (key === 3) {
                return tree?.map((item) => item?.icafeLink);
            }
            return [];
        } else if (!isEmpty(item?.children)) {
            return getIcafeLinkInfo(item?.children ?? [], key, item, nodeLink);
        }
    }
    return [];
};

// 获取有效的icafeLink
export const getValidIcafeLink = (icafeLink, checkedKeys, newIcafeLinkList) => {
    if (!isEmpty(icafeLink)) {
        return icafeLink;
    }
    if (!isEmpty(checkedKeys?.checked)) {
        return checkedKeys?.checked;
    }
    return newIcafeLinkList;
};

// 获取所有链接
export const getAllIcafeLink = (icafeDetailWithKu) => {
    const result = [];
    Object.keys(icafeDetailWithKu).forEach((icafeLink) => {
        const kuList = icafeDetailWithKu[icafeLink].icafeKuList;
        kuList.forEach((item) => {
            result.push(icafeLink + '_' + item.icafeLink);
        });
    });
    return result;
};

const getTargetNodesFromNodeTree = (nodeTree, checkIsTarget) => {
    const result = [];
    const getNodes = (nodes, checkIsTarget, result) => {
        nodes.forEach((node) => {
            const isTarget = checkIsTarget(node);
            if (isTarget) {
                result.push(node);
            }
            if (node.children) {
                getNodes(node.children, checkIsTarget, result);
            }
        });
    };
    getNodes(nodeTree, checkIsTarget, result);
    return result;
};

// 获取 Ku 文档详情
export const getIcafeDetailWithKuDetail = async (data, kuSDK) => {
    if (isEmpty(kuSDK)) {
        message.error('知识库 SDK 获取失败');
    }
    for (let item in data) {
        let newIcafeKuList = [];
        // 知识库链接去重
        let filterIcafeKuList = [...new Set(data[item]?.icafeKuList ?? [])];
        for (let ku of filterIcafeKuList) {
            if (isEmpty(kuSDK)) {
                newIcafeKuList.push({
                    icafeCardType: 'Ku',
                    icafeLink: ku,
                    title: ku,
                    icafeKey: item + '_' + ku,
                    icafeDetail: [],
                    mdDetail: '',
                    extra: [],
                    parentLink: item,
                    icafeName: ku,
                    isLinkContent: true
                });
            } else {
                try {
                    let docInfo = getDocInfoFromUrl(ku);
                    console.log('获取知识库详情', docInfo);
                    let doc = await kuSDK?.getDocContent({
                        spaceGuid: docInfo.spaceGuid,
                        groupGuid: docInfo.groupGuid,
                        repositoryGuid: docInfo.repositoryGuid,
                        docGuid: docInfo.docGuid,
                        type: 'markdown',
                        brCharacterInTable: ' '
                    });
                    console.log('获取知识库详情成功', doc);
                    // 获取文档内文件内容
                    const content = doc.content.children;
                    const sign = doc.sign;
                    const fileNode = getTargetNodesFromNodeTree(content, (node) => {
                        return ['mindmap', 'diagram', 'attachment'].includes(node.type);
                    });
                    const promiseArr = fileNode
                        .map((data) => {
                            if (data.type === 'attachment') {
                                return kuSDK.getFileDownloadPath({
                                    docGuid: docInfo.docGuid,
                                    fileId: data.fileId,
                                    fileInfo: data.fileInfo,
                                    sign
                                });
                            }
                            if (data.type === 'diagram') {
                                return kuSDK.getDiagramPath({
                                    docGuid: docInfo.docGuid,
                                    diagramId: data.id,
                                    sign
                                });
                            }
                            if (data.type === 'mindmap') {
                                return kuSDK.getMindmapPath({
                                    docGuid: docInfo.docGuid,
                                    mindmapId: data.id,
                                    sign
                                });
                            }
                            return null;
                        })
                        .filter((data) => {
                            return !!data;
                        });
                    const docExtra = await Promise.all(promiseArr);
                    console.log('docExtra', docExtra);
                    const title =
                        doc?.content?.children?.find((item) => item.type === 'title')?.children?.[0]?.text ?? ku;
                    newIcafeKuList.push({
                        icafeCardType: 'Ku',
                        icafeLink: ku,
                        title,
                        icafeKey: item + '_' + ku,
                        icafeDetail: doc?.content?.children ?? [],
                        mdDetail: doc?.markdown,
                        extra: docExtra,
                        parentLink: item,
                        isLinkContent: true,
                        icafeName: title
                    });
                } catch (err) {
                    console.log('获取知识库详情失败', err);
                    newIcafeKuList.push({
                        icafeCardType: 'Ku',
                        icafeLink: ku,
                        title: ku,
                        icafeKey: item + '_' + ku,
                        icafeDetail: [],
                        mdDetail: '',
                        extra: [],
                        parentLink: item,
                        isLinkContent: true,
                        icafeName: ku
                    });
                }
            }
        }
        data[item].icafeKuList = newIcafeKuList;
    }
    return data;
};

// 更新doc树
export const updateTreeNode = (data, linkList, linkListInfo, nodeLink) => {
    if (isEmpty(data)) {
        return [];
    }
    return (
        data?.map((item) => {
            if (nodeLink === item.icafeLink) {
                item.disabled = true;
            }
            if (linkList?.includes(item.icafeLink)) {
                let link = linkListInfo[item.icafeLink];
                item.title = item.icafeName;
                item.icafeKey = item.icafeLink;
                item.icafeDetail = link.detail;
                item.isGoLink = true;
                item.isLinkContent = link.isLinkContent ?? false;
                const children = [
                    ...(link?.icafeKuList ?? []),
                    ...updateTreeNode(
                        item?.children?.filter((v) => !v.isLinkContent) ?? [],
                        linkList,
                        linkListInfo,
                        nodeLink
                    )
                ];
                item.children = children;
            } else {
                item.children = updateTreeNode(item?.children ?? [], linkList, linkListInfo, nodeLink);
            }
            return {
                ...item,
                title: item.icafeName,
                icafeKey: item.icafeKey || item.icafeLink
            };
        }) ?? []
    );
};

// 上传内容到bos
export const uploadContent = async (icafeDetail, type = 'knowledge', isHtml = true) => {
    try {
        let res = await uploadContentToBos(icafeDetail, type, type !== 'knowledge' && isHtml);
        return res?.url;
    } catch (err) {
        return '';
    }
};

// 获取深层次节点树
export const getDeepDocNodeTree = async (tree, checkedKeys) => {
    let _data = [];
    for (let item of tree) {
        if (checkedKeys?.includes(item.icafeKey)) {
            let node = {
                nodeName: item.title,
                nodeLink: item.icafeLink,
                nodeType: item.icafeCardType === 'Ku' ? 2 : 1
            };
            let content = await uploadContent(item.icafeDetail);
            if (item.icafeCardType !== 'Ku') {
                node.icafeCardType = item.icafeCardType;
            }
            node.nodeContent = content;
            node.initGenerateTask = true;
            if (!isEmpty(item?.children)) {
                node.children = await getDeepDocNodeTree(item?.children, checkedKeys);
            }
            _data.push(node);
        }
    }
    return _data;
};

// 获取节点树
export const getDocNodeTree = async (tree, checkedKeys) => {
    if (isEmpty(tree)) {
        return [];
    }
    const children = [];
    // 获取第一层选中的节点
    for (let item of tree) {
        let _child = await getDeepDocNodeTree(item.children, checkedKeys);
        // 第一层存在
        if (checkedKeys?.includes(item.icafeKey)) {
            let content = await uploadContent(item.icafeDetail);
            children.push({
                nodeName: item.title,
                nodeLink: item.icafeLink,
                icafeCardType: item.icafeCardType,
                nodeContent: content,
                nodeType: item.icafeCardType === 'Ku' ? 2 : 1,
                initGenerateTask: true,
                children: _child
            });
        } else {
            // 第一层不存在, 所有子节点
            children.push(..._child);
        }
    }
    return children;
};

// 获取文档内容
export const getDocContent = async (nodeLink, kuSDK) => {
    // 获取 icafe 卡片的 ku 链接
    let icafeDetail = await getIcafeRelationDetail({
        icafeLinkList: [nodeLink],
        scopeTypeList: [1, 2]
    });
    let detail = icafeDetail?.[nodeLink].detail;
    // 非 html 格式 => html 格式
    if (!detail?.startsWith('<')) {
        detail = escape2Html(detail);
    }
    // 转 json 格式
    icafeDetail[nodeLink].jsonDetail = htmlDeserialize(detail);

    // 转 md 格式
    icafeDetail[nodeLink].mdDetail = mdSerialize(icafeDetail?.[nodeLink].jsonDetail, {
        standard: true // 必须要有
    });
    let content = {
        type: 'icafe',
        json: icafeDetail?.[nodeLink]?.jsonDetail,
        icafeId: nodeLink,
        title: icafeDetail?.[nodeLink]?.title,
        content: icafeDetail?.[nodeLink]?.mdDetail,
        extra: []
    };
    // mock 数据
    // icafeDetail[nodeLink].icafeKuList = [
    //     'https://ku-qa.dev.weiyun.baidu.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/d6-_MFlp5y/C2QUHJ8NdgRQa_'
    // ];
    let icafeDetailWithKuDetail = await getIcafeDetailWithKuDetail(icafeDetail, kuSDK);
    let kuContent = icafeDetailWithKuDetail?.[nodeLink]?.icafeKuList ?? [];
    content.docList = kuContent?.map((item) => ({
        type: item?.icafeCardType === 'Ku' ? 'ku' : 'icafe',
        kuUrl: item?.icafeLink,
        json: item?.icafeDetail,
        title: item?.title,
        content: item?.mdDetail,
        extra: item?.extra
    }));

    // 上传bos
    let url = await uploadContent(content, 'json', false);

    return [
        {
            nodeName: icafeDetail[nodeLink]?.title,
            nodeType: 3,
            icafeCardType: icafeDetail[nodeLink]?.icafeCardType,
            nodeLink: nodeLink,
            nodeContent: url
        }
    ];
};

// 查找卡片对应的知识库卡片
export const findKuKeys = (nodeLink, data) => {
    let result = [];
    const traverse = (nodes) => {
        // 遍历每个节点
        for (let node of nodes) {
            if (node.icafeKey === nodeLink) {
                node.children.forEach((child) => {
                    if (child.icafeCardType === 'Ku' && !result?.includes(child.icafeKey)) {
                        result.push(child.icafeKey);
                    }
                });
                return;
            }
            if (node.children) {
                traverse(node.children);
            }
        }
    };
    traverse(data);
    return result;
};

// 获取孩子节点信息
// flag: 是否需要判断当前输入卡片
export const getChildIcafeList = (node, flag = true) => {
    let list = [];
    const traverse = (node) => {
        for (let item of node) {
            if (flag && item.icafeKey === nodeLink) {
                continue;
            }
            if (!list?.includes(item?.icafeKey)) {
                list.push(item.icafeKey);
            }
            traverse(item?.children ?? [], flag);
        }
    };
    traverse(node);
    return list;
};
