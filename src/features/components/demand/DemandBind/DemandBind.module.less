.moreWrap {
    position: absolute;
    right: 0px;
    bottom: -25px;
    font-size: 12px;
    text-decoration: underline;

    .moreBtn {
        float: right;
        margin: 2px 0 0 10px;
        text-decoration: underline;
    }

    :global {
        .ant-checkbox-wrapper {
            font-size: 12px !important;
        }

        .ant-checkbox+span {
            padding: 2px 0 0 2px;
        }
    }
}


.link {
    padding: 2px 4px;
    background-color: #e3eafa;
    color: #2d6dfa;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
}

.icafeOption {
    overflow: hidden;
    white-space: nowrap;
}

.docNodeTree {
    max-height: 200px;
    overflow: auto;
    padding: 10px;

    ::-webkit-scrollbar {
        width: 4px;
        /* 滚动条宽度 */
        display: block !important;
    }

    ::-webkit-scrollbar-thumb {
        background-color: rgba(154, 154, 154, 0.943);
        /* 滚动条颜色 */
    }
}

