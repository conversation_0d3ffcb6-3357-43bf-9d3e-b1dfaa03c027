import { useState, useImperativeHandle, forwardRef, useEffect, useMemo } from 'react';
import { Form, Select, Checkbox, Input, Tabs, Dropdown, Tree, message } from 'antd';
import { CaretDownOutlined } from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { htmlDeserialize } from '@baidu/morpho-data-transform';
// redux
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
// components
import { CardTitle } from 'COMMON/components/common/Card';
import IcafeIcon from 'COMMON/components/IcafeIcon';
import NoContent from 'COMMON/components/common/NoContent';
// api
import { getIcafeRelation, getIcafeRelationDetail } from 'COMMON/api/front_qe_tools/demand';
// utils
import { escape2Html } from 'COMMON/utils/utils';
import { getDocInfoFromUrl } from 'FEATURES/front_qe_tools/case/demand/utils';
import { NODE_TYPE_ITEMS, FORM_CONFIG } from './const';
import {
    insertNodeLink,
    getValidIcafeLink,
    getIcafeDetailWithKuDetail,
    getIcafeLinkInfo,
    getAllIcafeLink,
    getIcafeLinkList,
    getDocNodeTree,
    getDocContent,
    findKuKeys,
    getChildIcafeList,
    uploadContent,
    updateTreeNode
} from './utils';
// styles
import styles from './DemandBind.module.less';

const DemandBind = (props, ref) => {
    const {
        docPromptList,
        kuSDK,
        icafeId,
        disabledItems,
        labelCol = { span: 3 },
        showTitle = true,
        showLevel = true,
        showColon = false,
        showRequired = false,
        showGenerateTaskKey = true
    } = props;
    const [isGetIcafeDetail, setIsGetIcafeDetail] = useState(false);
    const [promptActiveKey, setPromptActiveKey] = useState('strategy');
    const [disabled, setDisabled] = useState(false);
    const [docTree, setDocTree] = useState([]);
    const [nodeLink, setNodeLink] = useState(icafeId);
    const [checkedKeys, setCheckedKeys] = useState({});
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [demandForm] = Form.useForm();

    // 初始化需求生成任务的 Prompt
    const initialGenerateTaskPrompt = useMemo(() => {
        if (
            localStorage?.getItem('docGenerateTaskKey') !== null &&
            docPromptList.find((item) => item.id === localStorage?.getItem('docGenerateTaskKey'))
        ) {
            return localStorage?.getItem('docGenerateTaskKey');
        } else {
            return docPromptList.filter((item) => item.type === promptActiveKey)[0]?.id;
        }
    }, [docPromptList, demandForm]);

    // 初始化卡片 id
    useEffect(() => setNodeLink(icafeId), [icafeId]);

    useImperativeHandle(
        ref,
        () => ({
            handleSubmit: handleSubmit, // 提交表单（数据封装为 docTree）
            handleSubmitByTRD: handleSubmitByTRD, // 提交表单 （数据封装为 bos链接）
            handleGetICafeDetail: handleGetICafeDetail // 获取 iCafe 层级关系及卡片详情
        }),
        [handleGetICafeDetail, handleSubmit, nodeLink, checkedKeys, isGetIcafeDetail]
    );

    const handleSubmitByTRD = () => {
        return new Promise((resolve, reject) => {
            demandForm
                ?.validateFields()
                .then(async (values) => {
                    try {
                        let tree = [];
                        // docForm = 1 icafe
                        if (values?.docForm === 1) {
                            // 封装数据
                            tree = await getDocContent(nodeLink, kuSDK);
                        }
                        // docForm = 2 知识库
                        if (values?.docForm === 2) {
                        }
                        resolve({
                            docNodeTreeList: tree
                        });
                    } catch (err) {
                        console.log(err);
                        reject([]);
                    }
                })
                .catch((err) => {
                    console.log(err?.message ?? err);
                    reject([]);
                    message.warning('请填写完整表单');
                });
        });
    };

    // 提交表单，1. 获取选中卡片详情 2. 校验表单 3. 返回选中卡片详情
    const handleSubmit = () => {
        return new Promise((resolve, reject) => {
            demandForm
                ?.validateFields()
                .then(async (values) => {
                    try {
                        let tree = [];
                        // docForm = 1 icafe
                        if (values?.docForm === 1) {
                            // 所勾选卡片（直接提交则无勾选 isGetIcafeDetail=false）
                            let icafeCheckedKeys = checkedKeys;
                            let icafeTreeWithDetail = [...docTree];
                            // 未获取卡片详情，则在提交的时候获取下
                            if (!isGetIcafeDetail) {
                                let res = await handleGetICafeDetail(1, true, [nodeLink], nodeLink);
                                icafeTreeWithDetail = res?.icafeTreeWithDetail;
                                icafeCheckedKeys = res?.checkedKeys;
                            }
                            // 生成树结构
                            tree = await getDocNodeTree(
                                icafeTreeWithDetail,
                                icafeCheckedKeys.checked ?? null
                            );
                            if (isEmpty(tree)) {
                                message.warning('上传 bos 失败');
                                return;
                            }
                        }
                        // docForm = 2 知识库
                        if (values?.docForm === 2) {
                            if (isEmpty(kuSDK)) {
                                message.error('知识库 SDK 获取失败');
                            }
                            try {
                                let docInfo = getDocInfoFromUrl(nodeLink);
                                let doc = await kuSDK?.getDocContent({
                                    spaceGuid: docInfo.spaceGuid,
                                    groupGuid: docInfo.groupGuid,
                                    repositoryGuid: docInfo.repositoryGuid,
                                    docGuid: docInfo.docGuid,
                                    type: 'json',
                                    brCharacterInTable: ' '
                                });
                                let content = await uploadContent(doc?.content?.children ?? []);
                                tree = [
                                    {
                                        nodeName:
                                            doc?.content?.children?.find(
                                                (item) => item.type === 'title'
                                            )?.children?.[0]?.text ?? nodeLink,
                                        nodeLink: nodeLink,
                                        nodeType: 2,
                                        nodeContent: content,
                                        initGenerateTask: true
                                    }
                                ];
                            } catch (err) {
                                message.error('获取知识库文档失败：' + (err?.message ?? err));
                                tree = [
                                    {
                                        nodeName: nodeLink,
                                        nodeLink: nodeLink,
                                        nodeType: 2,
                                        nodeContent: '',
                                        initGenerateTask: true
                                    }
                                ];
                            }
                        }
                        // 返回树结构及生成任务 prompt
                        localStorage.setItem('docGenerateTaskKey', values?.generateTaskKey);
                        resolve({
                            docNodeTreeList: tree,
                            generateTaskKey: values?.generateTaskKey
                        });
                    } catch (err) {
                        console.log(err);
                        reject([]);
                    }
                })
                .catch((err) => {
                    console.log(err?.message ?? err);
                    reject([]);
                    message.warning('请填写完整表单');
                });
        });
    };

    // 更多层级卡片
    const handleMenuClick = async (e) => {
        let keys = checkedKeys?.checked || [];
        await handleGetICafeDetail(e.key, true, [...keys, nodeLink], nodeLink);
    };

    // 获取 iCafe 层级关系及卡片详情; key: 层级类型，子卡片、父卡片、兄弟卡片; tiers: 是否获取层级关系; icafeLink: 卡片链接
    const handleGetICafeDetail = async (key, tiers = true, icafeLink = [], link = nodeLink) => {
        try {
            let tree = docTree;
            setDisabled(true);
            // 获取层级关系
            if (tiers) {
                const { icafeInfoList } = await getIcafeRelation({
                    icafeLink: link,
                    scopeTypeList: [key]
                });
                // 拼接新数据到树形结构
                tree = !isEmpty(icafeInfoList)
                    ? insertNodeLink(tree, icafeInfoList, +key, link)
                    : [{ icafeLink: link }];
            }
            let icafeLinkList = getIcafeLinkInfo(tree, +key, null, nodeLink);
            let newIcafeLinkList = [link];
            if (icafeLinkList.length > 20) {
                message.warning('当前卡相关片数量过多，目前仅支持 20 个卡片');
                for (let _link of icafeLinkList) {
                    if (!newIcafeLinkList.includes(_link) && newIcafeLinkList.length < 20) {
                        newIcafeLinkList.push(_link);
                    }
                }
            } else {
                newIcafeLinkList = [link, ...icafeLinkList];
            }
            // qps 限制，获取卡片详情
            let icafeDetailWithKu = [];
            if (!isEmpty(icafeLink) || !isEmpty(checkedKeys?.checked)) {
                const _icafeLink = getValidIcafeLink(icafeLink, checkedKeys, newIcafeLinkList);
                if (_icafeLink?.length) {
                    let icafeDetail = await getIcafeRelationDetail({
                        icafeLinkList: Array.from(
                            new Set(_icafeLink.filter((item) => !item.includes('http')))
                        ),
                        scopeTypeList: [1, 2]
                    });
                    for (let item in icafeDetail) {
                        let detail = icafeDetail[item].detail;
                        // 非 html 格式 => html 格式
                        if (!detail?.startsWith('<')) {
                            detail = escape2Html(detail);
                        }
                        // 转 json 格式
                        let jsonContent = htmlDeserialize(detail);
                        icafeDetail[item].detail = jsonContent;
                    }
                    icafeDetailWithKu = await getIcafeDetailWithKuDetail(icafeDetail ?? {}, kuSDK);
                }
            }
            setCheckedKeys({
                checked: [
                    link,
                    ...icafeLink,
                    ...(checkedKeys?.checked ?? []),
                    ...(icafeDetailWithKu[link]?.icafeKuList?.map(
                        (item) => link + '_' + item.icafeLink
                    ) ?? []),
                    ...(getAllIcafeLink(icafeDetailWithKu) ?? [])
                ]
            });
            const uniqueExpandedKeys = Array.from(new Set([...expandedKeys, ...icafeLink])).filter(
                (key) => !key.includes('https')
            );
            setExpandedKeys(uniqueExpandedKeys);
            // 组合卡片树形结构
            let icafeTreeWithDetail = updateTreeNode(
                tree,
                Object.keys(icafeDetailWithKu),
                icafeDetailWithKu,
                nodeLink
            );
            setIsGetIcafeDetail(true);
            setDocTree(icafeTreeWithDetail);
            return {
                icafeTreeWithDetail,
                checkedKeys: {
                    checked: [
                        link,
                        ...(icafeDetailWithKu[link]?.icafeKuList?.map(
                            (item) => link + '_' + item.icafeLink
                        ) ?? [])
                    ]
                }
            };
        } catch (err) {
            console.log(err);
        } finally {
            setDisabled(false);
        }
    };

    // 勾选/取消勾选
    const handleChangeCheckedKeys = (checkedKey, { checked, node }) => {
        try {
            // Ku 勾选，其卡片也要勾选
            if (
                checked &&
                node?.icafeCardType === 'Ku' &&
                !checkedKey?.checked?.includes(node?.parentLink)
            ) {
                checkedKey.checked = [...checkedKey.checked, node?.parentLink];
            }
            // 取消勾选，其子卡片也要取消勾选
            if (!checked) {
                // 获取孩子节点信息
                let list = getChildIcafeList([node]);
                let newCheckedKey = [];
                for (let item of checkedKey.checked) {
                    if (!list.includes(item) && !newCheckedKey?.includes(item)) {
                        newCheckedKey.push(item);
                    }
                }
                checkedKey.checked = newCheckedKey;
            }
            setCheckedKeys(checkedKey);
            if (checked && node && !/^https?:\/\//.test(node?.icafeLink)) {
                handleGetICafeDetail(null, false, [node?.icafeLink], nodeLink);
            }
        } catch (err) {
            console.log(err?.message ?? err);
        }
    };

    // 展开/折叠
    const onExpand = (newExpandedKeys) => {
        const filteredKeys = newExpandedKeys.filter((key) => !key.includes('https'));
        setExpandedKeys([...filteredKeys]);
    };

    // 清空数据
    const onClear = () => {
        setDocTree([]);
        setCheckedKeys(null);
    };

    return (
        <>
            {showTitle && <CardTitle text="需求绑定" />}
            <Form
                form={demandForm}
                labelCol={labelCol}
                colon={showColon ?? false}
                requiredMark={showRequired ?? false}
            >
                <Form.Item name="docForm" {...FORM_CONFIG.docForm}>
                    <Select
                        disabled={disabledItems?.docForm ?? false}
                        options={[
                            { label: 'iCafe', value: 1 },
                            { label: '知识库 (Ku)', value: 2 }
                        ]}
                        placeholder="请选择需求来源"
                    />
                </Form.Item>
                <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) =>
                        prevValues.docForm !== currentValues.docForm
                    }
                >
                    {({ getFieldValue }) => {
                        return getFieldValue('docForm') === 1 ? (
                            <>
                                <Form.Item
                                    name="nodeLink"
                                    initialValue={nodeLink || ''}
                                    {...FORM_CONFIG.nodeLink}
                                >
                                    <Input
                                        placeholder="请输入 iCafe 卡片 ID"
                                        allowClear
                                        disabled={disabledItems?.nodeLink ?? false}
                                        onChange={(e) => {
                                            // 更新清空所有数据
                                            onClear();
                                            setNodeLink(e.target.value);
                                        }}
                                        suffix={
                                            <div>
                                                {nodeLink && nodeLink !== '' && (
                                                    <div className={styles.moreWrap}>
                                                        {docTree.length > 0 && (
                                                            <Checkbox
                                                                size="small"
                                                                className={styles.checkbox}
                                                                disabled={disabled}
                                                                onChange={(e) => {
                                                                    let value = e.target.checked;
                                                                    if (value) {
                                                                        setCheckedKeys({
                                                                            checked:
                                                                                getChildIcafeList(
                                                                                    docTree,
                                                                                    false
                                                                                )
                                                                        });
                                                                        let allList =
                                                                            getIcafeLinkList(
                                                                                docTree
                                                                            ) || [];
                                                                        let icafeList =
                                                                            allList.filter(
                                                                                (key) =>
                                                                                    !key.includes(
                                                                                        'https'
                                                                                    )
                                                                            );
                                                                        handleGetICafeDetail(
                                                                            null,
                                                                            false,
                                                                            icafeList,
                                                                            nodeLink
                                                                        );
                                                                    } else {
                                                                        let list = findKuKeys(
                                                                            nodeLink,
                                                                            docTree
                                                                        );
                                                                        // 取消全选
                                                                        // 保留 key 为 nodeLink 和它的知识库节点
                                                                        let newCheckedKey = [
                                                                            ...list,
                                                                            nodeLink
                                                                        ];
                                                                        setCheckedKeys({
                                                                            checked: newCheckedKey
                                                                        });
                                                                    }
                                                                }}
                                                            >
                                                                全选
                                                            </Checkbox>
                                                        )}
                                                        {showLevel && !isEmpty(nodeLink) && (
                                                            <Dropdown
                                                                menu={{
                                                                    items: NODE_TYPE_ITEMS,
                                                                    onClick: (e) =>
                                                                        handleMenuClick(e)
                                                                }}
                                                                arrow
                                                                placement="bottom"
                                                                trigger={['click']}
                                                            >
                                                                <a className={styles.moreBtn}>
                                                                    获取节点层级
                                                                </a>
                                                            </Dropdown>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        }
                                    />
                                </Form.Item>
                                {docTree.length > 0 && (
                                    <Form.Item name="docNodeTree">
                                        <Tree
                                            checkable
                                            checkStrictly
                                            defaultExpandAll
                                            treeData={docTree}
                                            onExpand={onExpand}
                                            expandedKeys={expandedKeys}
                                            showIcon
                                            fieldNames={{ key: 'icafeKey' }}
                                            checkedKeys={checkedKeys}
                                            onCheck={handleChangeCheckedKeys}
                                            className={styles.docNodeTree}
                                            titleRender={(node) => {
                                                return (
                                                    <div className={styles.icafeOption}>
                                                        <IcafeIcon node={node} />
                                                        {node.icafeCardType !== 'Ku' && (
                                                            <span className={styles.link}>
                                                                {node.icafeLink}
                                                            </span>
                                                        )}
                                                        <span className={styles.icafelink}>
                                                            <a
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                                href={
                                                                    node.icafeCardType === 'Ku'
                                                                        ? node.icafeLink
                                                                        : 'https://console.cloud.baidu-int.com/devops/icafe/issue/' +
                                                                          node.icafeLink +
                                                                          '/show?source=copy-shortcut'
                                                                }
                                                            >
                                                                {node.title}
                                                            </a>
                                                        </span>
                                                    </div>
                                                );
                                            }}
                                        />
                                    </Form.Item>
                                )}
                            </>
                        ) : (
                            <Form.Item name="kuLink" {...FORM_CONFIG.kuLink}>
                                <Input
                                    placeholder="请输入知识库链接"
                                    onChange={(e) => {
                                        onClear();
                                        setNodeLink(e.target.value);
                                    }}
                                />
                            </Form.Item>
                        );
                    }}
                </Form.Item>
                {showTitle && <CardTitle text="用例生成配置" />}
                {showGenerateTaskKey && (
                    <Form.Item
                        label="生成策略"
                        name="generateTaskKey"
                        initialValue={initialGenerateTaskPrompt}
                        {...FORM_CONFIG.generateTaskKey}
                    >
                        <Select
                            placeholder="请选择生成策略"
                            fieldNames={{ label: 'name', value: 'id' }}
                            options={
                                docPromptList?.filter((item) => item.type === promptActiveKey) || []
                            }
                            icon={<CaretDownOutlined />}
                            notFoundContent={<NoContent text="暂无数据" />}
                            placement="bottomRight"
                            dropdownRender={(menu) => {
                                return (
                                    <div style={{ height: 320 }}>
                                        <Tabs
                                            centered
                                            activeKey={promptActiveKey}
                                            onChange={setPromptActiveKey}
                                        >
                                            <Tabs.TabPane tab="通用" key="strategy">
                                                {menu}
                                            </Tabs.TabPane>
                                        </Tabs>
                                    </div>
                                );
                            }}
                        />
                    </Form.Item>
                )}
            </Form>
        </>
    );
};

export default connectModel([baseModel], (state) => ({
    docPromptList: state.common.base.docPromptList,
    kuSDK: state.common.base.kuSDK
}))(forwardRef(DemandBind));
