// 卡片类型
export const NODE_TYPE_ITEMS = [
    {
        label: '子卡片',
        key: 1
    },
    {
        label: '父卡片',
        key: 2
    },
    {
        label: '同级卡片',
        key: 3
    }
];

// 表单规则
export const FORM_CONFIG = {
    docForm: {
        label: '需求来源',
        initialValue: 1,
        rules: [
            {
                required: true,
                message: '请选择需求来源'
            }
        ]
    },
    nodeLink: {
        label: 'iCafe 卡片 ID',
        rules: [
            {
                required: true,
                message: '请输入 iCafe 卡片 ID'
            }
        ]
    },
    kuLink: {
        label: '知识库链接',
        validateTrigger: ['onBlur'],
        rules: [
            {
                validator: async (_, value) => {
                    if (!value) {
                        // 如果值为空，不进行 URL 检查
                        return Promise.reject('请输入知识库链接');
                    }
                    try {
                        new URL(value);
                    } catch (err) {
                        return Promise.reject('有效地址（http://xxx 或 https://xxx）');
                    }
                }
            }
        ]
    },
    generateTaskKey: {
        initialValue: localStorage.getItem('docGenerateTaskKey') ?? 'DOCUMENT_GENERAL_GENERATOR',
        rules: [
            {
                required: true,
                message: '请选择生成策略'
            }
        ]
    }
};
