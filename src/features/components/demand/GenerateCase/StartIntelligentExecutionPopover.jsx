import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'umi';
import { Form, Button, Tabs, message, Popconfirm, Modal, Tooltip, Switch, Checkbox } from 'antd';
import { stringifyUrl } from 'query-string';

import { createUIGenerateTask, getUIGenerateTaskConfig } from 'COMMON/api/front_qe_tools/generate';
import { getTreeNodeKnowledge } from 'COMMON/api/front_qe_tools/knowledge';
import { getDevicePoolList } from 'COMMON/api/front_qe_tools/device';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import demandModel from 'COMMON/models/demandModel';
import { getQueryParams } from 'COMMON/utils/utils';
import { findLeafNodesByIds } from 'COMMON/utils/treeUtils';
import { getAppList, getEnvList } from 'COMMON/api/front_qe_tools/config';
import { getUIGenerateTaskDetail } from 'COMMON/api/front_qe_tools/generate';
import { PlatformConfig } from './components';
import { getPlatformConfig } from './utils';
import styles from './StartIntelligentExecutionPopover.module.less';

const { TabPane } = Tabs;

const StartIntelligentExecutionPopover = (props) => {
    const {
        visible,
        onCancel,
        children,
        currentSpace,
        type = 'execute',
        caseNodeIdList = [],
        hasExecutionTask = false,
        caseConfig,
        currentGroup,
        setExecutePlanDetail,
        setIsExecutingTask,
        currentDoc,
        currentCase,
        caseType,
        editor,
        curOsType: propCurOsType
    } = props;
    const [form] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    const [loading, setLoading] = useState(false);
    const [androidDeviceList, setAndroidDeviceList] = useState([]);
    const [iosDeviceList, setIosDeviceList] = useState([]);
    const [androidAutoKnowledgeList, setAndroidAutoKnowledgeList] = useState([]);
    const [androidScenarioKnowledgeList, setAndroidScenarioKnowledgeList] = useState([]);
    const [iosAutoKnowledgeList, setIosAutoKnowledgeList] = useState([]);
    const [iosScenarioKnowledgeList, setIosScenarioKnowledgeList] = useState([]);
    // const [loading, setLoading] = useState(false);
    const [statusLoading, setStatusLoading] = useState(true);
    const [activeTab, setActiveTab] = useState('android'); // 设置默认值
    // const [isDebug, setIsDebug] = useState(false);
    const [androidIsDebug, setAndroidIsDebug] = useState(false);
    const [iosIsDebug, setIosIsDebug] = useState(false);
    const [androidNoConfig, setAndroidNoConfig] = useState(false);
    const [iosNoConfig, setIosNoConfig] = useState(false);
    const navigate = useNavigate();
    const location = useLocation();
    const query = getQueryParams();
    const getPlatformPrefix = (osType) => {
        return osType === 1 ? 'android' : 'ios';
    };

    // 根据用例类型选择不同的osType
    const curOsType = caseType === 'edit' && propCurOsType ? propCurOsType : caseConfig?.osType;
    // 获取app列表
    const [androidAppList, setAndroidAppList] = useState([]);
    const [iosAppList, setIosAppList] = useState([]);

    // 添加执行状态检查
    const [executionStatus, setExecutionStatus] = useState({
        android: false,
        ios: false
    });
    const [androidEnvList, setAndroidEnvList] = useState([]);
    const [iosEnvList, setIosEnvList] = useState([]);

    // caseRootId
    const getCaseRootId = () => {
        let caseRootId = null;
        if (caseType === 'intelligent' && currentDoc?.caseNodeId) {
            caseRootId = currentDoc.caseNodeId;
        } else if (caseType === 'edit') {
            if (currentCase?.caseRootId) {
                caseRootId = currentCase.caseRootId;
            } else if (currentGroup?.caseRootId) {
                caseRootId = currentGroup.caseRootId;
            }
        }
        return caseRootId;
    };

    // 检查执行状态
    useEffect(() => {
        const checkExecutionStatus = async () => {
            const caseRootId = getCaseRootId();
            if (!caseRootId || !visible) {
                setStatusLoading(false);
                return;
            }

            setStatusLoading(true);
            try {
                // 双端，检查两端状态
                if (curOsType === 3) {
                    const [androidResult, iosResult] = await Promise.all([
                        getUIGenerateTaskDetail({
                            caseRootId: caseRootId,
                            osType: 1
                        }),
                        getUIGenerateTaskDetail({
                            caseRootId: caseRootId,
                            osType: 2
                        })
                    ]);
                    setExecutionStatus({
                        android: [0, 1].includes(androidResult?.planStatus),
                        ios: [0, 1].includes(iosResult?.planStatus)
                    });
                }
                // 单端Android
                else if (curOsType === 1 && caseType === 'edit') {
                    const androidResult = await getUIGenerateTaskDetail({
                        caseRootId: caseRootId,
                        osType: 1
                    });
                    setExecutionStatus({
                        ...executionStatus,
                        android: [0, 1].includes(androidResult?.planStatus)
                    });
                }
                // 单端iOS
                else if (curOsType === 2 && caseType === 'edit') {
                    const iosResult = await getUIGenerateTaskDetail({
                        caseRootId: caseRootId,
                        osType: 2
                    });
                    setExecutionStatus({
                        ...executionStatus,
                        ios: [0, 1].includes(iosResult?.planStatus)
                    });
                }
            } finally {
                setStatusLoading(false);
            }
        };

        checkExecutionStatus();
    }, [currentDoc?.caseNodeId, currentCase?.caseRootId, curOsType, visible, caseType]);

    // 获取APP列表数据
    useEffect(() => {
        const fetchData = async () => {
            if (!visible || !currentSpace?.id) {
                return;
            }

            try {
                const fetchPromises = [];

                // 知识库加载状态
                setLoading(true);

                // 定义获取知识库的函数
                const fetchPlatformKnowledge = async (osType) => {
                    const autoRes = await getTreeNodeKnowledge({
                        moduleId: currentSpace?.id,
                        osType,
                        knowledgeType: 1
                    });

                    const scenarioRes = await getTreeNodeKnowledge({
                        moduleId: currentSpace?.id,
                        osType,
                        knowledgeType: 2
                    });

                    const autoOptions =
                        autoRes?.knowledgeTreeNodeList?.map((item) => ({
                            label: item?.nodeName,
                            value: item?.caseRootId
                        })) || [];

                    const scenarioOptions =
                        scenarioRes?.knowledgeTreeNodeList?.map((item) => ({
                            label: item?.nodeName,
                            value: item?.caseRootId
                        })) || [];

                    return { autoOptions, scenarioOptions };
                };

                // Android 数据
                if (curOsType === 1 || curOsType === 3) {
                    // 获取 Android APP 列表
                    fetchPromises.push(
                        getAppList({
                            moduleId: currentSpace?.id,
                            osType: 1
                        })
                    );

                    // 获取 Android 设备池列表
                    fetchPromises.push(
                        getDevicePoolList({
                            moduleId: currentSpace?.id,
                            poolType: 1
                        })
                    );

                    // 获取 Android 环境列表
                    fetchPromises.push(
                        getEnvList({
                            moduleId: currentSpace?.id,
                            osType: 1
                        })
                    );

                    // 获取 Android 知识库列表
                    fetchPromises.push(fetchPlatformKnowledge(1));
                }

                // iOS 数据
                if (curOsType === 2 || curOsType === 3) {
                    // 获取 iOS APP 列表
                    fetchPromises.push(
                        getAppList({
                            moduleId: currentSpace?.id,
                            osType: 2
                        })
                    );

                    // 获取 iOS 设备池列表
                    fetchPromises.push(
                        getDevicePoolList({
                            moduleId: currentSpace?.id,
                            poolType: 2
                        })
                    );

                    // 获取 iOS 环境列表
                    fetchPromises.push(
                        getEnvList({
                            moduleId: currentSpace?.id,
                            osType: 2
                        })
                    );

                    // 获取 iOS 知识库列表
                    fetchPromises.push(fetchPlatformKnowledge(2));
                }

                const res = await Promise.all(fetchPromises);
                if (curOsType === 1 || curOsType === 3) {
                    setAndroidAppList(res[0]?.appList || []);
                    setAndroidDeviceList(res[1]?.poolList || []);
                    setAndroidEnvList(res[2]?.envList || []);
                    setAndroidAutoKnowledgeList(res[3]?.autoOptions || []);
                    setAndroidScenarioKnowledgeList(res[3]?.scenarioOptions || []);
                }
                if (curOsType === 2 || curOsType === 3) {
                    let iosStartIndex = curOsType === 2 ? 0 : 4;
                    setIosAppList(res[iosStartIndex]?.appList || []);
                    setIosDeviceList(res[iosStartIndex + 1]?.poolList || []);
                    setIosEnvList(res[iosStartIndex + 2]?.envList || []);
                    setIosAutoKnowledgeList(res[iosStartIndex + 3]?.autoOptions || []);
                    setIosScenarioKnowledgeList(res[iosStartIndex + 3]?.scenarioOptions || []);
                }
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [visible, currentSpace?.id, curOsType, messageApi]);

    useEffect(() => {
        if (caseConfig?.osType === 3) {
            if (!executionStatus?.android) {
                setActiveTab('android');
            } else if (!executionStatus?.ios) {
                setActiveTab('ios');
            }
        } else if (caseConfig?.osType === 1) {
            setActiveTab('android');
        } else if (caseConfig?.osType === 2) {
            setActiveTab('ios');
        }
    }, [caseConfig?.osType, executionStatus]);

    // 获取默认配置
    useEffect(() => {
        const fetchDefaultConfig = async () => {
            const caseRootId = getCaseRootId();
            if (!visible || !caseRootId) {
                return;
            }

            const setFormValuesForPlatform = (config, platform) => {
                if (!config) {
                    return;
                }
                const prefix = getPlatformPrefix(platform);
                const agentParams = config.agentParams || {};
                const formValues = {
                    [`${prefix}AgentName`]: agentParams.agentName,
                    [`${prefix}CloudDevice`]: config.poolId || undefined,
                    [`${prefix}AppName`]: config.appId,
                    [`${prefix}AutoKbIdList`]: agentParams.autoKbIdList,
                    [`${prefix}SituationalKbIdList`]: agentParams.situationalKbIdList,
                    [`${prefix}EnvId`]: agentParams.envId,
                    [prefix]: config.installParams,
                    [`${prefix}maxSteps`]: agentParams.maxSteps,
                    [`${prefix}retry`]: agentParams.retry
                };
                form.setFieldsValue(formValues);

                if (platform === 1) {
                    setAndroidIsDebug(config.isDebug ?? false);
                } else {
                    setIosIsDebug(config.isDebug ?? false);
                }
            };

            if (curOsType === 3) {
                const [androidConfig, iosConfig] = await Promise.all([
                    getUIGenerateTaskConfig({
                        caseRootId: caseRootId,
                        osType: 1
                    }),
                    getUIGenerateTaskConfig({
                        caseRootId: caseRootId,
                        osType: 2
                    })
                ]);

                setFormValuesForPlatform(androidConfig, 1);
                setFormValuesForPlatform(iosConfig, 2);
            } else {
                // 单端
                const config = await getUIGenerateTaskConfig({
                    caseRootId: caseRootId,
                    osType: curOsType
                });

                setFormValuesForPlatform(config, curOsType);
            }
        };

        fetchDefaultConfig();
    }, [visible, curOsType, form, caseType, currentDoc?.caseNodeId, currentCase?.caseRootId]);

    const createExecutionTask = () => {
        // 执行状态检查
        if (caseType === 'edit') {
            if (
                (curOsType === 1 && executionStatus.android) ||
                (curOsType === 2 && executionStatus.ios)
            ) {
                messageApi.error('当前端正在执行中，请等待执行完成或取消执行后再试');
                return;
            }
        }

        // 双端执行状态检查
        if (curOsType === 3 && executionStatus?.android && executionStatus?.ios) {
            messageApi.error('当前两端都在执行中，请等待执行完成或取消执行后再试');
            return;
        }

        // 根据type决定是使用传入的caseNodeIdList还是新建空数组
        let nodeCaseNodeIdList = type === 'reexecute' ? caseNodeIdList : [];
        if (editor) {
            // 获取编辑器中最新的脑图数据
            let latestMindData = editor.minder.getRoot();
            nodeCaseNodeIdList = findLeafNodesByIds(nodeCaseNodeIdList, [latestMindData]);
        }
        setLoading(true);

        // 双端配置时，检查是否至少有一端需要配置
        if (curOsType === 3 && androidNoConfig && iosNoConfig) {
            messageApi.error('请至少配置 Android 或 iOS 其中一项');
            setLoading(false);
            return;
        }

        if (curOsType === 3) {
            if (androidNoConfig) {
                form.setFieldsValue({
                    androidCloudDevice: undefined,
                    androidAppName: undefined,
                    androidEnvId: undefined,
                    androidAutoKbIdList: undefined,
                    androidSituationalKbIdList: undefined,
                    android: undefined
                });
            }

            if (iosNoConfig) {
                form.setFieldsValue({
                    iosCloudDevice: undefined,
                    iosAppName: undefined,
                    iosEnvId: undefined,
                    iosAutoKbIdList: undefined,
                    iosSituationalKbIdList: undefined,
                    ios: undefined
                });
            }
        }

        form.validateFields()
            .then((values) => {
                const caseRootId = getCaseRootId();
                if (!caseRootId) {
                    messageApi.error('无法获取用例根节点ID');
                    setLoading(false);
                    return Promise.reject('无法获取用例根节点ID');
                }

                values.currentGroupType = +currentGroup?.groupType;
                values.caseType = caseType;

                const uiTestConfig = [];

                // 构建配置数组，根据osType添加相应配置
                if (curOsType === 1) {
                    uiTestConfig.push(getPlatformConfig(values, 1, androidAppList));
                } else if (curOsType === 2) {
                    uiTestConfig.push(getPlatformConfig(values, 2, iosAppList));
                } else if (curOsType === 3) {
                    if (!androidNoConfig) {
                        uiTestConfig.push(getPlatformConfig(values, 1, androidAppList));
                    }

                    if (!iosNoConfig) {
                        uiTestConfig.push(getPlatformConfig(values, 2, iosAppList));
                    }
                }

                const params = {
                    caseRootId: caseRootId,
                    caseNodeIdList: nodeCaseNodeIdList,
                    isDebug:
                        curOsType === 3
                            ? activeTab === 'android'
                                ? androidIsDebug
                                : iosIsDebug
                            : curOsType === 1
                            ? androidIsDebug
                            : iosIsDebug,

                    uiTestConfig
                };

                // 创建任务
                return createUIGenerateTask(params)
                    .then(() => {
                        // 任务创建成功，获取任务详情
                        messageApi.success(
                            `智能${type === 'reexecute' ? '重新执行' : '执行'}任务创建成功`
                        );
                        return getUIGenerateTaskDetail({
                            caseRootId: caseRootId,
                            osType: propCurOsType
                        });
                    })
                    .then((result) => {
                        // 更新UI状态
                        setExecutePlanDetail(result);
                        // 设置执行状态为true
                        if (caseType === 'edit') {
                            setIsExecutingTask && setIsExecutingTask(true);
                        }
                        // 切换到第三步（智能执行）
                        navigate(
                            stringifyUrl({
                                url: location.pathname,
                                query: {
                                    ...query,
                                    step: 2 // 设置步骤为第三步（索引为2）
                                }
                            })
                        );

                        onCancel();
                    });
            })
            .catch((errorInfo) => {
                console.error('表单验证或提交失败:', errorInfo);
                if (errorInfo?.errorFields?.length > 0) {
                    messageApi.error('表单填写不完整，请检查必填项');
                }
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const handleSubmit = async () => {
        // 如果是edit 并且当前端正在执行，不允许再次执行
        if (caseType === 'edit') {
            if (
                (curOsType === 1 && executionStatus.android) ||
                (curOsType === 2 && executionStatus.ios)
            ) {
                onCancel();
                return;
            }
        }
        // 如果是重新执行且有执行任务，不需要提示
        if (type === 'reexecute' && hasExecutionTask) {
            // 此处不执行任何操作，通过下方Popconfirm组件处理
            return;
        } else {
            // 其他情况直接创建任务
            await createExecutionTask();
        }
    };

    const renderContent = () => {
        // 根据 ostype 渲染
        const renderPlatformConfig = (platform) => {
            const isAndroidPlatform = platform === 1;
            const noConfig = isAndroidPlatform ? androidNoConfig : iosNoConfig;
            const setNoConfig = isAndroidPlatform ? setAndroidNoConfig : setIosNoConfig;

            return (
                <div className={styles.platformConfigContainer}>
                    {caseType === 'intelligent' && curOsType === 3 && (
                        <div className={styles.noConfigContainer}>
                            <span className={styles.noConfigSwitch}>无需配置</span>
                            <Checkbox
                                checked={noConfig}
                                onChange={(e) => setNoConfig(e.target.checked)}
                            />
                            {noConfig && (
                                <div className={styles.noConfigTip}>
                                    已设置为无需配置，该端将不会执行
                                </div>
                            )}
                        </div>
                    )}
                    {(!noConfig || caseType !== 'intelligent' || curOsType !== 3) && (
                        <PlatformConfig
                            form={form}
                            platform={platform}
                            cloudDeviceList={isAndroidPlatform ? androidDeviceList : iosDeviceList}
                            appList={isAndroidPlatform ? androidAppList : iosAppList}
                            autoKnowledgeList={
                                isAndroidPlatform ? androidAutoKnowledgeList : iosAutoKnowledgeList
                            }
                            scenarioKnowledgeList={
                                isAndroidPlatform
                                    ? androidScenarioKnowledgeList
                                    : iosScenarioKnowledgeList
                            }
                            envList={isAndroidPlatform ? androidEnvList : iosEnvList}
                            loading={loading}
                        />
                    )}
                </div>
            );
        };

        //执行中 提示
        const renderUnavailableConfig = () => (
            <div className={styles.noAvailableConfig}>
                <p>当前端正在执行中，请等待执行完成或取消执行后再试</p>
            </div>
        );

        // edit 模式下根据curOsType直接显示对应端的配置
        if (caseType === 'edit') {
            // 检查当前端是否正在执行中
            if (curOsType === 1 && executionStatus.android) {
                return renderUnavailableConfig();
            } else if (curOsType === 2 && executionStatus.ios) {
                return renderUnavailableConfig();
            }

            if (curOsType === 1) {
                return renderPlatformConfig(1);
            } else if (curOsType === 2) {
                return renderPlatformConfig(2);
            }
        }

        // 非edit模式下保持原有逻辑
        if (curOsType === 3) {
            // 双端 始终显示tab，但对执行中的设置为禁用
            const androidTabDisabled = executionStatus.android;
            const iosTabDisabled = executionStatus.ios;

            // 生成提示信息
            const androidTabTitle = (
                <Tooltip
                    title={androidTabDisabled ? '当前正在执行中，请取消执行后再操作' : ''}
                    placement="top"
                    visible={androidTabDisabled ? undefined : false}
                >
                    <span className={androidTabDisabled ? styles.disabledTab : ''}>
                        Android 设备池
                    </span>
                </Tooltip>
            );

            const iosTabTitle = (
                <Tooltip
                    title={iosTabDisabled ? '当前正在执行中，请取消执行后再操作' : ''}
                    placement="top"
                    visible={iosTabDisabled ? undefined : false}
                >
                    <span className={iosTabDisabled ? styles.disabledTab : ''}>iOS 设备池</span>
                </Tooltip>
            );

            // 如果两端都在执行中，提示
            const androidTabContent = androidTabDisabled
                ? renderUnavailableConfig()
                : renderPlatformConfig(1);

            const iosTabContent = iosTabDisabled
                ? renderUnavailableConfig()
                : renderPlatformConfig(2);

            return (
                <Tabs
                    activeKey={activeTab}
                    onChange={(key) => {
                        // 只有未禁用的tab才能被选中
                        if (
                            (key === 'android' && !androidTabDisabled) ||
                            (key === 'ios' && !iosTabDisabled)
                        ) {
                            setActiveTab(key);
                        }
                    }}
                >
                    <TabPane
                        tab={androidTabTitle}
                        key="android"
                        disabled={androidTabDisabled}
                        forceRender
                    >
                        {androidTabContent}
                    </TabPane>
                    <TabPane tab={iosTabTitle} key="ios" disabled={iosTabDisabled} forceRender>
                        {iosTabContent}
                    </TabPane>
                </Tabs>
            );
        } else if (curOsType === 1 && !executionStatus.android) {
            return renderPlatformConfig(1);
        } else if (curOsType === 2 && !executionStatus.ios) {
            return renderPlatformConfig(2);
        }

        return null;
    };

    // 确认按钮，根据条件决定是否需要Popconfirm
    const confirmButton =
        type === 'reexecuteALL' && query?.step === '2' ? (
            <Popconfirm
                title="重新执行将删除已有生成节点和步骤，确定要重新生成吗？"
                onConfirm={createExecutionTask}
                okText="确定"
                cancelText="取消"
            >
                <Button type="primary" loading={loading}>
                    确认
                </Button>
            </Popconfirm>
        ) : (
            <Button type="primary" loading={loading} onClick={handleSubmit}>
                确认
            </Button>
        );

    const content = (
        <>
            {contextHolder}
            <div className={styles.popoverContent}>
                <Form
                    form={form}
                    colon={false}
                    initialValues={{}}
                    labelCol={{
                        span: 4
                    }}
                >
                    {renderContent()}
                </Form>
                <div className={styles.buttonContainer}>
                    <Button onClick={onCancel}>取消</Button>
                    {confirmButton}
                </div>
            </div>
        </>
    );

    return (
        <>
            {children}
            <Modal
                centered
                destroyOnClose
                title={
                    <div>
                        智能执行配置
                        <span className={styles.switchContainer}>
                            <Switch
                                checked={
                                    curOsType === 3
                                        ? activeTab === 'android'
                                            ? androidIsDebug
                                            : iosIsDebug
                                        : curOsType === 1
                                        ? androidIsDebug
                                        : iosIsDebug
                                }
                                onChange={(checked) => {
                                    curOsType === 3
                                        ? activeTab === 'android'
                                            ? setAndroidIsDebug(checked)
                                            : setIosIsDebug(checked)
                                        : curOsType === 1
                                        ? setAndroidIsDebug(checked)
                                        : setIosIsDebug(checked);
                                }}
                                checkedChildren="调试模式"
                                unCheckedChildren="普通模式"
                            />
                        </span>
                    </div>
                }
                open={visible}
                mask
                width={700}
                maskClosable={!statusLoading}
                arrow={false}
                onClose={onCancel}
                onCancel={onCancel}
                footer={null}
                closable={!statusLoading}
                style={{ pointerEvents: statusLoading ? 'none' : 'auto' }}
            >
                {content}
            </Modal>
        </>
    );
};

export default connectModel([baseModel, commonModel, demandModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentCase: state.common.case.currentCase,
    caseConfig: state.common.case.caseConfig,
    currentGroup: state.common.case?.currentGroup,
    executePlanDetail: state.common.case.executePlanDetail,
    currentDoc: state.demand.doc.currentDoc,
    curOsType: state.common.case.curOsType
}))(StartIntelligentExecutionPopover);
