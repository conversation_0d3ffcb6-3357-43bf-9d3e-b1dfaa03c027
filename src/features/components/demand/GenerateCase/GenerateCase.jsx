import { useEffect, useState, useMemo } from 'react';
import { isEmpty } from 'lodash';
import zlib from 'zlib';
import { Button, Layout, Spin, Input, Select, Tabs, message } from 'antd';
import { CaretDownOutlined, SearchOutlined } from '@ant-design/icons';
import NoContent from 'COMMON/components/common/NoContent';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import demandModel from 'COMMON/models/demandModel';
import commonModel from 'COMMON/models/commonModel';
import { getCaseTree } from 'COMMON/api/front_qe_tools/node';
import { FILTER_DATA, filterObject } from 'COMMON/utils/commonUtils';
import { getGenerateTaskDetail, createGenerateTask } from 'COMMON/api/front_qe_tools/demand';
import CaseMinderView from 'PACKAGES/react-kityminder-editor-v2/src';

import { filterConditionCaseNodeRecursive } from 'COMMON/utils/commonUtils';
import { getTips } from './const';
import styles from './GenerateCase.module.less';

const { Header, Content } = Layout;

function GenerateCase(props) {
    const {
        currentDoc,
        curOsType,
        docPromptList,
        caseConfig,
        setCurOsType,
        extraConfig = {
            showIntelligentExecution: false
        },
        rightExtra
    } = props;
    const [loading, setLoading] = useState(true);
    const [generate, setGenerate] = useState(true);
    const [currentPrompt, setCurrentPrompt] = useState(null);
    const [caseNode, setCaseNode] = useState(null);
    const [taskInfo, setTaskInfo] = useState({});
    const [promptActiveKey, setPromptActiveKey] = useState(
        docPromptList.find((item) => item.id === currentPrompt?.id)?.type ?? 'strategy'
    );
    const [searchPromptList, setSearchPromptList] = useState([]);
    // filter
    const [filterData, setFilterData] = useState(
        filterObject(FILTER_DATA, [
            'executeType',
            'stampResult',
            'autoStatus',
            'record',
            'relation',
            'disabled'
        ])
    );

    const [searchFilterCaseNode, setSearchFilterCaseNode] = useState([]);
    const [messageApi, contextHolder] = message.useMessage();

    useEffect(() => {
        setSearchPromptList(docPromptList);
    }, [docPromptList]);

    useEffect(() => {
        async function func() {
            try {
                if (currentDoc?.docNodeId) {
                    let res = await getGenerateTaskDetail({ caseNodeId: currentDoc?.caseNodeId });
                    setTaskInfo(res);
                    setGenerate([0, 2, 3].includes(res?.taskStatus));
                    setCurrentPrompt(
                        docPromptList.find(
                            (item) => item.id === res?.taskParams?.params?.generateTaskKey
                        )
                    );
                    if (![0, 2, 3].includes(res?.taskStatus)) {
                        getCaseNode();
                    } else {
                        setCaseNode(null);
                    }
                }
            } catch (error) {
                setGenerate(false);
                setCaseNode(null);
            }
        }
        func();
    }, [currentDoc?.docNodeId]);

    useEffect(() => {
        if (!generate) {
            return;
        }
        let timer = setInterval(() => {
            if (!generate) {
                clearInterval(timer);
            }
            getGenerateTaskDetail({ caseNodeId: currentDoc?.caseNodeId }).then((res) => {
                setTaskInfo(res);
                setGenerate([0, 2, 3].includes(res?.taskStatus));
                setCurrentPrompt(
                    docPromptList.find(
                        (item) => item.id === res?.taskParams?.params?.generateTaskKey
                    )
                );
                if (![0, 2, 3].includes(res?.taskStatus) || !generate) {
                    getCaseNode();
                    setGenerate(false);
                    clearInterval(timer);
                }
            });
        }, 5000);
        return () => clearInterval(timer);
    }, [generate, currentDoc?.caseNodeId, filterData]);

    const getCaseNode = async (data = filterData) => {
        setLoading(true);
        let tree = await getCaseTree({ caseRootId: currentDoc?.caseNodeId, withSign: false });
        setCaseNode(tree);
        getSearchFilterCaseNode(data, tree);
        setLoading(false);
    };

    const handleGenerateTask = async () => {
        try {
            if (!currentPrompt?.id) {
                messageApi.warning('请选择生成策略');
                return;
            }
            let params = {
                caseNodeId: currentDoc?.caseNodeId,
                taskType: 1,
                params: {
                    generateTaskKey: currentPrompt?.id
                }
            };
            if (currentDoc?.nodeContent?.includes('.knowledge')) {
                params.params.contentType = 'json';
            }
            // 创建生成任务
            await createGenerateTask(params);
            messageApi.success('任务创建成功');
            setGenerate(true);
        } catch (error) {}
    };

    const onChange = (e) => {
        setSearchPromptList(docPromptList.filter((item) => item.name.includes(e.target.value)));
    };

    const handleChangePrompt = (id) => {
        let p = docPromptList.find((item) => item.id === id);
        setCurrentPrompt(p);
    };

    const getSearchFilterCaseNode = (data = filterData, tree = caseNode) => {
        let _caseNodeWithCondition = filterConditionCaseNodeRecursive(
            tree === null ? [] : [tree],
            data
        );
        if (isEmpty(_caseNodeWithCondition)) {
            _caseNodeWithCondition = [
                {
                    ...tree,
                    children: []
                }
            ];
        }
        setSearchFilterCaseNode(_caseNodeWithCondition);
    };

    const RenderCaseMinder = useMemo(() => {
        return (
            <CaseMinderView
                key={`doc-${currentDoc?.docNodeId}-taskId-${taskInfo?.taskId}-osType-${curOsType}`}
                // mindData={convertLazyMindTreeToMindData(searchFilterCaseNode, [])[0]}
                mindData={searchFilterCaseNode}
                refreshMindData={getCaseNode}
                caseRootId={currentDoc?.caseNodeId}
                // 筛选参数
                filterData={filterData}
                caseNode={searchFilterCaseNode?.[0]}
                handleSearchFilterData={() => {
                    getCaseNode();
                }}
                handleFilterData={(value, obj, activeKey) => {
                    setFilterData({
                        ...filterData,
                        [obj]: {
                            activeKey: activeKey ?? filterData[obj]?.activeKey,
                            data: value
                        }
                    });
                }}
                clearFilterData={(data) => {
                    setFilterData(data);
                    setTimeout(() => {
                        getCaseNode(data);
                    }, 0);
                }}
                // 通用参数
                commonConfig={
                    extraConfig?.showIntelligentExecution
                        ? {
                              caseOsType: caseConfig?.osType,
                              osType: curOsType,
                              caseType: 'intelligent-generate',
                              signType: null,
                              readonly: false,
                              stepEditType: 'edit',
                              status: 'intelligent-generate'
                          }
                        : {
                              osType: curOsType,
                              caseType: 'generate',
                              signType: null,
                              readonly: false,
                              stepEditType: 'readonly',
                              status: 'generate'
                          }
                }
                {...(extraConfig?.showIntelligentExecution && {
                    updateCurOsType: (value) => {
                        setCurOsType(value);
                    }
                })}
                // 展示配置
                showConfig={{
                    showImgPreview: localStorage.getItem('showImgPreview') === 'true',
                    showStepInfo: localStorage.getItem('showStepInfo') !== 'false'
                }}
                // 成员配置
                memberConfig={{
                    onlyMemberEdit: false,
                    onlyMemberRead: false,
                    locking: false
                }}
            />
        );
    }, [searchFilterCaseNode, curOsType, filterData]);
    return (
        <Layout className={styles.right}>
            {contextHolder}
            <Header className={styles.header}>
                <div className={styles.headerContent}>
                    <div className={styles.leftContent}>
                        <span className={styles.generateCaseTitle}>生成策略</span>
                        <Select
                            disabled={generate}
                            popupMatchSelectWidth={false}
                            placeholder="请选择生成策略"
                            value={currentPrompt?.id}
                            onChange={handleChangePrompt}
                            fieldNames={{ label: 'name', value: 'id' }}
                            options={searchPromptList?.filter(
                                (item) => item.type === promptActiveKey
                            )}
                            suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
                            notFoundContent={<NoContent text="暂无数据" />}
                            placement="bottomRight"
                            dropdownRender={(menu) => {
                                return (
                                    <>
                                        <div style={{ height: 320, width: 250 }}>
                                            <Tabs
                                                centered
                                                activeKey={promptActiveKey}
                                                onChange={setPromptActiveKey}
                                            >
                                                <Tabs.TabPane tab="通用" key="strategy">
                                                    {menu}
                                                </Tabs.TabPane>
                                                <Tabs.TabPane tab="实验室" key="lab">
                                                    {menu}
                                                </Tabs.TabPane>
                                            </Tabs>
                                        </div>
                                        <Input
                                            suffix={<SearchOutlined style={{ color: '#eee' }} />}
                                            placeholder="输入搜索内容"
                                            onChange={onChange}
                                            style={{
                                                width: '92%',
                                                margin: '0 4%'
                                            }}
                                        />
                                    </>
                                );
                            }}
                        />
                        <Button
                            className={styles.generateCaseBtn}
                            type="primary"
                            disabled={generate}
                            onClick={handleGenerateTask}
                        >
                            基于需求重新生成
                        </Button>
                        {[1, 3, 4].includes(taskInfo?.taskStatus) && (
                            <span className={styles.errMsg}>{getTips(taskInfo?.taskStatus)}</span>
                        )}
                    </div>
                    <div>
                        {rightExtra && rightExtra({ generate })}
                        {caseConfig?.signType === 1 && (
                            <Select
                                className={styles.osTypeSwitch}
                                value={curOsType}
                                popupMatchSelectWidth={false}
                                suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
                                size="small"
                                variant="borderless"
                                options={[
                                    {
                                        label: 'Android',
                                        value: 1
                                    },
                                    {
                                        label: 'iOS',
                                        value: 2
                                    },
                                    {
                                        label: '服务端',
                                        value: 3
                                    },
                                    {
                                        label: 'Web',
                                        value: 4
                                    },
                                    {
                                        label: 'HarmonyOS',
                                        value: 6
                                    }
                                ].filter((item) => {
                                    // 双端共用，支持Android和iOS
                                    if (caseConfig?.osType === 3) {
                                        return [1, 2].includes(item.value);
                                    } else {
                                        return item.value === caseConfig?.osType;
                                    }
                                })}
                                onChange={(value) => {
                                    setCurOsType(value);
                                }}
                            />
                        )}
                    </div>
                </div>
            </Header>
            <Content className={styles.content}>
                <div className={styles.mindContent}>
                    {generate || loading ? (
                        <Spin
                            tip={loading ? '获取中' : getTips(taskInfo?.taskStatus)}
                            wrapperClassName={styles.spinner}
                        >
                            <div className={styles.mindContent} />
                        </Spin>
                    ) : (
                        <>
                            {isEmpty(caseNode) ? (
                                <NoContent text="暂无对应用例" className={styles.noContent} />
                            ) : (
                                RenderCaseMinder
                            )}
                        </>
                    )}
                </div>
            </Content>
        </Layout>
    );
}

export default connectModel([baseModel, demandModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    docPromptList: state.common.base.docPromptList,
    currentDoc: state.demand.doc.currentDoc,
    docList: state.demand.doc.docList,
    curOsType: state.common.case.curOsType,
    caseConfig: state.common.case.caseConfig,
    executePlanDetail: state.common.case.executePlanDetail
}))(GenerateCase);
