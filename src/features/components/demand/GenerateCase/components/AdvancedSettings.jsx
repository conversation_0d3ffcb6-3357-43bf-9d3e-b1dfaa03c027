import { useState, useEffect } from 'react';
import { Form, InputNumber } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import styles from '../StartIntelligentExecutionPopover.module.less';

const AdvancedSettings = ({ form, fieldPrefix, initialExpanded = false, visible, style }) => {
    const [expanded, setExpanded] = useState(initialExpanded);
    const maxStepsFieldName = `${fieldPrefix}maxSteps`;
    const retryFieldName = `${fieldPrefix}retry`;

    useEffect(() => {
        if (visible) {
            const values = form.getFieldsValue();
            if (values[maxStepsFieldName] || values[retryFieldName]) {
                setExpanded(true);
            }
        }
    }, [form, maxStepsFieldName, retryFieldName, visible]);

    return (
        <div className={styles.advancedSettingsContainer} style={style}>
            <div className={styles.advancedSettingsToggle} onClick={() => setExpanded(!expanded)}>
                <span>{expanded ? '收起' : '高级设置'}</span>
                {expanded ? (
                    <UpOutlined className={styles.advancedSettingsIcon} />
                ) : (
                    <DownOutlined className={styles.advancedSettingsIcon} />
                )}
            </div>

            <div style={{ display: expanded ? 'block' : 'none' }}>
                <Form.Item label="最大步骤数" name={maxStepsFieldName}>
                    <InputNumber
                        className={styles.inputNumber}
                        placeholder="请输入最大步骤数 (非必填)"
                        min={1}
                        max={15}
                    />
                </Form.Item>
                <Form.Item label="最大重试次数" name={retryFieldName}>
                    <InputNumber
                        className={styles.inputNumber}
                        placeholder="请输入最大重试次数 (非必填)"
                        min={1}
                        max={3}
                    />
                </Form.Item>
            </div>
        </div>
    );
};

export default AdvancedSettings;
