
import { Form, Select, Tooltip } from 'antd';
import { CaretDownOutlined, InfoCircleOutlined } from '@ant-design/icons';
import NoContent from 'COMMON/components/common/NoContent';
import CloudDevice from 'COMMON/components/execution/CloudDevice';
import InstallParams from 'COMMON/components/execution/InstallParams';
import AdvancedSettings from './AdvancedSettings';
import styles from '../StartIntelligentExecutionPopover.module.less';
import { AGENT_OPTIONS } from '../const';

const PlatformConfig = ({
    form,
    platform,
    cloudDeviceList,
    appList,
    autoKnowledgeList,
    scenarioKnowledgeList,
    envList,
    loading
}) => {
    const getPlatformPrefix = (osType) => {
        return osType === 1 ? 'android' : 'ios';
    };

    const prefix = getPlatformPrefix(platform);
    return (
        <div className={styles.configContainer}>
            <Form.Item
                label="策略名称"
                name={`${prefix}AgentName`}
                initialValue="autocase_generate_general"
                rules={[{ required: true, message: '请填写策略名称' }]}
            >
                <Select
                    placeholder="请选择策略名称"
                    options={AGENT_OPTIONS.map((option) => ({
                        label: option.label.text,
                        value: option.value,
                        tooltip: option.label.tooltip
                    }))}
                    optionRender={(option) => (
                        <div className={styles.agentOption}>
                            <span>{option.label}</span>
                            <Tooltip title={option.data.tooltip}>
                                <InfoCircleOutlined style={{ color: '#1677ff' }} />
                            </Tooltip>
                        </div>
                    )}
                />
            </Form.Item>
            <CloudDevice
                form={form}
                cloudDeviceList={cloudDeviceList}
                osType={platform}
                name={`${prefix}CloudDevice`}
                mode="single"
            />
            <Form.Item
                label="APP 名称"
                name={`${prefix}AppName`}
                rules={[{ required: true, message: '请填写APP名称' }]}
            >
                <Select
                    placeholder="请选择 APP 名称"
                    popupMatchSelectWidth={false}
                    options={appList.map((item) => ({
                        label: item.appName,
                        value: item.appId
                    }))}
                    notFoundContent={<NoContent text="暂无数据" />}
                    allowClear
                />
            </Form.Item>
            <Form.Item
                label="运行环境"
                name={`${prefix}EnvId`}
                rules={[{ required: true, message: '请选择运行环境' }]}
            >
                <Select
                    placeholder="请选择运行环境"
                    popupMatchSelectWidth={false}
                    options={envList?.map((item) => ({
                        label: item.envName,
                        value: item.envId
                    }))}
                    notFoundContent={<NoContent text="暂无数据" />}
                    allowClear
                />
            </Form.Item>
            <Form.Item
                label="自动化来源"
                name={`${prefix}AutoKbIdList`}
                rules={[{ required: true, message: '请选择自动化来源' }]}
            >
                <Select
                    mode="multiple"
                    placeholder="请选择自动化知识来源"
                    popupMatchSelectWidth={false}
                    options={autoKnowledgeList}
                    notFoundContent={loading ? '加载中...' : <NoContent text="暂无数据" />}
                    loading={loading}
                />
            </Form.Item>
            <Form.Item
                label="场景来源"
                name={`${prefix}SituationalKbIdList`}
                rules={[{ required: true, message: '请选择场景来源' }]}
            >
                <Select
                    mode="multiple"
                    placeholder="请选择场景知识来源"
                    popupMatchSelectWidth={false}
                    options={scenarioKnowledgeList}
                    notFoundContent={loading ? '加载中...' : <NoContent text="暂无数据" />}
                    loading={loading}
                />
            </Form.Item>
            <InstallParams
                form={form}
                maxCount={1}
                key={prefix}
                fieldName={prefix}
                style={{ height: '35px' }}
                layout="vertical"
            />
            <AdvancedSettings form={form} fieldPrefix={prefix} visible={true} />
        </div>
    );
};

export default PlatformConfig;
