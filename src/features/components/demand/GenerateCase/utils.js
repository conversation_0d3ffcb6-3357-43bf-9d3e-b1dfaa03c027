export function updateDocContent(docList, docNodeId, newContent) {
    return docList.map((item) => {
        if (item?.docNodeId === docNodeId) {
            return { ...item, nodeContent: newContent };
        }
        if (item.children) {
            return {
                ...item,
                children: updateDocContent(item.children, docNodeId, newContent)
            };
        }
        return item;
    });
}

export const getPlatformConfig = (values, platform, appList) => {

    const prefix = platform === 1 ? 'android' : 'ios';

    //将数组中的所有元素转换为数字。
    const convertToNumbers = (arr) => {
        if (!arr || !Array.isArray(arr)) return [];
        return arr.map((item) => (typeof item === 'string' ? Number(item) : item)).filter((item) => !isNaN(item));
    };

    const cleanInstallParams = (params) => {
        if (!Array.isArray(params) || params.length === 0 || params.every((item) => item === null)) {
            return [];
        }
        return params;
    };

    const getAppNameFromId = (appId) => {
        return appId ? appList.find((app) => app.appId === appId)?.appName || '' : '';
    };

    const agentParams = {
        agentName: values[`${prefix}AgentName`] || 'autocase_generate_general',
        planType: values.currentGroupType === 6 ? 1 : 2, // 1-智能测试 2-指令执行
        needUICheck: values.caseType === 'edit' ? false : true,
        envId: values[`${prefix}EnvId`],
        appName: getAppNameFromId(values[`${prefix}AppName`]),
        autoKbIdList: convertToNumbers(values[`${prefix}AutoKbIdList`] || []),
        situationalKbIdList: convertToNumbers(values[`${prefix}SituationalKbIdList`] || []),
        maxSteps: values[`${prefix}maxSteps`] ? Number(values[`${prefix}maxSteps`]) : undefined,
        retry: values[`${prefix}retry`] ? Number(values[`${prefix}retry`]) : undefined
    };

    return {
        osType: platform,
        poolId: values[`${prefix}CloudDevice`] || null,
        appId: values[`${prefix}AppName`] || '',
        installParams: cleanInstallParams(values[prefix]),
        // isDebug: !!values[`${prefix}isDebug`],
        agentParams
    };
};
