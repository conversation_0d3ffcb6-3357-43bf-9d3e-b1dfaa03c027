.configContainer {
    padding: 10px 0;
    width: 100%;
}

.popoverContent {
    margin-right: 20px;
    height: 100%;
    .buttonContainer {
        display: flex;
        justify-content: flex-end;
        margin-top: 24px;

        button {
            margin-left: 8px;
        }
    }
}

.buttonContainer {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
    gap: 8px;
}

.intelligentPopover {
    :global {
        .ant-popover-inner-content {
            padding: 0 !important;
        }
    }
}

.noAvailableConfig {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    width: 100%;
}

.disabledTab {
    color: #999;
    cursor: not-allowed;
    
    &:hover {
        color: #999;
    }
} 

.inputNumber {
    width: 100%;
}
.agentOption {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.advancedSettingsContainer {
    // margin-bottom: 16px;
    margin-left: 20px;
    font-size: 12px;
    margin-top: 0;
    padding-top: 0;
    .advancedSettingsIcon {
        font-size: 10px;
    }
}

.advancedSettingsToggle {
    color: #1677ff;
    width: 80px;
    cursor: pointer;
    display: flex;
    align-items: center;

    
    span {
        margin-left: 4px;
    }
    
    &:hover {
        opacity: 0.8;
    }
}
.switchContainer {
    margin-left: 16px;
}

.platformConfigContainer {
    min-height: 520px;
    .noConfigContainer {
        display: flex;
        align-items: center;
    }
    .noConfigTip {
        margin-left: 12px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
        font-weight: 500;
        // margin-top: 40px;
    }
    .noConfigSwitch {
        margin-left: 34px;
        margin-right: 16px;
    }
}