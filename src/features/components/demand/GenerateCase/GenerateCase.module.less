.right {
    height: 100%;
    background-color: #fff;
}

.noContent {
    margin-top: 200px;
}

.header {
    position: relative;
    width: 100%;
    height: 80px;
    line-height: 80px;
    padding: 0px 30px;
    background-color: #fff;
}

.headerContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
}

.leftContent {
    display: flex;
    align-items: center;
}

.generateCaseTitle {
    margin-right: 10px;
}

.generateCaseBtn {
    margin-left: 10px;
}

.content {
    height: 100%;
}

.mindContent {
    height: 100%;
}

.spinner {
    height: 300px;
}

.osTypeSwitch {
    position: absolute;
    right: 20px;
    top: 25px;
}

.errMsg {
    color: var(--error-color);
    font-size: 12px;
    margin-left: 10px;
}

.intelligentBtn {
    margin-left: 10px;
}