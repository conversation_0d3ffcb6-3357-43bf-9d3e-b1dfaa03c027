import {<PERSON>er, Mo<PERSON>, Spin} from 'antd';
import {CloseOutlined} from '@ant-design/icons';
import {useState, useCallback, useImperativeHandle, forwardRef} from 'react';
import {connectModel} from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import SettingPage from 'FEATURES/setting/IndexPage';
import styles from './SettingModal.module.less';

const SettingModal = ({setShowModal, recording}, ref) => {
    const [open, setOpen] = useState(false);
    const [settingModule, setSettingModule] = useState('');

    const showModal =
        (data) => {
            setSettingModule(data);
            setShowModal(true);
            setOpen(true);
        };

    const hideModal = () => {
        setOpen(false);
        setShowModal(false);
    };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(ref, () => {
        return {
            show: showModal
        };
    }, [showModal]);

    return (
        <Drawer
            open={open}
            onClose={hideModal}
            closeIcon={false}
            footer={null}
            width={window.innerWidth * .8}
            destroyOnClose
            centered
        >
            <div className={styles.iconGroups}>
                <span
                    className={styles.icon}
                    onClick={hideModal}
                >
                    <CloseOutlined />
                </span>
            </div>
            <Spin spinning={recording}>
                <SettingPage module={settingModule} hideModal={hideModal} />
            </Spin>
        </Drawer>
    );
};
export default connectModel([baseModel], state => ({
    recording: state.common.base.recording,
    showModal: state.common.base.showModal,
    currentModule: state.common.base.currentModule
}))(forwardRef(SettingModal));