.executionPanel {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    transition: all 0.3s ease;
    pointer-events: auto;

    &:hover {
        transform: scale(1.02);
    }
}

.detailCard {
    margin-bottom: 10px;
    background: rgba(80, 181, 255, 0.1);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(80, 181, 255, 0.15);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
    border: 1px solid rgba(80, 181, 255, 0.2);
    z-index: 10001;

    :global(.ant-card-body) {
        padding: 12px 16px;
    }
}

.statusInfo {
    color: #4A5568;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.progressContainer {
    display: flex;
    align-items: center;
    background: linear-gradient(110deg, rgba(240, 249, 255, 0.95) 0%, rgba(224, 242, 254, 0.95) 100%);
    padding: 12px 18px;
    border-radius: 24px;
    box-shadow: 0 8px 24px rgba(80, 181, 255, 0.15), 0 2px 4px rgba(80, 181, 255, 0.1);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(80, 181, 255, 0.2);
    min-width: 300px;
}

.customProgress {
    flex: 1;
    height: 10px;
    border-radius: 5px;
    background-color: rgba(80, 181, 255, 0.1);
    overflow: hidden;
    display: flex;
    margin-right: 14px;
    box-shadow: inset 0 1px 3px rgba(80, 181, 255, 0.2);
    position: relative;
}

.progressSegment {
    height: 100%;
    transition: width 0.5s ease;
    position: relative;

    &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
    }
}

.pulsating {
    animation: pulse 1.5s infinite;
}

.progressPercentage {
    color: #4A5568;
    font-size: 14px;
    font-weight: 500;
    margin-right: 14px;
    min-width: 40px;
    text-align: center;
    text-shadow: none;
    background: none;
    -webkit-background-clip: initial;
    -webkit-text-fill-color: initial;
}

.cancelButton {
    margin-left: 6px;
    border-radius: 20px;
    height: 32px;
    padding: 0 14px;
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0.3px;
    border: none;
    background: linear-gradient(90deg, #50B5FF 0%, #7CC5FA 100%);
    box-shadow: 0 4px 8px rgba(80, 181, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
        transform: translateY(-1px);
        background: linear-gradient(90deg, #7CC5FA 0%, #50B5FF 100%);
        box-shadow: 0 6px 12px rgba(80, 181, 255, 0.4);
    }

    &:active {
        transform: translateY(0);
        background: linear-gradient(90deg, #3DA0E3 0%, #50B5FF 100%);
        box-shadow: 0 2px 6px rgba(80, 181, 255, 0.4);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        opacity: 0.7;
        box-shadow: 0 0 10px rgba(80, 181, 255, 0.3);
    }

    50% {
        opacity: 1;
        box-shadow: 0 0 15px rgba(80, 181, 255, 0.7);
    }

    100% {
        opacity: 0.7;
        box-shadow: 0 0 10px rgba(80, 181, 255, 0.3);
    }
}