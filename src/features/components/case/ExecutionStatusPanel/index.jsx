import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Badge, Card, message } from 'antd';
import { LoadingOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { cancelUIGenerateTask } from 'COMMON/api/front_qe_tools/generate';
import styles from './styles.module.less';

const ExecutionStatusPanel = ({ executePlanDetail, setExecutePlanDetail, caseType }) => {
    const [cancelLoading, setCancelLoading] = useState(false);
    // const [showDetail, setShowDetail] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();

    // 根据caseTaskList获取各状态用例数量
    const getCaseCounts = () => {
        if (!executePlanDetail || !executePlanDetail?.caseTaskList) {
            return {
                total: 0,
                creating: 0, // 0-创建中
                executing: 0, // 1-执行中
                completed: 0, // 2-执行完成
                failed: 0, // 3-执行失败
                canceled: 0 // 4-取消
            };
        }
        const total = executePlanDetail?.caseTaskList?.length;
        // 统计各状态的数量
        const statusCounts = {
            creating: 0, // 0-创建中
            executing: 0, // 1-执行中
            completed: 0, // 2-执行完成
            failed: 0, // 3-执行失败
            canceled: 0 // 4-取消
        };

        executePlanDetail?.caseTaskList?.forEach((task) => {
            switch (task.status) {
                case 0:
                    statusCounts.creating++;
                    break;
                case 1:
                    statusCounts.executing++;
                    break;
                case 2:
                    statusCounts.completed++;
                    break;
                case 3:
                    statusCounts.failed++;
                    break;
                case 4:
                    statusCounts.canceled++;
                    break;
                default:
                    break;
            }
        });

        return {
            total,
            ...statusCounts
        };
    };

    // 计算各状态百分比
    const getPercentages = () => {
        const counts = getCaseCounts();
        if (counts.total === 0)
            return {
                creatingPer: 0,
                executingPer: 0,
                completedPer: 0,
                failedPer: 0,
                canceledPer: 0
            };

        return {
            creatingPer: counts?.creating / counts?.total,
            executingPer: counts?.executing / counts?.total,
            completedPer: counts?.completed / counts?.total,
            failedPer: counts?.failed / counts?.total,
            canceledPer: counts?.canceled / counts?.total
        };
    };

    // 获取总体完成进度
    const calculateTotalProgress = () => {
        const counts = getCaseCounts();
        if (counts?.total === 0) {
            return 0;
        }
        // 完成进度包括 已完成、失败 取消
        return (
            ((counts?.completed + counts?.failed + counts?.canceled) / counts?.total) *
            100
        ).toFixed(1);
    };

    // 判断是否正在执行中
    const isExecuting = () => {
        if (!executePlanDetail) {
            return false;
        }
        return [0, 1].includes(executePlanDetail?.planStatus);
    };

    // 获取计划ID
    const getPlanId = () => {
        if (!executePlanDetail) {
            return null;
        }
        return executePlanDetail?.planId;
    };

    // 取消执行任务
    const handleCancelExecution = async () => {
        const planId = getPlanId();
        if (!planId) {
            message.error('未找到执行任务ID');
            return;
        }

        setCancelLoading(true);
        try {
            await cancelUIGenerateTask({ planId });
            message.success('取消执行任务成功');
            // 成功后更新执行计划状态
            if (executePlanDetail) {
                setExecutePlanDetail({
                    ...executePlanDetail,
                    planStatus: 2 // 已取消状态
                });
            }
        } finally {
            setCancelLoading(false);
        }
    };

    if (!isExecuting() || !['edit', 'intelligent', 'intelligent-generate'].includes(caseType)) {
        return null;
    }
    const counts = getCaseCounts();
    const percentages = getPercentages();
    const totalProgress = calculateTotalProgress();

    return (
        <div
            className={styles.executionPanel}
            // onMouseEnter={() => setShowDetail(true)}
            // onMouseLeave={() => setShowDetail(false)}
        >
            {contextHolder}

            <div className={styles.progressContainer}>
                <div className={styles.customProgress}>
                    {/* 已完成部分 */}
                    {counts?.completed > 0 && (
                        <Tooltip
                            title={
                                <div>
                                    已完成: {counts?.completed}/{counts?.total}
                                </div>
                            }
                            color="rgba(255, 255, 255, 0.95)"
                            overlayInnerStyle={{ color: '#333' }}
                        >
                            <div
                                className={styles.progressSegment}
                                style={{
                                    width: `${percentages.completedPer * 100}%`,
                                    backgroundColor: '#3DD598',
                                    boxShadow: '0 0 10px rgba(61, 213, 152, 0.5)'
                                }}
                            />
                        </Tooltip>
                    )}

                    {/* 执行中部分 */}
                    {counts?.executing > 0 && (
                        <Tooltip
                            title={
                                <div>
                                    执行中: {counts?.executing}/{counts?.total}
                                </div>
                            }
                            color="rgba(255, 255, 255, 0.95)"
                            overlayInnerStyle={{ color: '#333' }}
                        >
                            <div
                                className={`${styles.progressSegment} ${styles.pulsating}`}
                                style={{
                                    width: `${percentages.executingPer * 100}%`,
                                    backgroundColor: '#50B5FF',
                                    boxShadow: '0 0 10px rgba(80, 181, 255, 0.5)'
                                }}
                            />
                        </Tooltip>
                    )}

                    {/* 失败部分 */}
                    {counts?.failed > 0 && (
                        <Tooltip
                            title={
                                <div>
                                    执行失败: {counts?.failed}/{counts?.total}
                                </div>
                            }
                            color="rgba(255, 255, 255, 0.95)"
                            overlayInnerStyle={{ color: '#333' }}
                        >
                            <div
                                className={styles.progressSegment}
                                style={{
                                    width: `${percentages.failedPer * 100}%`,
                                    backgroundColor: '#FF575F',
                                    boxShadow: '0 0 10px rgba(255, 87, 95, 0.5)'
                                }}
                            />
                        </Tooltip>
                    )}

                    {/* 取消部分 */}
                    {counts?.canceled > 0 && (
                        <Tooltip
                            title={
                                <div>
                                    已取消: {counts.canceled}/{counts.total}
                                </div>
                            }
                            color="rgba(255, 255, 255, 0.95)"
                            overlayInnerStyle={{ color: '#333' }}
                        >
                            <div
                                className={styles.progressSegment}
                                style={{
                                    width: `${percentages.canceledPer * 100}%`,
                                    backgroundColor: '#ACB0BC',
                                    boxShadow: '0 0 10px rgba(172, 176, 188, 0.5)'
                                }}
                            />
                        </Tooltip>
                    )}
                    {/* 待执行 */}
                    {counts?.creating > 0 && (
                        <Tooltip
                            title={
                                <div>
                                    待执行: {counts.creating}/{counts.total}
                                </div>
                            }
                            color="rgba(255, 255, 255, 0.95)"
                            overlayInnerStyle={{ color: '#333' }}
                        >
                            <div
                                className={styles.progressSegment}
                                style={{
                                    width: `${percentages.creatingPer * 100}%`,
                                    backgroundColor: '#FFC542',
                                    boxShadow: '0 0 10px rgba(255, 197, 66, 0.5)'
                                }}
                            />
                        </Tooltip>
                    )}
                </div>

                <Tooltip
                    title={
                        <div>
                            总进度: {counts?.completed + counts?.failed + counts?.canceled}/
                            {counts?.total}
                        </div>
                    }
                    color="rgba(255, 255, 255, 0.95)"
                    overlayInnerStyle={{ color: '#333' }}
                >
                    <div className={styles.progressPercentage}>{totalProgress}%</div>
                </Tooltip>

                <Tooltip
                    title="智能执行中，点击取消执行"
                    color="rgba(255, 255, 255, 0.95)"
                    overlayInnerStyle={{ color: '#333' }}
                >
                    <Button
                        type="primary"
                        danger
                        icon={cancelLoading ? <LoadingOutlined /> : null}
                        onClick={handleCancelExecution}
                        loading={cancelLoading}
                        className={styles.cancelButton}
                    >
                        取消执行
                    </Button>
                </Tooltip>
            </div>
        </div>
    );
};

export default ExecutionStatusPanel;
