import { useState, useEffect, useRef, useMemo } from 'react';
import { Divider, Tooltip, Dropdown, Switch, Badge, Radio, Popover, message, Button } from 'antd';
import { TeamOutlined } from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { useLocation, useNavigate } from 'umi';
import classnames from 'classnames';
import { stringifyUrl, stringify } from 'query-string';
import {
    CaretDownOutlined,
    BgColorsOutlined,
    DownloadOutlined,
    SaveOutlined,
    PartitionOutlined,
    FunnelPlotOutlined,
    SearchOutlined,
    CompassOutlined,
    EllipsisOutlined,
    RedoOutlined,
    ApartmentOutlined,
    BarChartOutlined
} from '@ant-design/icons';
import { getQueryParams, updateQueryParam } from 'COMMON/utils/utils';
import { checkCollaborativeTestingWhitelist } from 'COMMON/config/whiteList';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import Filter from 'COMMON/components/Filter';
import Search from 'COMMON/components/Search';
import { cancelUIGenerateTask } from 'COMMON/api/front_qe_tools/generate';
import SearchBox from 'PACKAGES/react-kityminder-editor-v2/src/components/Directive/SearchBox';
import Previewer from 'PACKAGES/react-kityminder-editor-v2/src/components/Directive/Previewer';
import { viewMappingToNum, viewMappingToName } from 'FEATURES/front_qe_tools/case/edit/const';
import NewSaveAsTemplate from 'FEATURES/setting/BrainDiagramCases/NewMindTemplatePage/NewSaveAsTemplate';
import { getLeafNodeIds } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import electron from 'COMMON/utils/electron';
import EventBus from 'COMMON/utils/eventBus';
import { useTaskPolling } from 'HOOKS/useTaskPolling';
import styles from './HeaderLeft.module.less';

// 无title 状态
const NO_TITLE_VIEW = ['template', 'generate'];
// 不可切换脑图和竖图的视图 状态
const NO_SWAP_VIEW = [
    'template',
    'stamp',
    'generate',
    'precision',
    'intelligent',
    'intelligent-generate'
];
// 双端共用情况下特殊展示 状态
const OSTYPE_COMMON_VIEW = ['stamp', 'execute', 'intelligent', 'intelligent-generate'];

function HeaderLeft(props) {
    const {
        editor,
        version,
        caseType, // edit: 编辑 / stamp: 签章 / execute: 执行 / template: 模板 / generate: 生成
        showType, // edit / template / generate / finish / stamp /
        caseConfig,
        tipTitle,
        title,
        osType,
        curOsType,
        updateCurOsType,
        currentCase,
        tree,
        getCaseNode,
        handleExpandtolevel,
        showConfig,
        setShowConfig,
        searchValue,
        setSearchValue,
        filterData,
        handleFilterData,
        clearFilterData,
        handleSearchFilterData,
        getSearchFilterCaseNode,
        activedTab = 'mind',
        setActivedTab = () => {},
        setExecutePlanDetail,
        executePlanDetail,
        username,
        currentSpace,
        getTaskList,
        spaceList,
        isCollaborativeMode
    } = props;
    const [searchText, setSearchText] = useState('');
    const [activedShowKey, setActivedShowKey] = useState('');

    const [cancelLoading, setCancelLoading] = useState(false);
    const [taskId, setTaskId] = useState(null);
    const [isExecuteTask, setIsExecuteTask] = useState(false); // 任务状态

    const [messageApi, contextHolder] = message.useMessage();
    const location = useLocation();
    const navigate = useNavigate();
    const query = getQueryParams();
    const disabled =
        (!editor?.minder || activedTab === 'vertical') &&
        !['edit', 'execute', 'stamp']?.includes(caseType);
    const viewRef = useRef(null);
    const operatorRef = useRef(null);
    useTaskPolling({
        isExecuteTask,
        setIsExecuteTask,
        taskId,
        setTaskId,
        currentSpace,
        username,
        getTaskList
    });
    useEffect(() => {
        if (!NO_SWAP_VIEW?.includes(caseType)) {
            let view = localStorage.getItem('common_view');
            // 竖图
            if (query?.view?.startsWith('2')) {
                view = 'vertical';
            }
            // 脑图
            if (query?.view?.startsWith('1')) {
                view = 'mind';
            }
            // 默认
            if (!view) {
                view = 'mind';
            }
            setActivedTab(view);
        } else {
            setActivedTab('mind');
        }
    }, [query?.treeNodeId]);

    useEffect(() => {
        setSearchText(searchValue);
    }, [searchValue]);

    const swapView = (value) => {
        getCaseNode();
        localStorage.removeItem('showRightModal');
        localStorage.setItem('common_view', value);
        setActivedTab(value);
        let view = value;
        // 更新 url
        if (value === 'vertical') {
            view = query?.caseNodeId
                ? viewMappingToNum(localStorage.getItem('vertical_view'))
                : '221';
        }
        if (value === 'mind') {
            localStorage.setItem('vertical_view', viewMappingToName(query?.view));
            view = '100';
        }
        let url = updateQueryParam('view', view);
        let caseNodeId = getQueryParams('caseNodeId', url);
        // 禁用url编码
        const queryString = stringify(
            {
                ...query,
                caseNodeId: caseNodeId,
                view: view
            },
            { encode: false }
        );
        // 拼接url
        const URL = `${location.pathname}${queryString ? `?${queryString}` : ''}`;
        navigate(URL);
    };

    // 层级
    const expandItems = [
        {
            key: 'expand_1',
            label: <span className={styles.moreItem}>展开一级节点</span>
        },
        {
            key: 'expand_2',
            label: <span className={styles.moreItem}>展开二级节点</span>
        },
        {
            key: 'expand_3',
            label: <span className={styles.moreItem}>展开三级节点</span>
        },
        {
            key: 'expand_4',
            label: <span className={styles.moreItem}>展开四级节点</span>
        },
        {
            key: 'expand_5',
            label: <span className={styles.moreItem}>展开五级节点</span>
        },
        {
            key: 'expand_6',
            label: <span className={styles.moreItem}>展开六级节点</span>
        },
        {
            type: 'divider'
        },
        {
            key: 'expand_7',
            label: <span className={styles.moreItem}>展开所有节点</span>
        }
    ];

    const onExpandClick = (e) => {
        if (activedTab !== 'vertical') {
            switch (e.key) {
                case 'expand_1': {
                    editor.minder.execCommand('expandtolevel', 1);
                    break;
                }
                case 'expand_2': {
                    editor.minder.execCommand('expandtolevel', 2);
                    break;
                }
                case 'expand_3': {
                    editor.minder.execCommand('expandtolevel', 3);
                    break;
                }
                case 'expand_4': {
                    editor.minder.execCommand('expandtolevel', 4);
                    break;
                }
                case 'expand_5': {
                    editor.minder.execCommand('expandtolevel', 5);
                    break;
                }
                case 'expand_6': {
                    editor.minder.execCommand('expandtolevel', 6);
                    break;
                }
                case 'expand_7': {
                    editor.minder.execCommand('expandtolevel', 9999);
                    break;
                }
            }
        }
        if (activedTab === 'vertical') {
            switch (e.key) {
                case 'expand_1': {
                    handleExpandtolevel(1);
                    break;
                }
                case 'expand_2': {
                    handleExpandtolevel(2);
                    break;
                }
                case 'expand_3': {
                    handleExpandtolevel(3);
                    break;
                }
                case 'expand_4': {
                    handleExpandtolevel(4);
                    break;
                }
                case 'expand_5': {
                    handleExpandtolevel(5);
                    break;
                }
                case 'expand_6': {
                    handleExpandtolevel(6);
                    break;
                }
                case 'expand_7': {
                    handleExpandtolevel(9999);
                    break;
                }
            }
        }
    };

    const changeShowKey = (key) => {
        setActivedShowKey(activedShowKey !== key ? key : null);
    };

    // 更多操作
    const moreClick = (e) => {
        if (e.key.includes('expand')) {
            onExpandClick(e);
        } else {
            switch (e.key) {
                case 'shortcut': {
                    changeShowKey('shortcut');
                    break;
                }
                case 'download': {
                    handleDownloadMind();
                    break;
                }
                case 'refresh': {
                    getCaseNode();
                    break;
                }
            }
        }
    };
    // 下载
    const handleDownloadMind = () => {
        const url = `core/case/download?caseId=${currentCase?.caseRootId}`;
        window.open(getUrl() + url);
    };

    // 获取链接
    const getUrl = () => {
        if (
            window.location.protocol.includes('https') &&
            window.location.origin.includes('qamate')
        ) {
            return 'https://qamate.baidu-int.com/';
        } else if (
            window.location.protocol.includes('https') &&
            window.location.origin.includes('pioneer')
        ) {
            return 'https://pioneer.baidu-int.com/';
        } else {
            return 'http://10.138.46.248:8999/';
        }
    };

    // 更多操作
    const moreItems = [
        {
            key: 'showConfig',
            disabled: activedTab === 'vertical' || disabled,
            label: (
                <span className={styles.moreItem}>
                    <BgColorsOutlined className={styles.moreItemIcon} />
                    展示配置
                </span>
            ),
            children: [
                {
                    key: '1-1',
                    disabled: NO_TITLE_VIEW?.includes(caseType),
                    label: (
                        <div onClick={(e) => e.stopPropagation()} className={styles.subMoreItem}>
                            <span className={styles.moreItem}>步骤列表</span>
                            <Switch
                                size="small"
                                className={styles.moreItemSwitch}
                                value={showConfig?.showStepInfo}
                                disabled={NO_TITLE_VIEW?.includes(caseType)}
                                onChange={() => {
                                    localStorage.setItem('showStepInfo', !showConfig?.showStepInfo);
                                    setShowConfig({
                                        ...showConfig,
                                        showStepInfo: !showConfig?.showStepInfo
                                    });
                                }}
                            />
                        </div>
                    )
                },
                {
                    key: '1-2',
                    label: (
                        <div onClick={(e) => e.stopPropagation()} className={styles.subMoreItem}>
                            <span className={styles.moreItem}>图片详情</span>
                            <Switch
                                size="small"
                                className={styles.moreItemSwitch}
                                value={showConfig?.showImgPreview}
                                onChange={() => {
                                    localStorage.setItem(
                                        'showImgPreview',
                                        !showConfig?.showImgPreview
                                    );
                                    setShowConfig({
                                        ...showConfig,
                                        showImgPreview: !showConfig?.showImgPreview
                                    });
                                }}
                            />
                        </div>
                    )
                }
            ]
        },
        {
            type: 'divider'
        },
        {
            key: 'saveTemplate',
            disabled: caseType !== 'edit' || disabled,
            label: (
                <span className={styles.moreItem}>
                    <NewSaveAsTemplate icon={<SaveOutlined className={styles.moreItemIcon} />} />
                </span>
            )
        },
        {
            key: 'download',
            disabled: caseType !== 'edit' || disabled,
            label: (
                <span className={styles.moreItem}>
                    <DownloadOutlined className={styles.moreItemIcon} />
                    下载
                </span>
            )
        },
        {
            key: 'refresh',
            label: (
                <span className={styles.moreItem}>
                    <RedoOutlined className={styles.moreItemIcon} />
                    刷新
                </span>
            )
        }
    ];

    const changeOsTypeToUrl = (key) => {
        updateCurOsType(key);
        localStorage.setItem('case_osType', key);
        const query = getQueryParams();
        getCaseNode();
        navigate(
            stringifyUrl({
                url: location.pathname,
                query: {
                    ...query,
                    os: key
                }
            })
        );
    };

    const allOptions = [
        { label: 'Android', key: '1' },
        { label: 'iOS', key: '2' },
        { label: 'Server', key: '4' },
        { label: 'Web', key: '5' },
        { label: 'HarmonyOS', key: '6' }
    ];

    const getOsTypeItems = () => {
        if (
            osType === 7 ||
            (osType === 3 &&
                Array.isArray(caseConfig?.osTypeList) &&
                caseConfig.osTypeList.length > 1)
        ) {
            if (!isEmpty(caseConfig?.osTypeList)) {
                return allOptions.filter((option) =>
                    caseConfig.osTypeList.includes(Number(option.key))
                );
            }
            return allOptions;
        }
        if (osType === 3) {
            return [
                { label: 'Android', key: '1' },
                { label: 'iOS', key: '2' }
            ];
        }
        return [];
    };
    const isExecuting = useMemo(() => {
        if (!executePlanDetail) {
            return false;
        }
        return [0, 1].includes(executePlanDetail?.planStatus);
    }, [executePlanDetail]);

    // 获取计划ID
    const getPlanId = () => {
        if (!executePlanDetail) {
            return null;
        }
        return executePlanDetail.planId;
    };

    // 取消执行任务
    const handleCancelExecution = async () => {
        const planId = getPlanId();
        if (!planId) {
            messageApi.error('未找到执行任务ID');
            return;
        }
        setCancelLoading(true);
        cancelUIGenerateTask({
            planId: planId
        })
            .then(() => {
                messageApi.success('取消执行任务成功');
                if (executePlanDetail) {
                    setExecutePlanDetail({
                        ...executePlanDetail,
                        planStatus: 2
                    });
                }
            })
            .finally(() => {
                setCancelLoading(false);
            });
    };

    // 更新知识
    const handleUpdateKnowledge = async () => {
        try {
            // 更新知识任务
            const { taskId } = await electron.send('knowledge.task.updateKnowledge', {
                caseRootId: currentCase?.caseRootId,
                moduleId: +query.moduleId,
                osType: curOsType
            });
            message.success('知识任务更新中，请不要关闭，更新进度可在任务栏中查看～', 3);
            setTaskId(taskId);
            setIsExecuteTask(true);
        } catch (error) {
            console.log(error);
            messageApi.error('知识更新失败');
        }
    };
    return (
        <div className={styles.headerLeft}>
            {contextHolder}
            <div style={{ display: 'flex', alignItems: 'center' }}>
                {!NO_TITLE_VIEW?.includes(caseType) && (
                    <>
                        <Tooltip title={tipTitle ?? ''}>
                            {/* 用例模块展示：编辑/签章 */}
                            {title &&
                                showType !== 'historyStamp' &&
                                ((osType !== 3 && osType !== 7) ||
                                    (OSTYPE_COMMON_VIEW?.includes(caseType) &&
                                        (osType === 3 || osType === 7) &&
                                        caseConfig?.signType === 0) ||
                                    !OSTYPE_COMMON_VIEW?.includes(caseType)) && (
                                    <span className={styles.headerLeftTitle}>用例{title}</span>
                                )}
                            {/* 特殊：双端不共用签章情况 */}
                            {title &&
                                showType !== 'historyStamp' &&
                                OSTYPE_COMMON_VIEW?.includes(caseType) &&
                                (osType === 3 || osType === 7) &&
                                caseConfig?.signType === 1 && (
                                    <span className={styles.headerLeftTitle}>共用{title}</span>
                                )}
                        </Tooltip>
                        {/* 端类型展示 */}
                        {(!OSTYPE_COMMON_VIEW?.includes(caseType) ||
                            (OSTYPE_COMMON_VIEW?.includes(caseType) &&
                                ((osType === 3 && caseConfig?.signType !== 0) || osType !== 3) &&
                                ((osType === 7 && caseConfig?.signType !== 0) || osType !== 7))) &&
                            !(
                                ['execute', 'stamp'].includes(caseType) &&
                                caseConfig?.signType === 1
                            ) && (
                                <>
                                    <Dropdown
                                        trigger="click"
                                        disabled={![3, 7]?.includes(osType)}
                                        menu={{
                                            items: getOsTypeItems(),
                                            selectable: true,
                                            defaultSelectedKeys: [curOsType + ''],
                                            onClick: (info) => {
                                                changeOsTypeToUrl(+info.key);
                                            }
                                        }}
                                    >
                                        <span className={styles.osTypeView}>
                                            <span className={styles.osTypeTitle}>
                                                {
                                                    [
                                                        {
                                                            label: 'Android',
                                                            value: 1
                                                        },
                                                        {
                                                            label: 'iOS',
                                                            value: 2
                                                        },
                                                        {
                                                            label: 'Server',
                                                            value: 4
                                                        },
                                                        {
                                                            label: 'Web',
                                                            value: 5
                                                        },
                                                        {
                                                            label: 'HarmonyOS',
                                                            value: 6
                                                        }
                                                    ]
                                                        .filter((item) => !item?.disabled)
                                                        .find((item) => {
                                                            return item.value === curOsType;
                                                        })?.label
                                                }
                                                {[3, 7]?.includes(osType) && (
                                                    <CaretDownOutlined
                                                        className={styles.osTypeSwitch}
                                                    />
                                                )}
                                            </span>
                                        </span>
                                    </Dropdown>
                                </>
                            )}
                        {(osType === 3 || osType === 7) &&
                            caseConfig?.signType === 0 &&
                            ['stamp', 'intelligent', 'intelligent-generate']?.includes(caseType) &&
                            showType !== 'historyStamp' && (
                                <div className={styles.osTypeViewBtn}>
                                    <Radio.Group
                                        value={curOsType}
                                        block
                                        size="small"
                                        optionType="button"
                                        buttonStyle="solid"
                                        onChange={(e) => {
                                            let value = e.target.value;
                                            changeOsTypeToUrl(value);
                                            // 切换端类型的时候需要清空执行状态
                                            setExecutePlanDetail(null);
                                        }}
                                    >
                                        {(osType === 3 ? [1, 2] : caseConfig.osTypeList).map(
                                            (key) => {
                                                const option = allOptions.find(
                                                    (opt) => opt.key === String(key)
                                                );
                                                return option ? (
                                                    <Radio.Button
                                                        key={option.key}
                                                        value={+option.key}
                                                    >
                                                        {option.label}
                                                    </Radio.Button>
                                                ) : null;
                                            }
                                        )}
                                    </Radio.Group>
                                </div>
                            )}
                        {/* 特殊：双端不共用签章情况 */}
                        {!NO_SWAP_VIEW?.includes(caseType) && (
                            <>
                                <Divider type="vertical" />
                                <Tooltip
                                    size="small"
                                    title={`开启${
                                        activedTab !== 'vertical' ? '竖图' : '脑图'
                                    }视图(仅对个人生效)`}
                                    placement="top"
                                >
                                    <span
                                        ref={viewRef}
                                        className={classnames(styles.iconWrapper, styles.caseIcon)}
                                        onClick={() =>
                                            swapView(
                                                activedTab === 'vertical' ? 'mind' : 'vertical'
                                            )
                                        }
                                    >
                                        <ApartmentOutlined className={styles.caseIconPoint} />
                                        <span className={styles.caseIconTitle}>
                                            {activedTab !== 'vertical' ? '竖图' : '脑图'}视图
                                        </span>
                                    </span>
                                </Tooltip>
                            </>
                        )}
                        <Divider type="vertical" />
                    </>
                )}
                {!disabled &&
                    caseType === 'edit' &&
                    activedTab !== 'vertical' &&
                    checkCollaborativeTestingWhitelist(currentSpace, spaceList) && (
                        <>
                            <Tooltip size="small" title="协作模式" placement="top">
                                <span
                                    className={classnames(styles.iconWrapper, styles.caseIcon, {
                                        [styles.caseActivedIcon]: activedShowKey === 'collaboration'
                                    })}
                                    onClick={() => {
                                        if (disabled) {
                                            return;
                                        }
                                        setActivedShowKey(
                                            activedShowKey === 'collaboration'
                                                ? ''
                                                : 'collaboration'
                                        );
                                        EventBus.emit('toggleCollaboration');
                                    }}
                                >
                                    <TeamOutlined
                                        style={{
                                            color:
                                                activedShowKey === 'collaboration'
                                                    ? '#1890ff'
                                                    : '#666'
                                        }}
                                    />
                                </span>
                            </Tooltip>
                            <Divider type="vertical" />
                        </>
                    )}

                <span ref={operatorRef}>
                    {/* 筛选 */}
                    {!isCollaborativeMode && (
                        <Filter
                            editType="edit"
                            disabled={disabled}
                            caseNode={tree}
                            osType={curOsType}
                            filterData={filterData}
                            handleFilterData={handleFilterData}
                            clearFilterData={clearFilterData}
                            handleSearchFilterData={handleSearchFilterData}
                        >
                            <Badge
                                dot={Object?.values(filterData || {}).some(
                                    (value) => !isEmpty(value?.data)
                                )} // 一个条件为非空.true
                                size="small"
                            >
                                <FunnelPlotOutlined
                                    className={classnames(styles.iconWrapper, styles.caseIcon, {
                                        [styles.caseActivedIcon]: activedShowKey === 'filter'
                                    })}
                                    onClick={() => {
                                        if (disabled) {
                                            return;
                                        }
                                        changeShowKey('filter');
                                    }}
                                />
                            </Badge>
                        </Filter>
                    )}
                    <Tooltip size="small" title="数据统计" placement="bottom">
                        <span
                            className={classnames(styles.iconWrapper, styles.caseIcon)}
                            onClick={() => {
                                message.info(
                                    '当前展示用例数：' + getLeafNodeIds(tree?.[0])?.length ?? '异常'
                                );
                            }}
                        >
                            <BarChartOutlined />
                        </span>
                    </Tooltip>
                    {/* 搜索 */}
                    <Popover
                        open={activedShowKey === 'searchBox'}
                        trigger="click"
                        onOpenChange={(value) => {
                            setActivedShowKey(value ? 'searchBox' : '');
                        }}
                        content={
                            <>
                                {activedTab === 'vertical' ? (
                                    <Search
                                        className={classnames(styles.caseInput, 'inputEditor')}
                                        onSearch={async (value) => {
                                            setSearchValue && setSearchValue(value);
                                            setSearchText(value);
                                            getSearchFilterCaseNode(filterData, value, tree?.[0]);
                                        }}
                                        onBlur={async (value) => {
                                            setSearchValue && setSearchValue(value);
                                            setSearchText(value);
                                            getSearchFilterCaseNode(filterData, value, tree?.[0]);
                                        }}
                                    />
                                ) : (
                                    <SearchBox {...props} />
                                )}
                            </>
                        }
                    >
                        <Badge dot={!isEmpty(searchText)} size="small">
                            <SearchOutlined
                                className={classnames(styles.iconWrapper, styles.caseIcon, {
                                    [styles.caseActivedIcon]: activedShowKey === 'searchBox'
                                })}
                                onClick={() => {
                                    if (disabled) {
                                        return;
                                    }
                                    setActivedShowKey(
                                        activedShowKey === 'searchBox' ? '' : 'searchBox'
                                    );
                                }}
                            />
                        </Badge>
                    </Popover>
                    <Dropdown
                        menu={{
                            items: expandItems,
                            onClick: onExpandClick
                        }}
                    >
                        <PartitionOutlined
                            className={classnames(styles.iconWrapper, styles.caseIcon)}
                        />
                    </Dropdown>
                    {/* 导航器(仅脑图) */}
                    {activedTab !== 'vertical' && (
                        <Tooltip title="导航器" placement="top">
                            <CompassOutlined
                                className={classnames(styles.iconWrapper, styles.caseIcon, {
                                    [styles.caseActivedIcon]: activedShowKey === 'previewer'
                                })}
                                onClick={() => {
                                    if (disabled) {
                                        return;
                                    }
                                    setActivedShowKey(
                                        activedShowKey === 'previewer' ? '' : 'previewer'
                                    );
                                }}
                            />
                        </Tooltip>
                    )}
                    <Dropdown
                        menu={{
                            items: moreItems,
                            onClick: moreClick
                        }}
                    >
                        <EllipsisOutlined
                            className={classnames(styles.iconWrapper, styles.caseIcon)}
                        />
                    </Dropdown>
                    {/* 更新知识 */}
                    {isElectron() &&
                        activedTab === 'mind' &&
                        showType === 'edit' &&
                        caseConfig?.knowledgeType?.length > 0 &&
                        [1, 2].includes(curOsType) && (
                            <Button
                                className={styles.updateKnowBtn}
                                type="primary"
                                onClick={handleUpdateKnowledge}
                            >
                                更新知识
                            </Button>
                        )}
                </span>
            </div>
            {/* 导航 */}
            {activedShowKey === 'previewer' && <Previewer editor={editor} />}
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    version: state.common.base.version,
    currentModule: state.common.base.currentModule,
    currentCase: state.common.case.currentCase,
    caseConfig: state.common.case.caseConfig,
    executePlanDetail: state.common.case.executePlanDetail,
    username: state.common.base.username,
    taskList: state.common.base.taskList,
    currentSpace: state.common.base.currentSpace,
    spaceList: state.common.base.spaceList
}))(HeaderLeft);
