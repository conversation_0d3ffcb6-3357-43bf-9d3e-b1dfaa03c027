.headerLeft {
    width: auto;
    min-width: 370px;
    padding: 7px 0 5px 15px;
    border-radius: 10px;
    z-index: 98;
    background-color: rgba(255, 255, 255, .7);
    cursor: pointer;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
}

.headerLeftTitle {
    font-weight: bold;
    font-size: 13px;
    margin-right: 10px;
}

.functionView {
    font-size: 13px;
    font-weight: bold;
}

.osTypeViewBtn {
    display: inline-block;
}

.iconWrapper {
    border-radius: 3px;
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.activedIconWrapper {
    background-color: #3377ff1a !important;
}

.iconWrapper:hover {
    background-color: rgba(0, 0, 0, 0.1);
    /* 你可以根据需要调整颜色 */
}

.caseIcon {
    margin-left: 5px;
    padding: 2px;
    color: #777;
}

.caseIconPoint {
    font-size: 13px;
    margin-right: 3px;
    color: #37f;
}

.activedCaseIcon {
    background-color: var(--primary-color);
    color: #fff;

    .caseIconPoint {
        color: #fff;
    }
}

.caseIconTitle {
    font-size: 13px;
    width: 60px;
    letter-spacing: .2mm;
}

.stampTitle {
    font-size: 13px;
    padding: 0 2px;
}

.stampActivedTitle {
    background-color: #1677ff;
    color: #e6f4ff;
    box-shadow: 0 0 5px 3px rgba(56, 117, 246, 0.2);
}

.osTypeView {
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    z-index: 9;
}

.osTypeTitle {
    font-size: 12px;
    width: 100px;
}

.osTypeSwitch {
    margin-left: 5px;
    color: #777;
}

.filterItemShort {
    width: 20%;
}

.moreItem {
    font-size: 12px;
}

.moreItemIcon {
    margin-right: 5px;
}

.moreItemSwitch {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

// 展示配置
.subMoreItem {
    width: 80px;
}


// 快捷键
.nav-drawer-shortcut-name {
    font-size: 14px;
    color: #777;
}

.nav-drawer-shortcut-code {
    margin-top: 5px;
    color: #aeaeae;
    font-size: 12px;

    .nav-drawer-shortcut-code-item {
        float: right;
        margin-bottom: 5px;
    }
}

.updateKnowBtn {
    margin-left: 10px;
    font-size: 12px;
    padding: 2px 6px;
    height: 22px;
}