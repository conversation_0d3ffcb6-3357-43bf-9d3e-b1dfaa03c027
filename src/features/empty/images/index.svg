<?xml version="1.0" encoding="UTF-8"?>
<svg width="250px" height="157px" viewBox="0 0 250 157" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Illustration/LazyMind</title>
    <defs>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-1">
            <stop stop-color="#F5F7FA" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#D9DEE4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="50%" x2="50%" y2="0%" id="linearGradient-2">
            <stop stop-color="#F5F7FA" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#D9DEE4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="50%" x2="50%" y2="0%" id="linearGradient-3">
            <stop stop-color="#F5F7FA" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#D9DEE4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="70.998387%" y1="56.749425%" x2="0%" y2="56.749425%" id="linearGradient-4">
            <stop stop-color="#D9DEE4" offset="0%"></stop>
            <stop stop-color="#F5F7FA" offset="100%"></stop>
        </linearGradient>
        <filter x="-13.8%" y="-41.6%" width="127.5%" height="183.1%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="0.654999971" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="70.998387%" y1="56.749425%" x2="0%" y2="56.749425%" id="linearGradient-6">
            <stop stop-color="#D9DEE4" offset="0%"></stop>
            <stop stop-color="#F5F7FA" offset="100%"></stop>
        </linearGradient>
        <filter x="-13.8%" y="-41.6%" width="127.5%" height="183.1%" filterUnits="objectBoundingBox" id="filter-7">
            <feGaussianBlur stdDeviation="0.445399985" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-13.8%" y="-41.6%" width="127.5%" height="183.1%" filterUnits="objectBoundingBox" id="filter-8">
            <feGaussianBlur stdDeviation="0.445399985" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-11.5%" y="-150.0%" width="123.1%" height="400.0%" filterUnits="objectBoundingBox" id="filter-9">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="51.5726708%" y1="62.4292037%" x2="42.8800285%" y2="58.8360947%" id="linearGradient-10">
            <stop stop-color="#D9DEE4" offset="0%"></stop>
            <stop stop-color="#F5F7FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="38.9495878%" y1="73.9911655%" x2="70.5474064%" y2="50%" id="linearGradient-11">
            <stop stop-color="#ADBEF0" offset="0%"></stop>
            <stop stop-color="#D4E5FE" offset="100%"></stop>
        </linearGradient>
        <path d="M3.56758062,0.500471412 L0.178300633,9.69782638 C0.033055633,10.0914714 0.269380632,10.5230664 0.679585631,10.6131114 C2.85032562,11.0919714 5.20667562,11.1333714 7.80625061,10.6013814 C8.2237006,10.5161664 8.4683056,10.0783614 8.3185756,9.67954138 L4.86098562,0.495986412 C4.63570062,-0.102243586 3.78872562,-0.0991385861 3.56758062,0.500471412" id="path-12"></path>
        <linearGradient x1="61.4566185%" y1="50%" x2="30.4435489%" y2="83.525485%" id="linearGradient-14">
            <stop stop-color="#5186F0" offset="0%"></stop>
            <stop stop-color="#515FF0" offset="100%"></stop>
        </linearGradient>
        <filter x="-56.3%" y="-39.2%" width="212.6%" height="178.4%" filterUnits="objectBoundingBox" id="filter-15">
            <feGaussianBlur stdDeviation="1.72499999" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="31.1895581%" y1="41.6518927%" x2="100%" y2="41.6518927%" id="linearGradient-16">
            <stop stop-color="#D9DEE4" offset="0%"></stop>
            <stop stop-color="#F5F7FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-17">
            <stop stop-color="#5186F0" offset="0%"></stop>
            <stop stop-color="#515FF0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-18">
            <stop stop-color="#D9DEE4" offset="0%"></stop>
            <stop stop-color="#F5F7FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="86.2114368%" y1="85.2786021%" x2="17.5890354%" y2="13.2049346%" id="linearGradient-19">
            <stop stop-color="#D9DEE4" offset="0%"></stop>
            <stop stop-color="#F5F7FA" offset="100%"></stop>
        </linearGradient>
        <circle id="path-20" cx="1.5" cy="1.5" r="1.5"></circle>
        <circle id="path-22" cx="1.5" cy="1.5" r="1.5"></circle>
        <circle id="path-24" cx="1.5" cy="1.5" r="1.5"></circle>
        <circle id="path-26" cx="1.5" cy="1.5" r="1.5"></circle>
        <path d="M0.178300633,9.69782638 C0.033055633,10.0914714 0.269380632,10.5230664 0.679585631,10.6131114 C2.85032562,11.0919714 5.20667562,11.1333714 7.80625061,10.6013814 C8.2237006,10.5161664 8.4683056,10.0783614 8.3185756,9.67954138 L4.86098562,0.495986412 C4.63570062,-0.102243586 3.78872562,-0.0991385861 3.56758062,0.500471412 L0.178300633,9.69782638 Z" id="path-28"></path>
        <linearGradient x1="61.8151566%" y1="39.0541087%" x2="30.4435489%" y2="85.8369335%" id="linearGradient-30">
            <stop stop-color="#5186F0" offset="0%"></stop>
            <stop stop-color="#515FF0" offset="100%"></stop>
        </linearGradient>
        <filter x="-56.3%" y="-39.2%" width="212.6%" height="178.4%" filterUnits="objectBoundingBox" id="filter-31">
            <feGaussianBlur stdDeviation="1.72499999" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-32">
            <stop stop-color="#5186F0" offset="0%"></stop>
            <stop stop-color="#515FF0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-33">
            <stop stop-color="#5186F0" offset="0%"></stop>
            <stop stop-color="#515FF0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-34">
            <stop stop-color="#5186F0" offset="0%"></stop>
            <stop stop-color="#515FF0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Illustration/LazyMind" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-7">
            <path d="M249.960386,156.004844 C228.181855,108.766797 180.410001,75.9799967 124.980379,75.9799967 C69.5504419,75.9799967 21.7783595,108.76717 4.26325641e-13,156.00565" id="路径" fill="url(#linearGradient-1)"></path>
            <path d="M59.9046433,20.3049991 C63.9814796,20.3049991 67.6250742,22.1525793 70.0374893,25.0529631 C70.5667096,24.9535772 71.1134044,24.9014901 71.6723132,24.9014901 C76.5147834,24.9014901 80.440381,28.8115365 80.440381,33.6348231 C80.440381,38.4581097 76.5147834,42.368156 71.6723132,42.368156 C70.9940543,42.368156 70.3337826,42.2914483 69.6997656,42.1462675 C67.292485,44.8217829 63.7959139,46.504998 59.9046433,46.504998 C56.0521641,46.504998 52.5865544,44.855175 50.1811317,42.2261795 C49.6682825,42.3194202 49.1390806,42.368156 48.5984506,42.368156 C43.7559805,42.368156 39.8303828,38.4581097 39.8303828,33.6348231 C39.8303828,28.8115365 43.7559805,24.9014901 48.5984506,24.9014901 C49.0153109,24.9014901 49.4253768,24.9304655 49.8267288,24.9865045 C52.2402622,22.12428 55.8591498,20.3049991 59.9046433,20.3049991 Z" id="形状结合" fill="url(#linearGradient-2)"></path>
            <path d="M184.618871,0 C189.813667,0 194.456405,2.35577181 197.530287,6.05390611 C198.204705,5.92690862 198.901154,5.86052606 199.61316,5.86052606 C205.783404,5.86052606 210.785375,10.8458352 210.785375,16.9955256 C210.785375,23.145216 205.783404,28.1305251 199.61316,28.1305251 C198.749148,28.1305251 197.908043,28.0327727 197.100372,27.8477592 C194.032473,31.2588694 189.577137,33.4049985 184.618871,33.4049985 C179.710396,33.4049985 175.294801,31.3017564 172.229852,27.9501793 C171.575543,28.0684075 170.901346,28.1305251 170.212593,28.1305251 C164.042349,28.1305251 159.040378,23.145216 159.040378,16.9955256 C159.040378,10.8458352 164.042349,5.86052606 170.212593,5.86052606 C170.744171,5.86052606 171.267077,5.89752766 171.778861,5.96908766 C174.85263,2.31975205 179.463941,0 184.618871,0 Z" id="形状结合备份" fill="url(#linearGradient-3)"></path>
            <g id="编组-6" transform="translate(57.515382, 71.394997)">
                <polygon id="Fill-3" fill="#C2C6CC" points="10.4799995 34.0599985 13.0999994 34.0599985 13.0999994 13.0999994 10.4799995 13.0999994"></polygon>
                <path d="M19.6499991,18.363055 C19.6499991,24.5917615 16.1308991,26.1999989 11.789898,26.1999989 C7.44889695,26.1999989 3.92999983,24.5917615 3.92999983,18.363055 C3.92999983,12.1343485 7.44889695,0 11.789898,0 C16.1308991,0 19.6499991,12.1343485 19.6499991,18.363055" id="Fill-1" fill="url(#linearGradient-4)"></path>
                <polygon id="路径-20" fill="#C2C6CC" filter="url(#filter-5)" points="14.2791425 33.4049985 1.48325796e-13 38.1336143 11.6591426 33.4049985"></polygon>
            </g>
            <g id="编组-5" transform="translate(39.175383, 96.939996)">
                <polygon id="Fill-3" fill="#C2C6CB" points="7.12639976 23.1607992 8.9079997 23.1607992 8.9079997 8.9079997 7.12639976 8.9079997"></polygon>
                <path d="M13.3619996,12.4868775 C13.3619996,16.722398 10.9690115,17.8159994 8.01713075,17.8159994 C5.06524998,17.8159994 2.67239991,16.722398 2.67239991,12.4868775 C2.67239991,8.25135708 5.06524998,5.5067062e-14 8.01713075,5.5067062e-14 C10.9690115,5.5067062e-14 13.3619996,8.25135708 13.3619996,12.4868775" id="Fill-1" fill="url(#linearGradient-6)"></path>
                <polygon id="路径-20" fill="#C2C6CB" filter="url(#filter-7)" points="9.70981702 22.7153992 -3.66817687e-13 25.930858 7.92821708 22.7153992"></polygon>
            </g>
            <g id="编组-3" transform="translate(186.930385, 97.500000) scale(-1, 1) translate(-186.930385, -97.500000) translate(179.930385, 82.500000)">
                <polygon id="Fill-3" fill="#C2C6CB" points="7.55859088 26.4657955 9.34019082 26.4657955 9.34019082 12.212996 7.55859088 12.212996"></polygon>
                <path d="M13.6895996,12.4868775 C13.6895996,16.722398 11.2966116,17.8159994 8.34473084,17.8159994 C5.39285007,17.8159994 3,16.722398 3,12.4868775 C3,8.25135708 5.39285007,5.86197757e-14 8.34473084,5.86197757e-14 C11.2966116,5.86197757e-14 13.6895996,8.25135708 13.6895996,12.4868775" id="Fill-1" fill="url(#linearGradient-6)"></path>
                <polygon id="路径-20" fill="#C2C6CB" filter="url(#filter-8)" points="9.70981702 26.5 9.05941988e-14 29.7154587 7.92821708 26.5"></polygon>
            </g>
        </g>
        <ellipse id="椭圆形" fill="#C7C7C7" opacity="0.5" filter="url(#filter-9)" cx="124.930385" cy="92" rx="26" ry="2"></ellipse>
        <path d="M101.430385,64 L96.4303851,89.5 C96.4303851,90.0522847 95.311097,90.5 93.9303851,90.5 C92.5496732,90.5 91.4303851,90.0522847 91.4303851,89.5 C91.4303851,89.4456482 91.4412255,89.3923091 91.4620886,89.3403099 L96.4303851,64 L101.430385,64 Z" id="形状结合" fill="url(#linearGradient-10)"></path>
        <path d="M158.430385,64 L153.430385,89.5 C153.430385,90.0522847 152.311097,90.5 150.930385,90.5 C149.549673,90.5 148.430385,90.0522847 148.430385,89.5 C148.430385,89.4456482 148.441225,89.3923091 148.462089,89.3403099 L153.430385,64 L158.430385,64 Z" id="形状结合备份-2" fill="url(#linearGradient-10)" transform="translate(153.430385, 77.250000) scale(-1, 1) translate(-153.430385, -77.250000) "></path>
        <g id="编组备份" transform="translate(94.930385, 90.500000)">
            <path d="M11.73,10.695 C11.73,9.74245497 9.10420497,8.96999997 5.86499998,8.96999997 C2.62579499,8.96999997 0,9.74245497 0,10.695 L0,10.938915 C0,11.891805 2.62579499,12.663915 5.86499998,12.663915 C9.10420497,12.663915 11.73,11.891805 11.73,10.938915 L11.73,10.695 Z" id="Fill-1" fill="#313131"></path>
            <path d="M11.73,10.695 C11.73,11.64789 9.10420497,12.42 5.86499998,12.42 C2.62579499,12.42 0,11.64789 0,10.695 C0,9.74210997 2.62579499,8.96999997 5.86499998,8.96999997 C9.10420497,8.96999997 11.73,9.74210997 11.73,10.695" id="Fill-1" fill="#3F3F3F"></path>
            <g id="形状结合" transform="translate(1.793719, 0.295927)">
                <mask id="mask-13" fill="white">
                    <use xlink:href="#path-12"></use>
                </mask>
                <use id="蒙版" fill="url(#linearGradient-11)" xlink:href="#path-12"></use>
                <path d="M7.33938714,6.20999998 L8.47218216,6.9981468 C7.08004816,8.99904615 4.40049379,10.8604301 0.433483987,12.6189685 L0.207561266,12.7183988 L-0.344999999,11.4538531 C3.46204154,9.79031212 6.00002603,8.06324038 7.26891842,6.30930763 L7.33938714,6.20999998 Z M7.33938714,2.06999999 L8.47218216,2.85814682 C7.08004816,4.85904617 4.40049379,6.72043015 0.433483987,8.47896855 L0.207561266,8.57839886 L-0.344999999,7.31385308 C3.46204154,5.65031214 6.00002603,3.92324039 7.26891842,2.16930765 L7.33938714,2.06999999 Z M7.33938714,-1.38 L8.47218216,-0.591853171 C7.08004816,1.40904618 4.40049379,3.27043016 0.433483987,5.02896856 L0.207561266,5.12839888 L-0.344999999,3.86385309 C3.46204154,2.20031215 6.00002603,0.473240407 7.26891842,-1.28069234 L7.33938714,-1.38 Z" fill="url(#linearGradient-14)" mask="url(#mask-13)"></path>
                <polygon id="路径-23" fill-opacity="0.536931818" fill="#5161F0" filter="url(#filter-15)" mask="url(#mask-13)" points="2.26058291 1.87419944 4.07128062 1.41004152 7.79229285 11.8233881 11.4512073 11.04 5.91901045 -1.38 2.87583956 -1.38"></polygon>
            </g>
        </g>
        <path d="M98.9303851,35 C100.034955,35 100.930385,35.8954305 100.930385,37 L100.930385,39 L100.930385,39 L96.9303851,39 L96.9303851,37 C96.9303851,35.8954305 97.8258156,35 98.9303851,35 Z" id="矩形" fill="url(#linearGradient-16)"></path>
        <g id="编组-7" transform="translate(135.430385, 78.000000)">
            <rect id="矩形备份-4" fill="#F4F4F4" x="0" y="0" width="9" height="9" rx="0.5"></rect>
            <g id="齿轮" transform="translate(1.500000, 2.500000)" fill="url(#linearGradient-17)">
                <path d="M3.3795531,2.45034803 L3.21897001,2.45034803 C3.1524286,2.45034803 3.08201737,2.39841132 3.06223386,2.3349509 L2.97892706,2.13265152 C2.94766557,2.0741184 2.96053257,1.98775918 3.00731333,1.94078407 L3.12128143,1.8270046 C3.14027611,1.80795276 3.15210278,1.78165287 3.15210278,1.75260924 C3.15210278,1.72351416 3.14048189,1.69723142 3.12128143,1.67812242 L2.92787635,1.485026 C2.90888167,1.46603703 2.88267323,1.45427896 2.85368105,1.45427896 C2.82467171,1.45427896 2.79830323,1.46609419 2.77927997,1.485026 L2.66532902,1.59897123 C2.61854825,1.64591777 2.53188609,1.65881906 2.47331867,1.62736323 L2.27116219,1.54417075 C2.20794757,1.52454729 2.15579364,1.45399316 2.15579364,1.38750891 L2.15579364,1.22670289 C2.15579364,1.19985425 2.14554463,1.17291415 2.12498373,1.15239326 C2.10443426,1.13183236 2.07758562,1.12155477 2.05073127,1.12155477 L1.77759196,1.12155477 C1.75063471,1.12155477 1.72378035,1.13183236 1.70323089,1.15239326 C1.68269857,1.17291415 1.67239811,1.19985424 1.67239811,1.22670289 L1.67239811,1.38750891 C1.67239811,1.45399316 1.62071291,1.52454729 1.55722391,1.54417075 L1.35493025,1.62736323 C1.29618563,1.65881906 1.20997504,1.64606639 1.16322285,1.59897123 L1.04879175,1.48511174 C1.0297742,1.46609419 1.00340572,1.45444473 0.974419246,1.45444473 C0.945432775,1.45444473 0.91918433,1.46625996 0.900161066,1.48519748 L0.707241852,1.6781167 C0.688218588,1.69713996 0.676546263,1.72335983 0.676403358,1.75243204 C0.676403358,1.78141851 0.688218588,1.80772983 0.707241852,1.82674166 L0.821015604,1.94073835 C0.868105039,1.9878335 0.881000618,2.07412983 0.849401883,2.13264009 L0.766123659,2.33507666 C0.74647162,2.39857137 0.67609469,2.45049665 0.609547568,2.45049665 L0.448798703,2.45033088 C0.421801446,2.45033088 0.394958523,2.46061419 0.374426201,2.48116366 C0.353871017,2.50172456 0.343593422,2.52863037 0.343593422,2.55557046 L0.343593422,2.8286469 C0.343593422,2.85554698 0.353871017,2.88245278 0.374426201,2.90304226 C0.394958523,2.9235117 0.42180716,2.93379501 0.448798703,2.93379501 L0.609518985,2.93379501 C0.676088975,2.9339322 0.746328721,2.98583461 0.765969325,3.04919214 L0.849401883,3.25149152 C0.88069195,3.31017326 0.867962139,3.39672682 0.821015604,3.44350759 L0.70692747,3.55730421 C0.687904206,3.57632747 0.676088975,3.60263879 0.676088975,3.63175673 C0.676088975,3.6607432 0.687898492,3.68713455 0.70692747,3.70615781 L0.900338275,3.89922565 C0.91932724,3.91827749 0.945552822,3.93000698 0.974533573,3.93000698 C1.00354862,3.93000698 1.02991139,3.91827749 1.04894037,3.89922565 L1.16305138,3.78536615 C1.20983786,3.73849964 1.29601987,3.72563265 1.35444438,3.75738001 L1.5572182,3.84057821 C1.6207072,3.86028169 1.6723924,3.93069864 1.6723924,3.99720574 L1.6723924,4.15746874 C1.6723924,4.18431738 1.68266428,4.21130892 1.70322518,4.23184124 C1.72377465,4.25225924 1.750629,4.26253112 1.77762055,4.26253112 L2.05060552,4.26253112 C2.0775742,4.26253112 2.10442284,4.25225924 2.12497231,4.23184124 C2.14553321,4.21130892 2.1558108,4.1843231 2.1558108,4.15746874 L2.1558108,3.99720574 C2.1558108,3.93069292 2.20794187,3.86028169 2.27112219,3.84057821 L2.47358734,3.75738001 C2.53218334,3.72563265 2.61867974,3.73853394 2.66577489,3.78536615 L2.77911993,3.89913991 C2.7981089,3.91824319 2.82433448,3.93012131 2.85352101,3.93012131 C2.88247318,3.93020133 2.90886454,3.91850042 2.92785922,3.89936856 L3.1212643,3.70612924 C3.14045905,3.68702023 3.15208565,3.66074321 3.15208565,3.63167671 C3.15208565,3.60259878 3.14029328,3.57632176 3.1212643,3.55726991 L3.00732479,3.44350188 C2.96052116,3.39669825 2.94767702,3.31013897 2.97893279,3.25145724 L3.06221673,3.04915786 C3.08200024,2.98583463 3.15272585,2.93393221 3.21898145,2.93393221 L3.37953597,2.93393221 C3.40638461,2.93393221 3.43323897,2.92362033 3.45375985,2.90307086 C3.47432075,2.88250996 3.48460406,2.85563845 3.48460406,2.82872694 L3.48460406,2.55557048 C3.48460406,2.52863038 3.47432075,2.50172458 3.45375985,2.48116368 C3.43325609,2.46062563 3.40640746,2.45034803 3.3795531,2.45034803 L3.3795531,2.45034803 L3.3795531,2.45034803 Z M1.9145503,3.2957972 C1.58086008,3.2957972 1.31045879,3.02557312 1.31045879,2.69190576 C1.31045879,2.35832985 1.58086008,2.08792856 1.9145503,2.08792856 C2.2481605,2.08792856 2.51853892,2.35832985 2.51853892,2.69190576 C2.51853892,3.0255674 2.2481605,3.2957972 1.9145503,3.2957972 L1.9145503,3.2957972 L1.9145503,3.2957972 Z M5.55126329,1.10681288 C5.5363785,1.09188808 5.51692653,1.08445712 5.49745741,1.08445712 L5.3811457,1.08445712 C5.33291303,1.08445712 5.2818909,1.04684501 5.26757201,1.00085879 L5.20720401,0.854285914 C5.18456816,0.81189513 5.19387973,0.749343579 5.22776491,0.715286903 L5.31036292,0.632826087 C5.3241102,0.619050225 5.33268439,0.599998384 5.33268439,0.578963043 C5.33268439,0.557893405 5.32423024,0.538841564 5.31036292,0.525002822 L5.17021497,0.385094946 C5.15643911,0.37131337 5.13745014,0.36280206 5.11646625,0.36280206 C5.09544806,0.36280206 5.07634477,0.371376245 5.06256319,0.385094946 L4.97997091,0.46766437 C4.94609143,0.501658165 4.88330552,0.510975448 4.84085758,0.488196691 L4.6944276,0.427914439 C4.64863573,0.413709868 4.61085785,0.362601994 4.61085785,0.314397913 L4.61085785,0.197914718 C4.61085785,0.17846846 4.60342689,0.15897076 4.58853639,0.144051675 C4.5736516,0.129161168 4.55417104,0.121730206 4.53472478,0.121730206 L4.33684397,0.121730206 C4.31728339,0.121730206 4.29784285,0.129195476 4.28294663,0.144051686 C4.26806184,0.158970771 4.26062516,0.178468476 4.26062516,0.197914729 L4.26062516,0.314397924 C4.26062516,0.362602005 4.22317883,0.413709879 4.17717545,0.42791445 L4.03059686,0.488196702 C3.98802888,0.510975459 3.92559165,0.501772498 3.89167788,0.467664381 L3.80879406,0.385152118 C3.79501249,0.371370542 3.7759092,0.362910678 3.75489672,0.362910678 C3.73387853,0.362910678 3.71489528,0.371484864 3.7011137,0.385180701 L3.56134302,0.525002833 C3.54753858,0.538790129 3.53910729,0.557773374 3.53902155,0.57882015 C3.53902155,0.599832627 3.54756716,0.618878754 3.56134302,0.63266033 L3.64377526,0.715286915 C3.67788337,0.749389317 3.68724639,0.811866564 3.66433616,0.854257342 L3.60402532,1.00097312 C3.58979217,1.04695363 3.53876432,1.08457144 3.49056596,1.08457144 L3.37411135,1.08445712 C3.35455076,1.08445712 3.33511023,1.09192238 3.32024258,1.10681289 C3.30535207,1.1217034 3.29786395,1.14117252 3.29786395,1.16070452 L3.29786395,1.35852817 C3.29786395,1.3780373 3.30535207,1.397535 3.32024258,1.41242551 C3.33511022,1.42725885 3.35455076,1.43468982 3.37411135,1.43468982 L3.49057168,1.43468981 C3.53877005,1.43480985 3.58967786,1.47241624 3.60388243,1.5183453 L3.66433616,1.66488388 C3.68702346,1.7073947 3.67779764,1.77012345 3.64377526,1.80403151 L3.56111438,1.8864466 C3.54733852,1.90022817 3.53876433,1.91930288 3.53876433,1.9404011 C3.53876433,1.96142501 3.54733852,1.98052258 3.56111438,1.99429844 L3.70123375,2.13422346 C3.71500961,2.14796502 3.73399858,2.15648205 3.75503963,2.15648205 C3.77602924,2.15648205 3.79513254,2.14797074 3.80891411,2.13422346 L3.89159786,2.05169405 C3.92548305,2.01773455 3.98794886,2.0083944 4.0302539,2.03139609 L4.17714116,2.09171264 C4.22315596,2.10597437 4.26059658,2.15695649 4.26059658,2.20516628 L4.26059658,2.32130651 C4.26059658,2.34074705 4.26806184,2.36030763 4.28294662,2.37516955 C4.29783713,2.38997432 4.31725481,2.39743957 4.33684397,2.39743957 L4.53461617,2.39743957 C4.55420533,2.39743957 4.573623,2.38997432 4.58851351,2.37516955 C4.60340402,2.36030763 4.61086356,2.34075276 4.61086356,2.32130651 L4.61086356,2.20516628 C4.61086356,2.15695649 4.64864143,2.10597437 4.69443331,2.09171264 L4.84111479,2.03139609 C4.88356844,2.0083944 4.94620574,2.01772884 4.98034244,2.05169405 L5.06242599,2.13412629 C5.07623615,2.14796502 5.09522512,2.15656779 5.11635763,2.15656779 C5.13736439,2.15664782 5.15647911,2.14811364 5.17022067,2.13429777 L5.31034005,1.99430415 C5.32423594,1.98046542 5.33269009,1.96143072 5.33269009,1.94035537 C5.33269009,1.9193086 5.32411591,1.90026247 5.31034005,1.88648089 L5.2277992,1.80403722 C5.19391401,1.77012345 5.18457387,1.70740041 5.2072383,1.66488959 L5.26757771,1.51835101 C5.28189661,1.47245053 5.33314738,1.43481556 5.38112282,1.43481556 L5.49746311,1.43481556 C5.51693223,1.43481556 5.5363499,1.42737889 5.55124041,1.41248838 C5.5661595,1.39762645 5.57359046,1.37809446 5.57359046,1.35862533 L5.57359046,1.16071023 C5.57359046,1.14119538 5.56615951,1.12170339 5.55126329,1.10681288 L5.55126329,1.10681288 L5.55126329,1.10681288 Z M4.43606447,1.69700278 C4.19425524,1.69700278 3.99836935,1.50123122 3.99836935,1.25950201 C3.99836935,1.01780139 4.19426095,0.821915504 4.43606447,0.821915504 C4.67774223,0.821915504 4.87365669,1.0178071 4.87365669,1.25950201 C4.87365669,1.50123122 4.67774795,1.69700278 4.43606447,1.69700278 L4.43606447,1.69700278 L4.43606447,1.69700278 Z" id="形状"></path>
            </g>
        </g>
        <path d="M150.930385,35 C152.034955,35 152.930385,35.8954305 152.930385,37 L152.930385,39 L152.930385,39 L148.930385,39 L148.930385,37 C148.930385,35.8954305 149.825816,35 150.930385,35 Z" id="矩形备份-3" fill="url(#linearGradient-16)"></path>
        <g id="编组-4" transform="translate(85.930385, 37.500000)">
            <rect id="矩形" fill="url(#linearGradient-18)" x="0" y="0" width="78" height="37" rx="2"></rect>
            <rect id="矩形" fill="#F8F9FA" x="3" y="3" width="72" height="31" rx="2"></rect>
            <g id="矩形" transform="translate(5.500000, 5.500000)">
                <mask id="mask-21" fill="white">
                    <use xlink:href="#path-20"></use>
                </mask>
                <use id="蒙版" fill="url(#linearGradient-19)" xlink:href="#path-20"></use>
                <rect fill="#E5E8ED" style="mix-blend-mode: multiply;" mask="url(#mask-21)" transform="translate(1.500000, 1.500000) rotate(-45.000000) translate(-1.500000, -1.500000) " x="1" y="-0.5" width="1" height="4"></rect>
            </g>
            <g id="矩形备份" transform="translate(69.500000, 5.500000)">
                <mask id="mask-23" fill="white">
                    <use xlink:href="#path-22"></use>
                </mask>
                <use id="蒙版" fill="url(#linearGradient-19)" xlink:href="#path-22"></use>
                <rect id="矩形" fill="#E5E8ED" style="mix-blend-mode: multiply;" mask="url(#mask-23)" transform="translate(1.500000, 1.500000) scale(-1, 1) rotate(-45.000000) translate(-1.500000, -1.500000) " x="1" y="-0.5" width="1" height="4"></rect>
            </g>
            <g id="矩形" transform="translate(5.500000, 28.500000)">
                <mask id="mask-25" fill="white">
                    <use xlink:href="#path-24"></use>
                </mask>
                <use id="蒙版" fill="url(#linearGradient-19)" xlink:href="#path-24"></use>
                <rect fill="#E5E8ED" style="mix-blend-mode: multiply;" mask="url(#mask-25)" transform="translate(1.500000, 1.500000) scale(-1, 1) rotate(-45.000000) translate(-1.500000, -1.500000) " x="1" y="-0.5" width="1" height="4"></rect>
            </g>
            <g id="矩形备份-2" transform="translate(69.500000, 28.500000)">
                <mask id="mask-27" fill="white">
                    <use xlink:href="#path-26"></use>
                </mask>
                <use id="蒙版" fill="url(#linearGradient-19)" xlink:href="#path-26"></use>
                <rect id="矩形" fill="#E5E8ED" style="mix-blend-mode: multiply;" mask="url(#mask-27)" transform="translate(1.500000, 1.500000) rotate(-45.000000) translate(-1.500000, -1.500000) " x="1" y="-0.5" width="1" height="4"></rect>
            </g>
        </g>
        <g id="编组" transform="translate(149.930385, 98.000000)">
            <path d="M11.73,10.695 C11.73,9.74245497 9.10420497,8.96999997 5.86499998,8.96999997 C2.62579499,8.96999997 0,9.74245497 0,10.695 L0,10.938915 C0,11.891805 2.62579499,12.663915 5.86499998,12.663915 C9.10420497,12.663915 11.73,11.891805 11.73,10.938915 L11.73,10.695 Z" id="Fill-1" fill="#313131"></path>
            <path d="M11.73,10.695 C11.73,11.64789 9.10420497,12.42 5.86499998,12.42 C2.62579499,12.42 0,11.64789 0,10.695 C0,9.74210997 2.62579499,8.96999997 5.86499998,8.96999997 C9.10420497,8.96999997 11.73,9.74210997 11.73,10.695" id="Fill-1" fill="#3F3F3F"></path>
            <g id="形状结合" transform="translate(1.793719, 0.295927)">
                <mask id="mask-29" fill="white">
                    <use xlink:href="#path-28"></use>
                </mask>
                <use id="蒙版" fill="url(#linearGradient-11)" xlink:href="#path-28"></use>
                <path d="M7.33938714,6.20999998 L8.47218216,6.9981468 C7.08004816,8.99904615 4.40049379,10.8604301 0.433483987,12.6189685 L0.207561266,12.7183988 L-0.344999999,11.4538531 C3.46204154,9.79031212 6.00002603,8.06324038 7.26891842,6.30930763 L7.33938714,6.20999998 Z M7.33938714,2.06999999 L8.47218216,2.85814682 C7.08004816,4.85904617 4.40049379,6.72043015 0.433483987,8.47896855 L0.207561266,8.57839886 L-0.344999999,7.31385308 C3.46204154,5.65031214 6.00002603,3.92324039 7.26891842,2.16930765 L7.33938714,2.06999999 Z M7.33938714,-1.38 L8.47218216,-0.591853171 C7.08004816,1.40904618 4.40049379,3.27043016 0.433483987,5.02896856 L0.207561266,5.12839888 L-0.344999999,3.86385309 C3.46204154,2.20031215 6.00002603,0.473240407 7.26891842,-1.28069234 L7.33938714,-1.38 Z" fill="url(#linearGradient-30)" mask="url(#mask-29)"></path>
                <polygon id="路径-23" fill-opacity="0.536931818" fill="#5161F0" filter="url(#filter-31)" mask="url(#mask-29)" points="2.26058291 1.87419944 4.07128062 1.41004152 7.79229285 11.8233881 11.4512073 11.04 5.91901045 -1.38 2.87583956 -1.38"></polygon>
            </g>
        </g>
        <g id="编组-5" transform="translate(110.430385, 82.000000) rotate(42.000000) translate(-110.430385, -82.000000) translate(105.930385, 77.500000)">
            <rect id="矩形" fill="#F4F4F4" x="0" y="0" width="9" height="9" rx="0.5"></rect>
            <path d="M6.91466268,4.54507728 L5.89363481,3.58059336 C5.77722582,3.48642403 5.60369982,3.4927554 5.49532738,3.59512624 C5.38695494,3.69749707 5.38025238,3.86141331 5.47994249,3.97137564 L6.2926572,4.74185417 L5.4770085,5.51233271 C5.39327947,5.5800655 5.3568083,5.68641607 5.38258979,5.78765933 C5.40837127,5.88890258 5.49205412,5.96795112 5.59923288,5.99230484 C5.70641163,6.01665856 5.81899712,5.98220715 5.89070083,5.90311499 L6.91172869,4.93863106 C6.96766256,4.88696186 6.99943469,4.81633083 6.9999929,4.74243152 C7.00053655,4.66853221 6.96982064,4.59748549 6.91466268,4.54507728 L6.91466268,4.54507728 Z M3.52297241,3.58059336 C3.40853589,3.47313555 3.22371661,3.47313555 3.10928009,3.58059336 L2.08531823,4.54507728 C1.97156059,4.65317638 1.97156059,4.82776046 2.08531823,4.93585956 L3.1063461,5.90034348 C3.22275509,5.99451281 3.39628109,5.98818144 3.50465353,5.8858106 C3.61302597,5.78343976 3.61972853,5.61952353 3.52003842,5.5095612 L2.7102577,4.74185417 L3.52297241,3.97137564 C3.63673005,3.86327654 3.63673005,3.68869245 3.52297241,3.58059336 L3.52297241,3.58059336 Z M4.91074879,3.51962023 C4.83761836,3.49365859 4.75654668,3.49631989 4.68550054,3.52701437 C4.6144544,3.55770885 4.5593014,3.61390147 4.53226433,3.68313906 L3.77822938,5.6065639 C3.73430486,5.70062484 3.74910774,5.8099252 3.81668525,5.89051135 C3.88426276,5.9710975 3.99351073,6.00972772 4.1004966,5.99086741 C4.20748246,5.97200709 4.29462659,5.89875532 4.32688516,5.80056928 L5.0809201,3.87714445 C5.1085543,3.808418 5.10614954,3.73213596 5.07423501,3.6650846 C5.04232047,3.59803323 4.98351131,3.54570688 4.91074879,3.51962023 L4.91074879,3.51962023 Z" id="形状" fill="url(#linearGradient-32)"></path>
        </g>
        <g id="编组-6" transform="translate(125.246910, 84.000000) rotate(38.000000) translate(-125.246910, -84.000000) translate(120.746910, 79.500000)">
            <rect id="矩形备份-4" fill="#F4F4F4" x="0" y="0" width="9" height="9" rx="0.5"></rect>
            <path d="M6.51500966,3.04999514 C6.67329274,3.44020096 6.59789746,3.90597953 6.28732245,4.22322202 C5.94531915,4.57260752 5.42843123,4.6270506 5.02684753,4.39561233 L4.73655316,4.71963263 L4.94364794,4.9320518 L5.06741362,4.80555677 C5.13075467,4.74088702 5.23333312,4.74088702 5.29666937,4.80555677 L6.30089245,5.84177895 C6.36417114,5.9064487 6.36417114,6.01120967 6.30089245,6.07587942 L5.8424433,6.54420027 C5.77916461,6.60887002 5.67658616,6.60887002 5.61324511,6.54420027 L4.60902683,5.50797809 C4.54574814,5.4433707 4.54574814,5.33855217 4.60902683,5.27387283 L4.72208137,5.15840552 L4.52461846,4.95615066 L3.13572981,6.50617165 C3.00914365,6.63552075 2.80390042,6.63552075 2.67731425,6.50617165 L2.56271515,6.38906386 C2.43610021,6.25972436 2.43610021,6.0500921 2.56271515,5.92073821 L4.14482159,4.56710565 L3.09397893,3.49082584 L2.76224067,3.49070112 L2.37836175,2.85986167 L2.68757929,2.54353056 L3.31912386,2.9382789 L3.32342174,3.27029537 L4.38631384,4.36043779 L4.69561291,4.09582041 C4.39333148,3.6747859 4.42482213,3.08190791 4.79746226,2.7012668 C5.10652629,2.38554488 5.55982853,2.30718521 5.94070947,2.46590479 L5.25961449,3.15202677 L5.8325716,3.73739784 L6.51500966,3.04999514 Z M2.94320755,6.08580868 C2.87986171,6.02114373 2.77725448,6.02114373 2.71394701,6.08580868 C2.65070669,6.15048323 2.65070669,6.25536412 2.71394701,6.32003387 C2.77725448,6.38469882 2.87986171,6.38469882 2.94320755,6.32003387 C3.00648625,6.25536412 3.00648625,6.15048323 2.94320755,6.08580868 Z" id="形状" fill="url(#linearGradient-33)"></path>
        </g>
    </g>
</svg>