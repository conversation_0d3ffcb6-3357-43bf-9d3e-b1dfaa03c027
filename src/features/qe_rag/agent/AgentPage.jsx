import { useState, useEffect } from 'react';
import { useNavigate } from 'umi';
import { Card, Button, Pagination, Input, message, Spin, Empty} from 'antd';
import { PlusOutlined, SearchOutlined, UserOutlined } from '@ant-design/icons';
import { getAgentPaging } from 'COMMON/api/qe_rag/agent';
import { getAllGroupsList } from 'COMMON/api/qe_rag/workgroup';
import PageHeader from 'COMMON/components/PageHeader';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import { getAvatarColor } from 'FEATURES/qe_rag/utils';
import styles from './AgentPage.module.less';

// 自定义 hook：根据屏幕大小动态计算页面大小
const useResponsivePageSize = () => {
    const [pageSize, setPageSize] = useState(12);

    useEffect(() => {
        const calculatePageSize = () => {
            const screenWidth = window.innerWidth;
            // 根据屏幕宽度动态调整每页显示数量
            if (screenWidth < 768) {
                // 小屏幕（手机）：15条
                setPageSize(15);
            } else if (screenWidth < 1200) {
                // 中等屏幕（平板）：18条
                setPageSize(18);
            } else {
                // 大屏幕（桌面）：24条
                setPageSize(24);
            }
        };

        // 初始计算
        calculatePageSize();

        // 监听窗口大小变化
        const handleResize = () => {
            calculatePageSize();
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return pageSize;
};

const AgentPage = ({allWorkGroupList}) => {
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('my');
    const [agentList, setAgentList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [searchKeyword, setSearchKeyword] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const pageSize = useResponsivePageSize();
    const [total, setTotal] = useState(0);

    useEffect(() => {
        fetchAgentList();
    }, [activeTab, searchKeyword, currentPage, pageSize]);

    // 当页面大小改变时，重置到第一页
    useEffect(() => {
        if (currentPage !== 1) {
            setCurrentPage(1);
        }
    }, [pageSize]);



    const fetchAgentList = async () => {
        setLoading(true);
        try {
            const params = {
                type: activeTab === 'my' ? 0 : 1, // 0: 我的智能体, 1: 全部智能体
                keyword: searchKeyword,
                username: '',
                pageNum: currentPage,
                pageSize: pageSize
            };

            const res = await getAgentPaging(params);

            // 处理分页API的返回数据结构
            const { list = [], total: totalCount = 0 } = res || {};
            setAgentList(list);
            setTotal(totalCount);
        }  finally {
            setLoading(false);
        }
    };


    const handleSearch = (value) => {
        setSearchKeyword(value);
        setCurrentPage(1);
    };

    const handleTabChange = (key) => {
        setActiveTab(key);
        setCurrentPage(1);
    };

    const handleCreateAgent = () => {
        navigate('/qe_rag/agent/edit');
    };

    const handleEditAgent = (agentId) => {
        navigate(`/qe_rag/agent/edit?id=${agentId}`);
    };

    // 格式化工作组信息
    const formatGroupInfo = (groupId) => {
        if (!groupId) {
            return '默认工作组';
        }

        const numericGroupId = Number(groupId);
        const group = allWorkGroupList.find((g) => g.id === numericGroupId);
        return group ? group.name : `工作组 ${groupId}`;
    };

    const renderAgentCard = (agent) => {
        // 获取图片URL，优先使用url字段，其次使用imageUrl字段
        const imageUrl = agent.url || agent.imageUrl;

        return (
            <Card
                key={agent.id}
                className={styles.agentCard}
                hoverable
                onClick={() => handleEditAgent(agent.id)}
            >
                <div className={styles.cardContent}>
                    {/* 卡片头部 */}
                    <div className={styles.cardHeader}>
                        <div
                            className={styles.cardCover}
                            style={{
                                background: imageUrl ? 'transparent' : getAvatarColor(agent.name)
                            }}
                        >
                            {imageUrl ? (
                                <img src={imageUrl} alt={agent.name} />
                            ) : (
                                <span className={styles.iconText}>{agent.name?.charAt(0) || 'A'}</span>
                            )}
                        </div>
                        <div className={styles.cardTitle}>
                            <h3 className={styles.agentName}>{agent.name}</h3>
                            <div className={styles.cardId}>ID: {agent.id}</div>
                            {/* 智能体描述 */}
                            <div className={styles.agentDesc}>{agent.description || '暂无描述'}</div>
                        </div>
                    </div>

                    {/* 底部信息 */}
                    <div className={styles.cardFooter}>
                        <div className={styles.tags}>
                            <span className={styles.tag}>
                                <span className={styles.groupId}>
                                    #{agent.groupId}
                                </span>
                                {formatGroupInfo(agent.groupId)}
                            </span>
                        </div>
                        <div className={styles.createTime}>
                            {agent.createAt}
                        </div>
                    </div>
                </div>
            </Card>
        );
    };

    return (
        <div className={styles.container}>
            {/* 固定头部区域 */}
            <div className={styles.fixedHeader}>
                {/* 页面头部 */}
                <PageHeader
                    title="智能体"
                    tabs={[
                        { key: 'my', label: '我发布的' },
                        { key: 'all', label: '全部' }
                    ]}
                    activeTab={activeTab}
                    onTabChange={handleTabChange}
                    actionButton={{
                        text: '新建智能体',
                        icon: <PlusOutlined />,
                        onClick: handleCreateAgent
                    }}
                />

                {/* 搜索栏 */}
                <div className={styles.searchBar}>
                    <Input
                        placeholder="搜索智能体名称..."
                        prefix={<SearchOutlined />}
                        value={searchKeyword}
                        onChange={(e) => setSearchKeyword(e.target.value)}
                        onPressEnter={() => handleSearch(searchKeyword)}
                        className={styles.searchInput}
                    />
                </div>
            </div>

            {/* 可滚动内容区域 */}
            <div className={styles.scrollableContent}>
                <Spin spinning={loading} tip="加载中...">
                    {/* 智能体卡片列表 */}
                    {agentList.length === 0 && <Empty description="暂无智能体" />}
                    <div className={styles.cardGrid}>
                        {agentList.map((agent) => renderAgentCard(agent))}
                    </div>

                </Spin>

                {/* 分页 */}
                {total > pageSize && (
                    <div className={styles.pagination}>
                        <Pagination
                            current={currentPage}
                            pageSize={pageSize}
                            total={total}
                            onChange={(page) => setCurrentPage(page)}
                            showSizeChanger={false}
                            showQuickJumper
                            showTotal={(total, range) =>
                                `共 ${total} 条，第 ${range[0]}-${range[1]} 条，每页 ${pageSize} 条`
                            }
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    username: state.common.base.username,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(AgentPage);

