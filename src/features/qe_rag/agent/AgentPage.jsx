import { useState, useEffect } from 'react';
import { useNavigate } from 'umi';
import { Card, Button, Pagination, Input, message, Spin } from 'antd';
import { PlusOutlined, SearchOutlined, UserOutlined } from '@ant-design/icons';
import { getAgent } from 'COMMON/api/qe_rag/agent';
import { getAllGroupsList } from 'COMMON/api/qe_rag/workgroup';
import PageHeader from 'COMMON/components/PageHeader';
import { getAvatarColor } from 'FEATURES/qe_rag/utils';
import styles from './AgentPage.module.less';

const AgentPage = () => {
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('my');
    const [agentList, setAgentList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [searchKeyword, setSearchKeyword] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize] = useState(12);
    const [total, setTotal] = useState(0);
    const [allWorkGroupList, setAllWorkGroupList] = useState([]);

    useEffect(() => {
        fetchAgentList();
        fetchAllWorkGroups();
    }, [activeTab, searchKeyword, currentPage]);

    const fetchAllWorkGroups = async () => {
        try {
            const response = await getAllGroupsList();
            setAllWorkGroupList(response?.data || []);
        } catch (error) {
            console.error('获取工作组列表失败:', error);
        }
    };

    const fetchAgentList = async () => {
        setLoading(true);
        try {
            const params = {
                type: activeTab === 'my' ? 0 : 1, // 0: 我的智能体, 1: 全部智能体
                keyword: searchKeyword,
                username: '',
                pageNum: currentPage,
                pageSize: pageSize
            };

            const response = await getAgent(params);
            if (response && Array.isArray(response)) {
                setAgentList(response);
                setTotal(response.length); // 如果API返回总数，使用API的总数
            } else {
                setAgentList([]);
                setTotal(0);
            }
        } catch (error) {
            console.error('获取智能体列表失败:', error);
            message.error('获取智能体列表失败');
            setAgentList([]);
            setTotal(0);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setCurrentPage(1);
    };

    const handleTabChange = (key) => {
        setActiveTab(key);
        setCurrentPage(1);
    };

    const handleCreateAgent = () => {
        navigate('/qe_rag/agent/edit');
    };

    const handleEditAgent = (agentId) => {
        navigate(`/qe_rag/agent/edit?id=${agentId}`);
    };

    // 格式化工作组信息
    const formatGroupInfo = (groupId) => {
        if (!groupId) {
            return '默认工作组';
        }

        const numericGroupId = Number(groupId);
        const group = allWorkGroupList.find((g) => g.id === numericGroupId);
        return group ? group.name : `工作组 ${groupId}`;
    };

    const renderAgentCard = (agent) => {
        // 获取图片URL，优先使用url字段，其次使用imageUrl字段
        const imageUrl = agent.url || agent.imageUrl;

        return (
            <Card
                key={agent.id}
                className={styles.agentCard}
                hoverable
                onClick={() => handleEditAgent(agent.id)}
            >
                <div className={styles.cardContent}>
                    {/* 卡片头部 */}
                    <div className={styles.cardHeader}>
                        <div
                            className={styles.cardCover}
                            style={{
                                background: imageUrl ? 'transparent' : getAvatarColor(agent.name)
                            }}
                        >
                            {imageUrl ? (
                                <img src={imageUrl} alt={agent.name} />
                            ) : (
                                <span className={styles.iconText}>{agent.name?.charAt(0) || 'A'}</span>
                            )}
                        </div>
                        <div className={styles.cardTitle}>
                            <h3 className={styles.agentName}>{agent.name}</h3>
                            <div className={styles.cardId}>ID: {agent.id}</div>
                            {/* 智能体描述 */}
                            <div className={styles.agentDesc}>{agent.description || '暂无描述'}</div>
                        </div>
                    </div>

                    {/* 底部信息 */}
                    <div className={styles.cardFooter}>
                        <div className={styles.tags}>
                            <span className={styles.tag}>
                                <span className={styles.groupId}>
                                    #{agent.groupId}
                                </span>
                                {formatGroupInfo(agent.groupId)}
                            </span>
                        </div>
                        <div className={styles.createTime}>
                            {agent.createAt}
                        </div>
                    </div>
                </div>
            </Card>
        );
    };

    return (
        <div className={styles.container}>
            {/* 固定头部区域 */}
            <div className={styles.fixedHeader}>
                {/* 页面头部 */}
                <PageHeader
                    title="智能体"
                    tabs={[
                        { key: 'my', label: '我发布的' },
                        { key: 'all', label: '全部' }
                    ]}
                    activeTab={activeTab}
                    onTabChange={handleTabChange}
                    actionButton={{
                        text: '新建智能体',
                        icon: <PlusOutlined />,
                        onClick: handleCreateAgent
                    }}
                />

                {/* 搜索栏 */}
                <div className={styles.searchBar}>
                    <Input
                        placeholder="搜索智能体名称..."
                        prefix={<SearchOutlined />}
                        value={searchKeyword}
                        onChange={(e) => setSearchKeyword(e.target.value)}
                        onPressEnter={() => handleSearch(searchKeyword)}
                        className={styles.searchInput}
                    />
                </div>
            </div>

            {/* 可滚动内容区域 */}
            <div className={styles.scrollableContent}>
                <Spin spinning={loading} tip="加载中...">
                    {/* 智能体卡片列表 */}
                    <div className={styles.cardGrid}>
                        {agentList.map((agent) => renderAgentCard(agent))}
                    </div>

                    {agentList.length === 0 && !loading && (
                        <div className={styles.emptyState}>
                            <div className={styles.emptyIcon}>
                                <UserOutlined />
                            </div>
                            <div className={styles.emptyText}>
                                {searchKeyword ? '没有找到相关智能体' : '暂无智能体'}
                            </div>
                            {!searchKeyword && (
                                <Button
                                    type="primary"
                                    icon={<PlusOutlined />}
                                    onClick={handleCreateAgent}
                                >
                                    创建第一个智能体
                                </Button>
                            )}
                        </div>
                    )}
                </Spin>

                {/* 分页 */}
                {total > pageSize && (
                    <div className={styles.pagination}>
                        <Pagination
                            current={currentPage}
                            pageSize={pageSize}
                            total={total}
                            onChange={(page) => setCurrentPage(page)}
                            showSizeChanger={false}
                            showQuickJumper
                            showTotal={(total) =>
                                `共 ${total} 条 ${Math.ceil(total / pageSize)} 页/页`
                            }
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default AgentPage;
