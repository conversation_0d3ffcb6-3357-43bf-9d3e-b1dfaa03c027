import { useState, useEffect } from 'react';
import {
    Input,
    Button,
    Card,
    Pagination,
    message,
    Spin,
    Dropdown,
    Select,
    Modal
} from 'antd';
import {
    SearchOutlined,
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    MoreOutlined
} from '@ant-design/icons';
import { useNavigate } from 'umi';
import { getBooks, remove } from 'COMMON/api/qe_rag/book';
import { getJoinedList, getAllGroupsList } from 'COMMON/api/qe_rag/workgroup';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import PageHeader from 'COMMON/components/PageHeader';
import EllipsisTooltip from 'COMMON/components/EllipsisTooltip';
import KnowledgeFormModal from './components/KnowledgeFormModal';
import { getAvatarColor } from 'FEATURES/qe_rag/utils';
import commonStyles from 'FEATURES/qe_rag/common.module.less';

import styles from './KnowledgePage.module.less';

// 计算屏幕最大容纳条数的函数（只计算一次）
const calculateMaxPageSize = () => {
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    console.log('知识库页面 - 屏幕尺寸:', screenWidth, 'x', screenHeight);

    // 估算卡片网格布局能容纳的最大条数
    // 假设每个卡片大小约为 280x200px，加上间距
    const cardWidth = 300; // 卡片宽度 + 间距
    const cardHeight = 220; // 卡片高度 + 间距
    const headerHeight = 200; // 页面头部高度（包括搜索栏等）
    const paginationHeight = 80; // 分页组件高度

    const availableWidth = screenWidth - 40; // 减去页面左右边距
    const availableHeight = screenHeight - headerHeight - paginationHeight;

    const cols = Math.floor(availableWidth / cardWidth);
    const rows = Math.floor(availableHeight / cardHeight);

    const maxItems = Math.max(cols * rows, 15); // 最少15条

    console.log(`知识库页面计算结果: ${cols}列 x ${rows}行 = ${maxItems}条`);

    return maxItems;
};

// 自定义 hook：计算页面大小（只计算一次）
const usePageSize = () => {
    const [pageSize] = useState(() => calculateMaxPageSize());
    return pageSize;
};

const KnowledgePage = ({
    allWorkGroupList,
    joinedWorkGroupList
}) => {
    const navigate = useNavigate();
    // TODO：mock 数据
    const username = 'xushixuan01';
    const pageSize = usePageSize();

    // 状态管理
    const [loading, setLoading] = useState(false);
    const [books, setBooks] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingBook, setEditingBook] = useState(null);
    const [activeTab, setActiveTab] = useState('my'); // 'my' 或 'all'
    const [searchParams, setSearchParams] = useState({
        name: '',
        owner: username, // 默认显示当前用户的知识库
        groupId: '', // 工作组筛选
        page: 1,
        size: pageSize
    });
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: pageSize,
        total: 0
    });

    // 初始化数据
    useEffect(() => {
        // 更新搜索参数中的页面大小
        const updatedParams = { ...searchParams, size: pageSize };
        setSearchParams(updatedParams);
        setPagination(prev => ({ ...prev, pageSize }));
        fetchBooks(updatedParams);
    }, [pageSize]);


    // 获取知识库列表
    const fetchBooks = async (params = searchParams) => {
        setLoading(true);
        try {
            const response = await getBooks(params);
            setBooks(response.items || []);
            setPagination({
                current: response.page || 1,
                pageSize: pageSize,
                total: response.total || 0
            });
        } catch (error) {
            console.error('获取知识库列表失败:', error);
            // 如果API调用失败，使用模拟数据进行演示
            const mockBooks = [
                {
                    id: 1,
                    name: '123',
                    description: '前端测试(整合-EP-EP)',
                    owner: 'xushixuan01',
                    createAt: '2025-06-26',
                    embeddingRule: JSON.stringify({
                        delimiter: ['\\n', '.'],
                        chunkTokenNum: 600
                    })
                },
                {
                    id: 2,
                    name: '123',
                    description: '前端测试(整合-EP-EP)',
                    owner: 'xushixuan01',
                    createAt: '2025-06-26',
                    embeddingRule: JSON.stringify({
                        delimiter: ['\\n', '.'],
                        chunkTokenNum: 600
                    })
                },
                {
                    id: 3,
                    name: '123',
                    description: '前端测试(整合-EP-EP)',
                    owner: 'xushixuan01',
                    createAt: '2025-06-26',
                    embeddingRule: JSON.stringify({
                        delimiter: ['\\n', '.'],
                        chunkTokenNum: 600
                    })
                }
            ];
            setBooks(mockBooks);
            setPagination({
                current: 1,
                pageSize: pageSize,
                total: mockBooks.length
            });
        } finally {
            setLoading(false);
        }
    };

    // 打开新建/编辑模态框
    const handleOpenModal = (book = null) => {
        setEditingBook(book);
        setModalVisible(true);
    };

    // 关闭模态框
    const handleCloseModal = () => {
        setModalVisible(false);
        setEditingBook(null);
    };

    // 模态框保存成功后的回调
    const handleModalSuccess = () => {
        fetchBooks();
    };

    // 搜索处理
    const handleSearch = (values) => {
        const newParams = {
            ...searchParams,
            ...values,
            page: 1,
            size: pageSize
        };
        setSearchParams(newParams);
        fetchBooks(newParams);
    };

    // 分页处理
    const handlePageChange = (page) => {
        const newParams = {
            ...searchParams,
            page,
            size: pageSize
        };
        setSearchParams(newParams);
        fetchBooks(newParams);
    };

    // 标签切换处理
    const handleTabChange = (tab) => {
        setActiveTab(tab);
        const newParams = {
            ...searchParams,
            page: 1,
            size: pageSize,
            // 根据标签类型调整查询参数
            owner: tab === 'my' ? username : '',
            groupId: '' // 切换tab时重置工作组筛选
        };
        setSearchParams(newParams);
        fetchBooks(newParams);
    };





    // 删除知识库
    const handleDelete = async (id) => {
        try {
            const response = await remove({
                id,
                owner: username
            });
            if (response?.code === 200) {
                message.success('删除成功');
                fetchBooks();
            }
        } catch (error) {
            console.error('删除失败:', error);
            message.error('删除失败');
        }
    };

    // 查看文档
    const handleViewDocs = (id) => {
        navigate(`/qe_rag/knowledge/docs?knowledgeId=${id}`);
    };

    // 检查是否为管理员
    const isAdmin = (admins) => {
        return username && admins?.includes(username);
    };

    return (
        <div className={styles.container}>
            {/* 固定头部区域 */}
            <div className={styles.fixedHeader}>
                {/* 页面头部 */}
                <PageHeader
                    title="知识库"
                    tabs={[
                        { key: 'my', label: '我的知识库' },
                        { key: 'all', label: '全部知识库' }
                    ]}
                    activeTab={activeTab}
                    onTabChange={handleTabChange}
                    actionButton={{
                        text: '新建知识库',
                        icon: <PlusOutlined />,
                        onClick: () => handleOpenModal()
                    }}
                />

                {/* 搜索栏 */}
                <div className={styles.searchBar}>
                    <Input
                        placeholder="搜索知识库名称..."
                        prefix={<SearchOutlined />}
                        value={searchParams.name}
                        onChange={(e) => setSearchParams({ ...searchParams, name: e.target.value })}
                        onPressEnter={() => handleSearch(searchParams)}
                        className={styles.searchInput}
                    />
                    <Select
                        placeholder={'请选择工作组'}
                        value={searchParams.groupId || undefined}
                        onChange={(value) => {
                            const newParams = { ...searchParams, groupId: value || '', page: 1, size: pageSize };
                            setSearchParams(newParams);
                            handleSearch(newParams);
                        }}
                        allowClear
                        className={styles.workGroupSelect}
                        style={{ width: 240 }}
                    >
                        {(activeTab === 'my' ? joinedWorkGroupList : allWorkGroupList)?.map((group) => (
                            <Option key={group.id} value={group.id}>
                                <span className={commonStyles.groupId}>#{group.id}</span>
                                {group.name}
                            </Option>
                        ))}
                    </Select>
                </div>
            </div>

            {/* 可滚动内容区域 */}
            <div className={styles.scrollableContent}>
                {/* 知识库卡片列表 */}
                <Spin spinning={loading}>
                    <div className={styles.cardGrid}>
                        {books.map((book) => {
                            // 根据 groupId 从 allWorkGroupList 中找到对应的工作组名称
                            const workGroup = allWorkGroupList?.find(group => group.id === book.groupId);
                            const groupName = workGroup?.name || '默认工作组';

                            // 创建下拉菜单项
                            const menuItems = [
                                {
                                    key: 'edit',
                                    label: '编辑',
                                    icon: <EditOutlined />,
                                    disabled: !isAdmin(book.owner),
                                    onClick: () => handleOpenModal(book)
                                },
                                {
                                    key: 'delete',
                                    label: '删除',
                                    icon: <DeleteOutlined />,
                                    disabled: !isAdmin(book.owner),
                                    danger: true,
                                    onClick: () => {
                                        Modal.confirm({
                                            title: '确认删除',
                                            content: '确认要删除该知识库吗？删除后无法恢复。',
                                            okText: '确认',
                                            cancelText: '取消',
                                            okType: 'danger',
                                            onOk: () => handleDelete(book.id)
                                        });
                                    }
                                }
                            ];

                            return (
                                <Card
                                    key={book.id}
                                    className={styles.knowledgeCard}
                                    hoverable
                                    onClick={() => handleViewDocs(book.id)}
                                >
                                    <div className={styles.cardContent}>
                                        {/* 卡片头部 */}
                                        <div className={styles.cardHeader}>
                                            <div
                                                className={styles.cardIcon}
                                                style={{ background: getAvatarColor(book.name) }}
                                            >
                                                <span className={styles.iconText}>
                                                    {book.name?.charAt(0) || 'K'}
                                                </span>
                                            </div>
                                            <div className={styles.cardTitle}>
                                                <h3 className={styles.knowledgeName}>
                                                    {book.name}
                                                </h3>
                                                <div className={styles.cardId}>ID: {book.id}</div>
                                            </div>
                                            <div onClick={(e) => e.stopPropagation()}>
                                                <Dropdown
                                                    menu={{ items: menuItems }}
                                                    trigger={['hover']}
                                                    placement="bottomRight"
                                                >
                                                    <Button
                                                        type="text"
                                                        icon={<MoreOutlined />}
                                                        className={styles.moreButton}
                                                    />
                                                </Dropdown>
                                            </div>
                                        </div>

                                        {/* 知识库描述 */}
                                        {/* <div className={styles.cardDescription}>
                                        {book.description || '暂无描述'}
                                    </div> */}
                                        <EllipsisTooltip
                                            text={book.description}
                                            style={{
                                                margin: '10px 0',
                                                fontSize: '12px',
                                                color: '#666'
                                            }}
                                        />
                                        {/* 底部信息 */}
                                        <div className={styles.cardFooter}>
                                            <div className={styles.tags}>
                                                <span className={styles.tag}>
                                                    <span className={styles.groupId}>
                                                        # {book.groupId}
                                                    </span>
                                                    {groupName}
                                                </span>
                                            </div>
                                            <div className={styles.updateTime}>
                                                {book.updateAt || book.createAt}
                                            </div>
                                        </div>
                                    </div>
                                </Card>
                            );
                        })}
                    </div>
                </Spin>

                {/* 分页 */}
                {pagination.total > pagination.pageSize && (
                    <div className={styles.pagination}>
                        <Pagination
                            current={pagination.current}
                            pageSize={pagination.pageSize}
                            total={pagination.total}
                            showSizeChanger={false}
                            simple
                            // showTotal={(total, range) =>
                            //     `共 ${total} 条`
                            // }
                            onChange={handlePageChange}
                        />
                    </div>
                )}
            </div>

            {/* 新建/编辑知识库弹窗 */}
            <KnowledgeFormModal
                visible={modalVisible}
                onCancel={handleCloseModal}
                editingBook={editingBook}
                onSuccess={handleModalSuccess}
                joinedWorkGroupList={joinedWorkGroupList || []}
            />
        </div>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    username: state.common.base.username,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(KnowledgePage);
