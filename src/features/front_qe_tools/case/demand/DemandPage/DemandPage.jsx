import { Outlet } from 'umi';
import { Layout } from 'antd';
import { useState } from 'react';
import { getQueryParams } from 'COMMON/utils/utils';
import { CustomResizableBox } from 'COMMON/components/common';
import LayoutSider from './LayoutSider';
import styles from './DemandPage.module.less';

function DemandPage() {
    const query = getQueryParams();
    const [asider, setAsider] = useState(true);

    return (
        <Layout hasSider style={{ backgroundColor: '#fff' }}>
            <CustomResizableBox asider={asider} setAsider={setAsider} showResizableBoxIcon={false}>
                <div style={{ width: '100%' }}>
                    <LayoutSider />
                </div>
            </CustomResizableBox>
            <Layout className={styles.right}>
                <Outlet key={'docNodeId_' + query?.docNodeId} />
            </Layout>
        </Layout>
    );
}

export default DemandPage;
