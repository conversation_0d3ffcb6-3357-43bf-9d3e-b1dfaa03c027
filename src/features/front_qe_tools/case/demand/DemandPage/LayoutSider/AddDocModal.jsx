import { useState, useImperativeHandle, forwardRef, useRef } from 'react';
import { Modal, Form, message } from 'antd';
// redux
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import demandModel from 'COMMON/models/demandModel';
// components
import DemandBind from 'FEATURES/components/demand/DemandBind/DemandBind';
// api
import { createTreeNodeDoc } from 'COMMON/api/front_qe_tools/demand';
// utils
import { getQueryParams } from 'COMMON/utils/utils';
import styles from './LayoutSider.module.less';

function AddDocModal(props, ref) {
    const { getDocList } = props;
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const query = getQueryParams();
    const demandBindRef = useRef();

    useImperativeHandle(
        ref,
        () => ({
            show: showModal
        }),
        [showModal]
    );
    const showModal = () => {
        setOpen(true);
    };
    const hideModal = () => {
        form.resetFields();
        setOpen(false);
    };

    const handleSubmit = async () => {
        try {
            setLoading(true);
            // 需求绑定所需表单值
            let demandFormValues = await demandBindRef.current.handleSubmit();
            let params = {
                treeNodeId: query?.treeNodeId,
                generateTaskKey: demandFormValues?.generateTaskKey,
                docNodeTreeList: demandFormValues?.docNodeTreeList
            };
            await createTreeNodeDoc(params);
            // 重新获取文档列表
            await getDocList({ treeNodeId: query?.treeNodeId });
            localStorage.setItem('docGenerateTaskKey', demandFormValues?.generateTaskKey);
            message.success('创建成功');
            hideModal();
            setLoading(false);
        } catch (err) {
            setLoading(false);
            console.log(err);
        }
    };

    return (
        <Modal
            title="添加需求"
            open={open}
            onCancel={hideModal}
            okText="确认"
            cancelText="取消"
            onOk={handleSubmit}
            loading={loading}
            destroyOnClose
            style={{ widht: 400, overflow: 'scroll' }}
        >
            <DemandBind ref={demandBindRef} labelCol={{ span: 5 }} showTitle={false} />
        </Modal>
    );
}

export default connectModel([baseModel, demandModel], (state) => ({
    docList: state.demand.doc.docList
}))(forwardRef(AddDocModal));
