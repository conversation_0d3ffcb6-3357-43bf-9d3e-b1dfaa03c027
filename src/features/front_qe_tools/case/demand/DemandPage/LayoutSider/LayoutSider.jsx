import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import { Tooltip, Spin, Tree, message } from 'antd';
import { useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import { PlusOutlined, CaretDownFilled, CaretRightFilled, DeleteOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import demandModel from 'COMMON/models/demandModel';
import commonModel from 'COMMON/models/commonModel';
import NoContent from 'COMMON/components/common/NoContent';
import IcafeIcon from 'COMMON/components/IcafeIcon';
import { deleteTreeNodeDoc } from 'COMMON/api/front_qe_tools/demand';
import DeleteCase from './DeleteCase';
import AddDocModal from './AddDocModal';
import styles from './LayoutSider.module.less';

const { DirectoryTree } = Tree;

function LayoutSider(props) {
    const { currentModule, setCurrentDoc, getDocList, docList } = props;
    const [loading, setLoading] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const addDocModalRef = useRef(null);
    const query = getQueryParams();
    const navigate = useNavigate();

    const selectKey = useMemo(() => {
        const { docNodeId } = query;
        if (docNodeId) {
            return +docNodeId;
        }
        return undefined;
    }, [query]);

    const findCurrentDoc = (tree, docNodeId, data = null) => {
        tree.forEach((ele) => {
            if (ele.docNodeId === docNodeId) {
                data = ele;
            } else if (ele.children) {
                data = findCurrentDoc(ele.children, docNodeId, data);
            }
        });
        return data;
    };

    // 首次进入获取 currentDoc
    useEffect(() => {
        setLoading(true);
        getDocList({ treeNodeId: query?.treeNodeId })
            .then((res) => {
                if (isEmpty(res?.tree)) {
                    setLoading(false);
                    return;
                }
                if (!query?.docNodeId) {
                    handleClickDirectoryTreeNode(
                        res?.tree?.[0]?.docNodeId + '',
                        res?.tree?.[0],
                        'case'
                    );
                }
                setLoading(false);
            })
            .catch((err) => {
                setLoading(false);
            });
    }, [query?.treeNodeId]);

    // 首次进入获取 currentDoc
    useEffect(() => {
        if (query?.docNodeId && !isEmpty(docList)) {
            let _curDoc = findCurrentDoc(docList, +query?.docNodeId);
            setCurrentDoc(_curDoc);
        }
    }, [query?.docNodId, docList]);

    // 删除需求
    const handleDeleteCase = (node) => {
        deleteTreeNodeDoc({
            docNodeId: node?.docNodeId
        })
            .then(() => {
                message.success('删除成功');
                setCurrentDoc(null);
                navigate(
                    stringifyUrl({
                        url: '/' + currentModule + '/demand',
                        query: {
                            groupId: query?.groupId,
                            treeNodeId: query?.treeNodeId,
                            moduleId: query?.moduleId ?? query?.productId
                        }
                    })
                );
                getDocList({ treeNodeId: query?.treeNodeId });
            })
            .catch((err) => {});
    };

    // 跳转需求
    const handleClickDirectoryTreeNode = (value, node, view) => {
        if (isEmpty(value)) {
            message.warning('请重新选择');
            return;
        }
        setCurrentDoc(node);
        navigate(
            stringifyUrl({
                url: '/' + currentModule + '/demand/detail',
                query: {
                    ...query,
                    docNodeId: value,
                    view: view ?? query?.view ?? 'doc'
                }
            })
        );
    };

    const getDirectotyTree = useCallback(
        (data) => {
            if (!data) {
                return;
            }
            return data.map((item) => {
                return {
                    ...item,
                    title: item.nodeName,
                    key: item.docNodeId,
                    switcherIcon: ({ expanded }) => {
                        let jsx = [];
                        if (!expanded & !isEmpty(item.children)) {
                            jsx.push(<CaretRightFilled className={styles.arrowIcon} />);
                        }
                        if (expanded & !isEmpty(item.children)) {
                            jsx.push(<CaretDownFilled className={styles.arrowIcon} />);
                        }
                        return jsx;
                    },
                    children: getDirectotyTree(item.children)
                };
            });
        },
        [docList]
    );

    return (
        <>
            {contextHolder}
            <div className={styles.layoutSider}>
                <div className={styles.siderHeader}>
                    <div className={styles.siderTitle}>需求列表</div>
                    <Tooltip title="新建">
                        <PlusOutlined
                            className={styles.addDemandIcon}
                            onClick={() => addDocModalRef?.current?.show()}
                        />
                    </Tooltip>
                </div>
                <Spin spinning={loading}>
                    {isEmpty(docList) ? (
                        <NoContent text="暂无需求，请先创建需求" className={styles.noContent} />
                    ) : (
                        <DirectoryTree
                            icon={false}
                            treeData={getDirectotyTree(docList)}
                            selectedKeys={[selectKey]}
                            onSelect={(value, e) => {
                                const { selectedNodes } = e;
                                handleClickDirectoryTreeNode(value, selectedNodes[0]);
                            }}
                            height={window.innerHeight - 100}
                            titleRender={(node) => {
                                return (
                                    <div
                                        className={classnames(styles.treenode, styles.caseItem)}
                                        style={{ position: 'relative' }}
                                    >
                                        <Tooltip title={node.nodeName} placement="left">
                                            <div className={styles.treeNodeTitle}>
                                                <span>
                                                    <IcafeIcon node={node} />
                                                    &nbsp;{node.title}
                                                </span>
                                            </div>
                                        </Tooltip>
                                        <div className={classnames(styles.oprator)}>
                                            <a onClick={(e) => e.stopPropagation()}>
                                                <DeleteCase
                                                    className={styles.activedAddIcon}
                                                    node={node}
                                                    deleteCase={handleDeleteCase}
                                                    text={<DeleteOutlined />}
                                                />
                                            </a>
                                        </div>
                                    </div>
                                );
                            }}
                        />
                    )}
                </Spin>
            </div>
            <AddDocModal ref={addDocModalRef} />
        </>
    );
}

export default connectModel([baseModel, demandModel, commonModel], (state) => ({
    currentModule: state.common.base.currentModule,
    currentSpace: state.common.base.currentSpace,
    currentGroup: state.common.case.currentGroup,
    currentCase: state.common.case.currentCase,
    docList: state.demand.doc.docList,
    currentDoc: state.demand.doc.currentDoc
}))(LayoutSider);
