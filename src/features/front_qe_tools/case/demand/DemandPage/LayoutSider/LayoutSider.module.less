.noContent {
    margin-top: 200px;
}

.layoutSider {
    position: relative;
    height: calc(100vh - 40px);
    background-color: var(--background-color);
    z-index: 99;


    :global {

        .ant-tree,
        .ant-menu,
        .custom-default-tree,
        .custom-default-menu,
        .custom-dark-tree,
        .custom-dark-menu {
            background-color: transparent;
        }

        .ant-tree-switcher,
        .custom-default-tree-switcher,
        .custom-dark-tree-switcher {
            width: 16px;
            line-height: 30px !important;
        }

        .ant-tree-treenode,
        .custom-default-tree-treenode,
        .custom-dark-tree-treenode {
            padding-left: 10px;
        }

    }
}

.siderHeader {
    width: 100%;
    height: 40px;
    overflow: hidden;
    line-height: 40px;

    .siderTitle {
        width: calc(100% - 40px) !important;
        padding: 0 15px;
        font-weight: bold;
        font-size: 14px;
        float: left;

        .collapse {
            color: var(--color2);
        }
    }

    .searchStyle {
        width: calc(100% - 40px) !important;
        margin-left: 10px;
        margin-top: 9px;
    }

    .addIcon {
        margin-left: 5px;
        font-size: 16px;
        color: var(--text-color);
    }
}

// 新增
.addCaseText {
    font-size: 12px;
}

.addDemandIcon {
    float: right;
    position: absolute;
    top: 12px;
    right: 10px;
    color: var(--primary-color);
}

.btnPos {
    float: right;
}

.moreWrap {
    position: absolute;
    right: 0px;
    bottom: -25px;
    font-size: 12px;
    text-decoration: underline;

    .moreBtn {
        float: right;
        margin: 2px 0 0 10px;
        text-decoration: underline;
    }

    :global {
        .ant-checkbox-wrapper {
            font-size: 12px !important;
        }

        .ant-checkbox+span {
            padding: 2px 0 0 2px;
        }
    }
}

.link {
    padding: 2px 4px;
    background-color: #e3eafa;
    color: #2d6dfa;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
}

.icafeOption {
    overflow: hidden;
    white-space: nowrap;
}

@import "RESOURCES/css/common.less";

.docNodeTree {
    max-height: 200px;
    overflow: auto;
    padding: 10px;

    ::-webkit-scrollbar {
        width: 4px;
        /* 滚动条宽度 */
        display: block !important;
    }

    ::-webkit-scrollbar-thumb {
        background-color: rgba(154, 154, 154, 0.943);
        /* 滚动条颜色 */
    }
}

.treenode {
    display: flex;
    align-items: center;
    padding: 0px;
    height: 20px;
    width: 100%;
    margin: 5px;
    overflow: hidden;
    cursor: pointer;

    &:hover {
        .oprator {
            display: block;

            a {
                margin-right: 5px;
            }
        }

        .activedOprator {
            display: block;
            flex-shrink: 0;

            a {
                margin-right: 5px;
                color: #fff !important;
            }
        }
    }
}

.highlightTitle {
    background-color: yellow;
    color: #000;
    padding: 0 2px;
    border-radius: 2px;
}

.showTreenode {
    display: none;
}

.oprator {
    display: none;
    flex-shrink: 0;

    a {
        margin-right: 5px;
    }
}

.activedOprator {
    display: block;
    flex-shrink: 0;

    a {
        margin-right: 5px;
        color: #fff;
    }
}

.treeNodeTitle {
    width: 100px;
    padding: 0px;
    height: 30px;
    line-height: 30px;
    user-select: none;
    font-size: 12px;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.checkedTreeNode {
    background: var(--base-selected-background-color);
}

.checkedNodeIcon {
    font-size: 12px;
    color: #fff !important;
}

.btnPos {
    float: right;
}

.arrowIcon {
    font-size: 10px;
}