.right {
    height: 100%;
    background-color: #fff;
}

.header {
    width: 100%;
    height: 130px;
    padding: 0 30px;
    background-color: #fff;

    .headerTitle {
        float: left;
        width: calc(100% - 120px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 24px;
        font-weight: bold;
    }

    .linkInfo,
    .titleInfo {
        float: left;
        width: 100%;
    }

    .linkInfo {
        position: relative;

        span {
            float: left;
            color: #777;
        }

    }

    .linkUrl {
        float: left;
        max-width: calc(100% - 300px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-decoration: underline;
    }

    .linkRefresh {
        float: left;
        margin-left: 10px;
        width: 160px;
    }

    .headerBtn {
        max-width: 120px;
        float: right;
        cursor: pointer;
    }

    .btnGroup {
        margin-top: 16px;
        align-items: center;
        background-image: linear-gradient(90deg, #62afff, #37f);
        border-radius: 15px;
        box-shadow: 0 2px 10px 0 #37f3;
        color: #fff;
        font-size: 13px;
        height: 30px;
        line-height: 30px;
        padding: 0 16px;
        -webkit-user-select: none;
        user-select: none;
        white-space: nowrap;
    }

    .btnGroup:hover {
        background-image: linear-gradient(90deg, #4494e8, #2165eb);
        box-shadow: 0 4px 10px #3377ff4d;
    }

    .btn {
        display: inline-block;
    }

    .btnIcon {
        height: 30px;
        line-height: 30px;
        margin-right: 5px;
    }
}

.content {
    height: 100%;

    .editorToolbar {
        position: fixed;
        height: 30px;
        justify-content: flex-start;
        padding-left: 8px;
        margin-right: 10px;
        background-color: #fff !important;
        z-index: 999;
    }

    .editorContainer {
        height: 100%;
        overflow: auto;
    }

    :global {

        .mp-editor-container-outer .mp-editor-container {
            width: 100% !important;
            padding: 0 !important;
            margin-left: auto;
            justify-content: flex-start;
        }

    }
}

.nextStepContainer {
    display: flex;
    justify-content: flex-end;
    padding: 20px 30px;
    margin-top: 20px;
}

.btnContainer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 10px;
    /* 按钮之间的间距 */
    height: 30px;
    margin-top: 16px;
}

.nextStepBtnWrapper {
    margin-top: 15px;
    margin-left: 10px;
    display: flex;
    align-items: center;
    height: 30px;
}

.nextStepBtn {
    min-width: 120px;
    height: 40px;
    font-size: 16px;
}