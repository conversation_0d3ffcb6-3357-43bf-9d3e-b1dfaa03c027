import {useEffect, useState} from 'react';
import {Layout, Select, Space, Tabs, message} from 'antd';
import classnames from 'classnames';
import {useNavigate, useLocation} from 'umi';
import {getQueryParams} from 'COMMON/utils/utils';
import {stringifyUrl} from 'query-string';
import DocDetail from './DocDetail/DocDetail';
import DemandGenerateCase from './DemandGenerateCase/DemandGenerateCase';
import styles from './DemandDetailPage.module.less';

const {Header, Content} = Layout;

function DemandDetailPage(props) {
    const [activeKey, setActiveKey] = useState('');
    const query = getQueryParams();
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        setActiveKey(query?.view || 'doc');
    }, [query]);

    const items = [
        {
            key: 'doc',
            label: '需求文档',
        },
        {
            key: 'case',
            label: '生成用例',
        },
    ];

    return (
        <Layout style={{backgroundColor: '#fff'}}>
            <Header className={styles.header}>
                <Space>
                    {
                        items.map(item => (
                            <span
                                key={item.key}
                                className={classnames(styles.headerTab,
                                    {[styles.headerTabActive]: activeKey === item.key})}
                                onClick={() => {
                                    setActiveKey(item.key);
                                    navigate(stringifyUrl({
                                        url: location.pathname,
                                        query: {...query, view: item.key},
                                    }));
                                }}
                            >
                                {item.label}
                            </span>
                        ))
                    }
                </Space>
            </Header>
            <Content className={styles.content}>
                {activeKey === 'doc' && <DocDetail />}
                {activeKey === 'case' && <DemandGenerateCase />}
            </Content>
        </Layout>
    );
}

export default DemandDetailPage;