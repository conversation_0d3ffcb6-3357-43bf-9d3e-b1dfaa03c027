import {isEmpty} from 'lodash';

export const getDocInfoFromUrl = (url) => {
    console.log('开始解析URL:', url);
    try {
        if (!url) {
            console.error('URL为空');
            return {};
        }
        
        const urlObj = new URL(url);
        console.log('解析URL对象:', urlObj);
        const {pathname} = urlObj;
        console.log('提取pathname:', pathname);
        
        // 移除pathname开头和结尾的斜杠以规范化路径
        const cleanPath = pathname.replace(/^\/+|\/+$/g, '');
        console.log('规范化路径:', cleanPath);
        
        const pathData = cleanPath.split('/');
        console.log('路径分段:', pathData);
        
        // 判断URL结构
        let spaceGuid, groupGuid, repositoryGuid, docGuid;
        
        if (pathData[0] === 'knowledge') {
            // 格式: knowledge/spaceGuid/groupGuid/repositoryGuid/docGuid
            spaceGuid = pathData[1];
            groupGuid = pathData[2];
            repositoryGuid = pathData[3];
            docGuid = pathData[4];
        }
        
        const result = {docGuid, spaceGuid, groupGuid, repositoryGuid};
        console.log('解析结果:', result);
        return result;
    } catch (error) {
        console.error('解析URL失败:', error);
        console.error('错误详情:', JSON.stringify(error));
        return {};
    }
};

export function getActivePath(
    treeData,
    link,
    path = [],
) {
    treeData.forEach((element, index) => {
        path.push(element.link);
        if (element.link === link) {
            path = [...path];
            return {path};
        }
        if (!isEmpty(element.children)) {
            let res = getActivePath(
                element.children,
                link,
                path,
            );
            path = res.path;
        }
        path.pop();
    });
    return {path};
}