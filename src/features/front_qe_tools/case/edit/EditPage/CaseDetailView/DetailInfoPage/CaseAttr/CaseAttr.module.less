.container {
    height: 100%;
}

.header {
    border-bottom: 1px solid var(--border-color);
}

.tabs {
    height: 50px;
    margin-left: 120px;
    color: #747474;
    cursor: pointer;

    span {
        padding: 0 10px;
    }

    span:hover {
        color: var(--primary-color);
    }
}

.tabActive {
    color: var(--primary-color);
    font-weight: bold;
}

.cardLayout {
    padding: 0 5px;
    :global {
        .ant-tabs-nav{
            margin: 0;
        }
        .ant-tabs{
            height: 100%;
        }
        .ant-tabs-content {
            height: 100%;
        }
        .ant-tabs-content-holder{
            height: 100%;
        }
        .ant-tabs-tabpane{
            height: 100%;
        }
    }
}