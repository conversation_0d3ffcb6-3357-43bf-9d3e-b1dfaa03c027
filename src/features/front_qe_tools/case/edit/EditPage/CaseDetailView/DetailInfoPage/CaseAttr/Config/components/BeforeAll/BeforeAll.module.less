.content {
    overflow: hidden;
}

.attrInfo {
    float: left;
    margin-right: 10px;
}

.tagItem {
    margin-bottom: 5px;
}

.tagNameItem {
    padding: 5px;
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

.tagNameItem:hover {
    background-color: var(--base-hover-background-color);
}

.addIcon {
    background: #fff;
    border-style: dashed;
    cursor: pointer;
}

.listSearch {
    float: left;
    width: 85%;
    margin-top: 10px;
}

.createBtn {
    display: block;
    float: left;
    width: 13%;
    height: 32px;
    margin: 10px 0 0 2%;
}

.tagInfo {
    overflow: hidden;
}

.dropdown {
    background-color: #fff;
}