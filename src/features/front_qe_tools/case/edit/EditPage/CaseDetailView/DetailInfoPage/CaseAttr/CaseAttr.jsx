import { Space, message } from 'antd';
import { useState, useEffect } from 'react';
import classnames from 'classnames';
import { useLocation, useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import { getQueryParams } from 'COMMON/utils/utils';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import baseModel from 'COMMON/models/baseModel';
import { getSnippetList } from 'COMMON/api/front_qe_tools/config';
import styles from './CaseAttr.module.less';
import BaseInfo from './BaseInfo';
import Attr from './Attr';
import Config from './Config';
import { getCaseDetialInfo } from './api';

function CaseAttr(props) {
    const { currentSpace, leftExtra, curOsType, snippetList, setSnippetList } = props;
    const [activedKey, setActivedKey] = useState('');
    const [baseInfo, setBaseInfo] = useState({});
    const [attrInfo, setAttrInfo] = useState({});
    const [configInfo, setConfigInfo] = useState({});
    const [messageApi, contextHolder] = message.useMessage();
    const query = getQueryParams();
    const location = useLocation();
    const navigate = useNavigate();

    const items = [
        {
            key: 'info-baseInfo',
            label: '基础'
        },
        {
            key: 'info-attr',
            label: '属性'
        },
        {
            key: 'info-config',
            label: '配置',
            hidden: curOsType === 4
        }
    ];

    const onChange = (key) => {
        setActivedKey(key);
        localStorage.setItem('vertical_view', key);
        let view = '211';
        if (key === 'info-baseInfo') {
            view = '211';
        }
        if (key === 'info-attr') {
            view = '212';
        }
        if (key === 'info-config') {
            view = '213';
        }
        navigate(
            stringifyUrl({
                url: location.pathname,
                query: {
                    ...query,
                    view: view
                }
            })
        );
    };

    useEffect(() => {
        if (!curOsType || !query?.caseNodeId) {
            return;
        }
        let view = localStorage?.getItem('vertical_view') ?? 'info-baseInfo';
        if (query?.view === '211') {
            view = 'info-baseInfo';
        }
        if (query?.view === '212') {
            view = 'info-attr';
        }
        if (query?.view === '213') {
            view = 'info-config';
        }
        getCaseDetialInfo({
            caseNodeId: query?.caseNodeId,
            osType: curOsType
        })
            .then((res) => {
                setBaseInfo(res?.base ?? {});
                setAttrInfo(res?.attr ?? {});
                setConfigInfo(res?.config ?? {});
                setActivedKey(view);
            })
            .catch(messageApi.error);
    }, [query?.caseNodeId, curOsType]);

    return (
        <div className={styles.container}>
            {contextHolder}
            <div className={styles.header}>
                {leftExtra}
                <Space className={styles.tabs}>
                    {items
                        .filter((item) => !item.hidden)
                        .map((item) => (
                            <span
                                onClick={() => onChange(item.key)}
                                className={classnames({
                                    [styles.tabActive]: activedKey === item.key
                                })}
                            >
                                {item.label}
                            </span>
                        ))}
                </Space>
            </div>
            <div className={styles.content}>
                {activedKey === 'info-baseInfo' && <BaseInfo {...props} baseInfo={baseInfo} />}
                {activedKey === 'info-attr' && (
                    <Attr {...props} attrInfo={attrInfo} setAttrInfo={setAttrInfo} />
                )}
                {activedKey === 'info-config' && (
                    <Config {...props} configInfo={configInfo} setConfigInfo={setConfigInfo} />
                )}
            </div>
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    curOsType: state.common.case.curOsType,
    snippetList: state.common.case.snippetList
}))(CaseAttr);
