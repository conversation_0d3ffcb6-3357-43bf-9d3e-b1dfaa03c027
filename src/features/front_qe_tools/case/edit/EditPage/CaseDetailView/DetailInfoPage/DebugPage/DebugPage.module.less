@import "RESOURCES/css/common.less";

.layoutContainer {
    background: var(--background-color);
}

.noContent {
    position: absolute;
    top: 200px;
    left: 50%;
    transform: translateX(-50%);
}

.caseDesc {
    float: left;
    width: 50%;
}

.right {
    position: relative;
    display: flex;
    height: 100%;
    gap: 8px;
    background-color: var(--background-color);
    align-items: center;
}

.caseDebugTitle {
    position: relative;
    height: 50px !important;
    line-height: 50px;
    background-color: var(--background-color);
    padding: 0 15px !important;
    border-bottom: 1px solid var(--border-color);
    width: 100%;
    color: rgb(119, 119, 119);
    font-weight: bold;
}

.caseStep {
    height: 100%;
    width: 100%;
}

.stepList {
    width: 100%;
    padding: 10px 10px 50px 10px;
    height: 100%;
    overflow: scroll;

    :global {

        .ant-input,
        .custom-default-input,
        .custom-dark-input {
            width: 100%;
            font-size: 12px !important;
            color: rgb(119, 119, 119);
        }
    }

    .stepInfo {
        margin-bottom: 5px;
        padding: 10px 5px;
        overflow: hidden;
        border: 1px solid var(--border-color);
        border-radius: 5px;
    }

    .activedStepInfo {
        border: 1px solid #5191f0;
        box-shadow: 0 0 0 3px rgb(24 144 255 / 12%);
    }

    .stepInfoLeft {
        height: 20px;
        line-height: 20px;
    }

    .stepIndex {
        display: inline-block;
        width: 28px;
        padding: 0 3px;
        color: #777;
        text-align: center;
        font-size: 10px;
    }

    .defaultStepName {
        display: inline-block;
        width: 72px;
        padding: 3px;
        color: var(--color);
        text-align: center;
        font-size: 12px;
        background-color: #3e91f7;
        border-radius: 3px;
        cursor: pointer;
    }

    .manualStepName {
        color: #f9a52d;
    }

    .assestStepName {
        color: #ea722b;
    }

    .stepTest {
        color: rgb(149, 146, 146);
    }
}

.stepDebug {
    height: 100%;
    width: 100%;
    padding: 1%;
}

.nodeText {
    font-size: 12px;
    color: #777;
}

.nodeTextCut {
    cursor: pointer;
}

.executeTag {
    color: #4c84ee;
}

.errorTag {
    color: #ec5b56;
}

.successTag {
    color: rgb(115, 209, 61);
}

.abnormalTag {
    color: #ea722b;
}

.cancel {
    color: '#777'
}
.caseSysAlertClear {
    position: absolute;
    top: 50%;
    right: 300px;
    font-weight: normal;
    transform: translateY(-50%);
}

.caseDebugBtn {
    position: absolute;
    top: 50%;
    right: 115px;
    transform: translateY(-50%);
    font-size: 12px;
    background-color: #3e91f7;
    border: none;
}

.stepDetail {
    float: left;
    width: 50%;
    position: relative;
    height: 100%;
    overflow: scroll;
}


.stepRes {
    // float: left;
    // width: 50%;
    height: 100%;
    padding: 10px 0 0 10px;
    overflow: scroll;
}


.caseErrorStack {
    width: 100%;
    border-top: 1px solid var(--border-color);
    background-color: var(--background-color);
    z-index: 2;
    overflow-y: scroll;
    cursor: pointer;
}

.caseErrorStackTitle {
    position: absolute;
    width: 100%;
    padding: 5px;
    background-color: var(--background-color);
    font-weight: bold;
    border-bottom: 1px solid var(--border-color);
}

.caseErrorStackIcon {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    font-weight: bold;
    color: #777;
}

.caseErrorStackContent {
    margin-top: 20px;
    padding: 0 5px;
}

.reRunSettingBtn,
.runSettingBtn {
    margin-left: 5px;
    font-size: 12px;
    border-radius: 3px;
}

.runSettingBtn {
    position: absolute;
    top: 50%;
    right: 15px;
    font-weight: normal;
    transform: translateY(-50%);
    cursor: pointer;
}

.divider {
    position: absolute;
    top: 50%;
    right: 105px;
    transform: translateY(-50%);
    color: #eee;
}

// 智能校验展示
.aiAssertStepSuccessInfo,
.aiAssertStepErrorInfo { 
    font-size: 11px;
    padding: 0 5px;
    border-radius: 3px;

}

.aiAssertStepSuccessInfo {
    background-color: var(--success-color);
    color: #fff;
}

.aiAssertStepErrorInfo {
    background-color: var(--error-color);
    color: #fff;
}
