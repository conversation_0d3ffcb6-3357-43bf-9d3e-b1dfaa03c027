import classnames from 'classnames';
import { message } from 'antd';
import { useEffect, useState } from 'react';
import { getQueryParams } from 'COMMON/utils/utils';
import { changeNode } from 'COMMON/api/front_qe_tools/node';
import styles from './BaseAttrInfo.module.less';

const PRIORITY_DATA = [
    {
        value: 0,
        label: 'P0'
    },
    {
        value: 1,
        label: 'P1'
    },
    {
        value: 2,
        label: 'P2'
    }
];

function BaseAttrInfo(props) {
    const { attrInfo, setAttrInfo } = props;
    const [priority, setPriority] = useState(null);
    const [isAccess, setIsAccess] = useState(false);
    const query = getQueryParams();
    const [messageApi, contextHolder] = message.useMessage();

    useEffect(() => {
        setPriority(attrInfo?.priority ?? null);
        setIsAccess(attrInfo?.isAccess ?? false);
    }, [attrInfo]);

    return (
        <div className={styles.content}>
            {contextHolder}
            <div className={styles.attrInfo}>
                <span
                    className={classnames(styles.priorityItem, {
                        [styles.aceessActivedItem]: isAccess
                    })}
                    onClick={() => {
                        if (global.params.CASE_STATUS !== 'edit') {
                            messageApi.error('暂无权限编辑');
                            return;
                        }
                        changeNode({
                            caseNodeIdList: [+query?.caseNodeId],
                            isAccess: !isAccess
                        })
                            .then(() => {
                                setAttrInfo({ ...attrInfo, isAccess: !isAccess });
                                setIsAccess(!isAccess);
                            })
                            .catch((err) => {
                                console.log(err?.message ?? err);
                            });
                    }}
                >
                    准
                </span>
            </div>
            <div className={styles.divider}>|</div>
            <div className={styles.attrInfo}>
                {PRIORITY_DATA.map((item) => (
                    <span
                        key={item.value}
                        className={classnames(
                            styles.priorityItem,
                            {
                                [styles.priority0ActivedItem]:
                                    item.value === priority && priority === 0
                            },
                            {
                                [styles.priority1ActivedItem]:
                                    item.value === priority && priority === 1
                            },
                            {
                                [styles.priority2ActivedItem]:
                                    item.value === priority && priority === 2
                            }
                        )}
                        onClick={() => {
                            if (global.params.CASE_STATUS !== 'edit') {
                                messageApi.error('暂无权限编辑');
                                return;
                            }
                            let _p = item.value === priority ? -1 : item.value;
                            changeNode({
                                caseNodeIdList: [+query?.caseNodeId],
                                priority: _p
                            })
                                .then(() => {
                                    setAttrInfo({ ...attrInfo, priority: _p });
                                    setPriority(_p);
                                })
                                .catch((err) => {
                                    console.log(err?.message ?? err);
                                });
                        }}
                    >
                        {item.label}
                    </span>
                ))}
            </div>
        </div>
    );
}

export default BaseAttrInfo;
