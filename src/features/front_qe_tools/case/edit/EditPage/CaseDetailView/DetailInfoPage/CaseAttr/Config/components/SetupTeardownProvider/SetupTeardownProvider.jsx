import React, { forwardRef, useState, useMemo, useEffect, useRef } from 'react';
import { theme, Button, Divider, Space, Tag, Tooltip, message, Splitter } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import { getQueryParams } from 'COMMON/utils/utils';
import { changeNode } from 'COMMON/api/front_qe_tools/node';
import NoContent from 'COMMON/components/common/NoContent';
import ParamsSwitch from 'COMMON/components/ParamsSwitch/ParamsSwitch';
import { createNodeHookStep, createStep, updateStep } from 'COMMON/api/front_qe_tools/step';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import StepListWithCommon from 'COMMON/components/TreeComponents/StepDetail/StepList/StepListWithCommon';
import RunTemplate from 'COMMON/components/TreeComponents/Step/StepInfo/SystemAction/RunTemplate/RunTemplate';
import { getCaseDetialInfo } from '../../../../CaseAttr/api';
import {
    treeAction,
    HOOK_TYPE_INFO
} from 'FEATURES/front_qe_tools/case/edit/EditPage/CaseDetailView/LayoutSider/utils';
import styles from './SetupTeardownProvider.module.less';

const { useToken } = theme;

function SetupTeardownProvider(props) {
    const {
        curOsType,
        snippetList,
        configInfo,
        setConfigInfo,
        treeData,
        setTreeData,
        stepList = [],
        refreshStepList,
        updateStepList,
        hookType,
        currentNode,
        setCurrentNode
    } = props;
    const query = getQueryParams();
    const [beforeAllList, setBeforeAllList] = useState([]);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const settingModalRef = useRef();
    const [currentStep, setCurrentStep] = useState(null);
    const { token } = useToken();
    const contentStyle = {
        backgroundColor: token.colorBgElevated,
        borderRadius: token.borderRadiusLG,
        boxShadow: token.boxShadowSecondary
    };
    useEffect(() => {
        setBeforeAllList([...(configInfo?.beforeAll ?? [])]);
    }, [configInfo]);

    // const OPTIONS = useMemo(() => {
    //     // 获取 snippetList 的 tabname
    //     let tabNames = [...new Set((snippetList?.[curOsType] ?? [])?.map((item) => item.tabName))];
    //     let data = tabNames.map((tabName) => ({ key: tabName, label: tabName, children: [] }));
    //     for (let snippet of snippetList?.[curOsType] ?? []) {
    //         let item = data.find((_item) => _item.key === snippet.tabName);
    //         item.children.push({ label: snippet.templateName, key: snippet.templateId });
    //     }
    //     return data;
    // }, [snippetList]);

    const updateTreeData = (nodes, targetCaseNodeId, newBeforeAll) => {
        if (!Array.isArray(nodes)) {
            return nodes;
        }
        const updateNode = (node) => {
            if (node.caseNodeId === targetCaseNodeId) {
                node.extra.beforeAllInfo[convertOsTypeToType(curOsType)] = newBeforeAll;
            }
            if (Array.isArray(node.children)) {
                node.children.forEach(updateNode);
            }
        };
        // 因为 data 是数组，可能有多个根节点
        nodes.forEach(updateNode);
        return nodes;
    };

    // 获取节点信息
    const getNodeInfo = async () => {
        const info = await getCaseDetialInfo({
            caseNodeId: query?.caseNodeId,
            osType: curOsType
        });
        return info;
    };

    // 创建Hook节点
    const createHookNode = async () => {
        const params = {
            caseNodeId: +query?.caseNodeId,
            osType: curOsType,
            hookType: hookType // 1-setup 2-teardown
        };
        return await createNodeHookStep(params);
    };

    // 创建测试步骤
    const createTestStep = async (caseNodeId) => {
        const stepBody = {
            caseNodeId: caseNodeId,
            stepDesc: '新建步骤',
            stepType: 412,
            stepInfo: {
                type: 1,
                desc: '',
                params: {
                    type: 'runTemplate',
                    params: {
                        id: null
                    }
                },
                common: {
                    commonAlertClear: false,
                    stepInterval: 2
                }
            },
            osType: curOsType
        };
        return await createStep(stepBody);
    };

    // 更新树信息
    const updateTreeHookInfo = (caseNodeId) => {
        if (setTreeData && treeData) {
            const targetNodeId = currentNode?.data?.caseNodeId || +query?.caseNodeId;
            setTreeData(
                treeAction([...treeData], 'children', +targetNodeId, (node, item, index) => {
                    if (!item?.extra) {
                        item.extra = {};
                    }

                    // 处理对应的 hookType
                    const hookKey = HOOK_TYPE_INFO[hookType];
                    if (item?.extra && hookKey) {
                        // 设置该 osType 对应的 hookInfo
                        const osTypeKey = convertOsTypeToType(curOsType);
                        if (item.extra[hookKey]) {
                            item.extra[hookKey][osTypeKey] = caseNodeId;
                        } else {
                            item.extra[hookKey] = {
                                [osTypeKey]: caseNodeId
                            };
                        }
                        setCurrentNode && setCurrentNode(item);
                    }
                })
            );
        }
    };

    // 添加测试片段
    const handleAddSetup = async () => {
        try {
            // 检查是否已存在setup节点
            const info = await getNodeInfo();
            const nodes = hookType === 1 ? info?.config?.setup : info?.config?.teardown;

            if (!nodes) {
                // 如果不存在setup/teardown节点，先创建节点
                const res = await createHookNode();

                // 创建测试步骤
                await createTestStep(res?.caseNodeId);

                // 更新树信息
                updateTreeHookInfo(res?.caseNodeId);
            } else {
                // 如果已存在setup节点，向该节点添加runTemplate步骤
                await createTestStep(nodes);
            }

            refreshStepList();
            // 关闭下拉菜单
            setDropdownOpen(false);
        } catch (error) {
            console.error('添加测试片段失败', error);
        }
    };

    const handleAfterDeleteStep = () => {
        setCurrentStep(null);
    };

    const handleUpdateStepList = () => {};
    const handleUpdateStep = async (e, attr) => {
        let body = {
            stepId: e.stepId,
            stepDesc: e.stepDesc
        };
        if (attr !== 'desc') {
            body.stepInfo = e.stepInfo;
        }
        await updateStep(body);

        const newStepList = [...stepList];
        for (let step of newStepList) {
            if (step.stepId === e.stepId) {
                step.stepDesc = e.stepDesc;
                if (attr !== 'desc') {
                    step.stepInfo = e.stepInfo;
                }
            }
        }
        updateStepList(newStepList);

        // 如果当前选中的步骤是被更新的步骤，更新当前步骤状态
        if (currentStep?.stepId === e.stepId) {
            setCurrentStep(e);
        }
    };
    return (
        <div
            className={styles.content}
            style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
        >
            {contextHolder}

            {/* 展示步骤列表 */}
            <Splitter>
                <Splitter.Panel>
                    <div
                        className={styles.stepsList}
                        style={{
                            height: 'calc(100% - 20px)',
                            display: 'flex',
                            flexDirection: 'column',
                            borderRight: '1px solid #eee',
                            marginTop: 10,
                            paddingRight: 10
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                width: '100%',
                                backgroundColor: 'white',
                                padding: '5px 0'
                            }}
                        >
                            <ParamsSwitch className={styles.paramsSwitch} />
                            <Tooltip
                                title={global.params.CASE_STATUS !== 'edit' ? '暂无权限添加' : ''}
                            >
                                <Tag className={styles.addIcon} onClick={handleAddSetup}>
                                    <PlusOutlined /> 添加测试片段
                                </Tag>
                            </Tooltip>
                        </div>
                        <div style={{ flex: 1, overflow: 'auto', padding: '0 0 0 5px' }}>
                            {stepList && stepList.length > 0 ? (
                                <StepListWithCommon
                                    showAddGroup={false}
                                    stepList={stepList}
                                    moduleStepList={stepList}
                                    currentStep={currentStep}
                                    setCurrentStep={(e) => {
                                        console.log('setCurrentStep', e);
                                        setCurrentStep(e);
                                    }}
                                    curOsType={curOsType}
                                    editType={
                                        global.params.CASE_STATUS === 'edit' ? 'edit' : 'readonly'
                                    }
                                    nodeId={configInfo?.config?.setup?.[0]}
                                    handleUpdateStepList={handleUpdateStepList}
                                    handleUpdateStep={handleUpdateStep}
                                    snippetList={snippetList}
                                    type="setupTeardown"
                                    refreshStepList={refreshStepList}
                                    currentNode={currentNode}
                                    setCurrentNode={setCurrentNode}
                                    treeData={treeData}
                                    setTreeData={setTreeData}
                                    hookType={hookType}
                                    onAfterDelete={handleAfterDeleteStep}
                                />
                            ) : (
                                <div
                                    style={{
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}
                                >
                                    <NoContent text="请添加测试片段" className={styles.noContent} />
                                </div>
                            )}
                        </div>
                    </div>
                </Splitter.Panel>
                <Splitter.Panel>
                    <div style={{ height: '100%', overflow: 'auto', paddingLeft: 10 }}>
                        {currentStep?.stepId ? (
                            <RunTemplate
                                refreshStepList={refreshStepList}
                                currentStep={currentStep}
                                {...props}
                                key={currentStep?.stepId}
                                handleUpdateStep={handleUpdateStep}
                            />
                        ) : (
                            <div
                                style={{
                                    height: '100%',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                }}
                            >
                                <NoContent text="未选择步骤" className={styles.noContent} />
                            </div>
                        )}
                    </div>
                </Splitter.Panel>
            </Splitter>

            {/* <SettingModal ref={settingModalRef} /> */}
        </div>
    );
}

export default connectModel([commonModel], (state) => ({
    curOsType: state.common.case.curOsType,
    snippetList: state.common.case.snippetList,
    treeData: state.common.case.treeData
}))(SetupTeardownProvider);
