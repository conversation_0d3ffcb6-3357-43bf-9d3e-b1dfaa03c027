import { useState, useEffect } from 'react';
import { Select, Form, Input, Space, Button, message } from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import { getQueryParams } from 'COMMON/utils/utils';
import { changeNode } from 'COMMON/api/front_qe_tools/node';
import { VALUE_TYPE, OPTIONS } from './const';
import styles from './AssociationAutoInfo.module.less';

function AssociationAutoInfo(props) {
    const { curOsType, attrInfo, setAttrInfo } = props;
    const [form] = Form.useForm();
    const query = getQueryParams();
    const [fieldTypeMap, setFieldTypeMap] = useState({});
    const [messageApi, contextHolder] = message.useMessage();

    useEffect(() => {
        let relationInfo = attrInfo?.relationInfo ?? [];
        form.setFieldValue('relation', relationInfo);
        const newFieldTypeMap = {};
        relationInfo?.forEach((item, index) => {
            newFieldTypeMap[index] = item.platform;
        });
        setFieldTypeMap(newFieldTypeMap);
    }, [attrInfo]);

    const onChangeType = (id, value) => {
        setFieldTypeMap({
            ...fieldTypeMap,
            [id]: value
        });
    };

    const handleSave = async (type, key, name) => {
        const formValues = form?.getFieldsValue();
        if (!formValues.relation[key]?.address && type === 'select') {
            return;
        }
        form?.validateFields()
            .then(async (values) => {
                // 过滤掉空的对象
                const filteredRelations = values.relation.filter(
                    (item) =>
                        Object.keys(item).length > 0 &&
                        Object.values(item).every(
                            (value) => value !== undefined && value !== null && value !== ''
                        )
                );
                changeNode({
                    caseNodeIdList: [query?.caseNodeId],
                    osDetail: {
                        osType: curOsType,
                        relationList: filteredRelations
                    }
                })
                    .then(() => {
                        setAttrInfo({
                            ...attrInfo,
                            relationInfo: filteredRelations
                        });
                    })
                    .catch(messageApi.error);
            })
            .catch(() => {
                messageApi.warning('请填写完整');
            });
    };

    return (
        <div className={styles.content}>
            {contextHolder}
            <Form autoComplete="off" form={form} disabled={global.params.CASE_STATUS !== 'edit'}>
                <Form.List name="relation">
                    {(fields, { add, remove }) => (
                        <>
                            {fields.map(({ key, name, ...restField }) => (
                                <Space key={key} align="baseline">
                                    <Form.Item
                                        {...restField}
                                        name={[name, 'platform']}
                                        style={{ width: '200px' }}
                                        rules={[{ required: true, message: '请选择类型' }]}
                                    >
                                        <Select
                                            options={OPTIONS}
                                            className="inputEditor"
                                            placeholder="请选择类型"
                                            onChange={(e) => {
                                                onChangeType(key, e);
                                                handleSave('select', key, name);
                                            }}
                                        />
                                    </Form.Item>
                                    <Form.Item
                                        {...restField}
                                        style={{ width: '330px' }}
                                        name={[name, 'address']}
                                        rules={[
                                            {
                                                validator: (_, value, callback) => {
                                                    if (
                                                        !isEmpty(fieldTypeMap) &&
                                                        fieldTypeMap[key] &&
                                                        fieldTypeMap[key] !== VALUE_TYPE.Icode &&
                                                        fieldTypeMap[key] !== VALUE_TYPE.ICafe
                                                    ) {
                                                        if (value && !value.length) {
                                                            return callback('请填写ID');
                                                        }
                                                        // 输入数字
                                                        if (!/^\d+$/.test(value)) {
                                                            return callback('请输入数字');
                                                        }
                                                    }
                                                    if (
                                                        !isEmpty(fieldTypeMap) &&
                                                        fieldTypeMap[key] === VALUE_TYPE.Icode
                                                    ) {
                                                        if (
                                                            (value && value.length === 0) ||
                                                            !/^https?:\/\//i.test(value)
                                                        ) {
                                                            callback('请填写链接');
                                                        }
                                                    }
                                                    if (
                                                        !isEmpty(fieldTypeMap) &&
                                                        fieldTypeMap[key] === VALUE_TYPE.ICafe
                                                    ) {
                                                        if (value && value.length === 0) {
                                                            callback(
                                                                '请填写卡片，例如：ep-lazyone-1234'
                                                            );
                                                        }
                                                    }

                                                    callback();
                                                }
                                            }
                                        ]}
                                    >
                                        <Input
                                            className="inputEditor"
                                            placeholder={
                                                fieldTypeMap[key] === VALUE_TYPE.Icode
                                                    ? '填写链接（http(s)://）'
                                                    : fieldTypeMap[key] === VALUE_TYPE.ICafe
                                                    ? '填写卡片,例如：ep-lazyone-1234'
                                                    : '填写ID'
                                            }
                                            onBlur={() => handleSave('input', key, name)}
                                        />
                                    </Form.Item>
                                    <MinusCircleOutlined
                                        onClick={(e) => {
                                            remove(name);
                                            handleSave('input', key, name);
                                        }}
                                    />
                                </Space>
                            ))}
                            <Form.Item>
                                <Button
                                    type="dashed"
                                    onClick={() => add()}
                                    block
                                    icon={<PlusOutlined />}
                                >
                                    添加
                                </Button>
                            </Form.Item>
                        </>
                    )}
                </Form.List>
            </Form>
        </div>
    );
}

export default connectModel([commonModel], (state) => ({
    curOsType: state.common.case.curOsType
}))(AssociationAutoInfo);
