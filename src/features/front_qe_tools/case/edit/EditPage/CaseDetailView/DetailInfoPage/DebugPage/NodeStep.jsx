import {useEffect, useState} from 'react';
import {
    MinusCircleOutlined, PlusCircleOutlined
} from '@ant-design/icons';
import StepListWithCommon from 'COMMON/components/TreeComponents/StepDetail/StepList/StepListWithCommon';
import {deepcopy} from 'COMMON/components/TreeComponents/Step/utils';
import styles from './DebugPage.module.less';

function NodeStep(props) {
    const {node,
        stepAutoPos, setStepAutoPos, setShowStep,
        currentStep, setExecuteIndex, showStep,
        startRecord, type = 'pre'} = props;
    const [isClose, setIsClose] = useState(false);

    return (
        <>
            <span
                className={styles.nodeText}
                style={{display: 0 === node?.step.length ? 'inline-block' : 'block'}}
            >
                {
                    !isClose ?
                        <MinusCircleOutlined
                            className={styles.nodeTextCut}
                            onClick={() => setIsClose(true)}
                        /> : <PlusCircleOutlined
                            className={styles.nodeTextCut}
                            onClick={() => setIsClose(false)}
                        />
                }
                &nbsp;{node.nodeText}
                {0 === node?.step?.length && type && 'end' !== type ? (
                    <span>&nbsp;/&nbsp;</span>
                ) : (
                    ''
                )}
            </span>
            {
                !isClose &&
                <StepListWithCommon
                    {...props}
                    editType='debug'
                    onChangeStep={(step) => {
                        setStepAutoPos(false);
                        for (let _index in node.step) {
                            if (node.step[_index].stepId === step.stepId) {
                                setExecuteIndex(Number(_index));
                                break;
                            }
                        }
                        if (!stepAutoPos) {
                            setShowStep(step);
                        }
                    }}
                    currenStep={startRecord ? showStep : currentStep}
                    stepList={node.step}
                    moduleStepList={node.step}
                    isDebug={startRecord}
                />
            }
        </>
    );
};

export default NodeStep;