import {CardTitle} from 'COMMON/components/common/Card';
import AssociationAutoInfo from './components/AssociationAutoInfo';
import BaseAttrInfo from './components/BaseAttrInfo';
import LinkInfo from './components/LinkInfo';
import TagInfo from './components/TagInfo';
import styles from '../CaseAttr.module.less';

function Attr(props) {
    return (
        <div className={styles.cardLayout}>
            <CardTitle text='节点属性' />
            <div className={styles.cardContent}>
                <BaseAttrInfo {...props} />
            </div>
            <CardTitle text='节点标签' />
            <div className={styles.cardContent}>
                <TagInfo {...props} />
            </div>
            <CardTitle text='绑定链接' />
            <div className={styles.cardContent}>
                <LinkInfo {...props} />
            </div>
            <CardTitle text='绑定第三方平台' />
            <div className={styles.cardContent}>
                <AssociationAutoInfo {...props} />
            </div>
        </div>
    );
};

export default Attr;
