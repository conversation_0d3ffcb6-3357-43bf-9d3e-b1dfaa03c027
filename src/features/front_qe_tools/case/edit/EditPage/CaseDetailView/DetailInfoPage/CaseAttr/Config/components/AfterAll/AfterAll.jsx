import React, { forwardRef, useState, useMemo, useEffect, useRef } from 'react';
import { theme, Button, Dropdown, Divider, Space, Tag, Tooltip, message } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import { getQueryParams } from 'COMMON/utils/utils';
import { changeNode } from 'COMMON/api/front_qe_tools/node';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import styles from './AfterAll.module.less';
const { useToken } = theme;

function AfterAll(props) {
    const { curOsType, snippetList, configInfo, setConfigInfo, treeData, setTreeData } = props;
    const query = getQueryParams();
    const [afterAllList, setAfterAllList] = useState([]);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const settingModalRef = useRef();

    const { token } = useToken();
    const contentStyle = {
        backgroundColor: token.colorBgElevated,
        borderRadius: token.borderRadiusLG,
        boxShadow: token.boxShadowSecondary
    };

    useEffect(() => {
        setAfterAllList([...(configInfo?.afterAll ?? [])]);
    }, [configInfo]);
    const updateTreeData = (nodes, targetCaseNodeId, newAfterAll) => {
        if (!Array.isArray(nodes)) {
            return nodes;
        }
        const updateNode = (node) => {
            if (node.caseNodeId === targetCaseNodeId) {
                node.extra.afterAllInfo[convertOsTypeToType(curOsType)] = newAfterAll;
            }
            if (Array.isArray(node.children)) {
                node.children.forEach(updateNode);
            }
        };
        // 因为 data 是数组，可能有多个根节点
        nodes.forEach(updateNode);
        return nodes;
    };
    const OPTIONS = useMemo(() => {
        // 获取 snippetList 的 tabname
        let tabNames = [...new Set((snippetList?.[curOsType] ?? [])?.map((item) => item.tabName))];
        let data = tabNames.map((tabName) => ({ key: tabName, label: tabName, children: [] }));
        for (let snippet of snippetList?.[curOsType] ?? []) {
            let item = data.find((_item) => _item.key === snippet.tabName);
            item.children.push({ label: snippet.templateName, key: snippet.templateId });
        }
        return data;
    }, [snippetList]);

    const onClick = (value) => {
        let _afterAll = [];
        let id = +value.key;
        if (afterAllList.includes(id)) {
            _afterAll = afterAllList.filter((_ele) => _ele !== id);
        } else {
            _afterAll = [...afterAllList, id];
        }

        changeNode({
            caseNodeIdList: [+query?.caseNodeId],
            osDetail: {
                osType: curOsType,
                afterAll: _afterAll
            }
        })
            .then((res) => {
                // 更新树缓存信息
                setAfterAllList(_afterAll);
                setConfigInfo({ ...configInfo, afterAll: _afterAll });
                // 更新 treeData
                const updatedTreeData = updateTreeData(treeData, +query?.caseNodeId, _afterAll);
                setTreeData(updatedTreeData);
            })
            .catch((error) => {
                console.error('afterAll 更新失败', error);
            });
    };

    const menuStyle = {
        boxShadow: 'none'
    };
    return (
        <div className={styles.content}>
            {contextHolder}
            <div className={styles.attrInfo}>
                {afterAllList.map((item) => (
                    <Tag
                        className={styles.tagItem}
                        closable
                        key={item}
                        onClose={() => {
                            let _afterAll = afterAllList.filter((_ele) => _ele !== item);
                            changeNode({
                                caseNodeIdList: [+query?.caseNodeId],
                                osDetail: {
                                    osType: curOsType,
                                    afterAll: _afterAll
                                }
                            })
                                .then(() => {
                                    // 更新树缓存信息
                                    setAfterAllList(_afterAll);
                                    setConfigInfo({ ...configInfo, afterAll: _afterAll });
                                    // 更新 treeData
                                    const updatedTreeData = updateTreeData(
                                        treeData,
                                        +query?.caseNodeId,
                                        _afterAll
                                    );
                                    setTreeData(updatedTreeData);
                                })
                                .catch(messageApi.error);
                        }}
                    >
                        {(snippetList?.[curOsType] ?? [])?.find(
                            (_item) => _item.templateId === item
                        )?.templateName ?? item}
                    </Tag>
                ))}
                <Dropdown
                    menu={{
                        items: OPTIONS,
                        onClick: onClick
                    }}
                    disabled={global.params.CASE_STATUS !== 'edit'}
                    trigger={['click']}
                    open={dropdownOpen}
                    onOpenChange={(value) => setDropdownOpen(value)}
                    dropdownRender={(menu) => {
                        return (
                            <div style={contentStyle}>
                                {React.cloneElement(menu, {
                                    style: menuStyle
                                })}
                                <Divider style={{ margin: 0 }} />
                                <Space style={{ padding: 8 }}>
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            settingModalRef?.current?.show({
                                                key: 'step-template'
                                            });
                                            setDropdownOpen(false);
                                        }}
                                    >
                                        模版配置
                                    </Button>
                                </Space>
                            </div>
                        );
                    }}
                >
                    <Tooltip title={global.params.CASE_STATUS !== 'edit' ? '暂无权限添加' : ''}>
                        <Tag className={styles.addIcon}>
                            <PlusOutlined /> 添加
                        </Tag>
                    </Tooltip>
                </Dropdown>
            </div>
            <SettingModal ref={settingModalRef} />
        </div>
    );
}

export default connectModel([commonModel], (state) => ({
    curOsType: state.common.case.curOsType,
    snippetList: state.common.case.snippetList,
    treeData: state.common.case.treeData
}))(AfterAll);
