.content {
    overflow: hidden;
}

.tagItem{
    border: 1px solid red;
}
.tagNameItem {
    padding: 5px;
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

.tagNameItem:hover {
    background-color: var(--base-hover-background-color);
}

.addIcon {
    background: #fff;
    border-style: dashed;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 0 10px 0 6px;
    margin-left: auto;
}

.listSearch {
    float: left;
    width: 85%;
    margin-top: 10px;
}

.createBtn {
    display: block;
    float: left;
    width: 13%;
    height: 32px;
    margin: 10px 0 0 2%;
}

.tagInfo {
    overflow: hidden;
}

.dropdown {
    background-color: #fff;
}


.templateName {
    color: #1890ff;
    padding: 2px 6px;
    background-color: #e6f7ff;
    border-radius: 4px;
    font-size: 12px;
}