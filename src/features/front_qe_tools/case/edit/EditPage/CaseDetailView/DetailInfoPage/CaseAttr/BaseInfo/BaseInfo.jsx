import {Descriptions} from 'antd';
import {getQueryParams} from 'COMMON/utils/utils';
import {getFormatTime} from 'COMMON/utils/dateUtils';
import {CardTitle} from 'COMMON/components/common/Card';
import styles from '../CaseAttr.module.less';

function BaseInfo(props) {
    const {baseInfo} = props;
    const query = getQueryParams();
    return (
        <div className={styles.cardLayout}>
            <CardTitle text='基础信息' />
            <div className={styles.cardContent}>
                <Descriptions column={1} size='small'>
                    <Descriptions.Item label="节点 ID">
                        {query?.caseNodeId}
                    </Descriptions.Item>
                    <Descriptions.Item label="节点名称">
                        {baseInfo?.nodeName}
                    </Descriptions.Item>
                    <Descriptions.Item label="最新更新时间">
                        {baseInfo?.updateTime ? getFormatTime(baseInfo?.updateTime) : '暂无'}
                    </Descriptions.Item>
                    <Descriptions.Item label="最新更新人">
                        {baseInfo?.updateUser ?? '暂无'}
                    </Descriptions.Item>
                </Descriptions>
            </div>
        </div>
    );
};

export default BaseInfo;
