import { useEffect } from 'react';
import { Form, Input, Space, Button, message } from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import commonModel from 'COMMON/models/commonModel';
import { getQueryParams } from 'COMMON/utils/utils';
import { connectModel } from 'COMMON/middleware';
import { changeNode } from 'COMMON/api/front_qe_tools/node';
import styles from './LinkInfo.module.less';

function LinkInfo(props) {
    const { attrInfo, setAttrInfo } = props;
    const [form] = Form.useForm();
    const query = getQueryParams();
    const [messageApi, contextHolder] = message.useMessage();

    useEffect(() => {
        form.setFieldValue('link', attrInfo?.linkList || []);
    }, [attrInfo]);

    const handleSave = async () => {
        form?.validateFields()
            .then((values) => {
                // 过滤掉空的对象
                const filteredLinks = values.link.filter(
                    (item) =>
                        Object.keys(item).length > 0 &&
                        Object.values(item).every(
                            (value) => value !== undefined && value !== null && value !== ''
                        )
                );
                changeNode({
                    caseNodeIdList: [+query?.caseNodeId],
                    linkList: filteredLinks
                })
                    .then(() => {
                        setAttrInfo({ ...attrInfo, linkList: filteredLinks });
                    })
                    .catch(messageApi.error);
            })
            .catch(() => {
                messageApi.warning('请填写完整');
            });
    };

    return (
        <div className={styles.content}>
            {contextHolder}
            <Form autoComplete="off" form={form} disabled={global.params.CASE_STATUS !== 'edit'}>
                <Form.List name="link">
                    {(fields, { add, remove }) => {
                        return (
                            <>
                                {fields.map(({ key, name, ...restField }) => (
                                    <Space key={key} className={styles.linkItem} align="baseline">
                                        <Form.Item
                                            {...restField}
                                            name={[name, 'name']}
                                            style={{
                                                width: 200
                                            }}
                                            rules={[{ required: true, message: '请输入描述' }]}
                                        >
                                            <Input placeholder="描述" onBlur={() => handleSave()} />
                                        </Form.Item>
                                        <Form.Item
                                            {...restField}
                                            name={[name, 'path']}
                                            style={{
                                                width: 330
                                            }}
                                            rules={[{ required: true, message: '请输入网址' }]}
                                        >
                                            <Input placeholder="网址" onBlur={() => handleSave()} />
                                        </Form.Item>
                                        <MinusCircleOutlined
                                            onClick={() => {
                                                remove(name);
                                                handleSave();
                                            }}
                                        />
                                    </Space>
                                ))}
                                <Form.Item>
                                    <Button
                                        type="dashed"
                                        onClick={() => add()}
                                        block
                                        icon={<PlusOutlined />}
                                    >
                                        添加
                                    </Button>
                                </Form.Item>
                            </>
                        );
                    }}
                </Form.List>
            </Form>
        </div>
    );
}
export default LinkInfo;
