import { useState, useEffect } from 'react';
import { getQueryParams } from 'COMMON/utils/utils';
import { Tabs } from 'antd';
import { CardTitle } from 'COMMON/components/common/Card';
import { getStepList } from 'COMMON/api/front_qe_tools/step';
import SetupTeardownProvider from './components/SetupTeardownProvider';
// import AfterAll from './components/AfterAll';
import styles from '../CaseAttr.module.less';

function Config(props) {
    const { curOsType } = props;
    const query = getQueryParams();
    const [setupStepList, setSetupStepList] = useState([]);
    const [teardownStepList, setTeardownStepList] = useState([]);
    const refreshStepList = () => {
        if (!curOsType || !query?.caseNodeId) {
            return;
        }
        getStepList({
            caseNodeId: query?.caseNodeId,
            osType: curOsType
        })
            .then((res) => {
                setSetupStepList(res?.setupStepList || []);
                setTeardownStepList(res?.teardownStepList || []);
            })
            .catch((err) => {
                setSetupStepList([]);
                setTeardownStepList([]);
            });
    };
    useEffect(() => {
        refreshStepList();
    }, [curOsType, query?.caseNodeId]);
    return (
        <div
            className={styles.cardLayout}
            style={{
                height: 'calc(100vh - 100px)'
            }}
        >
            <Tabs
                defaultActiveKey="1"
                type="card"
                style={{ margin: 10 }}
                items={[
                    {
                        key: '1',
                        label: 'Setup（在设备初始化阶段执行）',
                        children: (
                            <SetupTeardownProvider
                                {...props}
                                stepList={setupStepList}
                                refreshStepList={refreshStepList}
                                hookType={1}
                                updateStepList={(stepList) => {
                                    setSetupStepList(stepList);
                                }}
                            />
                        )
                    },
                    {
                        key: '2',
                        label: 'Teardown（在设备执行完毕后执行）',
                        children: (
                            <SetupTeardownProvider
                                {...props}
                                stepList={teardownStepList}
                                refreshStepList={refreshStepList}
                                hookType={2}
                                updateStepList={(stepList) => {
                                    setTeardownStepList(stepList);
                                }}
                            />
                        )
                    }
                ]}
                indicator={{
                    size: (origin) => origin - 20,
                    align: 'center'
                }}
            />

            {/* <CardTitle text='Teardown（在设备执行完毕后执行）' />
            <div className={styles.cardContent}>
                <AfterAll {...props} teardownStepList={teardownStepList} refreshStepList={refreshStepList}/>
            </div> */}
        </div>
    );
}

export default Config;
