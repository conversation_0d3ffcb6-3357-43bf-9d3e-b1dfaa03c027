import classnames from 'classnames';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useLocation } from 'umi';
import { Tag, Popover, Input, Divider, Tooltip, Button, message, Select } from 'antd';
import { stringifyUrl } from 'query-string';
import { PlusOutlined, SettingOutlined, SearchOutlined } from '@ant-design/icons';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import NoContent from 'COMMON/components/common/NoContent';
import { getQueryParams } from 'COMMON/utils/utils';
import { connectModel } from 'COMMON/middleware';
import { changeNode } from 'COMMON/api/front_qe_tools/node';
import { convertTagColorTypeToHexadecimal } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import SettingModal from 'FEATURES/components/Modal/SettingModal';
import styles from './TagInfo.module.less';

function TagInfo(props) {
    const { attrInfo, setAttrInfo, tagList, setShowModal } = props;
    const [tagInit, setTagInit] = useState([]);
    const query = getQueryParams();
    const [messageApi, contextHolder] = message.useMessage();
    const settingModalRef = useRef();

    useEffect(() => {
        setTagInit([
            ...(attrInfo?.tagInfo?.moduleTagList ?? []),
            ...(attrInfo?.tagInfo?.caseTagList ?? [])
        ]);
    }, [attrInfo]);

    const tagOption = useMemo(() => {
        const newTag = tagList.map((item) => {
            const color = convertTagColorTypeToHexadecimal(item.color);
            return {
                value: item.id,
                tagName: item.tagName,
                label: (
                    <div className={styles.tagItem}>
                        <span
                            className={styles.icon}
                            style={{
                                background: color,
                                color: [
                                    '#fa8231',
                                    '#a55eea',
                                    '#45aaf2',
                                    '#20bf6b',
                                    '#f7b731',
                                    '#4b7bec',
                                    '#eb3b5a',
                                    '#FFEBEE',
                                    '#E3F2FD',
                                    '#E8F5E9',
                                    '#FFFDE7',
                                    '#FFF3E0'
                                ].includes(item.color)
                                    ? '#fff'
                                    : '#777',
                                border: '#d9d9d9 solid 1px'
                            }}
                        />
                        <span className={styles.tagLabel}>{item.tagName}</span>
                    </div>
                )
            };
        });
        return newTag;
    }, [tagList]);

    const tagRender = (props, tagList) => {
        const { value } = props;
        const tagItem = tagList.find((item) => item.id === value);
        const currentColor = convertTagColorTypeToHexadecimal(tagItem?.color);
        return (
            <div style={{ marginRight: 4 }}>
                <span
                    className={styles.tagItem}
                    style={{
                        background: currentColor,
                        color: [
                            '#fa8231',
                            '#a55eea',
                            '#45aaf2',
                            '#20bf6b',
                            '#f7b731',
                            '#4b7bec',
                            '#eb3b5a',
                            '#FFEBEE',
                            '#E3F2FD',
                            '#E8F5E9',
                            '#FFFDE7',
                            '#FFF3E0'
                        ].includes(tagItem?.color)
                            ? '#fff'
                            : '#777',
                        border: '#d9d9d9 solid 1px'
                    }}
                >
                    {tagItem?.tagName ?? value}
                </span>
            </div>
        );
    };

    const handleOptionChange = (v) => {
        let _tagInfo = {
            caseTagList: [],
            moduleTagList: []
        };
        for (let tag of v) {
            if (typeof tag === 'number') {
                _tagInfo.moduleTagList.push(tag);
            } else {
                _tagInfo.caseTagList.push(tag);
            }
        }
        changeNode({
            caseNodeIdList: [+query?.caseNodeId],
            tagInfo: _tagInfo
        })
            .then((res) => {
                // 更新树缓存信息
                setTagInit([..._tagInfo.moduleTagList, ..._tagInfo.caseTagList]);
                setAttrInfo({ ...attrInfo, tagInfo: _tagInfo });
            })
            .catch(messageApi.error);
    };

    const transTagManagement = () => {
        setShowModal(true);
        settingModalRef?.current.show({ key: 'tag-manage' });
    };

    return (
        <div className={styles.content}>
            {contextHolder}
            <Select
                mode="tags"
                value={tagInit}
                variant="borderless"
                placeholder="请选择"
                style={{ minWidth: 80 }}
                onChange={handleOptionChange}
                tagRender={(props) => tagRender(props, tagList)}
                optionLabelProp="label"
                options={tagOption}
                popupMatchSelectWidth={false}
                popupClassName={styles.popupTagDropdown}
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider style={{ margin: '5px 0' }} />
                        <Button
                            style={{
                                width: '96%',
                                margin: '0 2%',
                                borderRadius: 6
                            }}
                            type="text"
                            onClick={transTagManagement}
                            icon={<SettingOutlined />}
                        >
                            标签管理
                        </Button>
                    </>
                )}
            />
            <SettingModal ref={settingModalRef} />
        </div>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    tagList: state.common.base.tagList,
    wholePath: state.common.base.wholePath,
    currentModule: state.common.base.currentModule,
    showModal: state.common.base.showModal
}))(TagInfo);
