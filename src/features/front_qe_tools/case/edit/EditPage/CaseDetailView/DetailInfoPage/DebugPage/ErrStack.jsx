import { useState } from 'react';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import { connectModel } from 'COMMON/middleware';

import styles from './DebugPage.module.less';

function ErrStack(props) {
    const { errStep, curOsType } = props;
    const [isShowErrorStack, setIsShowErrorStack] = useState(true);
    let logIdList = [];
    if (
        errStep &&
        errStep.result &&
        errStep.result.data &&
        errStep.result.data.extra &&
        errStep.result.data.extra.fishLogList
    ) {
        for (let _logItem of errStep.result.data.extra.fishLogList) {
            logIdList.push(_logItem.logId);
        }
    } else if (errStep && errStep && errStep.fishLogList) {
        for (let _logItem of errStep.fishLogList) {
            logIdList.push(_logItem.logId);
        }
    }
    return (
        <div
            className={styles.caseErrorStack}
            style={{
                height: isShowErrorStack ? 285 : 80,
                overflowY: isShowErrorStack ? 'scroll' : 'hidden'
            }}
        >
            <div
                className={styles.caseErrorStackTitle}
                onClick={() => setIsShowErrorStack(!isShowErrorStack)}
            >
                <span>问题堆栈信息</span>
                {isShowErrorStack ? (
                    <Tooltip title="收起">
                        <MinusOutlined className={styles.caseErrorStackIcon} />
                    </Tooltip>
                ) : null}
                {!isShowErrorStack ? (
                    <Tooltip title="展开">
                        <PlusOutlined className={styles.caseErrorStackIcon} />
                    </Tooltip>
                ) : null}
            </div>
            {+curOsType === 4 ? (
                <div className={styles.caseErrorStackContent} style={{ paddingBottom: 100 }}>
                    <div style={{ overflowY: 'scroll' }}>
                        {isShowErrorStack ? <br /> : null}
                        {isShowErrorStack ? <span>问题描述:&nbsp;&nbsp;</span> : null}
                        {isShowErrorStack ? (errStep?.message ? errStep?.message : '暂无') : null}
                        {isShowErrorStack ? <br /> : null}
                        {isShowErrorStack ? <span>问题代号:&nbsp;&nbsp;</span> : null}
                        {isShowErrorStack ? (errStep?.code ? errStep?.code : '暂无') : null}
                        {isShowErrorStack ? <br /> : null}
                        {isShowErrorStack ? <span>问题堆栈:&nbsp;&nbsp;</span> : null}
                        {isShowErrorStack
                            ? errStep?.errorStack
                                ? errStep?.errorStack
                                : '暂无'
                            : null}
                        {isShowErrorStack ? <br /> : null}
                    </div>
                </div>
            ) : (
                <div className={styles.caseErrorStackContent}>
                    {isShowErrorStack ? <br /> : null}
                    {isShowErrorStack ? <span>问题描述:&nbsp;&nbsp;</span> : null}
                    {isShowErrorStack
                        ? undefined !== errStep?.result.err &&
                          undefined !== errStep?.result.err.message
                            ? errStep?.result.err.message
                            : errStep?.result.msg
                            ? errStep?.result.msg
                            : '暂无'
                        : null}
                    {isShowErrorStack ? <br /> : null}
                    {isShowErrorStack ? <span>问题代号:&nbsp;&nbsp;</span> : null}
                    {isShowErrorStack
                        ? undefined !== errStep?.result.err &&
                          undefined !== errStep?.result.err.code
                            ? errStep?.result.err.code
                            : '暂无'
                        : null}
                    {isShowErrorStack ? <br /> : null}
                    {/* 旧建模 */}
                    {isShowErrorStack &&
                    5 === errStep?.stepInfo.type &&
                    undefined !== errStep?.stepInfo.params.dom &&
                    undefined !== errStep?.stepInfo.params.dom.dom_info &&
                    undefined !== errStep?.stepInfo.params.dom.dom_info.version ? (
                        <span>
                            <span>建模方式:&nbsp;&nbsp;</span>
                            {4 === errStep?.stepInfo.params.findType ? 'UIDom' : 'BatDom'}&nbsp;
                            {errStep?.stepInfo.params.dom.dom_info.version}
                            <br />
                        </span>
                    ) : null}
                    {/* 新通用建模 */}
                    {isShowErrorStack &&
                    6 === errStep?.stepInfo.type &&
                    errStep?.stepInfo.params?.domInfo?.version ? (
                        <span>
                            <span>建模方式:&nbsp;&nbsp;</span>
                            {errStep?.stepInfo.params.domInfo.name}&nbsp;
                            {errStep?.stepInfo.params.domInfo.version}
                            <br />
                        </span>
                    ) : null}
                    {isShowErrorStack ? <span>问题堆栈:&nbsp;&nbsp;</span> : null}
                    {isShowErrorStack
                        ? undefined !== errStep?.result.err &&
                          undefined !== errStep?.result.err.stack
                            ? errStep?.result.err.stack
                            : '暂无'
                        : null}
                </div>
            )}
        </div>
    );
}

export default ErrStack;
