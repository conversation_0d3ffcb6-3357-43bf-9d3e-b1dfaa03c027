.content {
    overflow: hidden;
}

.divider {
    float: left;
    height: 25px;
    line-height: 25px;
    color: #eee;
    margin: 0 10px 0 5px;
}

.attrInfo {
    float: left;
}

.priorityItem {
    display: inline-block;
    height: 25px;
    line-height: 25px;
    width: 25px;
    margin-right: 5px;
    text-align: center;
    border-radius: 5px;
    color: #777;
    background-color: #efefef;
    font-size: 12px;
    cursor: pointer;
}

.aceessActivedItem {
    color: var(--background-color);
    background-color: #71ad47;
}

.priority0ActivedItem {
    color: var(--background-color);
    background-color: #B71C1C;
}

.priority1ActivedItem {
    color: var(--background-color);
    background-color: #EF5350;
}

.priority2ActivedItem {
    color: var(--background-color);
    background-color: #E57373;
}