import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'umi';
import { Layout, message, Button, Switch, Popover } from 'antd';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import { CodeSandboxOutlined } from '@ant-design/icons';
import { stringifyUrl } from 'query-string';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import electron from 'COMMON/utils/electron';
import { getQueryParams, delay } from 'COMMON/utils/utils';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import { getStepListWithPath } from 'COMMON/api/front_qe_tools/step';
import NoContent from 'COMMON/components/common/NoContent';
import Splitter from 'COMMON/components/common/SplitterPlus';
import Loading from 'COMMON/components/common/Loading';
import TestStep from 'COMMON/components/TreeComponents/Step/TestStep';
import ParamsSwitch from 'COMMON/components/ParamsSwitch/ParamsSwitch';
import StepInfo from 'COMMON/components/TreeComponents/Step/StepInfo';
import StepRes from 'COMMON/components/TreeComponents/Step/StepRes/StepRes';
import InterfaceStepRes from 'COMMON/components/TreeComponents/Step/InterfaceStepRes';
import { getRealStepDetail } from 'COMMON/components/TreeComponents/Step/StepItem/Operation/StepTest/utils';
import NodeStep from './NodeStep';
import ErrStack from './ErrStack';

import styles from './DebugPage.module.less';

const { Header, Content } = Layout;

const getStatus = (status) => {
    let jsx = [];
    switch (status) {
        case -1:
            jsx = <span className={styles.defaultTag}>未执行</span>;
            break;
        case 0:
            jsx = <span className={styles.executeTag}>执行中</span>;
            break;
        case 1:
            jsx = <span className={styles.errorTag}>执行失败</span>;
            break;
        case 2:
            jsx = <span className={styles.successTag}>执行成功</span>;
            break;
        case 3:
            jsx = <span className={styles.abnormalTag}>执行异常</span>;
            break;
        case 4:
            jsx = <span className={styles.cancel}>主动取消</span>;
            break;
        default:
            break;
    }
    return jsx;
};

function DebugPage(props) {
    const {
        schemeList,
        mockList,
        currentDevice,
        snippetList,
        currentModule,
        currentEnv,
        paramList,
        treeData,
        testStep,
        curOsType,
        setRecording
    } = props;
    const { caseNodeId: nodeId } = getQueryParams();
    const [open, setOpen] = useState(false);
    const [nodeList, setNodeList] = useState([]);
    const [stepList, setStepList] = useState([]);
    const [currentStep, setCurrentStep] = useState(null);
    const [executeIndex, setExecuteIndex] = useState(0);
    const [stepAutoPos, setStepAutoPos] = useState(true);
    const [resStatus, setResStatus] = useState(-1); // -1: 未执行; 0: 执行中; 1: 执行失败 2:执行成功
    const [loading, setLoading] = useState(false);
    const [errStep, setErrStep] = useState(null);
    const [showStep, setShowStep] = useState(null);
    const [startRecord, setStartRecord] = useState(false);
    const [sysAlertClear, setSysAlertClear] = useState(true);
    const query = getQueryParams();
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        if (currentDevice?.deviceId && curOsType && curOsType !== 4) {
            // 进入页面后 判断是否任务结束
            electron.send('device.replay.cancelDebug', {
                deviceType: curOsType,
                deviceId: currentDevice?.deviceId
            });
        }
    }, [query?.caseNodeId, currentDevice, curOsType]);

    useEffect(() => {
        setExecuteIndex(0);
        setCurrentStep(null);
        setShowStep(null);
        setErrStep(null);
        setResStatus(-1);
        setStartRecord(false);
        if (query?.caseNodeId) {
            // 获取步骤信息
            getStepInfo(+query?.caseNodeId, curOsType);
        }
    }, [query?.caseNodeId, curOsType]);

    // 监听步骤开始
    useEffect(() => {
        if (isElectron() && curOsType !== 4) {
            electron.on('debug.step.start', async ({ taskId, nodeId, stepId }) => {});
            return () => electron.remove('debug.step.start');
        }
    }, [startRecord, stepAutoPos, stepList, nodeList]);

    const updateRes = (result, stepId = null, clearRes = false) => {
        // 更新步骤 list
        let newStepList = stepList.map((step) => {
            let newResult = step?.result;
            let stepChildren = [];
            if (step.stepId === stepId) {
                newResult = result;
            }
            if (clearRes) {
                newResult = result;
            }
            // 步骤组传入步骤结果
            if (step.stepType === 1401) {
                stepChildren = [
                    ...step.stepChildren.map((child) => {
                        if (clearRes) {
                            return {
                                ...child,
                                result: {}
                            };
                        }
                        return (
                            result?.data?.extra?.stepResult?.find((res) => {
                                res.stepId === child.stepId;
                            }) ?? child
                        );
                    })
                ];
            }
            return {
                ...step,
                result: newResult,
                stepChildren
            };
        });
        setStepList(newStepList);
        // 更新节点和步骤 list
        let newNodeList = nodeList.map((node) => {
            return {
                ...node,
                step: node.step.map((step) => {
                    let stepChildren = [];
                    let newResult = step?.result;
                    if (step.stepId === stepId) {
                        newResult = result;
                    }
                    if (clearRes) {
                        newResult = {};
                    }
                    // 步骤组传入步骤结果
                    if (step.stepType === 1401) {
                        stepChildren = [
                            ...step.stepChildren.map((child) => {
                                if (clearRes) {
                                    return {
                                        ...child,
                                        result: {}
                                    };
                                }
                                return (
                                    result?.data?.extra?.stepResult?.find(
                                        (res) => res.stepId === child.stepId
                                    ) ?? child
                                );
                            })
                        ];
                    }
                    return {
                        ...step,
                        result: newResult,
                        stepChildren
                    };
                })
            };
        });
        setNodeList(newNodeList);
        return newStepList;
    };

    // 监听步骤结果
    useEffect(() => {
        if (isElectron() && curOsType !== 4) {
            electron.on('debug.step.end', async ({ taskId, stepId, isSuccess, result }) => {
                let showIndex = -1;
                let exeIndex = -1;
                stepList.forEach((item, index) => {
                    if (item.stepId === stepId) {
                        showIndex = index;
                        // 当前步骤结束，切到下一步执行展示
                        exeIndex = index !== stepList.length - 1 ? index + 1 : index;
                    }
                    return item;
                });
                let newStepList = updateRes(result, stepId);
                if (stepAutoPos) {
                    setExecuteIndex(exeIndex);
                    setCurrentStep(newStepList[exeIndex]);
                    setShowStep(newStepList[showIndex]);
                }
            });
            return () => electron.remove('debug.step.end');
        }
    }, [startRecord, stepAutoPos, stepList, nodeList]);

    // 监听任务开始
    useEffect(() => {
        if (isElectron() && curOsType !== 4) {
            electron.on('debug.task.start', async ({ taskId }) => {});
            return () => electron.remove('debug.task.start');
        }
    }, [startRecord, stepList, nodeList]);

    // 监听任务结束
    useEffect(() => {
        if (isElectron() && curOsType !== 4) {
            electron.on('debug.task.finish', async ({ taskId, isSuccess }) => {
                setStartRecord(false);
                setLoading(false);
                setResStatus(isSuccess ? 1 : 2);
            });
            return () => electron.remove('debug.task.finish');
        }
    }, [startRecord, stepAutoPos, stepList, nodeList]);

    // 任务结束后，获取错误步骤
    useEffect(() => {
        if (!startRecord && ![0, -1]?.includes(resStatus)) {
            let curErrStepIndex = stepList.findIndex((item) => item?.result?.status === -1);
            setErrStep(stepList[curErrStepIndex]);
            if (stepAutoPos) {
                setExecuteIndex(
                    stepList[curErrStepIndex]?.stepId ? curErrStepIndex : stepList.length - 1
                );
                setCurrentStep(
                    stepList[curErrStepIndex]?.stepId
                        ? stepList[curErrStepIndex]
                        : stepList[stepList.length - 1]
                );
            }
        }
    }, [resStatus, startRecord, stepList, nodeList]);

    // 获取用例路径上的所有步骤
    const getStepInfo = async (leafNodeId, curOsType) => {
        try {
            if (!curOsType) {
                return;
            }
            setLoading(true);
            let newNodeList = [];
            let newStepList = [];
            let hasProxy = false;
            let { caseNodeList } = await getStepListWithPath({
                caseNodeId: leafNodeId,
                osType: curOsType,
                withInfo: true
            });
            for (let node of caseNodeList) {
                let curStepList = [];
                let stepList = node?.extra?.stepInfo?.[convertOsTypeToType(curOsType)];
                for (let item of stepList) {
                    item.result = {
                        status: -2,
                        msg: '',
                        err: {},
                        data: {}
                    };
                    curStepList.push(item);
                    if (
                        -1 !==
                        ['mock', 'networkConnect', 'requestVerify'].indexOf(
                            item?.stepInfo?.params?.type
                        )
                    ) {
                        hasProxy = true;
                    }
                }
                newNodeList.push({
                    id: node?.caseNodeId,
                    nodeText: node?.nodeName,
                    step: curStepList
                });
                newStepList = [...newStepList, ...curStepList];
            }
            if (hasProxy) {
                newNodeList.unshift({
                    id: -1,
                    nodeText: '前置操作',
                    step: [
                        {
                            id: -1,
                            nodeId: Number(nodeId),
                            stepType: 'clearProxy',
                            stepInfo: {
                                type: 1,
                                params: {
                                    type: 'clearProxy',
                                    params: {
                                        willVerify: true
                                    }
                                },
                                desc: '重置清空与代理相关的内容'
                            },
                            result: {
                                status: -2,
                                msg: '',
                                err: {},
                                data: {}
                            }
                        }
                    ]
                });
                newStepList.unshift({
                    id: -1,
                    stepId: -1,
                    nodeId: Number(nodeId),
                    stepType: 'clearProxy',
                    stepInfo: {
                        type: 1,
                        params: {
                            type: 'clearProxy',
                            params: {
                                willVerify: true
                            }
                        },
                        desc: '重置清空与代理相关的内容'
                    },
                    result: {
                        status: -2,
                        msg: '',
                        err: {},
                        data: {}
                    }
                });
            }
            setNodeList(newNodeList);
            setStepList(newStepList);
            setCurrentStep(0 === newStepList.length ? null : newStepList[0]);
        } catch (err) {
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return <Loading />;
    }

    const updateStepList = () => {
        if (-1 === executeIndex) {
            return;
        }
        updateRes({}, null, true);
    };

    // 是否上报：needFishLog
    const startExecute = async (needFishLog = false) => {
        if ([1, 2]?.includes(curOsType)) {
            if (!currentDevice?.deviceId) {
                message.error('暂无设备');
                return;
            }
        }
        updateStepList();
        let newStepList = [];
        let flag = true;
        for (let index in stepList) {
            let step = stepList[index];
            if (executeIndex <= index) {
                let curItem = await getRealStepDetail(step, {
                    currentEnv,
                    curOsType,
                    snippetList,
                    schemeList,
                    mockList
                });
                newStepList.push(curItem);
            }
        }
        if (!flag) {
            return;
        }
        setStepAutoPos(true);
        setErrStep(null);
        let body = {
            deviceType: curOsType,
            deviceId: currentDevice?.deviceId,
            caseInfo: newStepList,
            needFishLog: needFishLog,
            sysAlertClear: sysAlertClear,
            paramsList: paramList.map((item) => ({
                name: item.paramKey,
                value: item.paramValue
            }))
        };
        // 如果有锚定步骤则从该步骤开始
        // 若无则 从第一个步骤开始
        if (currentStep?.stepId) {
            body.startStepId = currentStep.stepId;
        } else {
            body.startStepId = newStepList?.[0]?.stepId;
        }
        electron
            .send('device.replay.debugCase', body)
            .then(() => {
                setStartRecord(true);
                setResStatus(0);
            })
            .catch((err) => {});
    };

    return isEmpty(nodeList) ? (
        <NoContent text="暂无步骤" className={styles.noContent} />
    ) : (
        <Layout className={styles.layoutContainer}>
            <Header className={styles.caseDebugTitle}>
                <span className={styles.caseDebugStatus}>
                    调试状态:&nbsp;{getStatus(resStatus)}
                </span>
                {!startRecord ? (
                    <Popover
                        content={
                            curOsType !== 4 && (
                                <>
                                    <Switch
                                        size="small"
                                        checked={sysAlertClear}
                                        onClick={setSysAlertClear}
                                    />
                                    &nbsp;系统弹窗点除
                                </>
                            )
                        }
                    >
                        <Button
                            className={styles.caseDebugBtn}
                            type="primary"
                            size="small"
                            onClick={async () => startExecute()}
                        >
                            {curOsType !== 4 ? '执行回放' : '立即执行'}
                        </Button>
                    </Popover>
                ) : (
                    <Button
                        className={styles.caseDebugBtn}
                        type="primary"
                        size="small"
                        onClick={async () => {
                            setRecording(true);
                            electron
                                .send('device.replay.cancelDebug', {
                                    deviceType: curOsType,
                                    deviceId: currentDevice?.deviceId
                                })
                                .then(() => {
                                    // 前端给等待 3 秒
                                    // 设置当前步骤为人工取消
                                    delay(3000).then(() => {
                                        let result = {
                                            status: -1,
                                            msg: '人工取消',
                                            err: {
                                                cause: undefined,
                                                code: undefined,
                                                message: '人工取消',
                                                oriMessage: undefined,
                                                stack: undefined,
                                                type: undefined
                                            },
                                            screenshot: {},
                                            matchUrl: {},
                                            matchStatus: {}
                                        };
                                        let newStepList = updateRes(result, currentStep?.stepId);
                                        let errorStep = newStepList.find(
                                            (item) => item.stepId === currentStep?.stepId
                                        );
                                        if (errorStep) {
                                            setErrStep(errorStep);
                                            setCurrentStep(errorStep);
                                        }
                                        setStartRecord(false);
                                        setResStatus(1);
                                        setRecording(false);
                                    });
                                })
                                .catch((err) => {
                                    setRecording(false);
                                });
                        }}
                    >
                        {curOsType !== 4 ? '取消回放' : '取消执行'}
                    </Button>
                )}
                <div className={styles.divider}>|</div>
                {(location.pathname.includes('/' + currentModule + '/edit') ||
                    (location.pathname.includes('traffic') && query.view === '222')) && (
                    <Button
                        size="small"
                        type="primary"
                        ghost
                        onClick={() => {
                            if (location.pathname.includes('/traffic')) {
                                navigate(
                                    stringifyUrl({
                                        url: '/' + currentModule + '/traffic/detail',
                                        query: {
                                            ...query,
                                            view: '221'
                                        }
                                    })
                                );
                                return;
                            }
                            navigate(
                                stringifyUrl({
                                    url: '/' + currentModule + '/edit',
                                    query: {
                                        ...query,
                                        view: '221'
                                    }
                                })
                            );
                        }}
                        className={classnames(styles.runSettingBtn, styles.reRunSettingBtn)}
                    >
                        <CodeSandboxOutlined />
                        返回编辑
                    </Button>
                )}
            </Header>
            <Content className={styles.right}>
                <Splitter>
                    <Splitter.Panel min="20%" defaultSize={curOsType === 4 ? '20%' : '30%'}>
                        <div className={styles.stepList}>
                            {curOsType !== 4 && <ParamsSwitch />}
                            {nodeList.map((item) => {
                                return (
                                    <div key={`node_desc_${item.id}`}>
                                        <NodeStep
                                            editType={startRecord ? 'execute' : 'readonly'}
                                            showStep={startRecord ? showStep : currentStep}
                                            setShowStep={setShowStep}
                                            node={item}
                                            curOsType={curOsType}
                                            startRecord={startRecord}
                                            currentStep={currentStep}
                                            setCurrentStep={setCurrentStep}
                                            setExecuteIndex={setExecuteIndex}
                                            stepAutoPos={stepAutoPos}
                                            setStepAutoPos={setStepAutoPos}
                                            type={'end'}
                                        />
                                    </div>
                                );
                            })}
                        </div>
                    </Splitter.Panel>
                    <Splitter.Panel min="20%">
                        <StepInfo
                            editType={'execute'}
                            currentStep={startRecord ? showStep : currentStep}
                            setCurrentStep={setCurrentStep}
                            stepList={stepList}
                            curOsType={curOsType}
                        />
                    </Splitter.Panel>
                    <Splitter.Panel min="38%" defaultSize="38%">
                        <div style={{ borderLeft: '1px solid #f0f0f0', height: '100%' }}>
                            <div className={styles.stepRes}>
                                {startRecord &&
                                    showStep?.stepId &&
                                    showStep?.result?.status !== -2 &&
                                    showStep.result?.data?.screenshot && (
                                        <StepRes
                                            editType={startRecord ? 'execute' : 'edit'}
                                            curOsType={curOsType}
                                            currentStep={showStep}
                                            testRes={showStep?.result}
                                        />
                                    )}
                                {!startRecord &&
                                    -1 !== executeIndex &&
                                    null !== currentStep &&
                                    currentStep?.result?.data?.screenshot && (
                                        <StepRes
                                            editType={startRecord ? 'execute' : 'edit'}
                                            curOsType={curOsType}
                                            currentStep={currentStep}
                                            testRes={currentStep?.result}
                                        />
                                    )}
                                {!startRecord &&
                                    -1 !== executeIndex &&
                                    null !== currentStep &&
                                    currentStep?.result?.data?.extra?.screenshot && (
                                        <StepRes
                                            editType={startRecord ? 'execute' : 'edit'}
                                            curOsType={curOsType}
                                            currentStep={currentStep}
                                            testRes={currentStep?.result}
                                        />
                                    )}
                                {curOsType === 4 &&
                                    -1 !== resStatus &&
                                    query?.mode === 'default' && (
                                        <InterfaceStepRes
                                            currentStepDetail={currentStep}
                                            key={currentStep?.stepId}
                                        />
                                    )}
                            </div>
                        </div>
                    </Splitter.Panel>
                </Splitter>
            </Content>
            {1 === resStatus || (curOsType === 4 && [1, 2, 3, 4].includes(resStatus)) ? (
                <ErrStack errStep={errStep} />
            ) : null}
            {null !== testStep && [1, 2]?.includes(curOsType) && (
                <TestStep {...props} open={open} setOpen={setOpen} curOsType={curOsType} />
            )}
        </Layout>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentDevice: state.common.base.currentDevice,
    recording: state.common.base.recording,
    showModal: state.common.base.showModal,
    testStep: state.common.case.testStep,
    testRes: state.common.case.testRes,
    treeData: state.common.case.treeData,
    curOsType: state.common.case.curOsType,
    paramList: state.common.case.paramList,
    snippetList: state.common.case.snippetList,
    schemeList: state.common.case.schemeList,
    mockList: state.common.case.mockList,
    currentModule: state.common.base.currentModule,
    currentEnv: state.common.base.currentEnv,
    username: state.common.base.username,
    currentCase: state.common.case.currentCase
}))(DebugPage);
