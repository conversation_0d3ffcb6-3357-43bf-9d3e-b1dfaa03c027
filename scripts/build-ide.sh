#!/bin/bash
set -e
echo "start success"
echo "node $(node -v)"
echo "yarn $(yarn -v)"
yarn
yarn add @baidu/bat-tool
cd ../../ && mkdir qamate-native && cd qamate-native/ && mkdir core && cd core/ && mkdir pubilc
cd ../../qamate-front/core
npm run build:electron
cp -r ../../qamate-native/core/public/front ./front
tar -czf qamate.tar.gz front/
node node_modules/@baidu/bat-tool/scripts/upload-qamate.js
echo "build success!!!"