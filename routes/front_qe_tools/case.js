const routes = {
    path: '/case',
    component: '@/pages/front_qe_tools/case/CasePage',
    routes: [
        {
            path: '/case/index',
            component: '@/features/empty/IndexPage'
        },
        {
            path: '/case/info',
            component: '@/features/front_qe_tools/case/info/InfoPage'
        },
        {
            path: '/case/demand',
            component: '@/features/front_qe_tools/case/demand/DemandPage',
            routes: [
                {
                    path: '/case/demand',
                    component: '@/features/empty/IndexPage'
                },
                {
                    path: '/case/demand/detail',
                    component: '@/features/front_qe_tools/case/demand/DemandPage/DemandDetailPage'
                }
            ]
        },
        {
            path: '/case/edit',
            component: '@/features/front_qe_tools/case/edit/EditPage',
            routes: [
                {
                    path: '/case/edit',
                    component: '@/features/empty/IndexPage'
                }
            ]
        },
        {
            path: '/case/stamp',
            component: '@/features/front_qe_tools/case/stamp/StampPage'
        }
    ]
};

export default routes;
