const routes = {
    path: '/workshop',
    component: '@/pages/front_qe_tools/workshop/WorkshopPage',
    routes: [
        {
            path: '/workshop/ailab',
            component: '@/features/front_qe_tools/workshop/AILabPage'
        },
        {
            path: '/workshop/knowledge',
            component: '@/features/front_qe_tools/workshop/KnowledgePage'
        },
        {
            path: '/workshop/traffic',
            component: '@/features/front_qe_tools/workshop/TrafficCaseGenerationPage',
            routes: [
                {
                    path: 'create',
                    component:
                        '@/features/front_qe_tools/workshop/TrafficCaseGenerationPage/CreatMiningTaskPage/CreatMiningTaskPage'
                },
                {
                    path: 'info',
                    component: '@/features/front_qe_tools/workshop/TrafficCaseGenerationPage'
                },
                {
                    path: 'detail',
                    component: '@/features/front_qe_tools/case/edit/EditPage',
                    routes: [
                        {
                            path: 'detail',
                            component: '@/features/empty/IndexPage'
                        }
                    ]
                },
                {
                    path: '/workshop/traffic/*',
                    redirect: '/workshop/traffic/info'
                }
            ]
        },
        { path: '/workshop/index', redirect: '/workshop/ailab' },
        { path: '/workshop/*', redirect: '/workshop/index' }
    ]
};

export default routes;
