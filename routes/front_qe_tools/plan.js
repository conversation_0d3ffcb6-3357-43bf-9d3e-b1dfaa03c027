const routes = {
    path: '/plan',
    component: '@/pages/front_qe_tools/plan/PlanPage',
    routes: [
        {
            path: '/plan/index',
            component: '@/features/front_qe_tools/plan/IndexPage'
        },
        {
            path: '/plan/create',
            component: '@/features/front_qe_tools/plan/common/CreateGroupPage'
        },
        {
            path: '/plan/detail',
            component: '@/features/front_qe_tools/plan/common/DetailPage'
        },
        {
            path: '/plan/group',
            component: '@/features/front_qe_tools/plan/common/GroupPage'
        },
        {
            path: '/plan/daily/index',
            component: '@/features/front_qe_tools/plan/daily/IndexPage'
        },
        {
            path: '/plan/stability/index',
            component: '@/features/front_qe_tools/plan/stability/IndexPage'
        },
        {
            path: '/plan/daily/create',
            component: '@/features/front_qe_tools/plan/daily/CreateGroupPage'
        },
        {
            path: '/plan/daily/group',
            component: '@/features/front_qe_tools/plan/daily/GroupPage'
        },
        {
            path: '/plan/daily/overview',
            component: '@/features/front_qe_tools/plan/daily/OverviewPage'
        },
        {
            path: '/plan/daily/detail',
            component: '@/features/front_qe_tools/plan/daily/DetailPage'
        },
        {
            path: '/plan/api/index',
            component: '@/features/front_qe_tools/plan/api/IndexPage'
        },
        {
            path: '/plan/api/create',
            component: '@/features/front_qe_tools/plan/api/CreateGroupPage'
        },
        {
            path: '/plan/api/group',
            component: '@/features/front_qe_tools/plan/api/GroupPage'
        },
        {
            path: '/plan/api/overview',
            component: '@/features/front_qe_tools/plan/api/OverviewPage'
        },
        {
            path: '/plan/api/detail',
            component: '@/features/front_qe_tools/plan/api/DetailPage'
        }
    ]
};

export default routes;
